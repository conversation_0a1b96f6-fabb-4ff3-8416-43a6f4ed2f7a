{"cells": [{"cell_type": "markdown", "id": "61f56afc-bc15-46a4-8eb1-d940c332cf52", "metadata": {}, "source": ["# Introducing Colab\n", "\n", "Learn about Google Colab and set up a Google account (if you don't already have one) [here](https://colab.research.google.com/)\n", "\n", "You should be able to use the free tier or minimal spend to complete all the projects in the class. I personally signed up for Colab Pro+ and I'm loving it - but it's not required.\n", "\n", "To see some of what <PERSON><PERSON> can do, please follow the link below!\n", "\n", "https://colab.research.google.com/drive/1DjcrYDZldAXKJ08x1uYIVCtItoLPk1Wr?usp=sharing"]}, {"cell_type": "code", "execution_count": null, "id": "e9289ba7-200c-43a9-b67a-c5ce826c9537", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}