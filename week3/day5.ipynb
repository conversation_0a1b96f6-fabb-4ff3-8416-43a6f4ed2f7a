{"cells": [{"cell_type": "markdown", "id": "61f56afc-bc15-46a4-8eb1-d940c332cf52", "metadata": {}, "source": ["# Meeting minutes creator\n", "\n", "In this colab, we make a meeting minutes program.\n", "\n", "It includes useful code to connect your Google Drive to your colab.\n", "\n", "Upload your own audio to make this work!!\n", "\n", "https://colab.research.google.com/drive/1KSMxOCprsl1QRpt_Rq0UqCAyMtPqDQYx?usp=sharing\n", "\n", "This should run nicely on a low-cost or free T4 box."]}, {"cell_type": "code", "execution_count": null, "id": "e9289ba7-200c-43a9-b67a-c5ce826c9537", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}