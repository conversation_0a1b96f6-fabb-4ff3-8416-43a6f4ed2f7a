{"cells": [{"cell_type": "markdown", "id": "61f56afc-bc15-46a4-8eb1-d940c332cf52", "metadata": {}, "source": ["# Tokenizers\n", "\n", "Please can I bring you back to the wonderful Google Colab where we'll look at different Tokenizers:\n", "\n", "https://colab.research.google.com/drive/1WD6Y2N7ctQi1X9wa6rpkg8UfyA4iSVuz?usp=sharing"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}