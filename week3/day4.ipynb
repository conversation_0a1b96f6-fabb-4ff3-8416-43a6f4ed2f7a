{"cells": [{"cell_type": "markdown", "id": "61f56afc-bc15-46a4-8eb1-d940c332cf52", "metadata": {}, "source": ["# Models\n", "\n", "And now - this colab unveils the heart (or the brains?) of the transformers library - the models:\n", "\n", "https://colab.research.google.com/drive/1hhR9Z-yiqjUe7pJjVQw4c74z_V3VchLy?usp=sharing\n", "\n", "This should run nicely on a low-cost or free T4 box."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}