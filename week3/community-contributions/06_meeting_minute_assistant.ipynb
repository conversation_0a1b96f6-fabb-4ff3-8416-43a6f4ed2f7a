{"cells": [{"cell_type": "markdown", "metadata": {"id": "HFOR8SGHPyj3"}, "source": ["# Meeting Minutes Generator (STT with LLMs)\n", "---\n", "\n", "- 🌍 Task: Generate structured meeting minutes from audio recordings using Speech-to-Text (STT) and Large Language Models\n", "- 🧠 Models:\n", "    - AUDIO_MODEL: whisper1\n", "    - LLM_MODEL: meta-llama/Meta-Llama-3.1-8B-Instruct\n", "- 🚀 Tools: Python, Gradio UI, OpenAI / HuggingFace APIs\n", "- 📤 Output: Structured meeting minutes in Markdown format with real-time streaming\n", "- 🧑‍💻 Skill Level: Intermediate\n", "\n", "🎯 How It Works\n", "- 1️⃣ Upload a .mp3 meeting recording\n", "- 2️⃣ Submit the audio to generate meeting minutes in text format\n", "\n", "You can download some meetings from this link to test the code:\n", "[https://www.rmofspringfield.ca/p/meeting-audio-files](https://www.rmofspringfield.ca/p/meeting-audio-files)\n", "\n", "\n", "🛠️ Requirements\n", "- ⚙️ Hardware: ✅ GPU required (model download); Google Colab recommended (T4)\n", "- 🔑 OpenAI API Key (used for whisper1 transcription)\n", "- 🔑 Hugging Face Token (for the LLM model)\n", "\n", "⚙️ Customizable by user\n", "- 🤖 Selected model: AUDIO_MODEL / LLM_MODEL\n", "- 📜 system_prompt: Controls model behavior (concise, accurate, structured output)\n", "- 💬 user_prompt\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "A_osPeBQNAdv", "outputId": "11cc73e0-9aad-4f57-e1ae-2d71c4eb0444"}, "outputs": [], "source": ["# Install required packages in Google Colab\n", "%pip install -q requests torch bitsandbytes transformers sentencepiece accelerate openai httpx==0.27.2 gradio"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "pL-8yTOlQiOH"}, "outputs": [], "source": ["# imports\n", "import torch\n", "import threading\n", "from openai import OpenAI\n", "from huggingface_hub import login\n", "from google.colab import userdata\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, TextIteratorStreamer, BitsAndBytesConfig\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Constants\n", "AUDIO_MODEL = \"whisper-1\"  # OpenAI Whisper API model\n", "LLM_MODEL = \"meta-llama/Meta-Llama-3.1-8B-Instruct\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "62c2Wbt3P5Ew"}, "outputs": [], "source": ["# Google Colab User Data\n", "# Ensure you have set the following in your Google Colab environment:\n", "hf_token = userdata.get('HF_TOKEN')\n", "openai_api_key = userdata.get('OPENAI_API_KEY')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["login(hf_token, add_to_git_credential=True)\n", "openai = OpenAI(api_key=openai_api_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "smyocqu_P6yg"}, "outputs": [], "source": ["class MeetingAssistant:\n", "    def __init__(self, model_name=LLM_MODEL, audio_model=AUDIO_MODEL):\n", "\n", "        # Load tokenizer and llm model\n", "        quant_config = BitsAndBytesConfig(\n", "            load_in_4bit=True,\n", "            bnb_4bit_use_double_quant=True,\n", "            bnb_4bit_compute_dtype=torch.bfloat16,\n", "            bnb_4bit_quant_type=\"nf4\"\n", "        )\n", "\n", "        self.audio_model = audio_model\n", "        self.tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "        self.model = AutoModelForCausalLM.from_pretrained(\n", "            model_name,\n", "            device_map=\"auto\",\n", "            quantization_config=quant_config\n", "        )\n", "\n", "    def transcribe_audio(self, audio_path, progress):\n", "        \"\"\"Transcribes the uploaded audio file using OpenAI Whisper API.\"\"\"\n", "\n", "        progress(0.3, desc=\"Transcribing audio...\")\n", "\n", "        try:\n", "            with open(audio_path, \"rb\") as audio_file:\n", "                transcription = openai.audio.transcriptions.create(\n", "                    model=self.audio_model,\n", "                    file=audio_file,\n", "                    response_format=\"text\"\n", "                )\n", "                return transcription\n", "        except Exception as e:\n", "            return f\"Error during transcription: {str(e)}\"\n", "\n", "    def generate_minutes(self, transcription, progress):\n", "        \"\"\"Generates meeting minutes from the transcript using the Llama model.\"\"\"\n", "        progress(0.6, desc=\"Generating meeting minutes...\")\n", "\n", "        system_message = \"You are an assistant that produces minutes of meetings from transcripts, with summary, key discussion points, takeaways and action items with owners, in markdown.\"\n", "        user_prompt = f\"Below is an extract transcript of a meeting. Please write minutes in markdown, including a summary with attendees, location and date; discussion points; takeaways; and action items with owners.\\n{transcription}\"\n", "\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": system_message},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "\n", "        inputs = self.tokenizer.apply_chat_template(messages, return_tensors=\"pt\").to(\"cuda\")\n", "        streamer = TextIteratorStreamer(self.tokenizer)\n", "\n", "        thread = threading.Thread(\n", "            target=self.model.generate, kwargs={\n", "                    \"input_ids\": inputs,\n", "                    \"max_new_tokens\": 2000,\n", "                    \"streamer\": streamer\n", "                  })\n", "        thread.start()\n", "\n", "\n", "        started = False\n", "        # buffer = \"\"\n", "        for new_text in streamer:\n", "          if not started:\n", "              if \"<|start_header_id|>assistant<|end_header_id|>\" in new_text:\n", "                  started = True\n", "                  new_text = new_text.split(\"<|start_header_id|>assistant<|end_header_id|>\")[-1].strip()\n", "\n", "          if started:\n", "              if \"<|eot_id|>\" in new_text:\n", "                  new_text = new_text.replace(\"<|eot_id|>\", \"\")  # Remove the unwanted token\n", "\n", "              if new_text.strip():  # Only yield non-empty chunks\n", "                  yield new_text\n", "\n", "    def process_meeting(self, audio_file, progress):\n", "        \"\"\"Handles the complete process: transcribes audio and generates minutes.\"\"\"\n", "        progress(0.1, desc=\"Processing audio file...\")\n", "\n", "        # Check if a file is uploaded\n", "        if audio_file is None:\n", "            return \"Please upload an audio file.\"\n", "\n", "        try:\n", "          # Check file format\n", "          if not str(audio_file).lower().endswith('.mp3'):\n", "              return \"Please upload an MP3 file.\"\n", "\n", "          # Get transcription\n", "          transcription = self.transcribe_audio(audio_file, progress)\n", "\n", "          # Generate minutes\n", "          accumulated_text = \"\"\n", "          minutes = self.generate_minutes(transcription, progress)\n", "          for chunk in minutes:\n", "            accumulated_text += chunk  # Append new text\n", "            yield accumulated_text   # Update Gradio output with full text\n", "\n", "        except Exception as e:\n", "          return f\"Error processing file: {str(e)}\""]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "fyMu9JrBRBGI"}, "outputs": [], "source": ["class GradioInterface:\n", "    def __init__(self):\n", "        \"\"\"Initializes the Gradio interface for processing audio files.\"\"\"\n", "        self.assistant = MeetingAssistant()\n", "        self.iface = gr.Interface(\n", "            fn=self.process_audio,\n", "            inputs=gr.Audio(type=\"filepath\", label=\"Upload MP3 File\", format=\"mp3\"),\n", "            outputs=gr.Markdown(label=\"Meeting Minutes\", min_height=60),\n", "            title=\"AI Meeting Assistant\",\n", "            description=\"Upload an audio file to transcribe and generate meeting minutes.\",\n", "            flagging_mode=\"never\"\n", "        )\n", "\n", "    def process_audio(self, audio_file, progress=gr.Progress()): # Adapter between the UI and the backend.\n", "        \"\"\"<PERSON>les user input from Gradio, processes the audio, and returns meeting minutes.\"\"\"\n", "        response = self.assistant.process_meeting(audio_file, progress)\n", "        for chunk in response:\n", "          yield chunk\n", "\n", "    def launch(self):\n", "        \"\"\"Launches the Gradio interface.\"\"\"\n", "        self.iface.launch()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["0f705a9046d34fb0a7ab8177a6521b88", "28e61ba2b56f4d3484dd3ec0eecb12aa", "ecb3207e61d44ca39c7f8a97546c6686", "5a4f5291a0b24178a44b1b3c2401a957", "2472dbee01b149c0ac7efe6eaa5ffd66", "65c6998bdf7444de85b37468f6b6f42e", "4323c3fbb5d24b38a920313479bb5c57", "8af3f0faa5b144efbf1aa443c2839d2c", "e0f3096904354279a95175d116816262", "0e6ad8796e9e4b868c8507722c9cbf33", "a32fde87feaf42199de6efe2c94085aa", "e6c97a25f41044ab89e49d9bf9836de4", "78562aef2a6a422dba1145306b294823", "980453816461473fab04be9b6fbf03b5", "39ce806000744113ae35a617adfe2571", "2991097320c148b7b0eb81e3ce866df2", "1a502c41fc3044c2a7c24ad144c209e9", "91a907a6aa044a3288cbf4deca77eb67", "7f78522bd56847bfa740ff8146e726d2", "eb2129af41b24e2ea64a962cb041164c", "44bdcc01d31a43eeaa7139018c24a83b", "a21477f95f604f618a3aa2f48c00f7f3", "0b97c1be64664458abfc0109857d86eb", "2d0a0c7b89a64b7499ce87e81044d461", "19acf6f364d8478ea264dde4fd4a1ca1", "c084a0f7a7c04c90a0fed54c60cc8e79", "482baf34221048bd8bb4e57cebe44707", "f9c94568e6b342dda23ccd3be906eec0", "c6f719622eae45b0b110b377918c2eb2", "83e68bf6b3994fd5a6eea4ba722864c3", "01d3ea10affb447ead36c6b4476e7a4c", "5f93b389541e4ec09aced45d018bc8c1", "90b2cfcc49804e78b6bebb383e9e6893", "6f1e02e1c1da4bd9a6d13b3907cd78ae", "39ea33000ee741c2b9fdf518f657d872", "1b6204edaebf489e9d3e70f6d722c33a", "4ae96d4297b84fd1a9022a9c07f7987e", "2c43cc66619945a18a82cd9437ea60be", "9e1dc2cc46fa4a4ea6c5d3a50333a02f", "7c11259f23a6440babc156ac7d4b94c7", "48c2c5afef3d47e3b9bada3cdc339ec9", "a0ca5ccd08df4b9191a6400907f239fa", "72a5d5d5f42e44f197b5829801fee49c", "37cefb5abc424fca84d5ec4d7b90ff1f", "afce35f6ca0545d99937a2fca8030cb0", "fab983c8f0d544a2950d03acd5c39644", "f1a00e2402d2498292cbc5b767b1b3a9", "ca4db027b9764a8180617aae1b215f60", "d96a8910fbc5451083df650386ce6726", "5bd8e043fbd64c7c9dfb0d871737786d", "1ca523532aa5433c91df9cb53291ba29", "0b5340eb370a490ea946a446a9ab2eaa", "5f822db8ca764ce4b8dd7b99a83c7286", "37622f8dcbe14ac5ad80c9b09c8c4005", "1a73f8a262a94cd48d0370bbfd582405", "99bfb965add64f609f0ef008c443cdb9", "f1afbbe6e1fa4239af3d79b42f1ffc26", "2574026b82a040f089bfd202db5ef91d", "32bc3c434f824c618659693de6bd929a", "6d19a5bd166443b1acbf261287be09ac", "58e73b94784645f699f957693aaf6e6e", "4e5c99b156c545b096ba538b1a8c588a", "bde0c4ad4eea4944b76b34ad9c19bb89", "b9ab6e3935c646e691c0b5143d47d4b3", "d4a06441bff74e0e9fe8978014660e90", "a8010eecf9bc4e8ebfc906489ff54543", "7e9cdfd05f074c1798b8e3d936f6e7de", "57b3fe293dda483bb3717d7bd3509cce", "b080c2078a3f4d93b4d8367755d96272", "a1a7f450bd8d4917b796c6e13a5be9e1", "e58ff13df5a04fd5b0496e82384fe439", "ddf5e150f83944bfb07d8f19a177a50a", "71939dd7929243e38419abec94b209f9", "fe17e6c350c54a2d85864bb8d6d50d85", "4a69b6ea437e4682819ed2d0aef048b8", "99a8e8cb1ad44d5f999c07cf9a913ef4", "1dd62e85589f4d60912e79dee1b39a3e", "0a634fec1cf544af82bd17af73bf417a", "c7e74bf1bb0f4d57ae95aa4397691e01", "e144ffa2b708446d940bfdf54741c7ab", "d65f1f5e345546b380c8e9be9d4dfb9b", "c2e4a8d768d245529bdac929585136c5", "a4317d864cc4445d8597ed695c3d4c35", "5429cebc5a28408985824c4f501e050e", "dc3100a6c9d946568ee0e297934773ff", "10ecb81d605a4534a13332371ac9041d", "570b18cdf9034cc780e838724a70904e", "1915b872ec55435080456092d9ec8717", "50f682e340cb4e58899aa7e9ea4741db", "569ec309d2f3490f94c85bbb3680258b", "dd319b7cb2b7425e849d6682c7f05390", "fde199a94cc7488690f81d5add9eb08d", "38d2575bf5144cf3a02d304444bdc481", "a06cc81bd0114aefb1e80868791b5be8", "819319dd58b44e999a13b0bd0e78c88e", "38f581406490488981c76bc7e7e64005", "2ef62c2bd93c462eb7c4522c8a156e0d", "c2cb2d57701a4e55b7bfa3f842a91c09", "4ba3375ddb584f068d2cdcf060cdfa9c", "125f8ae49e504b809e5e39f6d940204e", "2c4abe3e713846deb1b0a9bec03298d4", "b5f524e95a0d42febd4466bb7a8ad239", "b5b9a98cb74c409cbdc50a16a3393665", "ec145da746d74c5e896900f7462b630b", "aa7d2a5d452b4eb1b537542a9731b94a", "3d03ed01daeb43d58c4f15bc591043ba", "17b7619e5bfd4d74b1a3bee1c7643e74", "4c95edb35fab4e12b027248e93b61883", "7d7bcf713d3b4531846635fe43fb268e", "918a8b2b832645d18767bc2e4451d556", "3b8be978c3af4ef4b7bc16420e6a9f8a", "25efbd99d0134866940cc3fde41aacf7", "07e8ec9ab2ba4339a8e2736156f28eab", "7fa4e5411e384c568b37a51bc94b3ee2", "45fa63f56c814015861d05beb8800e09", "8a73f7fbe20a4d56a76588db2ac35cea", "3a18d0771ef74923ba209733afdd0e47", "9104c3953c254878b2020764569613a9", "8a237f2467734eddace5d9f9aafce9e7", "a737c2d22cd84141a2f28721ab69d28d", "be17f6c8dc9c40efa94c3af82a8efa6a", "7a74e712b53c4a659dc09766885c12d9", "9afb4b8b3ddf4a0abb75eedbcf3bc7c1", "27697007fa6d4736a5eb1e1b0eea2d82", "e5d4b0e78c3740cd8cf9cda0e4a93972", "2740d759410d435087c4ae0772d6ad73", "19f5f9369e3043e0984e160c50e0a32e", "7b488376756843fe84fcce2e7abb5cd9", "c2745572ac434351ad9b2c9506d8d0b7", "25119517d9a043ba91fd3fbefb8377a4", "4079ed7e7f794755afd5daad3f00a34a", "14f5761bfbd340198267e3986f4035a0"]}, "id": "BI91BBEJRB0K", "outputId": "c4853642-832e-4167-e220-2a2d0fd279a8"}, "outputs": [], "source": ["if __name__ == \"__main__\":\n", "    app = GradioInterface()\n", "    app.launch()"]}, {"attachments": {"image-2.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABXsAAAGJCAYAAADbveCbAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAP+lSURBVHhe7N17XFV1vv/xV6diT06b0aChw04HGOeAlmAdIBLJVMwgE3QUTSXG8TKpOah4y9HEdMy7kYnmJQdRU5wxNIMcbxniGDiT0k1ODpi2mTGhPO4ZO9D4m98fe2/YLDYI3lJ8Px+P/XDv7/e71vqu77oAH7/7s25rHxzyb0QMnp37Bt0rVpG0+LCxSuSmo/P5ZjWAhRt7Qu4vmbTBXhKV8hrJPztB2nOvkGdsLg2c6zfPWD479w16/wRKXfp687h5xvmq6zyOjFEt2Ts4lfXGuisxJJXfx8COq71eEbl66lynEUxbOZLAz939PLq13NQ/A3Rfr6tO313O9TNP1l93mddB3fOn7u8ZN7P6f2+9MT079w16s4t+07bgF9KFJ0ctNDa5IiufC8dn9F76tNrPyt/O4bmVBcYmN7SVz4VD/yye6w4Hn0vgYwAG03lRMj8pSWPj2bh668zByYa1XVr1eAUU89bE0ZwBYDpPr+wNe8OxdL+5xu9qWfncDv7j/83iNgV7xZ2b7cYr0hCdzzexIan8PqaNS8Gpm/OPg+ukwXNdY3l93KrjfLWDAp3HkTEqmB9ygb+seJ65B40NRORG4vyPumpf2AMit6zqnwU38c8A3dfdauhcb6ju8tgDxg+ba0r++Zd6fs+7CTX4e+sNKYJpK4fAhmt3/roGe29WD/6mgM6tXQpO76jen4bqLo89YPzg3TUltqI0NqZvdG10i5muYK+IiIiIiIiIiIhIc/Af/+9i44K91i9OGYtERERERERERERE5Cpr3dpiLGq0/zAWiIiIiIiIiIiIiMjNR8FeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeERERERERERERkRtc9+7dGTJkCN27dzdWVVOwV0REREREREREROQG9uCDD9KxY0d8fHzo2LEjDz74oLEJKNgrIiIiIiIiIiIicuNq0aIFXbt2rVXWtWtXWrRoUasMBXtFREREREREREREbly9evXCw8OjVpmHhwe9evWqVYaCvSIiIiIiIiIiIiI3ptDQUFq3bm0sBqB169aEhobWKlOwV0REREREREREROQG9OijjxqLajHWK9grIiIiIiIiIiIicgP6/PPPjUW1GOtvax8c8u9aJW5YvzhlLBIRERERERERERGRq6x1a4uxqNE0s1dERERERERERESkGVCwV0RERERERERERKQZULBXREREREREREREpBlQsFdERERERERERESkGVCwV0RERERERERERKQZuK19cMi/jYVG1i9OGYtERERERERERERE5Cpr3dpiLCIqKoqgoCA8PT05f/48x48fJy8vz9hMM3tFREREREREREREblQDBgwgPDwcT09PADw9PQkPD2fAgAHGpgr2ioiIiMj3wKcn81ZmkjmrP7FmY+WtKyhmEpmb0lnyTCh153OIiIiIyK0mKiqK+++/31gMwP33309UVFStMqVxEBEREblWzH5EP+CNh+Nj+SdHKLAZ2jgEtQ8lwBPARsnhYo4bG7iw+IcS4mN/X3WmmD2l9ay01vYvvd7rKXVTAX3b29+X546m2wtHjE2+P004bldXMtlHBxMAQBl5z8Uz5rCxjYiIiIg0d65pHEaMGFE9o9ed8+fPs3r16urPCvaKiIiIXCOWiZnkDgms/mzdlkDMSydrtXFK3VJA30CAYrZ1TCTV2MBFUtpeUro4psOe2U9qzylsMzYCwqdlsSbBz/Hp0uu9qiKSyfx1KGbKOfrqeFINQcuU9QdJCraHU8t3jqbb9Bsn2NuU43Z1DSXrg1EEmQDK2PNcPBOaGuy9xLhfH/1Zsqk3AUDJ24lMeNNYLyIiIiINcQ32pqSk1KpzZ/HixdXvlcZBRERE5JowkxTsX6vE8lB/wmuVXAU+wXTvYiwE8GPgo85A7/fA24+A9oEEtA/mfm9jJSweM4OM7TvYk7GQmTdQoPe6HTe31jH+hRXs2b6DbXNnND3Qy6XH/frwdfQhkADlohARERG5rhTsFREREbkWzEPoGOgBVHH88Ef2Mv8w+rY1NrxSXoQ/099YCG2H0K61sfA6CvKmwVS8tv0snjmHCUu3UvcZwt+j63bc3LPuW8eEmXNIzXJsu6kuNe7XQy8/7jWWiYiIiMh1oTQOIiIiIteAZfRackd2sKdP6FnEI7v6YwGsWQnEzK2bEuCy0jics1HZ0ozJdoRlUaOpydQFsS9nMy/GF85UYPPxwlzfes2hjJ34S7qHBWJpCbayYo7+4Q0Wv3kEq7Et9gerpU5L4JEHAvFuYaO8+DgfZM0hNbfCXt+2N9N+Hohn227EhnkBVZTs3s7xcoB/8NkfVpBxAsLjRtE36O5aZQB0Gcy8CF+gjIL5G9nm05PUaUN4LMQfM+VYC7ez6qV15NTJoWsmPO6XjIyPpF2gLx4Xyij9pIiSssqaJn8/xNSMfNeF6mjqcbNv9znGJkYS4OuNR1U51uNH2Ls2jWWFrp1sTDs/ksb2p10L4EIx25btoMBlDRb/3oydMoTwIF/MHpW1j1Ujx925nqTRTxL+QCCWliaqzpVRUriVNxdtNYxrJGOndLLv/+GFLDvgxcDx0xnYI9h+rpw8wjtL57C4uv+O9t5hdO/hhwkoL9xKgWO79nW4rl9ERERE3LmSNA4K9oqIiIhcdWamrc9lYLAHlO1iaux2nspJJ8oXKN3K8D4LawXxuNxgb/ERjrUOJaRFFccWdSZxg7NFJMt3LSXKp4rjRaUEBQe6Xa+l23TWzOmNpYVLoYOtMI2pIzbWmnUblZTOrF+H4n27SyEAVVi3zWD4S/ux9lpK/pzIemaX2iiY3p3hO13zDteUATAxk6IhgcBJcuaXEj6uK96m2mupLN7IhAFpLn0zk7Qki5RuXrXa1VG8keABacZSF009bg1vtzx3Ct1e2N+Edj1Z895swlsC5/JZ/Ph4MhxtLHHzyXyxq5uxB87sYu6yuxl7qXE/0JV5r08ntr37VpzZz+J+U8ioDvjWPDDOum0hJRHJRPk6H1vnUFnMthGJpBbVbu9OyYZw4hcZS0VERETE6EqCvUrjICIiInLV9XOkAgDb5wfJ4QgFnzsiaP7BxNYTa2syLxvWIhvgQcgTo6j+lbBLb9r52ANxHxTVXqSaeShLX3YEei/aKNm9lZw3t5L3qb2f5rBRjB3p0tEus5k13hHovVDGse1byXlzB8dOVwEeWPomM60LUH6Skk+LsZZXORaswlZaTMmnxZR8WsSX5TWrbJgfsVO64n2HjfITxZScqMA5R9cU2JuRCTUtLSNf4XlHINVWtJHUPjEMn7CR486g5YUK+/Y/L6tZyK0mHreIySQ5A7jlH5GzaAZzF22l4IQNKot5/839TWtXr1BSfuUM9FZwPCuNuS+kkXPgJLaLVZTszmJzY8bdVo6H2b4TlWXFFGzfSs72fKzOcfLpysCx7vM8W/pOIsrXg8qyk5R8epLyC44KUyBPjXamESmzb6+sZnpwZZmzD8WUuJ0qLiIiIiJXk4K9IiIiIlfbyM4EmQBsfLZ7FwAZ+cWOYGUgHX9ljBpepjs8+GyfY72BoSQ5VpvUNxRvoLJoH+WtfQ0L2Y1YOKS6jwUvxRM/aSFT5y9kzKBhbCu2B3CDYp5zPJjMj3njeuINUHmSnOfiSZy5kKnz55D49GwKzgH42nMHH04jcVAimz9xhmYr+WxtIvGDEokfNJ7Upjx0rPwIGUO7061fIvH9Ypiw3RmsNWN5KLS62cDOgZgAKj/inTFpbCutoGBfGuPfdOS9bVFJyYuJxE/fWr2MW009bo/62ccEKHl3GFM37GLzhoUM79edhN7jHLNdm9CuXpEEOA9j8bskzN3I5tyNTE1OIDI2jqmLPmrkuH/EhNfXse2FGMJiExk+cyFTZ44nZsyO6pQdlkeH1PMwugqOLR1GWGwC8YMS6PbcDpxxe1ObYGIB2MqEQYnE76sJqlv3OfuQyIQ3q4tFRERE5BpRsFdERETkKkvpHGh/c6GYY870BFn5HHfMhgwI+2XNLNwrlbWdY+cAUwfCf2UGehIVYk+PcHz3RmNrh548EuQIXJ7ez6rtrolaT7L6L45gnW8QUQDmOIL87UWVRduZWis4uYu8j+3L1wT9ro6Sd0ez2GVbeYWlOHtqalGTEsFscqQW+PZ8zSxVwHrqvKO9Gc+gmvL6NPm4fV5e3R9Lt6VM61zTp+NnHDmMaUK7eh2n/JzjrV9X1oyOpHp3zlRwvKbhpe1cUZNfGbD4hxJrsVHpnKlr9qJdda2L4ndJzHB5aFxRISXOPrW4Ww9kExEREblBKNgrIiIiclUN5RFHKgBOl/LXiFBiI0KJjSjGetrRJDCUEVdpci/sIqfQHrwLCPslll5P0q4lcK6IPVnGtk5BeLd0vPV9kuWHDlLo8sru6/gqv8nDngO2i191MM8UPKpW28JDB3n+UcfOfE9Bv9K/O0KpLf0JCa4pj3rY35HD1kblJdNHXMZx2/kGuz+1p00w+UYy8LVcivKyyZrWsyYY25R29dpFxk7HDGOTL+Ejl5L1573sWzeJgT7GtpcWFJPMmk3Z5P+5gNy30pn38mAC3ORtFhEREZGbj4K9IiIiIldTdSoAILA/81amV79iHRNH3aYEuALbdhbZv1IfGMq0bkGYgfLC31c/3KtBt3tgamF4GR6IVovJTXvnQ8P+VVWdV/d6yli7g5JKAF+il2eyfMok5i3MZF5fe+6Dyk/3k3Gp9BGXddw+InVQHHOzPqrJYWv2JShhNlnvrWVadeC5se3ql7cokcQXtnL8jCMn7+1mvB/qz7ScvWSN7mBsXo8OpKzMJevlwYS398VMFbbSjyjYvp8SZ79ERERE5KamYK+IiIjIVTQ2rDoySOWFqjovp4DgIVcvlcOBFRw9DRBIVDcvoILPduYbW7l3YisxHcMJdvtKJBWgsqbftoNz3LRzvKKnsNl13ddLURqr3jlpf28OJOqZ/sT2CMQMVBbvYPGv0igwLmNw+cetgs1zh9GtU3eGz3QJxrbswMCJLg/Na3S7+h3PXUhCz87E9JlDzoeOB9bdbiYoaRzTGvF/B5aR4xgYYU8hUVm0jqmPdyayzzCGz9xH9XPdREREROSmpmCviIiIyFUzmPAHnKkAdjCmU2fCar3Gked8dpXLA9Wu3Ek2/8kR7AQoO8I7B1zrjbZT4kxN0DaSlEvNLN1dVD3z0xzyJGOvWr+vkl7z+U1fP7AdIWPQFFZnbCUnfQ4T+nQnbMAcNrumJHbrahw3GwXbF5LQcxR7nGPb5r+INrRqfLv6WUt3MHWoywPrTH4EdDG2qiv6AT/7g+woI++lFeRcclxERERE5GajYK+IiIjI1TIkkiBH7tPyj991M5v0CO8ccwboAumY6DZqeFkKluVT4nhvPbiWHEN9bSdZ/PZHjpQLvkQvSSclrKYvFv/ezJs1yv5wNgA28tYBR7/NoTy7fnqtXLFBnUexZErvWrNTz15wRhLNtHvSUWc2N2oGa1MN7BZsz817wYb19H6WLV3I1FU72FPayGjm5R634Onk5mWzZlhozX6Z/fF0DqUzrUVj29WrA0ve2ktu2lBiqw+TmQAv54cqnCto3LibuT/S8ZA4cygpK8cR7szhfDWcd5kJHTHJcR6ZsVy9011ERERE6qFgr4iIiMhVMrZLsGPmpI2S/CPGagBy8kuxh+M8CIowpgS4ArY32JtbTMmn+9mT5TLLtx7WVa/wVpEjMOgdStLqvdUPXMt9azqxcYMZO7ImOrdt7hvklTkeMubfm2m7ChztC8h6bSjRz/ySaS6zS3Pe/cieRxgwd55O7p8PUpSXybSImjZXy+bjjkCsT1emvVf74XGFh/ay7/fpLHnGJdBqcHnHLZLlC3tjMfsSPjad3A8c23tvuiNwWkXJuxvY3Oh29ev78gKi/c1YuoxiXvX+7SWls/34VBa/S8Zue9uGxj0j3/GQN8wEjc+l6NBBCt9LJynCCy46t3YVvHmQ487oddv+LP/zQQr/nMvSZwztREREROSqU7BXRERE5KpwSQVgK+aDncZ6h51Hah6GVW9KgMthY9kLicQPmsLiE8Y6dz5i7rMJzM0qxuYI9DkfuAZgO5HPBx+6NLftYMyAcWQcOEllrfbARRvlH+aTd9yl/YEZvJZ1smbG6u0egC8WR4Dyqlo1m82fOh9cZnyAnBnvtqFET0kn8+VI45JXcNzyGfPsDHI+LLOPh/PBdbfbx6Nk2wzGLPoIaGy7+m17YYj94W421/2z11UW72Dx8DTynI0bGvesKbyWW1ZT18IDEzZKsmYw1ZkS4mqwrWPRsnzKnQHk2z0w3e6B5YGehoYiIiIicrXd1j445N/GQiPrF6eMRSIiIiLSbJgJDwvE+3b7p/JPjlDQYAYEL6Ii/O2pE7BRcrgY1zhvLT6BxPrbW1adKW58aoUmSFqYS0oPLyqLt7J4aTEBXQLxdNR5+IbxSBc/e1/L9zP3mjxErrHj0dh29QtqH0qAY+dspUfIO2Ns4dDQuFfXXV4fGs3sR/QD3ngAnC8l59MKYwsRERERcaN165rvpKWkpNSqc2fx4sXV7xXsFREREZGbWDLZRwcTgI2Cmd0Zvt1Y35M17822p0y4ZsFeEREREZGr50qCvUrjICIiIiLNgJl2Mf0JMpTFjk0gxPHwsfLCHQr0ioiIiEizpmCviIiIiNzEtnPUka/XHDGJrD8fJD8ni+y3cin8YC/zhnXABFR+upGZL+QbFxYRERERaVYU7BURERGRm9hJUgeNYtn2mgeYmX39CPD3wnRHFbayj9gzfzTxg1weYiYiIiIi0kwpZ6+IiIiIiIiIiIjIDUI5e0VERERERERERERucQr2ioiIiIiIiIiIiDQDCvaKiIiIiIiIiIiINAMK9oqIiIiIiIiIiIg0Awr2ioiIiIiIiIiIiDQDCvaKiIiIiIiIiIiINAMK9oqIiIiIiIiIiIg0Awr2ioiIiIiIiIiIiDQDCvaKiIiIiIiIiIiINAMK9oqIiIiIiIiIiIg0Awr2ioiIiIiIiIiIiDQDCvaKiIiIiIiIiIiINAMK9oqIiIiIiIiIiIg0Awr2ioiIiIiIiIiIiDQDCvaKiIiIiIiIiIiINAMK9oqIiIiIiIiIiIg0Awr2ioiIiIiIiIiIiDQDCvaKiIiIiIiIiIiINAMK9oqIiIiIiIiIiIg0Awr2ioiIiIiIiIiIiDQDCvYK+AQS5WMsFBERERERERERkZvJDRzs9SIqIpAg1yKzH9Fhflhcy9xKJvtoJqnG4svWkzXvFZA90Vj+/UvdUkB+Wk8AkuZksS+tv7FJg/q+mEVhzlqWrF/KiF6zyc1ZygiAXkvJf28pScYFmj0/UlZnkz0lFBzj+/0e9xv33LuZXM61ccVcrycRERERERERkevgBg72DiFl5XQGuhZ1SWbWwmSiXcukWkmZlfJzlY0IhjuF0j3CD+ubnQnrOZ7Vp8qwnqmgymxs1xhdWf52Nst7GMsvYexaCteNMpZ+jyooL6ug3FZprJCG9JhN7tuza1+v3yfDedX0a+MquKLryekyrysRERERERERuSXdwMHeS/EiKswPC2bCw0KJNc4CNjL7ER0RSmx7L0OFF1ERocRGhBJuDMr4BBIbEUq0/z+ovGCoq+Zu+Sb0zbGNWNcZy8YZzD6Btfvtsi+2yqrq4pLcjaz63X6sNS0Jam/smwuzP54twGQOJdrfDKW5bE7fwB6bsaGdxd++LrcpH3y88TSb8WxTe4wbXAYz4a3MmFp6VffR4m9va/F3GRN3Y3SpMXYuYzjezv5E+zsGxOxHdHuv6jENN9v4YMsKMv7wkdvl6uxHveeVk7N/tZd17mfNOWY4QE089+yvmjFw19+g9jVjbKzjEssEtXfZRzfHI8jbC0+zFxY3yzuPrZFxDJzL2Zepe8246x+4OwZ1z6ta14bzmDvHz/htAef+OV9uzqFG9bvW9dTA+drQ9V7PdeV2XJ3jYNwfEREREREREbll3NY+OOTfxkIj6xenjEXXQTLZR0M52jGxJh1Dr6XkT4RVj48ng2Syj/bGswy4UEaldyDeZzYyYUAaeYZlLXHzyXwxEkpL7e3KNjJhUBp5DyWT/WpvPM+Ucd7kS8BdR1gcPYUM4zItffE2m7FuDSd+kUsX610+meyjXTEVmzDdbgNfP8wnVhD/7LpagdjoiZnMijNz9rQNU+tAPAtnEDlhl2E/gYmZFIUdIXhAGgQnk71yMPeeKeasyZd7W5qgcDaRybtIStvLSBYQmbwL6EDqphX09S2jpNyMxR+OvZTA8O01kdxp6w4y8CEPqKzCum8GMfm9a7Zbqw9mkpZk8fzDNqxlcO9PzRydHcOYnc41dWX527OJau0BF6qoLN5I2NANJC3JIiUCSk7auDfQl7NvjiJ+kUsQdexaCn/RARNVVFaWUzAznoJeexl5XxmVvmYqj+ezvziYOHdj1NAY95pP/qxQzheXUenlC+8mEr/URtLLmTzfxYT1pA3P1mZKFsUw/OJS8id6c7bSF88LxRTMH83/jS+gY6H9WKduKaBHyzK4WMXZSi8CfMvYNiKR1CLHOTItlMq/loGvP6bC2XSbtKtm/+jNmj2TaWcr5exFbyyty3lncCKpJyApbS/PtynDZjZx3uZFgL+NvOR4xhxo5Lln7s2atyYTcLqQEnMw4b5VlJw8wjuDFlBVz7FK3VLAY7cXY2ph4uztvgS0LCYjehiLbfUf39QtBTxysRjPH5s4+2Eur53pVvecze1M7pyeWExQeaGK0jc7k7CsA6mbXuEprwqsNjMWn3LeGWrfd6daY3DR3p+8w150/FkV51v44X3Kec3U3z+3x+BUgvvzynlt9FpK/hQLZ21mTOdseAb6UfnH8XR7IR9L3FKypgRhLTyO+eFIvC8UYy3cQfz0rU3vd537VX3na33Xe5Gb6+qg+3Elmez1/fEsK+X87d54lq6g24QdNYMtIiIiIiIiIjeN1q1rpnGlpKTUqnNn8eLF1e9v4pm9ADaOzoynW79EYp5Oo7R1b0YmGNtEMm10JJVZoxzt5nDMtz9jR5vhwzcYE9udbv0SiX86jYI7Ioke4maZ6DSOfWtcLw0sD2DGuimBbv0S6DY/n6q2YfStvTR7Xh9NQlQ88YMSiVmUDxFxjDW0qc3MtIn9ufcvc0jok0h8bDx5ZR7GRgBYRo+jr28hi59OIL5fDBOybISPnl6rD3OHzqbgHJRs7UzMC/tdagy6TCcprJT1TycQPyiBhIwywhOGujTYz5int1KCjYK5nQkbusK+TKSNnOdi7Mu8VMi9/ccxzXUm4rJhvHbQBie2EtYpnjG77cVmn3IyYuOJGbGQBQ2OkfsxTuoRCn9KI2ZQIvE9uxO/tMzen25V7HX0p1tsAjO3O1bT0pfy9Hi69RnN1MPVK69WWTiHhNgE4vvEk/GpP0+N7u84R0KxLosnZlAiMU9vpDwigdqX3w5m9okhsk8i8f0S2Hs6kEcG+VXXmi4WMTPavt6cE74EdPFr/Lk3vD/h57aTOHQ8w/ulUVBVifXVGay+xLEy/X2rfV96LqDg20A6xl36+Fo8ipganUD8pHXuz9ndM4jZWgzn8nmtU2cSltnPv6fM+5nQ037+vXzYm8eG2fMgu6oeg54LKPi2A+1s9rGOWZRPVWBne1qIevtXzzGo57yqxQOOT40hZlACCVkn8X6wK+HAiAGRnN+eQELyeHsfLp5klUug16lR/a7D/flav7rXVb3jGh9KQNl2EvslEt8nRoFeERERERERkVvUTR/sPV/omKlq28inp82Y2xiamDsR4FPGsXTHjFLbDvI+rsQS2AmwYe6czJp1Wex7L5kQswfm+wBCsbguQ335W+tbHqCML52zaC8CLczU+aK/zYOo8UvJeiuX/PFhmN21qaUTAW0q+Wz3DscMYRv/d9HYxi66vR+2j/eT4ehCXnohVh9fgo0NG8ES4Y83vnR/PZPsTZks7+GLyTeonoCWnSXCH+/ThSwrsn+2bt/PZ9/6EdDF2LIu27F3q/vd8Bi5H+OMtTs4+/B0CnPWMi/O/pV2Y3+w2WpmWZ8rIs9lxrPR+XNHqsd78SelmFr5Os4rsMSmk70pk+zXu2Lx8KWdMbdq0JMsWZnFvj1ZRLUGT6/A6iqbtYg8+zv+76KzrpHn3j+rwGQiAMBsxtMR87/UsTp70nnuVAL28/VSy5QcXujo56WOR43o9n6Y7gomZZN9nc8+4IH3TyKNzVzGwL6f1WO98yRnTR6YG9qnxh4Dd7618pnz3LxYCWYv2gG2f1VhamEPyFtaeuL+v1Ia1++63J+vTVHvuL6+lQJzf3L3ZLF8dGSdFBgiIiIiIiIicmu4wYO9Jn7gGjVpacLjX1X1hL/88GxhLGvA7SYsI9eSOS2YL9eOotvjCzhWf7zPrStb3kzK+u2kPHSSzc/FELmokCYtflkM49kU5UdY9Wqa/TV/BlNfWFETAGyK240FDbnMMSpKI75TDC9vKiVoYhZZS3oaW1y28BauA1jBUeeYvLqA3ybPYJXrzOAus8l8bTCeByaTGB3D7pMudVdq1Qr23N6bJTlZ5L71S8wH3mCuc9uXc6watUzTjoetNLdmnS9NYeqcDcYmjVdv/y5xDJpo8bL90OsV9v0+i6zhXhS8voAcY6Pvmdtxte1geM/uDF+Wj2ev+WRuSibcuKCIiIiIiIiINHs3cLA3n5IyP0ImhjoeNuTFtCeC4cQRNle3MeMZZg++WXqMoqPvSY5nV1fa2Q7Z1zO+g/2zeTDRISasRfuJfsCPqmPbST1YAT6+1bMj4Tjl53wJGe1YplZdjfqXb4xOtGtTybE/pLHtDAT5uMwirKyCuzyxmLHn3g3zd1Qc4sszZtr16G0fE3NovQHuPUUn8Qh5krGO2GTU+EgsZcUUNBSdq4f1cDHlvsE8duEIOYePkHO4mGPHT9bKPwznqXKJwlsPF1PuH0mKYyqxZciThNx1ks/21bQBOHuhoQ41MEYNsIRFEmuuYNuGOYzZeRKzJaimP47zheBQYhsZ+PY0O8+driRF+GI9tt1xXvnSrkulY0yOcOwTw/iG+eF9Op+Zb57E2sCxqq1x5x49+vPIha3ExyYQE92dmBfsM3Ybd6xqa/wyDRyP8zUPCsR5/v0smHs/cazzk2KOfVpRq01j1du/Bo5Bw+dV/QY+E0plVjzd+iUQGRXPmAZmfF819V7v1Lmu6h3X4Ej6+tgo2J5G4uuFVPn60Q6ISkgmpVsjT3QRERERERERuendwMHeIyxetAN6pJN76CCFH+Qy0Cef1ya55s8003FWLoWHDpI9L5Lzb85m6gmAMmy2QPquHwXkM2bqRip7rKDw0EEK94zCUriA8atsZOw+QuWj08nPySZ/Yzc8quM6u5i5NB9Twlr7Mlu6YT7nslmH+pdvjF3kFVYR/mIu+3L2sra7R80syd37OHquAwP3HKTw0Gx+eqrUMZvZRupvN3L24en2Mdk1nYB/ud+oddU4Xiv059k9Byk8dJAlPWxsmzqDbcaG9TlexvmWkSS93BUOzGBmho2o1Qft+3oom6W/8DUs8Hs++NRE+JRc8t+aT9KBGcx8075M4aGDZI/159jccSw2dDfn3Y8obzuY/D25ZI03BqUaGKN6+RKbMIlZe3LJ3ZRFVl8zBZvegAMLWLW9iqiV9vOlcPV8nkkw7oM7VfDwbMe5M5/wcxuZO/dk9Xlli1lB4Z5s9r13kKzlQwhxXTT7CCW+/cnOySb/7ckEUDsg6l7jzj1Kyzjbuj/Zh+zjW7gni8wpXbE06lgZNHqZBo7Hmwc57hHJ83tyyV3Y037+/cmf5/fsZV9OLoW7MpkWU3ttjVZv/+o/Bg2fV/UrKSvHkuA4Rw4dJP+tdOZd62Bpvdc7da6r6HrGNbb7L0nZsZd9v89i38Rgzu58gwy6kjRyMEm/6FdrcyIiIiIiIiLSfN3WPjjk38ZCI+sXp4xF15GZ8LBAPMuL2VPqGupLJvtoKEc7JrK5fSj3Vhwh74xLtU8g0S3KXJaxr8d0ytDO7Ed0EHxW6GYmo9mP6AdMfHm4mOPGOqeGlm8Ei38o7TDuG9X95fgRN7NxvYiK8KfyE3d1Bj6BxLap5Nhl9M/iH0rABdfxutR23YxxY8bwEn2sf4waYPYj+gFvzhv72pj+uOMTSKxXOTluZqcGtQ/l/m/r658XURHenG3q9i7Rz/BpWSz13UDC8zuw4kVUzDhmvezH+x0TSYVGHCt3GrdMvcfDXZ/dlV22+vvn9hhc4ryqy495b63A+3cJDN9uA59ABo6bz7SA/QQPSDM2vsoaut4be13Zx4dSl3Y+foRfOOlmnSIiIiIiIiJyo2rd2p7nACAlJaVWnTuLFy+ufn8TBHvrUxPstQe3RG4dI1bu5ResZNhzWzmOmdgp6czqUcZr0VPIMDaWRupPZt5zsCyBxKwKMIcyb80rRFlnEzlhl7GxiIiIiIiIiMg1cSXB3hs4jcOllFHyaWNn7Ik0L6tnLiDPNJi1eXvJfy+Lsf5FLB6sQO+V2cpv576L6RdZFL63l/y3JmM5NpthCvSKiIiIiIiIyE3iJp7ZKyIiIiIiIiIiItK83KIze0VERERERERERETEScFeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeERERERERERERkWbgtvbBIf82FhpZvzhlLLoufhZwPx0f/C/uaeVprBIRERGRW9TX35zn6Mf/w+clXxqrRERERERueq1bW6rfp6Sk1KpzZ/HixdXvb9hg788C7ueR/36Qv331DXff9QPu82llbCI3gK/Kz/GnPxcbi0VERETciuv5CNt3fWAsbpIWd5mIeDiQwg8/UcBXRERERJqdZhns7d+7G/9ru0Bl5XcUfPiZsVpuEM8mPHHFf7CJiIjIreNqBHsBftjiBzz8oD9bd+wzVomIiIiI3NSuJNh7w+bsvaeVJz/4gUmBXhEREZFm45JzDBrtnxf+T6m+REREREQMbthgL8B//lipG0RERERERERE5CbjE0iUj7FQ5Nq7oYO9IiIiInJraNWypbGolp+0ud9YdOXMfkRHhBLb3qtWscU/lNiIUMLNQMR0cv9cQO6s0FptblThI9PJ/3MBRUcPkjXMWOtqFFmHDpI11ljeFGbCuw0mdcokpvUNJMhY3VjD0in880EyG+zvpfRkydsHKTpaQFHObF5duZeiQ+lc6kuP09YdpHDdKGMxAANfzqbw0FqmGcqD2tvPD+er1h/yPWaTeyib5T1cyq6mqzJWTl5ERYQSG+ZHzRdF7cGJOvt1pa5qvw3HwNj/68HsR3TEFZzz+JGyOpvsKfb7SuqWArInGttgv8bC6t6jGi+Z7KOZpBqLL8VxDoSbjRWOsa/Tn8vtp/0cjPZ3s6FrKKWR94cr4hPJ2PGTmDdlKEnXef8AkuZksS+tv/192l7y03oamziYCY8byrwpk0gdEll9Tje8zJW6zPOysa7G2PeaTW7OUkYYy5ug74tZFOasZcn6pYxo1PoaGpeerHmvvvtEze8tV/W+3Uj1/ayU75eCvSIiIiLyvfLxuZflyxbyX//V1lgFwIjhz/KrkUONxVeuSzKzVqYzb+lk+lYXhjJteTrzVs5nZBeg3Er5NxWUW8trLdpk1zoICEAoSfGhmMt2kTpiGIuyXKrqbN8DjxYeeJhc2jSJHynrslmzJJm+feMY+GImWXnppLQ2tmuEMxWUf1PG2TPGiiYYmUB0azi+ajQTJr7Cn8srsJ2p4FJHzaOFB6YWHsZiAEwtzJhaeGCsHTgznXkra17Ldx0k9+Xe9oCj6W48W5gxXfa4XsLVGKtqQ0hZmc6812aQ5BIPSVm4lnkr00lJdG3bRGPX1v7j/6r223AMVmeReyibNUkdjM3qMvbrMsVOe4Ulaem8NNJY01gVlJdVUG6rNFZUs4SNImtPLkunTWbi67kUbkomytAmakoWRUf3sqaXs6Qry9++kvuMFyMWZpP/+/mMnDKbNe/lsibOeXJ0IHXTXjJfnczY9O3sW2I/5xvqZ1LaXor+fJDCQ45Xnf9YsZ+Ds8Z1MpRfXcb/1Clv5P3hckWNXkt+znz6RAYTFBLDyN9nkzXS19jsmiops1J+rrLh/wgx92b527msGR9DUEgwHRPnk5Uz2+Xn4U2o13z2uY795ssc+1NlWM9UUHWZsWIIpXuEH9Y3OxPWczyrr3h9DXH+3pLOrHEu/zFd5+d+3WvhaqjvZ6V8vxTsFREREZHv1ZkzZ3ktfTWzXpxSJ+A7ZtRwHgoJZt6CV2qVXz1V0DKUWGewJCKOdq5/F57YwarpM1i1/SQ4Z8+09wKzH0kjJzEtrmZWYVB711mG9plu0f5m+yy5h/2wtDDj2cZ19ptjRtXI3obZOF5E9R3KvPGD6VvfrCSfQEaMrD2jNqh9BzxbAP+q5P+oxGqraet++47lOg8mdfxg+hpmBFWXu+tDxDCeeshM+fZhBD/SmeCeM9iW9S6bT7vOevSi75DkWrPFnDP5ws32fU9N6AAH1rJ4+gI2H3Ctr69fjrEZ2btmZu1P/Yi2mIFKKiuhsqKCPWsX8Nu5a9njsmSD+1Otps+VF5wD6Ma5fBZ3DCe4YwzLDldiifklKW7/r8KLqJjBpBpmmDV0HjVUd8VjVWfWVxXQgfBfOfs2lEcC3fzJ7uyLmxncxnG1+Icyoq0vphZmvJ2zNq96v2uOQUyfOew54034+NksCXZW1h13t/2CBq7D+oTyVAjk7SvDv7Prf0J5ERXmh8U5y7XWzF9nnX3fo3xsfLBlBRl/+MhleQMzfLoqjsg+CXSLTeN46/6MHOJSHzyJab08KD/nUubjjafZ3XXurk/u+OJZtp3fxsYT3yeGMdsrCR+eTDjAsHE85XWEl6MTiHl6BdawZGb1unQ/rduHENaps/01dIXLthrBzX3OKah9b6ZNSWZsZ5f9NPsR7bx3+uDY7/4E+HpgaulVPVvZ7f2hfW+mTRnKCNdxq76X1Zwj7mY719I2mZQkPz57KYZu/RKJH5RAZGwCL64qq27ifhamF1Gux8fsR7Tz54nZj+j2XtXXjrvZ7M7Z7s51luRuZNXv9mM1tHOVNCeZqMrtjHk8gfhBicT3jCFh6AK2uTZyzPI2zr527oPreWbxd2zf8O0Ae9/qHsOGzkt3YxTU3nHfcDu73C6pRyimPy2oGfveNWPvXL5R6y7NZXP6BvbYGjP+jvLqVyBBZn88W4DJ7Bg71/U5OI9Zg+dU9fj/g8oLxkqHtk8S4GujpNiGd0gcsbj7ud/G7bUAzpnQyYzt5rJflzz3G/pZ2cA91d22qr8lZLj+5Irc1j445JJPyrB+ccpYdM39KikegPVZfzRWNcoPfvADfvQjT86c+cpYJVfRswlPXJUnaouIiMit4N/E9Yyo93eHTo+GM3bMCGa+NJ//+Z8TjBk1nAfaBzH1N7M4f974xwTE9XyE1zOyjcWN12sp+XOCqSwzYT6VRthzW+m7MJfUoErKW5spmd6d4TuTyT46GDaEE7/IPlMtJagCq9kPSwuAKkoyhhC/9CSpWwro65PP4sfHk0FP1rw3m3bHZhB5eghFQwJrtlu8keABx1mSM4NoH6is9MBkqiBvZgxjdvqRumkDfQOhshJMLWzkTYhhzL6axcOHLGXe+Ei8/1UFJg84s5/F/abgv6aAvs7NnHP2A5iY6Wb7kH10MPeeqcDs4/jjqnw/i6OnkIEfKevWkvSQmcoLVZhaUL2P1SJms29lT8ylO1j83Bw2u87YdIwr58yYPaqghQeVpTt4uc8ctpFM9tHe8GkVAe29qCxMI2x7KPlzgvmserz7Yzphw9LW0C9zb9b8fjrh5gqsNjMWHw+4UMXxwkIsXSJx/h1asiGct1rvJSWkyDEG9e9P6pYC+rKR4AFp4Fy/j33soQqTqZRtHRNrfa229nF2jq8vBdO7Mxz7vn82vTvDj40ia8tQgkxVVP7LfowLZiYwfLutwfOooTrn2F72WL3ZmYRlzj2xn9ueZRV4V+5jeJ+FWEevJTfBC6uHL5Xb7Oc8vWazb0ZPvKmi8g4PTOX7Wdyz/vPkLb+1pHSpiQrYDswgcveTV7Hfbo5B2+nk/r43ptzRdEsPczvued2y6/YrGffX4fGhZK4cgvn9F4l/Kb9mwzjO/Xl3k/H0SfrsCaYgehhzbTjGszeeZcCFMiq9A/E+s5EJA9LII5nso13hUzP33lXB0Z0JnH2igI6F9jFO3VLz3j0/lry9gfvf7kzCKoAOzPv9KwQdfoPyXr+ERd0ZvrMry9+eTVRr+5hVFm8kbKgH2UefxFQMpttt4OuH+cQK4p9d12AQsNqQdAoHVTAzdgb3pu1lJAuITN4FwNjVB+lTPo5uLxxxWaB2P1M3FdDxLw3tl/0cvPfAjOr1OtV3n8uwmUlakkVKNy+orAITHF+VQELpKPbN6Yn3vxzn6b+K2TZiA/e/Opvw6ixBNgqmdyevR+37w9jXVjCisxeVF8DUAsr3zSFxwg6svZaSPyeI8yfMWNra/xOk8tONjBmURkGv2eRODKV8TQKJG2p+PlmmZJHbuYjhT8+hoLrUydH3CCg5aePeQF/OvjmK+EUfOcYilKPO+02vpeRPhFWPjyej11Lyp1g4azNjOmfDM9CPyj+Op9sL+UAHpq1/hYFtKigpg3v9Ktk9IJHSiTXHK8lw7Ox6s+a9yZjWdCZxg0uxQ1LaXp4PqMB2F5y3eRHgbyMvOZ4xB2DEklxGBtuwfgWeP/Xl/Oaae9fzbcqwmU2cv+hLQMti8g570fFnVZxv4Yf3Kee519C1Yh+j5x+2YS2De39q5ujsGMbstF8nj1wsxvPHJs5+mEv8pHXGbhM7J5t5EaWsHvEiy0pr/96QuqWAHi3L4GIVZyu9CPAtY9uIRFKL3Kz7QHDjxt/cmzVvTSbgdCEl5mDCfasoOXmEf1R2JfghD6iswrpvBjH5vWvWRwdSN73CU17O+1w57wxNJPVE7XPAEjefzBcjobSUypa+eJvNWLfWvZ7CX8xmzZNlrE6HZyf6UvBcPGM6G3/u51PgE1nnWljlu5aloztgvlBFpckDip3ndwPnfoM/K3u6v6fuhPCR7rfVbkkuKd28qn+OHF+VQMIfe9Z/D76FtG5dExJPSbl04pnFixdXv282M3t/1jaAR8L/myee6EZC/z70iXuKfj+P40c/ugZPaf7vYcx7eRgPG8ubJIRRL6cy6r+N5ddJ+16MGtSJu4E+U9OY3sfYQEREROT6OvSnApYtt8/wnTZ1QoOB3qup5JNiTA90ZQSRdA82c/x4KQ1+C7+lB59N705wVBrHL3gQEBFnbFHbokSCNxQ7/rgKJ3hAGuHThhHtVUbO0M6EdUog55QXUQlDgTg6tvfA+oc4wjp1JqHPKOa6BHqhP2N/FYl36VbGPNKZmEn7KffpStKLkaQOmEHBOUcw1xkEq2f71co2MKZjOGO2l4F3KFG9gITJDHwICmZ2J6xTZ5YdriSgxzD7bCGnwwvIyC0D/95M21VA/u/nkxLmOu3HxPk/jiamU2fGbCvD5P8kfapn+5kJ+PFxlvUMJ2zERpdlnDzwtLnp1zNxhPtUkPdCDDE9F1BwDsoPjCMheTyRLvtn/EO4UfsDhI8dQriPjeNLYwh7pDurPqz/K/ZgwjsilNi+yWQ9GQiVJ/lsp6HJ6Q1smz+DhP/uTFh0GscveBH+83419Q2dRw3V1dLEsXIJmDqd/6SIcv8w+rY1kxQRSHlhMZUtnLV+zBvWE+/T9vMtbMBWrD6RPDWy/nE9m9ydxQdsQDHbOobXCeLZXXm/azlRSvkFMJl96x33DDf9qvc69A7A4m3G4v9fxi0RG9cB8/FDZNi2c7wskI6Jrue9jaMz4+nWL5GYp9Mobd2bkQnOOm9Mx8YR2SeBMWtdFmmMLsPo+ONijr5p/xg1ZQZRVTuYu8g1EcF+xjy9lRJsFMx1nUXrgXVTAt36JdBtfj5VbcMa+TV9MylPBGP75F1yAP/7zJwvO1Rde/5CJaYWhll4hn5yOwT0zWVfzl4KP8glszGpNqDB+xwJ83m+mxfW7aOJeaQzMT3jGJ9eBrlZrHrJXhY2dAflpkAee6aC4Y+73Bc7dme48TpNmMyznc1Y3xxGWKfuzN1dhne3UUzr4mxghk9mENOxOxkfVmFqH+qYOemLd0svLEEuATUg2teLyr+Xugn0Al2mkxRpI+e5GOIHJZDwUiH39h/HtIZmdjp5wPGpMcQMSiAh6yTeD3YlHLCMHsfANkUsfjrBvs6eo0k9bVzYHX+8W5Zz9rCxvIapspCZ0QnE94kn54QvAV38AFg9M4H4aPv2EreVEdBtiH32N2C6WGRfpucCCr7tQDvbHBJiE4hZlE9VYGcGVq+9nmuly3SSwkpZ79yfjDLCE2pm0Fs8ipganeA20AuQM38Om08HMeL3e8n//VJSY2qfo5WF9v7E94kn41N/nhptz2vMpdZdz/gzvD/h57aTOHQ8w/ulUVBVifXVGQwZOpuCc1CytTMxL+yvtSrL6HE8Zd7PhJ4JxPeL4eXD3jw2zPhcgEimjY6kMmuUfYyi0zj2raEJAH4MDPOFE4Us21BIaaUv7eJC3fzcH+/mWujP2MQOcHAOMZ06Ezb/CLTvSlKEc93uz/2GflbWe0+lvm31JOphLyoPLySsU2di+gzhxfSyBu/B0jjNJtj7UMcQbrvtP/j440/Z+c4u3tzyB6zWmq9K1G8Yq7elEl39+XHmbUujwSwm97TmwcDW3GMsb5JW/CSwHT+ptZJhrN67jeUu+bEe/s1a3qvu3zBW52aSkZ7G6nWZ/HF7GqPaw92dn2P11m38cec2/rg9jeRHa5YHiJ6zhZ0b01idbn/NGxkClgAeDGyNF3Cf/0/5yX21lxERERH5Phz6UwGfnyghLPRhVr+x/poHegE4cJDjHsE8NqU37VoWc/SgsYHBt6Uc22cDWznnq4yVjRP7oB9gJmRqJtmbZhNirgLvAKLZztFPq7Ak5FKYs5ZfBNlqz8DrEUqAGayFK8kDrLv3UXIOvNsY/1BsnLOfbCQPyLPVjPPACH9MQMCAdLI3ZfJUG8DLl3a1lrSR8UI88SMWsufDMjz8u5K0MpMl1QGSSqxFR7ACeX8pxYYHZpffN627X2R1A/lb3fWLc5VU4oFnazOYzXi6yTbgTuP2B6KCfOFCMXkZFYCNqoZivS1DSVqZzrwXB2Op+oicF8ZRM5/GycbRqmB+81YuhbuSCWoBmFwmojR0HjVUZ3DFY3VmK5+V+RGS+BwdA218ttP1b6g4gvyBlmGkbMok+6UwPCs98G7TodHjWp8r7nct3phbABcrLz3uLuq9Dg/PYPSgRBKHGoM+kTz1sC/l//Jj3pT+eHxbSVDEEJevI9s4X+jYH9tGPj1txtzGWVfKB/MbSNtQH3Nv1szoyvmtr9hnEAdPYlqvSna/mOY+oFhHGV9ud/TpItDCTGO+KB01MZ2BvoVkOGfVXQRMDSxp7CeQ+usYEjrF0C22O4kbygkZPuoSD6hyaOA+Zz/vTnJskf3+Yj1T4bhHnuS8d3+W5+ylcHVvvKFuMNoN+/rKOJb+EWBj84FSbHhhCXO2qMRauB8rNsrPu9wU1o5jzKAEEqe7zmy2M/3Q21gEgCXCH+/ThSwrsn+2bt/PZ9/6EVB932zAt1Y+cy53sRLMXrQDotv7Yft4PxmOMbe6Xk+XZMLT31hWw2YtIs/+jv+7CJ5ezsB2IH1npZOdk0tWjG91X2ovYx+r8+fsx4mdJzlr8qj+BkZ914olwh9vfOn+eibZmzJZ3sMXk29QdZC45PBCx/rrYTvC3KExxPSbw+5yf556eTvZ4+1Balz7g43Fn5RialWTt6nBddcz/vyzCkwmAqDR96zo9n6Y7gq231M3ZfLsAx54/yTS0CoUi4/zvKR6POtoO4R2rcF20cK8KRawUZPK4VIc15lHQH+Wb8okO94X8ObeB5wN3J/7Df2srPeeWu+2dpFXWIEpYhJFe7KY1s0bG0C992BprGYT7P3nhX9y4q8llJX9nQsX6ktm0kR3t+bhwFbcE96HUc8P4Kk2PzS2sLs3lAEjnyPxydbcXV3Yip89NYBk43J3h9Bn5HMkPlnTspaz/+Se8ETHekKIbQt/O+tS/+1fyRydzIihifTd7UGfUQP4x9E/sWJmX57o1ZcRB+6mx1OPuyxg97eDyYwYbX9NXXUMjr7D2k1/5AtjQ37IT54cQPLIPkTca6wTERERubbGjBqOt5cXy1esZnLK2Do5fK+Ndez+sJKQvpGYi/Yx96Kx/uqz/asKvi1lz6tprHo1jWXTxzF14ivs4SSpg2IYPnMjxy8GEvtyFpnVMwMBW5X9T76Ggi5X6HxlJVDBUUffVr00hanPz2azsSFgLdzKhKHxhL2Uj+12XwKqAyQubjcWQOXFpgQkHLJm8E6xmZCJeyl6Lxn/M7vImFs30GLUlP3hdlPjHjJTnbM3nMjYYUzd52Z/EtLJfDkO76IVTOjjmFF1vTRprI6QcfAklifjCPoqn4wDrnXnqaoEW2mufexeXcBvk0czc9lHTRvXxmpSv2tYhgRiAc6fKW7SuNd/HcLxT4s5blygS2/amU9S4pg5WfX5SWyBobUecFfDz55D+4p0IHXNZNp9soAxi+xBn4HPROJ9uz9PrT9I4aEZhLc0Ez7tSh7KVpclbinz+sI7E8ZXBxGP/70Czx9XJ0XGs4WJ8xXFjk91+wn2B/M5x/D474qwms11cs26dcn7nAcehlyg4dPWMm9sMLadcxgTvZGS2tX1sp/Hddd3aTYKPj1ZJx1GxicnwdePJEN5g9zcI6+9fKxlXniHuD15GxDJ8i2v0Me8n7kDYojcWWpscBkM10r5Ecf9Jo1V82cw9YUV9Qdh62Et3UHqc/FM2F5e7zcjwls0dd/dWLWCPbf3ZklOFrlv/RLzgTeY28Bsaaeae6rj3jnHTS6NRggfFIoF8Ah8ku5xT+JvBnw7EN2YX50c11l5oaMfr85h5nPjaj/ctT71/Kys957awLYyJsWQ8Hwae77yImpsOmum2YPzbu/B0mjNJth7TTw6jJdeWcnyX7TD464whqXPZZTxf77aD2P166N45Efws96L2LygD3cDA15exPRHfeCuMIalzyIR4O4+LFk/jdj74L7OT/Ggu3jv7Wf4+u4QBtwNPPoUD/zjG/5xqaDrP47xl0/tgeRo/7upOPtXY4u6+v2aJROMX+D5IU/NWcnSGB/4USjJi6e5zHgWERERubZcc/Tu2XugOqXD9Qj4rt5XTKWpkuO73aUVaJyK81Vwl4V23ULpOzGBdtW58Ryz4jDh6WXGYjaz+XAxtAwmqgscO3yEkjt88ayqAHqSOqsf3vvSSPzNu5TjOjMQOPwun5WB5bFkRviYiR2bQEhLsB7b7tLIDcP2G5LzbjHl+PHIEH9KDh/h2BlfPFvUDmpY4payLy+bNaN7EhsRyogwf8xUUfWNs4UJy8ORBJlDmTcoDDMnOX4F6ZUB6DGZHoEnyekZTvB/hxPWZ0Z1IKohl9yf2z2wAHnHy8AUSNSUUCw+PQkJaDCZR8NuB9qYMVHOZ3/YQUmbYCx3GRtdQ00cq4KsQqwmD6x/2mCYLfp7jhZXYQ7uxmMXi8k5XMoPfMxUnml4XCsvVgFmPIO55PlWS5P6bU+lMWJ8OpljQzHZjpCz7GSD427sV73XYduhZO7ZS/aLtWfb9e0VjPeJXMbMX8jU+QuZOnMFRytcUzmY8XSkM7H0GEVH3ys57zuQumkFT7GVqck7qs/XzS/E1zzwrNNsCs7ZKJgbz5jdVAfnG8XclZTx/Yk1HB57ntAgPps/mlTHTEaAzYdL8Xiwqz2wbR5MeJCNkgMn6+0nbYcyb6w9EAUQNToMy5kyXFZZo4Vv7YdbNXCf27zvODZ8eeQ3Q4k1m4kdNoqxbaGdjxdcKONo5n7OxwRS8yd0FfzLPqM5CjMWw/7mvPsR5fjyyLj+BJlDmZYQ3Lj7Va/Z5L6XS+YQwwpXZVFAJCPTetc8vDNpOksSvLAeLqbcP5IUR8zcMuRJQu46yWfVqXq8ubcL9ty+PYJcZsHWb0/RSTxC4khxBKstYaFEGRu5dYSMfcUE9E8n1RnDN4cyb9akOudEbaFYfMsomLmVApuZ2JYNNm6A/TrEcK1YDxdT7hvMYxeOkHP4CDmHizl2vG5QHTowYvxQBtYK0ncgdd1a5lU/tM9MgJcZKs9Xt/Bs6TgnzV1JivC99M/OS+nRn0cubCU+NoGY6O7EvOByDdRjT9FJPH4WzL2fOPbxk2KOfVphaHWc8nO+hIx2pD7x8XUza9iPvg/5wukdjHHeEwbvwIov7Qb5ufm5b7gWPnmXz86AJXIwltIj5HxSzr2tTBTUe8+1a+hnZb331MP1bcuPlBcn0fHzjUz41RscvwCePoH13oOl8ZpFsNf3P2vnIEh69hmG/mIwP/vZT2uVX5ZvP2Pt6LmkLZzIluLWPNanda3qAcOe4u6DqUxYuJIXR2/mf9r3Ytx/w5YXhpE0PZOcfYWcrGrNAz3gJyN78WDpZka8tJKF07fw8T9qrcru9n/w/kc/JCLxh0T08OFvuz+rXX/XT0lMT2N1+grW9/Dg/R1b7OXDFvHHzb9lwD3HyNpUN0nPz3o70jzsXMQwY6WT/zCGtP2MBcmvkrbwN2yw/pRYl7RiIiIiIteKu4exuebwveYB36wZTHhuNL9tzIyWeizbno/N5EfsknRSe5Tz2QmXyjX7OH7Bg6Dxe8ndMpmQ9FfIOGwj4Jl0co8WkJWWzMD4DtAjmI5dRjEvr4Ci9b3xPrOft153WQ/5zF20gxJTJGN37WXesA5UfbiOuXNdHp7mjmH7DX7F88AcVm0rxuPRSWQdLSD3remMjetdq4m19Agl5WbCR85m3sp0xvbypfzwCpa55CL17DafrLx0YgOhZMNsprqOx+U4fISSc37E7iqg6Kj9VbhnLdNqJhu618D+lForoG1/lk+Egrlr2XMa+zHZNYN2la75UBvp+EnOVpoJH7+UpD8eoeSCL9HrC8hNi4SKS+RjuJqaOlYnVjJzxGhmLjOeRzbmLlrBsXOBxK7eS9HRXFIn/pLYhxoe183bj1B+0bHvy5vwB0VT+u1IpTF2SDCm0/vJGDOaZTaggXE39sta33XoNl+kI6f3QdevFR9h7yc2l1QOZjrOyqXw0EGy50Vy/s3GnffWb2wEPLOWaS5lAxcuoG97D0yt+7Pk0EEKDx2kcF2DyQaB3/PBpybCp+SS/9b8hmeXJgxhYNIkRg53LRzF8he74o2ZkCn2/Sg85Jg1nLWAzZ8H83xOLvk5o7AUrmDugQb62aIK7yfmk+0oWx7nQV76HLa5bs7BHDaKeSvTHa/pDGzoPrfzRZZtO4nHQ6OYl7eXeaP7ERXjy559Ryg3dSApr4Cs0d6cr57RvZ+cw2XQujfLj+ay9Jna2+bAgprzOC+dgQ/A8VWNOG715OyFXQz/9TqsIdPJ/cC+75nDA6G8Cg7MYOabNqJW28uzx/pzbO44FtsAtnP0UzNRS+xj3qfqOI25A1lXvcLmT3xJynGsc8lk+sTUbvPZmQrMncfVmf1dsGg0i/eZeWpdgf247XmFcJ8yGs4EsZ2jn/oSm5PNvj3ZjA2oN8FAwyqh3TxHn12vlQMzmJlhH6P8nGzyD2Wz9Bc1qRaqRSTwTNIoxk7p6lJ4koK/QFSa89zdS8qDJ9m8qOaaNYVNt5+Te+YTfm7jpX92XkppGWdb968+zwv3ZJE5pWuDM9itq8bx2p/8eX7PXvbl5FK4K5NpMQBl2GyB9F0/CtjFzKX5mBLW2te7pRtm47cU2vYnxB/KP3635j/pTrxLSRlYwoYQXufnvvFayGfufOd1VkBRXhYpKb0vmWqloZ+V9d5TqWdbrbvSLjKOabsKKMpLJohidq/dVc89WJritvbBIf82FhpZvzhlLLrmfpUUD8D6rD8aq2pJShxIecU3hAQ/wKo1Gfz73//m0Ygwdr6zi8kTk3l99Tr+939r/ienrmGs3taaLX1T2QOOnL19+KJvMit6pLJzFLziqIues4VxrKDXgcery3/2+jYe3NeXMVsAfkjymjf4zy2/JC98EcMC/8nHBYXw333x2DSAPV0cy09/z7GdUbBiAFN3G/qS8ncSJ93H3+66kw+GlhBb3b9hrN4WwPu/zeKTi1/zP0dPY4wX3zNoEet7nGDM0JXVKRqi52xhgHUAI5w5+gFGpfFexyIe/9VaRr2+k4eP9mLEiVR2Pt+av1lr1vrF7mTmvOWynMGzCU/U+0RtERERkdr+TVzPiDq/O/j7tWF88mimz/yt2xy9nSMjaBf0X6xeu75WeVzPR3g941LTr64zn0Bi21RyrNDdTCQvoiL8qfzkSM3MGbMf0Q+Y+PJw7a8rBrUPJYBScurM9nEyEx4WiOnUEfIayH1bm5vtN6gR7c1+RD/gzXnXNr2Wkj8nmM+md2dRSSj3VjSlj/ULH5/J8jgb66e+wV8BfPsz8cWunN/g5qFsbrnbH+c4FpN3xl4Y1P4K++x6DtRzfK+1Kx+ruiz+oYTc5e6cdDeu9Z/bDblq/W5o2+7q3JQFtQ+EJn2NOJnso6Ec7ZjI5iafQ15EhZkpcXvfaKrG3xvGrs4mfHc8iU34Ty6LfyjtKGZPaX03hdos/qGEeJfXc0+8lAb2xc0xa+j+W//56+BufQ0yE97eC6ubVA5O9W6zgW1d9v2ngX23j6Mv5wvrbs+unmu4AZfdT4P613PpPoW/mMWsf00mpk7A1n7eeP+z9tinbimgY2E48ZmBxHqV1z0ulyF8WhZLfTeQ8PwOrHgRFTOOWS/78X7HRFKNjY3cnQc+gUS3KKu5vty1aZK64+juvAxqH8r93zb+uqbBY9dwv91ty919oun34Oandeua/zZISUmpVefO4sU1Tw+4qWf2et3Tin9e+JZ3cnZx/ryNFi1aUFHxNffd50NszBN89dXZSwR6Ac5gu701D7V3fHy0E//FN3xuaAVBPNTmTr4ofq9W6YfWf/KfgY4HYtzdl/+y/J0v/tSL2M5V7H52Ii++9kecl9Bh69/x+JEzr2+dOfg1SjM5TCj/Zd1N3Tjrd/ztz8f4i0ug9+6OoTzsSAnx9dl/gMfdjUq6X8ef/soXfMPhyTX5fRsK9IqIiIhcDaUnT/Hr8VPdBnoBDuYfrhPovWGdKSbH7R/bABXkHTb84Wo7yR43fwwd//TIJf4QtVFQWM8fWfVys/0GNaK97SR7Gmhz/NOm9rF+3j82Y7rLm6C2XoAv4TGBeF8so6TQ2LI+7vbHOY41hVfcZ9dzoJ7je61d+VjVZS2t75x0N66Xt+9Xrd8NbdtdnZuyK8kX2fRzqIK8eu8bTdW4e0PslEz63LGLVU0I9OI4D5oSELKWHmngnngpDeyLm2PW0P23/vPXwd36GuQ+Z6+rerfZwLaafu44NLDv9nF0vz27eq7hBlx2Pw3qX88l+vTMUuY9VMzmOt9GoPq8cTv2OMaqvromCmnjBXeYHCk3qjAH+2EuL6NRmYzdnQdnDAFXd22apO44ujsvj3/atOuaBo9dw/12ty1394kruQfLTR7srfj6G2677TaGDU2krOxvnDplT1+wZu16Co/8hd+t32RcxI2drH3rGx5dkEnGmrXsfLEd/7NprWOWL3BXO0avX0HG5ll0+cc7pGcCJ/5OxV2tiegBh5dk8nH7aWxbl0bGul547FjBin8c4uPS+3hqXRqr16Ty8N32rw79I/MdDt/bh83r0shY34efuHajln+SOe83TEmrHViuz086DmT6+rVkpK9gW/JP+Z93t/AXY6MG/P1//8l/th/AT/6RSfpb8NTGTDLSV/Dm+mk8ZWwsIiIiItKQ4/t5583fk3eV/0rLeWEGy94txzJoMrPmDKHjxSOsfi6RCbUeKCbcxGN1s/bbroySSwT/biQ56aNJHNr0B1+J3BB2vkhigzm967J+XkzJVb5AV89cQJ5pMGvz9pL/XhZj/YtYPHgKGcaGItfZTZ/G4eppxc863s3fXFMjONM4PLuWL+75ms9P/bO69d1tforX13/lC0fjewJDMFuPVX+2l/0Uiv/K1zVFDZZfmR/yk473YTt6Oettxc8C4fNi5xM13IxFPZTGQURERBrv38R0CyV335+NFZflhkzjICIiIiJyhW7ZNA5X1zd8Xl9w8x+nawV6Af5xqibQC/B1ce1Ar73MfeC1vvIr80++uKxAL/Z9rw70Oj7XNxYiIiIiV+Cr8nOEdfwZt9+uX0NFRERERK42zextyN2tebgtbh+EJnaa2SsiIiJN8SPPFvzM38KPvX/EnXfcbqxuMs3sFREREZHm5kpm9irYK1dEwV4RERFpin//u+ZXT9f3TXXbbbcR/2SEgr0iIiIi0uxcSbBX358TERERkevmtttuc7zgP/7jtst+3XbbbcZVi4iIiIjc8hTslSvy969cc/2KiIiINNZtV/gSEREREREjBXvlsoU/1I5v/6/KWCwiIiIiIiIiIiLfgxs6Z+9X5ef4sXdLY5XcAP7+1Tdc+Pb/KC4po6rqX8ZqERERkWsurucjytkrIiIiIs3OleTsvaGDvXrwl4iIiIjUR8FeEREREWmOriTYqzQOIiIiIiIiIiIiIs2Agr0iIiIiIiIiIiIizYCCvSIiIiIiIiIiIiLNgIK9IiIiIiIiIiIiIs2Agr0iIiIiIlfA4h9KbJgfNY/RAPAiKiKUKJ9ahdecxT+U2IiaV7jZ2OIy9JpNbs5SRhjLXSSl7SU/raexuIZPYK1+Vb/aexlbioiIiMgVULBXREREROQKRI+bz7zVsxnhGlhNmM2SlemkJLqUXQfR4+Yzb85kRv46mZETp7P8vYNkT+xgbFbb2LUUrhtlLK1xqgzrmQqqriRw3G2IvU+/nsxv0l7hN1OS7Z8HdTO2FBEREZEroGCviIiIiNyS7mnViqRnB5P07GDuuaeVsbpJbOe8eWSsX/XnEd38sZ2x1WpTPbu1ehawF1ERgQS5NLH4hxLtXxNVDWpfd4auxd8+Y9j9jGKwfbKW+EGJxPeLJ/7VYiy9fkkSAGbCw+zrq5lxbCa8lRlTS6/q7dRZf2kum9M3sMdld5z9cj9z2b6dWnVvzrD3adBaPvu2ks/WJto/T9/qWMSPaMNM3zr9wIso578RoUQ9EGiYGWzf7lWZzSwiIiJyk1KwV0RERERuSZs2/Q6LxYLFYmHTxt9xT6vLD/ieLSzCFNafcAAG81hQGdYzpur66ImZ5P9+PiN/nczYJVlkLekJBDFw1lpeGu2MTpoZMS+dlGe8gA6kbjpIVvpkRk6czfL3clkTZ28XPW4+817NJHP9dMaO7E9I9Vbqslacp8rDhCe9WbMnl6XTkhk5cTZLdmSS2hYY+wrL4/ygzZPMWjKdpAg36++SzKyFyUQD0IFp6/fa+/Xrycx7K5PU1rW3GTUxneUvDybgQu3y+lji5rNvz1pSfp3M2PTt7FtoTwdRpx8MIWX1KyzflMW8KZMZ+MwvmbhuAdOqh++XTFs5mb5uA9AiIiIitwYFe0VERETklvT++/nMfXkBc19ewPvv5/P007HGJo13ZgeftYgkKQIY2Y2Az/fxV5NHdfWe10eTEBVP/KBEYhblQ0QcY8kn40AZ/hFD7LNzzUNo71fMB8tOYhk9jr6+hSx+OoH4fjFMyLIRPno6fR3rM/uUkxEbT8yIheRUb8Whha8jJ25vlgwLw+NEIdvYwcw+MUT2SSS+XwJ7TwfyyCA/WDaM1w7a4MRWwjrFM2Z3w+u3jB7HwDZF9n4NSiCh52hST7vUxy1lXl94Z8J4MgwTm92LZNroUKzL4okZlEjM0xspj0ggxVFbtx/emI6NI7JPAmOmr6CgLJCOifZoryUxGMuJfJadcFm9iIiIyC1GwV4RERERkSuWzzt/gXZxoaR09qVk98ba1TYPosYvJeutXPLHh2FuYcYLKFi2n9LAroxtC5bEUPyLD7HaBtHt/bB9vL86YJqXXojVx5dg5+qOvVtvMNX88DBmLXmF30zpz71FaUx4dh1WgKAnWbIyi317sohqDZ5egcZFq9W3fmO/rDaXRreHMW9iJJX70kgtqilukLkTAT5giU0ne1Mm2a93xeLhS7se9uq6/Sjlg/kfOd6fZNnuYvx7PEc4ZpIi/Cg98IZ9X0VERERuUc0m2Hu76U5jkYiIiIhcJYnDRtO1xxXMfL0BPfZYJNNemMy0Fybz2GORvL2zzhzZJslZm09lyGSivUt5P8u1xkzK+u2kPHSSzc/FELmokOr4pW0Fuz/0IiTBj4ERfpQe3tBAsNLEDxqRj9Z2cDZhnToT2SeRxJlbyQPoMpvM1wbjeWAyidEx7D5pXOrKmTs/iXnfLiq7TGaeMyrdKBUcfTWNVa+mserVBfw2eQarDhvbuGdNf5fjrcLo27YfHf1PcjTTTYRaRERE5BbSbIK9IiIiInLt3PefFlq3qXkAWXMwaPAvsFqtWK1WBg36BV9//Y2xSdOc2MqxSj9Mn2xkda2KTrRrU8mxP6Sx7QwE+XhSk+ABVr95BNOjM3ikTU2wck/RSTxCnmSsI7gbNT4SS1kxBZcbywzzw/t0PjPfPInVHIpni5qqsxcav1J7v+JIceTFtYSFEuWoq/xwBWNmzmD1QQ+6vzipurxBtkOUlPnSrkslOYePkHP4CMc+acp+buWtQjMhL3bGUnrE7WxkERERkVuJgr0iIiIil6ntf7Xj+QnT+FHLe4xVN7Xmul9GX3/9DRnrN5KxfiNff3OFgV6wpxWYMJqZi/IN5bvIK6wi/MVc9uXsZW13j5qZvQAHdvCZRweCTh1krjM9wqpxvFboz7N7DlJ46CBLetjYNnUG21yXa4rsI5T49ic7J5v8tycTQFV1Vc67H1HedjD5e3LJGt/w1GHrqlfY/IkvSTn2fmUvmUyfGHtd1flyrMC2F1ZwrGUcKRM7GBd3I58xUzdii1lB4Z5s9r13kKzlQxp86JzRtp1FmIIDsR5c0cCsaBEREZFbw23tg0P+bSw0sn5xylh0zf0qKZ7tuz4wFtfrdtOdXKz8zlgsIiIi0iiJw0bz5amT7N9t/yp/2/9qx5O9+pD5xkr+99zXxuYAdHqsO5GPdeMPW9ZT8nkxuFnP5bpa6zG63P2aNP23nPmblfVr043Nq12rPtcnrucjvJ6RbSy+YVn8Q2lHMXtKjdNP/Vjy9gbuf7szCasMVT6BxLap5FjhyasQyPQiKsKbs4eLOW6saup2mtq+EYLah3L/t+7G5xLaTid3oz97HhnGYmOdiIiIyE2odWtL9fuUFOeja+u3eHHNb0Ga2SsiIiK3HHczV41pCn58ny93tfghXvfeW13WGMb1NEZj+nO1XO5+NUZj+twcc/82lrX0SJ1AZlTnnoyYtYCou/J5xxjoBThTTM5VC6hWkOcu0MtlbKep7Rvh+Kd1x6dhHegb05slC5/EMz9LgV4RERERBXtFRETkVnQtA56X40brT2O4C1A3xqUCwoOHPmcsataCnhzCUz5FrJowhQxjpVxCEJ0T+3NvURpTJ+wyVoqIiIjckhTsFREREZEmu1YBal9La2NRs7Z6eiLxz81hdZGxRi5tKxMGJZI4cyt5xioRERGRW5SCvSIiIiLNWGPSJjSmzeW43Nm/IiIiIiJyeRTsFREREbnOLie4ermB00ulTaCRbS7HtZr9KyIiIiIi7inYKyIiInKdXU5w1V3g9HKCxjeayw1ii4iIiIhIXQr2ioiIiNykLidofKNxF8QWEREREZHLo2CviIiIiIiIiIiISDOgYK+IiIjINaQ0BSIiIiIicr0o2CsiIiLSSJeTI1dpCkRERERE5HpRsFdERESkkW70HLmaRSwiIiIicmtTsFdERESkmdAsYhERERGRW5uCvSIiIiIiIiIiIiLNgIK9IiIiIiIiIiIiIs2Agr0iIiI3EOVcFbn5WPxDiY1wvML8sBgbfO+8iIoIJcqndqnF/3vsr08gI0ZOYt6UZMZ29qop77WU/PeWkuTa9lrwCaw5Zo6XcXwaKyltL/lpPe0frlf/RUREROqhYK+IiMh14i6QmzhsNF17xFZ/Vs5VkZtP9Lj5zJszmZG/TmbsyxvIPZTN8jizsdnVM3YthetGGUsbMISUlenMm+YISALgx9gl6cxbmEy0S+n1EJSwlH05mfwiJpigkFBi52xn35xIY7NrK3E68xZOZ+Svk6tffboZG4mIiIjcfBTsFRERuU7cBXLv+08Lrdv41WonItfHPa1akfTsYJKeHcw997QyVjeJ7ZO1xA9KJCa6M2O22Qif9gopLvFe5+xf19mjQe1DCTfXX1fz2Ux4mL0tmAlvZcbU0ovYiFDCAwKJbe8yM7ZWWxfnbBASxwjn57b9CTFVUF6rkX1Z175Y/EOJ9nddmRdREYEEVS/iR3REqKEPXkSF+WFxN6PYPJSXUoIpfzWGyD6J9jF7Oo7R8/NdGtUW1N7ep5p9cq7f2V+X/jRYZ1C2n/hB9j7ED0pkwpt197f2cahvf0VERERuHAr2ioiISLNnnEEtArBp0++wWCxYLBY2bfwd97S6soCvU96iDRz7tgNRwwHMJC3JJXvdZEb+ejLzduSyvJe93cCZ6Sz9fTZZyyczcsp8lu/IJDW4pi4l0bnGToxcOJ+RXYCxr7A8zg/aPMmsJdNJevqXTFy3gGnO+KT5l0xbOZm+dVISFHH0lD+PJdg/hQ+KxHSq1KW+N2v25LJ0WjIjJ85myY5MUtsCMaNYsnI6fZ3Nek1n3sJkogBL3Hz2vbeBaROTGZueS+EmezkMIWX1KyzflMW8KZMZ6NhfAMuvuhH01X4WZVTUFNoqOG6r+VijA6mb9pK5dDIjJ85meY6jT471r9liX//EJZlkrh/qSEfRUN2lRY+bz6xxnao/ux4HS9x89u1ZS8qvkxmbvp19C11nSouIiIjcGJpVsDeu36Dq9w8GP8yk6b/lxz73VZf9auwktVEbtVEbtVGb763N5TKupzHbUpvabf79//5f9XsRp/ffz2fuywuY+/IC3n8/n6efvlr/IbCLL8/Ava27QpfpJIWVsv7pBOIHJZCQUUZ4wtDqlpWFc0iITSC+TzwZn/rz1Oj+tdZUx7JhvHbQBie2EtYpnjFpKygoC6Rjoj3aa0kMxnIin2UnjAtCweEygnoNBUJJioCjBysxVdfuYGYfx2zbfgnsPR3II4P8sKa/y7EWwXTvYm/Vt0sQ/GU7q4lk2uhIKrNG0a1fIjFPz+GYb3/GjnZGnb0xHRtHZJ8Exqyt3gjRrX2p/HspBVCdS7i+XMeW0eN4yryfCT0TiO8Xw8uHvXlsWKij1ox1UwLd+iXQbX4+VW3DagLSDda58O1K9qZM++u1ZBo++pFMGx2KdVk8MYMSiXl6I+URCaQYm4mIiIh8z5pVsFdERERE5Ptn5ge3w/lzpVgi/PHGl+6v24OKy3v4YvINYqCj5flzR7ACYGPxJ6WYWvnWWtOlnWTZ7mL8ezxHOGaSIvwoPfCGY50G6Qcp9e9MSkQc7Srz2XzOUB/0JEtWZrFvTxZRrcHTKxDYyluHIfyZ/kAk3YPh6LZdYO5EgE8Zx9I/si9r20Hex5VYAp2zYkv5YL6jzsD0Q2/Hu270+XUyI6fMdps7OLq9H6a7gklxBGSffcAD7584c/uW8eV2x3Tgi0ALMzWJFRqqc1F+hFWvptlfGds5Zqx3Ze5EgA9YYtPtweHXu2Lx8KVdD2NDERERke/Xbe2DQ/5tLDSyfnHKWHTN/Sopnu27PjAW1+t2051crPzOWCwiInLD6PRYdyIf68Yftqyn5PNiACZN/y1n/mZl/dr0etvI1edunBtzLNTm2reZNP23LJzzGxojrucjvJ6RbSxutHdzt/P++/ZcsY89Fsmgwb/g66+/MTa7pKS0vYxkAZHJu+wF5lFk7elH5eLuTPXPIjeiiKnz361Z4GI5xwpPMmJLAR0Lw4lfZC8OfzGbNQ/sJ3hAGqm16nqy5r3JsKg7w3fat5dy3w6CB6Q5VtifzPf6Yx2eS8C6zhyNHcbcWmkRksl+z4+3Hh+Px8pc+npXwl/GEVOUTP5EWPX4eDK6zGbfgg6UvDKOmW/a+9bj7zPs+9R2Erlr/Nk2tYJnZnnwWs8pbDNPIjcvjGNRCUx1bCvptb2MJI3I5/3JPhrK0Y6JpLp2A2DkWooGnWfx4+PJcJb1WlrTD5f3vLaXkaYN/HatS9D4fCk5nw6pvf5eS8mf483ujomkktxAnYuJmRSFHXEZQzvjsaw+Dq/b9/ez5xawx6V9+SdHaDfHZRnXfXFpJyIiItIUrVvXfOcpJeXS3yVavHhx9XvN7BUREblOzvzNyrcX/snX5V9Vl/3NeprTp07Waici18egwb/AarVitVoZNOjyAr11mEOZtrwfQRWHeCsLrIeLKfcN5rELR8g5fIScw8UcO36yeuatZ8tQe/oCc1eSInyxHttur7gInj72WayWuCdp17J6C5y9YExwu5W3Cs2EvNgZS+kRMozVLlbvK8W7dQV5ywz3nTA/vE/nM/PNk1jNoXi2cKk7sZK8U4H0GN0BinawDcB2iJIyP0LGd7C3MQ8mOsSEtWi/y4JurMqi4F+RjHytf82D026v3cRpT9FJPH4WzL2fOMbuk2KOfeqS6/caqLxYhblVgP2YBCfT0fn8TNshSsp8adel0nEcj3Dsk2IKGhhrERERke+Dgr0iIiLXyV8/P85rS+Zy7puagNKGdSvZvzun+rO7gLCIXBtff/0NGes3krF+I1+7XJeXw9x5BoWHDlL03is8dce7zB06wx4UPTCDmRk2olYfJD8nm/xD2Sz9RU2qBlPYdLIPHaRwz3zCz21k7lx7EDansBhzt6UUHjpI1qAqSk7XbCvn3Y8obzuY/D25ZI2358jdtrMIU3Ag1oMr3KdwcMqawYTnZ9cNCGcfocS3P9k52eS/PZkAqlwqbWQcPklQMHz2B/tMaMhnzNSNVPZYQeGhgxTuGYWlcAHjVxlXbLSL4RPWYQ2aRNYHB+1j9mIwZ/ftsM+YPV7G+ZaRJL3cFeuqcbz2J3+e37OXfTm5FO7KZFqMcX1X1+Z9RZQ/MNR+TOYFYv2rcxzs+2uLWUHhnmz2vXeQrOVDCAE+O1OBufM4lveo3X8RERGR74OCvSIiIjcQdwFhEbmxZSR3J/i/OxPWqTPB/92ZyEEL2Xympj4vfRiRj8Qx9aU5jO/ZnYRlZdV1Z3fHE9ZnGDOHxhA2KI08R3nB0kTCYhOZOTyOyAFTSHzansIB7AHkbrGJ/PaFUYxf6giuWm1UVhbzwSrnml2lEV+dVqCCvELHzOKd44l0lp9II75TDBNemsKwxxOIf7pzTVoKwHq+ksqyj3jncHURFKURHxXDmORxTOjdmW4TdjgCzWnEG9MmuCpaQUJ0ODEDxzFzwjAS/rs78S/tty97YiHD+4xm5iv77UHmF+IJix7G3JfGkdgpnjG51F3/zvFEVn9uqM7FosQ6KRwA2DmFbo8n8ELyEOJjRzNmUOfqNBsUrSDh8c4k/noOc4fGEPnsCo4BBXOHMfy5cSzbbey/iIiIyPWnYK+IiIiIyDVXQd7hI+6/9n+mmBx36QnqK8dRV3gSKx3oG9ObJQufxDM/i5psbZejgrzDxRx3LWobSt+YUWT+Ihjr7rXUfA/ByUZB4RHyXILbjWUttae1qLU9R3mt9dlOssdNu2vGdpI9zoC4G8c/PcKeUtcDaaOgsKZ/dfovIiIich0p2CsiIiIi8j2wfl5MSX0RxUYLonNif+4tSmPqhJqZuFeNdyR9EkOp3D6DMUuVX1xERETkRqdgr4iIiEgzoZzPN5fV0xOZ8KaxtKm2MmFQIokzt1angLiqDqeROGgYw5c50iyIiIiIyA1NwV4RERGRRvqb9TSnT924sxuV81lERERE5NamYK+IiIhII21Yt5L9u+tmLW2IZtuKiIiIiMj1omCviIiIyDWk2bYiIiIiInK9KNgrIiIiIiIiIiIi0gwo2CsiIiJyk7rRcwg3htJciIiIiIhcPQr2ioiIiFxnlxOkdRcUvZwcwjcapbkQEREREbl6FOwVERERuc4uJ0h7uUHRxgSWG9PmcrgLUIuIiIiIyLWjYK+IiIhIM9aYwHJj2lyOyw1Qi4iIiIjI5VGwV0RERESa7FrN2v2b9bSxSEREREREGknBXhEREbnlXKtA5eW60frTGJc7a/dSKSM2rFtpLBIRERERkUZSsFdERERuOe4ClcYg5OUGYI3raYzG9Odqudz9aozG9PlapYwQEREREREFe0VERETATRDSXQDWyF3g1Liey3W11mN0ufvVGNeqzzcHM+FxQ5k3ZRKpQyIJMlZfa71mk5uzlBEAJJN9NJNUR1XqlgKyJ9ZqfXnMfkRHBF7+vk3MpOi9pSQZy+th8Q8l2t9cU2D2IzoilHCXohtD7fGu4UfK6myyp4QaK0RERESuGQV7RURERC5TYwKnN6Pmul/XjLk3y9/OZc34GIJCgumYOJ+snNn0Nba7lk6VYT1TQdU1DITGTnuFJWnpvDTSWHNtRI+bz6xxnRyfOpC6ZgOznvHFajM0vGFVUF5WQbmt0lghIiIics0o2CsiIiIil9SYFA03m3tatSLp2cEkPTuYe+5pZaxutKQ5yURVbmfM4wnED0okvmcMCUMXsK26hRdREaHEhvlhcVkuqH0oUT7OT2bCw2pmrQa1t7+3+IcSG+Hazs5ZXj3ztTSXzekb2NOYQKhjhmxse69axc5tBrWvWwehPBUCefvK8O881KXci6gwPyyO/sfWmfnrUn6+qmYZQ7s6s3hrMZOUtoCn2MrU5B1YncU+gcS6GVcc+1Izbs4+1Lyi/f+z1niDY33tvezj096r3uNWvd06Y+TgE+hYxsYHW1aQ8YePjC1ERERErpnb2geH/NtYaGT94pSx6Jr7VVI823d9YCwWEREREQEgrucjvJ6RbSxutHdzt/P++/kAPPZYJIMG/YKvmzybuTdr3puMaU1nEjcY64DgZLJXDubeM8WcbeFPAPks7jeFDJs9vULHwnDiFwH0ZM17k2FRd4bvtNc9dnsxphYmzt7uS0DLYjKih7HYZibp5Uye72LCetKGZ2szJYtiGH5xKfkTYdXj48kgmeyjoRztmEiqI42DczuWuPlkvhgJpaVUegfiXbaRCYPSyHO0e+RiMZ4/NnH2w1ziJ62r2Y+I2eybdzcZT5+kz55gCqKHMdeGI4VBV0zFJky328DXD/OJFcQ/uw4rHUjdtIK+vmWUlHtwr4835ouFLH7894Tvms+922NISLcBZlK37OWRYwnEzK35D4WktL2MZAFTTw9hSf8qNkcPY7EjmB09MZNZcWbOnrZhah2IZ+EMIifsAjowbf0rDGxTQUkZ3OtXye4Bb3Bv+mzCHbFZUwso2fArCoJfJ/rMFLpNsp8DSWl7ed60krCdncifYuGszYzpnA3PQD8q/ziebi/kQ6/55M8K5XxxGZVevvBuIvFL+9eMt7k3a96ajPe7o4hf9JHhGIuIiIg0TuvWNf/VnJKSUqvOncWLF1e/18xeEREREbklvf9+PnNfXsDclxfw/vv5PP10rLFJI/jj3bKcs4eN5QBmpk3sz71/mUNCn0Tie44ixxZJ0ouRxoZumf6+lYTYBOJ7LqDg20A6xgFdppPUrYq9z8UQPyiBbrEJzNxuXLI+kUwbHUll1ii69Usk5uk5HPPtz9jRNdNbLR5FTI1OqB3oBWLjOmA+fogM23aOlwXSMdF1SqwZ66YEuvVLoNv8fKrahtEXsIweR1/fQhY/nUB8v3gSdpY52ueTcaAM/4gh9hmz5iG09yvmg2VuZo5bhjDvmUBMhuI9r48mISqe+EGJxCzKh4g4xjq2ObBNkX2bgxJI6Dma1NP7GfN0Z8I6deblA+VUntjO4kWfMHdnEebg3o50G5GEB8GxnVvtG/CA41NjiBmUQELWSbwf7Eo4kNQjFP6URsygROJ7did+qXOfcKSamEy7TxYwZpFm84qIiMj3Q8FeEREREZErYsLT31gG0JWANpV8ttuZeuAjlhWW4d2mcQ/sOnvSuVwl4IH5PrBE+ON9upBlRY5GNltNWoNLMXciwKeMY+mOQKRtB3kfV2IJdObFhZLDC8mrWcIhkqce9qX8X37Mm9Ifj28rCXIGagEo48vtjim3F4EWZryA6EA/bB/vJ8NRZb1Yk7u2YNl+SgO7MrYtWBJD8S8+xGo3KSjMbf05++ZoMooDGbhkcM02bR5EjV9K1lu55I8Pw+zcZnvDNm0uK+0ym+e72HjnJcc+Zm2kgFD6JABdetOOI+TsdLT91spnjjG2XqwEsxftgIy1Ozj78HQKc9YyL652egfPF2fT16+YbdNdUk2IiIiIXGcK9oqIiIjILemxxyKZ9sJkpr0wmccei+TtnTnGJo2Qj7XMC++Q+vLNunG7R50cs9+r243zZg269Kad+SQlp+0fqz4/iS0wlKQm7HIdthXs/tCLkAQ/Bkb4UXp4g9sAaeWHKxiz6AiLF22nPLg/07oAmElZv52Uh06y+bkYIhcV4iZObNCTNTO6cn7bAlKdgXLyyThQQUC3/sQ+GQhFO1zyLNejKI34TjG8vKmUoIlZZC3p6agIJDqylJwP/ej7cu8b6/iKiIjILUXBXhERERG5JQ0a/AusVitWq9Wer/frpubrBThCxr5iAvqnkxrsKDKHMm/WJGLN+/nslImQXs4ZqR1I6exH+eeH7IHNi+DpY0/pYIl7knYta9ZaH+vhYsr9I0kJc0Rag0OJbWzQ1XaIkjI/QsZ3sH82DyY6xIS1aL+xZS19ewXjfSKXMfMXMnX+QqbOXMHRCmMqh7r2nC7D/GBXR1DYTGzL2u1Xv3kE06MzeKTNSY5mug/XVp0vt49V0UJW74OoidOJohPt2lRy7A9pbDsDQT6eeDja7yk6iUdIHCmOB9pZwkKJApKWjCOkYiuLDekVCpbtxxr0JL940MRnO+25extiCYsk1lzBtg1zGLPzJGaL8zFzZeyZNJ6pkzZgDUtmVlzDYyMiIiJyrSjYKyIiIiK3pK+//oaM9RvJWL/xMh7MVqNg0WgW7zPz1LoCCg8dpHDPK4T7lGGz2Vg8ZgHH2owi+9BBCj9YQZRtIzNfsAcVcwqLMXdbSuGhg2QNqqqeOdugAwtYtb2KqJW59m2tns8zCb6GRmXYbIH0XT8KAOs3NgKeWcs08hkzdSOVPVY4+jkKS+ECxq9yH2i1i6R7sJnjB11z+B5h7yc2QyqHuqyLFrCtLIyU9w5SeCiXiT+roiaRA3BgB595dCDo1EHHw94atm3uVo61fJKxo4+QV1hF+Iu57MvZy9ruHtUze62rXmHzJ74k5Ryk8NBBspdMps+vZ5PUzQtT6/4sOWQvz325q30B2waOngok6PYjvHPAdWvu+BKbMIlZe3LJ3ZRFVl8zBZvecNTZOF8E2NaxaHMZIWNfurKZzyIiIiKX6bb2wSH/NhYaWb84ZSy65n6VFM/2XR8Yi0VEREREAIjr+QivZ2Qbi79HXkRF+FP5yREKDMHLoPah3P9tMXtKDRU+gcR6lZPzaUXt8ksx+xH9gIkvDxdz3FiHfb3RLcoc2/MiKsxMSeFJR6oEM+FhgZhOHSHvjHHBq6/efcePJW9v4P63O5OwylDVCBb/UNrhbr2OcW1TybHqfa7f2NUH6VM+jm4vHDFWuWf2I/oBb867Oc4iIiIiV0Pr1jX/pZ6SklKrzp3FixdXv282wd4779QkZREREZHm4Lvv/p+xyK0bL9grjRXVuSdBPYYxMrKU16KnkGFscB2Eh3UlIKw/Y4eY2f1sIqknjC1EREREvh9XEuxVhFRERERERK6roCeH8JRPEasmfD+BXgDvzr0ZGFbJO9NHK9ArIiIizYaCvSIiIiIicl2tnp5I/HNzWF1krLl+cpaOJ37oeObuUy4GERERaT5umTQOLVq04Oc//zmtWrVi48aNVFQ0nBctKiqKoKAgPD09jVV1nD9/nuPHj5OXl2esEhEREbnleXl5MXjwYL755hv+8Ic/cOHCBWOTWpTGQURERERuZUrjcAkdOnRg+PDh/PjHP+bOO++kY8eOxia1DBgwgPDw8EYFegE8PT0JDw9nwIABxioRERGRW17Hjh258847+fGPf8zw4cPp0KGDsYmIiIiIiFwFzTrY26JFCwYMGMATTzzBnXfeaax2Kyoqivvvv99Y3Cj3338/UVFRxmIRERERcbjzzjt54oknGDBgAC1atDBWi4iIiIjIFWi2wd4WLVowfPjwJgdug4KCjEVNcqXLi4iIiNwK7r//foYPH46Xl5exSkRERERELlOzDfb+9Kc/bfRsXleNTd1QnytdXkRERORWceedd+Lr62ssFhERERGRy9Rsg71//etf+e6774zFl3T+/HljUZNc6fIiIiIit4rvvvuOsrIyY7GIiIiIiFymZhvsvXDhAmvWrOHLL780VjXo+PHjxqImudLlRURERG4FX375JWvWrKGiosJYJSIiIiIil6nZBntxBHy3bNnCH//4x0bP8s3Ly2tygNjpyy+/JC8vz1gsIiIiIg7fffcdf/zjH9myZQsXLlwwVouIiIiIyBVo1sFep48++og1a9bw1Vdf8d1333H06FFjk1q2bNlCQUFBo1MynD9/noKCArZs2WKsEhEREbnlHT16lO+++46vvvqKNWvW8NFHHxmbiIiIiIiIG5eKTxrrb2sfHPLvWiVuWL84ZSy65n6VFM/2XR8Yi+t15523RNxaREREpNn77rv/ZyxyK67nI7yekW0sFhERERG5qbVubal+HxUVRXh4eK16VwUFBbUyDShCKiIiIiIiIiIiInIDaijlrLuUsgr2ioiIiIhcMTPhcUOZN2USqUMiCTJWX2u9ZpObs5QRACSTfTSTVEdV6pYCsifWat1EZsLDQon2N9cu9gkkNiLwMva1dv/c8yIqIpQon9qlFv9QYsP8sHCt91lERETkxmFMOdtQStlmk8ZBRERERG4tN0waB3Nvlm+aTJS5jJKySvDyJ+DiflJjZ7DN2PZaCR7FmvFe5P16Dhm2ZLKPhnK0YyKpjsBnx8Jw4hcZF2qsnqx5bzbhZzYSMyANq6N0xMq9jI0oY5tjO41Xu3/uJZN9dDD3HphBZPIuR5kf897KIrZVPosfH0/GNd1nERERke+PaxqHptLMXhERERG5Jd3TqhVJzw4m6dnB3HNPK2N1oyXNSSaqcjtjHk8gflAi8T1jSBi6wCXQa5+lWj0j1SGovevMVfvs2XDH5Nmg9vb3Fv9QYuub4RrhMtu2NJfN6RvYY6vdzi2zH9ERocS296pV7NxmUPu6dWDD5hXJ2LbOz/15rG0V5edqt3L2y3V5i7+9/7Vm5bryCXRffs4GIXGOmbtA2/6EmCood35uyj67OwZmP6Lbe1WPh3PsRURERG5mCvaKiIiIyC1p06bfYbFYsFgsbNr4O+5pdTkB395EhZg4lr2QmmxpNo6fcUQgg5PJPpTLvCnJjJyzgdxd80lyBBUHzkwnJdG5TCdGLpzPyC41dfPWZZK1fDIjZ73C8h1rSTEDmEl6OZvsjbMZ+evJTFufxZo4oEsysxYmE+1cXT0scfPZ994Gpk1MZmx6LoWbkoly1A2cmc6s1zNZ++pkRg7tbViyjKMfehCS4Gf/OKQrAWWllN9V02LEklyyV09m5K8nM3HddrLH29tGj5vPvFczyVw/nbEj+xNSswiYe7Nm41pGdjFXzxiuUcTRU/48lmD/FD4oEtOp0prqRu4zwclk52Uxa2IyI+esJXtTMuE4lk9/hezfr2XalF/S9wHjgiIiIiI3HwV7RUREROSW9P77+cx9eQFzX17A++/n8/TTscYmjeCPd8tyzh42lgOYmTaxP/f+ZQ4JfRKJ7zmKHFskSS9GGhu6Zfr7VhJiE4jvuYCCbwPpGAd0mU5Styr2PhdD/KAEusUmMHO7ccn6RDJtdCSVWaPo1i+RmKfncMy3P2NH10xptXgUMTU6gfhJ62otCXB2ZzGmzsOIBVKeCKTkj8V4mGrqV89MID46gfhBCSRuKyOg2xB7UBUw+5STERtPzIiF5FQv0YHUNZNp98kCxiz6qLrUVcHhMoJ6DQVCSYqAowcrcdlkI9iPgWn3OLr1SyS+54sUeHUlKcJR3dKX8vR4uvUZzVS3x1BERETk5qJgr4iIiIjIFTHh6W8sA+hKQJtKPtu9wzFr9SOWFZbh3SbU2NCtsyedy1UCHpjvA0uEP96nC1lW5Ghks7mZEVsPcycCfMo4lu4IrNp2kPdxJZbATtVNSg67zlA2OLCVz+jAUxFDecS3mPc3GBsE0ndWOtk5uWTF+ILZi3aOGtuxd8kwpFvwfHE2ff2K2TbduZ9upB+k1L8zKRFxtKvMZ7MhbcSldSWgDXg+NJnsTZlkb3qOdi18sTzqqD5XRN72RuWBEBEREbkpKNgrIiIiIrekxx6LZNoLk5n2wmQeeyySt3fWzDltvHysZV54hzQh4evtHnXz036fbm/sXNkjZBysot3EGLxP7Gd1rbpIlm95hT7m/cwdEEPkTpd0C24FEh1ZSs6HfvR9uXcD47GO3Z/4Ej2xA5WFWykwVjdKJSU701j1qv21aNJoFm8ythERERFpHhTsFREREZFb0qDBv8BqtWK1Whk06Bd8/fU3xiaNcISMfcUE9E8nNdhRZA5l3qxJxJr389kpEyG9BjuCmR1I6exH+eeH7DNZL4Knjz2lgyXuSdq1rFlrfayHiyn3jyQlzBFcDg4ltrFxZtshSsr8CBnfwf7ZPJjoEBPWov3GlvUqyCqksq2Zz97caqgJxeJbRsHMrRTYzMS2vFSnytgzaTxTJ23AGpbMrLj626/eV4p36wrylp00VjWC/RgEPOTLscNHyDl8hGOfFJN3xthOREREpHlQsFdEREREbklff/0NGes3krF+I19/czmBXruCRaNZvM/MU+sKKDx0kMI9rxDuU4bNZmPxmAUcazOK7EMHKfxgBVG2jcx8IR+AnMJizN2WUnjoIFmDqig5bVyzGwcWsGp7FVErc+3bWj2fZxJ8DY3KsNkC6bt+FADWb2wEPLOWaeQzZupGKnuscPRzFJbCBYxf1YQ0BidWMnPEOJYdMFZs5+invsTmZLNvTzZjA+zJJ+pn43wRYFvHos1lhIx9qfrBdXVkzWDC87PrpIGorb59th+Doz9JJjsvm317DpK9ZTIDjYuLiIiINBO3tQ8O+bex0Mj6xSlj0TX3q6R4tu/6wFh8CT/A+/57oKKM8m+NdTXu9Pwx9/3wW/7+Nxvf1aowc5/PXVw48xXna1WIiIiI3Nru9Pwx93l+R8WX33DBWHkF6v29rBHiej7C6xnZxuLvkRdREf5UfnKEAkNgMqh9KPd/W8yeUkOFTyCxXuXkfFpRu/xSzH5EP2Diy8PFHDfWYV9vdIsyx/a8iAozU1J40pEb10x4WCCmU0eu+gzXoPah3Ftx9dfbKA3uM1j8Qwm5q7TpYy0iIiJynbVuXX+Sq0tpPsHee7syeVI3vM99DffcyUe/W0Lmx/8yNLqDBwdP4FchUPHtXXh+8z5zX9lPOXDngz9n5i8ehK+/pUVLG7sXriD3rGFxERERkVtOKx4fNYqf+/+Lv5+7g/u8/0XBG7V/z7rzwQH89lcdKX/7BRb8sdbCALR+YgjDogK57z8+5rXfbOFjR/mD/cbzy/A7qTh3B/fdVcYfXv0d7zXh968bL9grIiIiInLlriTY22zSOET264bnh6uZPHcpv8n6ioee+TkPAtzZhsjuD+ANcH8s/R76mj/8ZgGpM18lv8UT/PKJOwBffh7fkYq3f8v0uQuYe+gHxD3blTuNGxERERG55dg4dWALUyYv4Ldz57K0ECKjH62pvjOQxAE/oeprl0Vcf/8CKj7cx9rcv3LepQktnyAuwsaO39jXO+vPrfh5v1DXFiIiIiIi0kTNJNgbSOD931JyzD4D+buCo5Tc+RMevB947CkS458i+n6gvYX7/vZX3vsO4Bt2f/oVrX8WBgTSxqeM4wfsM1TK3yvm75afYn9choiIiMit7F+UfPpXR+qGO/D2vIsL/3BGdu/gwcE/J+iv2yl0jeS6/v4FXDhbxulzhhxbvq34UaWNckfuhvL3iqm4/6f2/6wXEREREZHL0kyCvb54e9qo+Nz5+VsufHcHLTyBvauZ/JtX2fwlPOjbivMVZdVLnb/wf9z5gx9A+x/jff5rqp+Jce5bLtx5J3dVtxQRERG5tT3UbxS/efE3DPT8E69lfAKONFjP/rSY9b8rpsq1scvvX/X6/CvK7/Yl8D/vAH5AYLef4uV5D62N7UREREREpNGaSbD3X8Ad3PFDYznAvzh//v8A+O47uMPkJoT73b/gzrtoYSwXEREREQCKD2wnZ//HnG75KM/GBTrSN/yUj978Q3UO3ho1v3/V67v9vPFmGR2e/w1LFvyaHv/7BX93/FYnIiIiIiKXp5kEe8v427m7+JHjq4JwFy3u/Jb/rZnEC0Dx37+hheePq3Pxerb4ARf+8Y19ZsldZv7TWdHyLlp8+y16Tq+IiIiI3YWzZXyY9weWvnmcFp2ieLzzozxoupOHfjGTJQtmEtsGAp6YyfNdjUvWr/zwFlJ/M4sJkxfw2pm7+NFZK8eNjUREREREpNGaSbD3r3z8xZ0EhQZyJ3BneEcCvj3Fh+cMDwj581857RNI7L0ArejR/h5OH/8YOErxlz/mwSdaAeD9eCBeXxbzoWErIiIiIreclo/y86faVP9n+Z1338Udlf/H/+7/HRMmz6p+5ZyCkj/O4rX9dR/QVh/v/3T+J3wrYno+SNXxozVptUREREREpMluv9fnvlRjoZHtf//XWHTNhXYMovivVmNxvf5eUkXg0z8noUsYPR76IR9u+h0Hvvp/0HUQv4l/kP/4KJ+Pv/or33hG0mdgN6KiHuOn5/exfEsJF6ii+Bsz3eP7Edv5UaID/kHO629RbH8SiYiIiMit67YfERo/mF/GPU6P6GhiO9zJZ9s2s/PL2mkaftYpGp+v9pL/V2r//nU+iucXDGXgw/dx9w9/zENdH+ORVl/y3if3EPPrYfwq7nF6PBHJT7/Zx8I3jjkeBNc4QW3v58/HNBdYRERERJqXH/3I01jUaLe1Dw75t7HQyPrFKWPRNferpHi27/rAWHwJd+D5n/dA+VecdzzZGe7A0/OO2nnj7mpF67u/5fRZYy65H+B9/11c+PKbJv2hISIiItLs3dWK1l5Q0ajfk9z8/lWPFvf64ln5FX8/3/RsvXE9H+H1jGxjsYiIiIjITa11a4uxqNGaSRoHp39x/m+ugV5HmfEPjW+/cRPoBfg/yhv1B4yIiIjILebbbzjd6N+T3Pz+VY8LZ8suK9ArIiIiIiJ1NbNgr4iIiIiIiIiIiMitScFeERERERERERERkWZAwV4RERERERERERGRZkDBXhEREREREREREZFmQMFeEREREZErZiY8bijzpkwidUgkQcbqa63XbHJzljICgGSyj2aS6qhK3VJA9sRarZvITHhYKNH+5trFPoHERgRecl+T0vaSn9bT/qFWP5tgYiZF7y0lyVjujtmP6DA/ap5h7UVUrX56ERURSrhhd0RERESaAwV7RURERESuhLk3y9/OZc34GIJCgumYOJ+snNn0Nba7lk79f/buPi6qKn/g+IfcmHT3kgVGzaQLZAtaovUDYkUyEdfAJ3R1MpXIfCifFvE51qfSzGcj8yHTXCOtsAzNIFd8CnEN2FawEjYD0mY2E8qY0oZSfn/MDMxcZgbwoRS/79eLV3LOufeec+6598Z3zpxrxHCqgqorEsDszOglq1m+8Am7ACqMemY1C9fOZLBdWr2uaD2tfIcx+ZXFTLAdo99MFq5dzZTe1t/bjiE5ZQyRJrtthBBCCCGaCAn2CiGEEEKI69Ktt9xCwmNDSXhsKLfeeos6u8ES5icSad7OuIf0xA2JJ65nDPrhi9lWU8IykzTWYbYpBLUPIdLX9ptl9qxttmlQe8u/df4hxIbbl7OwpdfMti3N5M3Vr5PVkACm4kd0eAix7b0dkm3HDGpfNw9MmLwjmNDW9vsgHmxbRfkZx1Ku9l3Dvp6KH9HtvV32j61PYsMDCaqscsip2cbZDN3jORjKtej6WX6NDffHfKoK3f1+loRwLT7flJJtLW7rS/s66/wtfa7zt9XLm0jbf+3Ph5P22ra1zHx2MiNaCCGEEOIK8mgf3LFanahm+PKEOumKezIhju27PlInCyGEEEIIAUC/ng/w8qZ0dXKDfZC5nQ8/zAHgwQcjGDLkcb797jt1sXr0Zf3+aWjWdyH+dXUeEJxI+tqhtDpVzOkW/gSQw7KB09lksiyv0CkvjLilAD1Zv38aLO3OyJ2WvAebFaNpoeF0My0BLYvZFD2CZSaFhOdTGd9Vg6HMhFdrhZKlMYw8v4KcKbDuoSQ2kUj6kRCOdIpnrnUZB9txdP0WkTo7AkpLMfsE4mPczKQhKWRbyz1wvhiv2zSc/k8mcVM3WhvRk/X7h2HOUwj4biIxC8pg2Gpy/gKGQIXPHrAcR9dvEanJIZi/MILWH03ePKKm7iIhZQ+jWUxE4i7obVfP3ivIma7jtElBc8aEV6Af5n8mEfV0DtCBuVvWMEBrpKTck1a+Pijn81j2UBKb7ksk/cW+eJ0yUqnREtA8n2XR09lk1+2TXztI9PFhxDwLy997Ac0BI2GhxcQ9kkLH59P5u/IqEeN3MGp5JqODTRi+Aa+7tFS+OYy4FWWWOt9uxKxVMBflsHJUFaOPdIPPFFo1r+DITj0Lyl23d3wbIyZFQ6XJmwB/E9mJcYw7YFdBIYQQQgg3Wrd2/Ai8MWRmrxBCCCGEuC59+GEOC55fzILnF/Phhzn06ROrLtIA/vi0LOf0YXU6gELylEG0+ng++v7xxPUcQ4YpgoTZEeqCTmm+3oo+Vk9cz8XkngukUz+g60wSoqrY81QMcUP0RMXqmbNdvaUrESSPjcCcNoaogfHE9JlPgXYQE8bWzjzVeRYyI1pvF+itdXpnMZouI4gFJv8lkJJ/FuOpseVGkDw2BMPKOGKGxBPTZzPl4XomO+6iLk8omhFDzBA9+rQyfO7tRhigGzuRAdo8lvXREzcwDv1OY+02/3mVcbHdiRoYT1yfFHJ/F0H0MPudQvbxcnRtuwHduFMxUvRyMQa/YAYDYX/UcvqLHQC8MkdPXLSeuCF64rcZCYgaRph1H4pvOZti44gZtYQMAHzQFEwkor+ecRvct1dzvpA50Xri+seRcVxLQFfrrGIhhBBCiCtMgr1CCCGEEEJcEg1e/uo0gG4EtDFzbPcODAAcZWWeEZ82IeqCTp0us21nBjxRbgdduD8+J/NYWWgtZDJZyzSA0pkAXyMFq49afjftIPsTM7rAzjVFSg4vqVneoI4DWzlGB3qFD+cBbTEf2s9kVjoT4Au62NWkb0kl/eVu6Dy1tOthV8aZcwaOWdtiOG8GxZt2QHSgH6ZP9rHJuiyF4bzZbiMTSpdE1m9MY+/+RDoqlr6xl1tQikkbxOB+weiMxaw05WCo8KNd757cqTVi+JetZCADnllNekYmaTHamuMDmAo+qDm+RSkfLbL2XT3tNRkKrf1o4qfz4OUdaLcfIYQQQogrR4K9QgghhBDiuvTggxEkPz2N5Ken8eCDEby30zJ/s3FyMBi98enYiHVZm3mq1qb9jTWrmZ5bj3w2Hayi3ZQYfI7v4xV1NhUceTGFdS+msO7FxTyXOIt1Tmc8Xxrd6A2kJgfz1YYxRD20mAJn6xRvL+P0LVrCwv2pLNoO5JP7OejuD8XH00jRYYAIVr31Av2VfSx4JIaInaXqvdTj12mvEEIIIURjSLBXCCGEEEJcl4YMfRyDwYDBYLCs1/ttY9frxRIA3VtMwKDVzA22JikhLHxmKrHKPo6d0NCx91BrcLcDk7v4Uf75Icts3PPg5WtZ0kHX72HatazdqyuGw8WU+0cwOdQaXA4OIbahcWbTIUqMfnRM6mD5XRlKdEcNhsJ96pIu5ablYW6rcOyNrY4ZpkOUGLW062om43A+GYfzKfi0mFxngdgGyDppRLm3GwkKgEJsy9pGRt/jR1XBduYerABfLV6e9lva5GA45UOn+zUYPi4DYNOnZfiEh9DKWGp9eV4IOq2R3DlbyTU5HqNel7m9QgghhBCXiwR7hRBCCCHEdenbb79j02ub2fTa5ot4MVut3KVjWbZXodfGXPIOHSQv6wXCfI2YTCaWjVtMQZsxpB86SN5Ha4g0bWbO05aXwmXkFaNErSDv0EHShlRRclK9ZycOLGbd9ioi12ZajvXKIh7Va1WFjJhMgQx4bQwAhu9MBDy6gWRyGDdjM+Yea6z1HIMubzFJ6xoRoTy+ljmjJrKyzsvGLPs2xawhLyudvfsPkrZqGB2BY6cqULpMZFV9SzrYMSxdzDZjKJP3HyTvUCZT7q7CtpDDpt35mP88k5yMdHI2R+HptPr5FBkUfJRSCnZak94oxKDVwol869IX2znymZbYjHT2ZqUzIcCyYEbDuG6vEEIIIcRvyaN9cMdqdaKa4csT6qQr7smEOLbv+kidLIQQQgghBAD9ej7Ay5vS1cm/IW8iw/0xf5pfZ4ZnUPsQ7jxXTFapKsM3kFjvcjI+q3BMr4/iR/Q9Gr46XEyROg/LfqNbGK3H8yYyVKEkr8wa5FQICw1EcyKf7FPqDS9d3bYqhIVqqcxzUVc36u7LSvEjOgiO1bTp4gW1D6FVxcX3hcs6CiGEEEJcpNatL37RLwn2CiGEEEKIa9LVF+wVQgghhBDi0l1KsFeWcRBCCCGEEEIIIYQQQogmQIK9QghxFVCUP6C9w1edLIQQQgghhBBCCNFgEuwVQoirwNSkJ5mdPJFH9f3QeDp9rbgQQgghhBBCCCGEWxLsFUKIq4DnjZYAb9fIcObOnES7oLvVRYQQQgghhBBCCCHckmCvEEJcDTxq/3nLLTeTOO4JHhs6kObNb7IvJYQQQgghhBBCCOGSR/vgjtXqRDXDlyfUSVfckwlxbN/1kTr5qtbiD7/nDzffzI1X+CvYP1dV8cP333P2hx/VWUKIa9Si55K52UtRJ2My/cBrm9/h6KdF6iwhhLju9ev5AC9vSlcnCyGEEEIIcU1r3VqnTmowCfZeJi3+8Hu8brmF78orMJ87p86+rDTNm3OLjzeV330nAV8hmojFzyXj5STYa5P/7wK2pG3n7Nkre38RQly/brjhBnRaXzxv9OTEV0Z+/vlndZFG+b/7g7nDtxX/O3Waf39cqM6+LCTYK4QQQgghmqJLCfY2a+V7+1x1oprp++/VSVdcSKcgir8wqJMvK+0dvgz6a29uuOEG/vf1Nw55LVt68dNPZoc0d25p1YozFd9e8UAvwPlffuHnn3/G65aW/GgyqbOFEJdJ5/D/Izoqks+Pl7oMenh4eBDVtTNdIsIoLv6C8+fPq4s0SI/ukWg0GnVyDa32diI6h/Ltd99j/N8pdfZl0/YuP/4aF0N5xbd8X+n6/hJ8bzt6xXTnxEkj5879pM4WQlyl7m0fyA8//MjPv/xSk3Zfp3t54jE9jwzsw91tAwgKbIv+r71o3+5PNG9+E6VlJx320RCjRwyld0x3/nR3AP93Xwe02tv593+OqovV+Ev3B7njjts4cdKoznIrqO2d/LtAvvkghBBCCCGalptv9lInNdh1Gey94YYbePgvDzFq+KOcLv+WmJ7d+GD3AaqrLZOcvbwUnp09hdy8I5z7qWFBjJY+3nx3ulydfMWc/+UXWvp4YzpzRp0lhLhEN92kYXi8npie3bhTdwcXLlyg+L9fqIvhpfyBMaPi6frgn2nTWsv3lSbKvvxKXaxB6gv2Atx4443833338sc/3klR8RdUVVWpi1yySX8bRdu7/Ij4cyjnfjK7DPIkT5vAH9voiIwI5XTFd1c0AC2EuHxGjxhKxbff8fWp09xwww2MGRVP94cieC8ji9def5s9+3M4eCiP/dmHOXfuJx7+Szda+dzKZ8c+V+/Kpf+7P5jeMd15I207q9ZuwvTDj/yl+4P879Rp/ufkXhHXtyd9evXgi9Iv+aLkS3W2WxLsFUIIIYQQTdGlBHuvuxe0ae/wJXnaeB6MeIAXXtpA6ua3ueGGG9B43lhTprLSxMGcXIY92t9hWyFE03en7g5mJ0/k/vs6APDL+fNOg7CBdwcwZ2YSgX+6q6bcTU7KXU4e1pe4dbgniGdnT6Zz+P+pi1yyG633Qg8PD/R/7c34px53+pK4G2/8HQC/+93vGPn4YB4b8teaNCHE1e+Wljczd2YS3rfewvxFL3I492N+Mtd+o+ns2XPk5h9h0dJV+Pu14VF9P4ft3bnDtxUAB7IPO/zXlm4vrm9PHu7xEHv25bBr9wF1thBCCCGEEKKRmnSw94YbbiDwT3fRK6Y7vWOjeWRgH2bO+BslpSeYO385//28xKF8cId2jHx8MADp7/2TO3V34ONzq0OZS9YykB6PPs785+Ywvps6UwjxW+reLYKnp46n5c1eeFgjqx7WH5sbbriBfn3+wsQJI2l+U/Oacr+25jfdxGNDB5L0t1Hc0vJmdfYls7XrnvZ/Ys7fk/D3a6MuAtb+APhzeAiznp6I7211gzlCiKvLzTd7MW3yGL7/3sSSFWspL/9WXaSG6YcfWf7iOoLvbYe/X2t1tlP/O3UagK6R4QCMHP6oQ7qNfaB367adDnlCCCGEEEKIi9Okg709ukcyYczjtAtsy5/uDkB7hy8vrn6VLW+lY3by9efPj5fSLuhugju04+effyb/P0fxa3OnutilqzRx7sabaGGbTHyjwu133obXjQA34XOnltu9ZIacEL+WFi2akzhuBIMG9KZZsxtqAphqt9xyM9MmjSHmL93w8PDghht+m0CvvcC7A3hm1mS6RoZflsCzeg8eHh7c7KUwNekpHv7LQ3bHcCzp4QGtfG5l1tN/o0vnUIc8IcTV48Ybf0f/vg/zv6+/IWXVqw6zeV2pqvqZPfsO0jO6qzrLqX9/XMjHRz7hUX0/1q58npD7gwHwa1P7koneMd0l0CuEEEIIIcQV0KTX7B0QF8P7mXtIe2cn//ro3xzO/Zjyiu8cytx44+/o2eMhdu0+wLlzP9GiRXPu1N3B0U+K0N7hi+9trThWVP86dV63tGzY+rk/VVDy+QXufqgTLb7cQ84XwIXf03P8FB4LqiC7xcPMf/xuTn+QT9kF9caOGnxMIYRLf2xzJ9MmjUGn9XUaLK2urqbshIHqapj8t9F4e9/itNyFC9X89/MSjpeUqbMapHtUF2666eKWgWjWrBkd7gki6E93UVT8RYPXGncmuntkneUoPDw88PDwICiwLX9q609B4Wf8JfrBOv1gCYDfQHCHdmjv8KXwkyIuXKjnRiaE+FXF9enJTTdpaOXjTezDUfSOja735WkARuMp9IP68vGRTzh7tv6X0f77P0dr1ug9cPAjvv32O3p0fxCNxpM/tr6Tvr3/woHsw7z19nvqTRtF1uwVQgghhBBNkazZ64LG07PRb4n/39ff4HubDwDfnamk9Z1adZEr4Dve2fIvzraPJfkvd1Fx8B32/6wuI4S4Ep4aNQwv5Q91Apf2mt1wA2NHx3PTTRq35S6J5f2Ql6TtXX706RWtTr6s/nR3ADEPR9Vb3fvv60DXyAfUyUKI39jEqXN5asLTDj/rNmxWF6vjJ7OZiorvuFN7uzrLpX9/XMjOzD38++NC3knPZPeeD+nR/UHi+vbkQPZh3kjbrt5ECCGEEEIIcYmadLD3Yvzh9y1qAsRef/j9FXnbvVNlxZT8oODjdYKc9x1nHwshrpzi/35BdbX70OX5CxcoKT3B+Ss5S/USYsi2+v/3eCmZu/arsy8b23G++KLMbXWrq6u5cOECpWUn1VlCiGuYovyeStMP6uQGO/N9JQAnvzJKoFcIIYQQQogrpEkHe81VVdx4o21h3Pp5eHjQKfgeThr+B4CvbyuMX59SF7siAvT9CPsxn5xTbYh5ohMNr7UQ4lL8I3Ur7+74wBqgrBv0taW88NIG9u7LsaQ5Cw67i342wA2XMGP43E9mUre8w/KUdXxzulyd3SiuanH+/Hl+/PEsS194mSOFn7ksef7CBb478z3zF77IFyVfqrOFEFeJO26/rVHfVPD0vBEv5Q98bw3YNkTI/cH0julOyP3BdO8WwaABvTmQfZjnFq1k4viRDB7Ut1F1EEIIIYQQQtSvSQd7jx4ton/fh5mUOJpJiaMZ92QCgX+6S12sxn0d76HlzV5k7ckG4Pbbb+Prrx3fHH3J/jKGtSsfJ8wLAvo8z9rnHuHeVrE8FtmcT7K2k7rlX5y9tx9PhskL2oT4tfwz60OeX7KKH374gfPnHWfvelh/Lly4wDvpGby46lV++slcp9yvzRZw/vjIJ8yZt4ycf+Wri1w2Fy5U8/kXZcyet4zjX7hek7i6upqCws+YO385xv/9Oh+UCSEuzqgnhtAjKlKd7FLEn0P58oShzrsPXHlyxFBGDn+UqG5dGDn8UQYN6M3BQ3k1M3q/OV3OQw/+maGD+6s3FUIIIYQQQlyCJv2CtpKyE3xl/B8XLlTz7bdn+OWXX3hkYB9uvtmL/35ewvnz5x1e0Hbqm3Jy/13Ajz+epe1dfkRGhPHW2zv45Zfz6l3X0eCXpX2Rz87MPbU/ez/lm7Ofsz/zALmGC3CmmL22f9ejwccUQtTr+0oTB/+VR5s2Olr53Foz26y6upovTxj49LP/AnC6/Fv+dfjf/Oluf2728qopd6H60l7Q1qN7JBrVi9HcqTT9wIaNb5K5a99lXW7Gvh4XLlygurqad7d/wJa30vn5519qyvWK6V7b9gsX+OX8eV5/4122v7frNw+ECyHqV/Htdzyqj+PQ4XzMZvf3EA8PD54cOYztO//J16fq/xA85P5gesV054207azbsBlv71tofaeWf2Z9WPNB0CefFXPH7bcRHnYfivIHPvm0WL2bBpEXtAkhhBBCiKZIXtDmwoULFygq/oL3M/ewMyOLt95+j/kLXyTAvw1zZ07iT3cHOJQ3V1Xx/feVtGh+E2NHP8am17fy009mhzJCiKbr3LmfWLl6I2+9/R7nz1+wLOvg4VHnZWSVph9YvHwtGR/ssSz/UF2NBx5U1yl5+VUDHx78iDnPLuPop5c/wGFrwfnz56ms/IGFS1eze6/l2w6OLCUvXLjA6fJvefa5F/jXR/9WFxJCXKU++bSYouLjTEl6kjt1d6iza/y+RQvGPZWA2VxFQeFn6mynbvdtxdlzP3Eg+zAAm15/m7PnfuJ231Y1Zaqrq1m/8Q0O/SufrpHhDBrQ224PQgghhBBCiIvVpGf2OmP64UcOHsrjxht/R8LQgfj63kYrn1t5P3MvF2zrcHrAt9+eafAfNfxGs2x/i2MKcT0o+/IrCo8eo8M9Qfy+RXNy/pXHiZNGdTH+e7yU4v9+QccO7bjpJg37Dhzi1DcXt2auu5m91dXVeHh48M3pclat3UR2zkf8cr7+bxxcjHva/YlWPrdy9JMiXnhpPRXfOv/Kdsj/dUT5w+/JzsllzbpUfvjhR3URIcRVrvDoMW69tSXDHu3PTz+ZKS//lqqqnwHQeHoScn8wT44ahtlcxZpXUmteYFsfLy+FB0Lvw/TDj3x54iu6RoZzf6d7+fDgR3WWeCk4egxF+QPdunbm519+afRa3zKzVwghhBBCNEWXMrPXo31wx3qnohm+PKFOuuKeTIhj+66P1MmXlfYOX/r3fZj8jwv5KO8/6uxGuU2n5ftvv8N87pw664rQNG/OzbfewjeGugEoIcTlodF40vLmmzn1jfuvLf/+9y1ofpOmwWtZOrP4uWS8vBR1MgDnz1/gg937yfxg7xUL8tp4eHjQprWOL098pc5y0KxZM+64/Ta+sr7QUghx7Qrwb8MjA/vwxzZ3Ul7xHWazmTtuv41K0w/s2n2A/R/+y/mLKd14csRQ7ut0L2fP/USL5jfxnyOf8PKGzepiNfr27oHJ9CP7DhxSZ7nVr+cDvLwpXZ0shBBCCCHENa11a506qcGu62Dv5dTiD7/H65Zb+K684ooHfDXNm3OLjzeV333HWZlNJ0STsOi5ZG62C/baZvN+eeIrNqZu5euvv3EoL4QQl9sdt9+GovwBrEu0lJSe4MKFi1+DO+T+YG73bcXXp06T/3GhOvuykGCvEEIIIXT+gejKi8k1qXOEuHZdSrC3Sa/Z+2s6+8OPVH73HTffegs6f78r+nPzrbdIoFeIJqb6guPnbj///Atp7+xk4dLVEugVQvwq/vf1N/z38xLLyya/KLukQC9A/seF7Mzcc8UCvZePN5EDhrNweiITovy4+P+tvoopfkSHhxCr/gm1tHfuW7mkT1FvdBVqO5zUjFQWhqszrjK9V5CzfwUJ6nS3Ekk/korz9fV6sn7/FTxHih/R4YEEqdMbzI/Jr6STPj0E3I4nhbDQEGLbe6szGuai+hV0/iHEOm2fN5HhIUT6Ok+P9nf+jasrz1W97Fiv6bA6VXS/raUvan8ctvcNdNJPlnNWty8cz/nFcTfmGy6oy1DmTp/KwtF9nfTHFaa6J7ke+5ZzljB6quVZ08V2DVyePnDpIq+ZhtL59yV5+lQWJg1lgIsxV5+E+WnsTRmkTm4EhQlr95D55gZWrZ/KzIbsz12/9F5BjrtzUs/zNCFlDzkpPdVbXd2mpFLoqj+cuNRz5vY6ceninoNz38q99s7HZSLB3svo7A8/8o3BiKG07Ir+fGMwSqBXiKbGo/afn372X+bMX8be/TmN/uq0EEKIxujJ8oztrJo9htgBQxm1PI3092YyWF3sWndPPxL+lsjovyUyZclqnplt+ffohH50VJe9qnRj1XvprOph/fVUBadPlVN5VlVMXJLY5BdYnrKaZ0ercxqqgnJjBeUm1y+21oWOIS0rkxXJ05jyciZ5WxKJVJWJnJ5G4ZE9rLe9r7HHPDLfm3fx16MSQvLGTNJfmcbo2atJO5TK3GBrXnAi6dnbWTh9Ggt3ZLK+nyVKGKRfRGZ2Gs9MmcYzb+9h7/K+1g+ALIGGwkMHybP+pE2wO1ZjTNhA3sYx6lQAdP3mkVlTrz1kzo5QF0HXbxF7szaQPH0eqzLqb5O96ImLWDh/muX6nzKTVfsPkj6lgyUzfiYL165miv37Mts+xTOvrOaZiZ3tEmnQOa9LdT1fsg4kb9xD2uJhdOoYTFBMIuszNpDcWl3uCmrgPUnXbx6Zu9IYHRNMUMcI+i/OZO/8uuf2WhKWlEr624lEdgwmKGIQT+9IZ3lXdan6lRgNlJ8xX8IHrQOJvA9y53Uh9JElHLjk/dXjmn2eXj6Xfs7Er0GCvUIIcRX4/vtKfvjhR17d9BYr12zku+9+/RdjCiHEdWe0nmgtFK3oTvADYcSMWsP7b2/nTSCofe1MHfXsNp2/dXaibwQTkmpnaen8+5KsmuEU1N6ynWUG1HASrPsI6jKUueqZaIof0QOG15kl5bCPpEHE1Zmp6H42H4dTiB8ST9yQeD40gmGv5d9x41PIsCtmm/Wn3o+rdLDWub13TR1qZm36BjrfxjYrqs7sTuv2NX0O+PrgpSh4tbGWN+Xw7str2VZoLR/qh842W9TFjETnsxgteTW/K35EOxw30K5+1nqpZ0Ha2mG/XR1O2mQV1N7JPtWsfRjt/wNmN8EkZ/vS+Vv7vmYfrg4UQq+OkL3XiH+X4bXJ6vNq3wZbXs3sUhMfvbWGTe8crd1eTYHP1vUjor+eqNgUiloPYvQwu/zgqST39qTc7t3TQT7eeCne6OqMIyd1ckqBwleJj9YTFxvHK59q6TVhKACTkwbhdXg6Ef316FeW0jHpWRKAVs2NZE2KI2qgnojhOzBHPcHktrb9Gcl6rAuhnS0/+pX2x3KmdgzW1l8h7BYFTUvvOucMBrFwSmcMS2OI6K8nYuhEFqzIASBSn8jkKAUIYfKTEZRviiOqfwyTdisM+HsiOjdtUjN9usFy/Q+MI+7FYnS9n6gpZzoD7XrXztgL04eiOVVR83stx3Me1N7SFrf3CvX1XMPVNex8bNuETZnGYP9ClvWMsbSnf3f0A6ex4GRtGWfb6/wdr4eg9rX1rfe6UV/3DvckV3ryTFI32D6CiP7xxA3RE9UzhrGLLOfWwlUfOBtDdfvb0j5rWafXhetrpm4f2e6t7p8rseGBlKfFEWMbS31HsOwA9dyb6+67JHMz6/6xD0ND+t+aXvPT3ht8fVA0oPGtuz8L1223V9MPph9w+xFGA5+nLtvg8hlo4/yc42Rmfu2+nbTR3T3cUqD2/FRWOeS4fO5ZOfRxvcex7Us9ti3cXY9Qz3PQVV/apZvM6rZdP2TNXiGEuAq0aH4TzZo1wySz9oUQosEuec3e0RsoHNsBU94anpu0kQy7tf7mvpXLAN8clj2UxCZ6sn7/PNoVzCIicRcJKXuYHFSBoaUfOg2AiaLDRvzDA9EAmI+yKXoEy0yW/fRqUUZVaz8sf84YKTisoWO49Y8T4y7mxs5iW8w89s7vic8vVZh/54nml2K2jYpnbqFlHz0ohsBAlLP5vPICPJYcSNGC7sSnAfrV5CUHUjCzO9nhaYwON/Bm/yRWOlm7cO5buXTKCyNuqWPag57FaDQaTjfTEtCy2Fp/hYTlaYy/34TBCK3uUjgyL4ZxO+122HsFOdN1nDYpaM6CT1uF0t15+IT6U/mdQoC2rKYvdP0WkZocgvkLI2j90eTNI2rqLsuMxFV98TplpFLRoqvYwbghhSS8N4/I1p5wtgpz8WZCh3uSfiSEI53imUsi6Ue6oSnWoGlmAq0fyvE1xD22EQMdmPvWGno0y+OYKYiOwZ6UFxfy0YtJzD0M4Mfy99Jod7A7MYtM1nHgTdbAOCYdhwmvHCT25DBi3utH+ovWemm0BDTPZ1n0dDa1TST9tUF4GUupbOaDV+kaoibtcOyTZH8qz2jgjAmvQD/M/0wi6ukcoANzt7xAL+8KDCYFnW857w+PZ+7xRLu2WftqdgSUlmJuqcVHUTBsdTxvln2tYYDWSEm5gs4fCp7VM3K7iYSUPYxvY8SkaKg0eRPgbyI7MY5xB+y3B8LnsXfhH9jUp4z+WcHkRo9ggcnWhiDMVWA2mtD4+aHJm48+cQeG3ivImeLDabMWr7PF5C4ay09JtePK2Rhz5Mfy917nzve6oF8H0IGFb79A0OFXKe/9BCztzkjzPDLn90SnAfPZKkrf6IK+1F2/1i9sdjrr224n+DEj6/dPsxxnJ8BQUg8NonJSHOMO229hn+54furXl/VZ02hnKuX0eR90rct5f2g8c2M2kPd4BzRUYTaXkzsnjnG7rZsMW03ekArmxM5yDBrRjfVZiwgzriH4MS/HerSdSeZmf7IeSKNdA9qUkLKH0SwmInGXJaH3CnKSNbzZeSwrp6SS42fEEKSwO3osr+DH8vdeoJUBAsxrarexsj/Pc9/K5cFmxWhaqO8httLdWOX0en4YTTHOr2Gn14ltfwoL395Dx0I9Mc+W1dSplvtrw74P7Nvh7rrR9ZtH6vRuYCylUtHS6tMUIib5O5wPp2O/3wpyplv72C7ZItFNH7gYQ8cd+7uyhR+6szlknwmmU/MKzL5+cMB6Xbi7jl32seXeymcKrZpXcGSnnnEb1PWGyRsPMrjFdp7/2xK2nbLPSST9SF+8jMBZI2afQHxObWbSIylkW+/b9vvODa49H+77fwVp04Mw5BWh3B+Bz9liDHlFGDrG1Iyroje68H5bu/Pr9NmSQm7vFeRMgXUPJbEJy3NucjiUlJlopfXGs6WR9xtwrTs73wkpexgfUIGpOU7a4OIZWMP1OY+cksryfhoKPjYREN4BjbGYkg9TiN8T4bqNtmdznful/fXhSStfH5TzeZb/37kv0flzz66WDteQu+MEjyFt1XB05cWcRouuagcjH0mhv+qac3U9unsOuuzL4ETS1w6l1aliTmu0tGqpgbx5de5f1wpZs1cIIa5xZ8/9JIFeIYT4ta17gTcLTSihY1i4fw97N05lsItZTHW09OTY090JjtxMCQpBujKW9QwjZt1R0ATSqV9tUY1iYttjYQTPzMGElgBNGjMiw5i7uwK0HejSFshMY92zY4l5oAuhw3dQrgnkwUdr18NUAn04tiCG4M5jWZm2jyKTQlCUZQZeQkQgmjOFZO8EH50Wxccf/3tqj98QGsNW9LF64nouJvectf5dZ5IQWsprffTEDdGj32QkTG83+9PGE4pmxBAzMIaXDpoJCvqBpX3iiOtv2dcDjwJEkDw2BMNKy0ywmD6bKQ/XMxmF5CmD0OyeSNTAeOJ6zibXuxsJ4fsY12crJZjIXdCF0OFr1EcFFAxb9EQN1BO1KIeqtqEMAOgxjB6+eawbmMTI4Xr2lGow7bcFegHKyPrEiO6egQBMCNViOKkhIE4BetLxbjOGj8vgP68yLra7pV59Usj9XQTRw4C4EAKM24kfGE9c/xjHQK9NsyoKZsQQM0SPPq0Mn3u7EQboxk6kl7KPST31xA2M4fnDPjw4Qr3uaQTJYyMwp40hamA8MdEpFDh5/7Nu7EQGaPNY1seyr0lpJsLGzrT0AaA5X8icaD1x/ePIOK4loKufag8Q268DStEhNpm2U2QMpFO8/TQuI+8/YmlD1FM7MHcdxgTbLNeWWspXxxHVfywzHAKkDdB1BJ1uK+bIG5ZfI6fPIrJqBwuWlteW2T2LmK3FcCaHl+xn0bro1/pFkBDuQ9Hht4EgfFpWUF4T+C7HXKWg8XHcQjc6iiBTMXtq2hdIr13p7M0+SN6u1Uy2LZ/g1A7m9I+xzOQcqGfPyUAeGOIHK0fw0kETHN9KaGe7QC/A7Qqas38g+u10cvYfJOfdFdZj7GPkUD0jx22E3n60OlNOqW2b4ybMGk+UBrYJgBZa68zAviwfEYrn8Ty22fLO53DEGEiP0UD4CDqRT67ZydQ+JzRfO7mH1HB1PXs6vYbrv0464+NTRXmJs0Bv/deGO86vmwiSx3bDvN1yTcb1jEM/x8l178zdPigVRlxPY3PeBy7HkJWtv2P6bMWgDaXVHj0R/fXEbyvGp+PDxNaUdH4du+9jHzQFE4no7zzQC7BsRgq5ng8zN2MPezfOZEKo/TgxcWROnOX+1SeF0tZ9Ga235bnft/P+h1GPRFC5XY8+MYmYpTlUnS9j3cznHMaV42x7V88W+zLW51yEiYynYogboidiRSGXOhdUY85z0gZXz0B7rs55NxIe1lKwVM/IxBHEbytDMe0lfkWx+zbans1OnkO110cc+p3G2iq4eu654/Q4CslThqIrmI++fzxx/eOIG5lCgXpbl9w9B131peWct/rYeszYOLKNno67vY5IsFcIIYQQQlynjrLgse7on95MbmkVPvcNIvndDUxuSGzjXCkFe01gKqL8DJhKDvLmKTCcqMSEJ8rtdmVPFbKsENhZxmng9KeWWcTbTpUDCl5BAGVU+gxiVcYe8l7piw+gaWH31cTje5mTZvs69VbePVyBJrgbE4ggLEihPO9tNgHL/jaMcf3jmdTIANzpsh3Wr72awVp/Xbg/Pmjp/nIq6VtSWdVDi0YbVHcN1XMGjtm+xnweOFtunSW9i69OeeLpBSidCfAFXexq0rekkv5yN3SeWtr16EZAG/C6b5olfctTtGuhRfdnx0M4Z+Sr7dapg+eBFgreAKYqzJ5e+CgAfnhqHLcCyMgpxdQmmAR60lFnJOs9I7rggdA2FJ1nMQU7AUwoXRJZvzGNvfsT6ahYz+vLW8lVBpGZlcaqsRFOv5pq3yeG82ZQvGkHRLf3Q9M8mMlbLH362D2e+PxRvXZnCDpfIwWrbcsiOP9ScXR7P0yf7GOTtQuyV+dh8NVii0GaDIVkW/7FT+fByzuwdmMAIuh1v5byX/xYOH0QnufMBIUPq/0KblUl5bYZe4V5lJzxxsfW2DOFZNv6vjGUvqyf1Y3KrS9YZhAHTyW5t5nds1PIVZd1xkW/uqeQkDKTMNNWVq621dkTT3cf7AQnsupxLQWr51sDoSnM6BlDaM84oiL78W5FMIPH1vOCoqCHWb42jb1ZaUS2dtb/TrT2oWpRPBEPxbDOEEzCFOuHK6fKyDUB5irw1OCl3g7qb5OVcv8Inln+An+fPohWhSlMemyj3VfezSw4WIZ/l+HE9uuA+V+vU+mwtWvO7iH1c34NN+w68URppUqyqu/acMfpdaN0JsDhmjRhaMzwb64QoE6r4bwPwP0YqulvUxVmzFRal9swFJVjavEHarrGxXXsvo9L+WiRm2VZAE5tZVz/7ugTX+WIphujXkmrXW8bE5V51jaZNvPZSQWljS3P/b6d9j9g+qUKTQtL4FfX0ov6Q3gNfLaEavE5mcdK+2fYJXLaBpfPQNXGTs+5CXOVBq+Wlv85CWhha309bXRxv4wOdLw+DOftnzEunnvuOD1ONwLamDm223ZfaOQ14+456LIvO9c55k+X4XxeqyTYK4QQQgghrmtFmSmMHBhDzBtl0MKPdhfxkplLFZa8gYUTgjHtnM+46M2UqAucr7ILyMC2l/diaBFM2JSHaedbwbGd1q+ym8rILm3UX1Tuleez7sUUy8+iWcx4eo31j9iLUcER275eXMxzibNYdxjATMlOW3oKS6eOZdkW9baNcHgx7xcHkvBuGum7XuCB8s2sXKcqszOfEk9/wh7tQsDZIt58oxCDfygT+gTiczyPlYBu9AZSk4P5asMYoh5aTIGtW007GNmzOyNX5uDVexGpWxIbOLvUwlSaWdunz05nxvzX1UUugYabGvJhBUDXvrRTyiixrnFa9XkZpsAQEpxu74Om/uhKPTowd/002n26mHFLLX/AD340Ap9m/vR67SB5h2YR1lIhLPlyvsQLIqesZvw9Rbw0MsU6dgspL1fwqpn97oPG00RlkfVXpS/rlw+CbdMYaRfQLqpZu7aCTQVGNLdoa/Lq6DqP1JeG4nVgGvHRMex2PgHV0XngZCEr80yAiU27CzH5BjnORt1t5HQLH3S2c9RWQXPWREV9bbJjOjiP0M5diOgfT/ycrXWv53UfUKSNYcK9VRSkNaTiV4b762QXx05U0cqvp11afRpxbVxOB8so99ES1NhjX8wYqpfjdey+jxum6OBmJg2JY9N/FNr1cHY+/PBqoU5rvGUr90HvF9j7dhppI73JfXmxarkTZy7zs+WSuXoGWrk85/kseKcY/7+lkfl2JgsjKnhz6UZr3uVto8vn3lWnnr4UEuwVQgghhBDXp8gpqeTtT2XhgBBiw3uSEOQNmKgsh4rKKmiuo11UCAOm6GnXUr315dXO1xvOGjmSuo/KmMDaWVmuHF9LdiEE9e6Mz6lC9li/vp0wP42crBVMaGxgwQnD4WLKtcE8eDafjMP5ZBwupqCozCHo3GCmQ5QYtbTrarbuK5+CT4vJNe3j2AkNAfdpKbBLzz4FUEmV80mt9XiCSP88lkXrievZnYjhzgLUmzly3IeAvwbC5/swmLZTckZL5H1aDIVvAxB9jx9VBduZe7ACfLV42YIkwREM8DWRuz2F+JfzqNL6NWB2qUVWYRmedwfT6lNrn35aTMFn6hdgFVF+RkvHsR0sv9of205WYRmeHR+uOdeRSRHojMWWGaANMKB3MD7HMxm3aAkzFi1hxpw1HKmwW8rB0wsf60zRoLFRBJ0v5iP79ZobxbJGZC+2MiPRNusK3nw6ruaFZ6Gd55F7xkTuAuvyBnVeGuSaLmo4cx8NqfMCpsgpqSwfAO9PSqqZxQb7+Og4tOvRFx2gGxZBkKmYg8etgd63ZzoEpAHCRk8lueZr6h2YEKql/EQ+WF+elhyjekFQqB8+J3OY80YZBiXEIdh1+qyLE7T+EEWtI2qWh4i83x/lVBHbHF7Qtp2i0tqxETkkBJ/jR9nmrk2NtpUPj3uj+24fKy9qe1cafj035DpZlpYHXSeyvp/tvHgz+ZmZjPJ1f22Yz1eh3BJgGSvBiXSqu7pJXaZDlBj96DjFNsY6EOuwbIEbhzfwYbE/vdYnEmlN0oVOZfn0uuPVgZsx1Cg133JwvI4b0scAKN2YnDSIWPvmKn1Zv2UFE2perOVHKwXMZ23bK3hZ+0fXYwydtGUUXcLy+gCDHw3BnGZ9eWJkHOPq/WaBu2eLnaJyTK1DmWC97oJ87WcNd2BU0nAGN/BUu+XyGWhXxs05H9Xdj4JnLcslRUSPYEEhDW+jStZJI8q93awf7CnEWmcM4+6512iWunWMG2r99otCbKj1mWbH9fXo5jnosi8P8dUppeY+yKVcN02ABHuFEEIIIcR1qeTjQgy/BBI7ezUL185jcDCUbEth2WFYuT0Hk8aP2OWrmdujnGOXNehRV9befMo1HUjIziVtrA+VZ9Ql1EwsOFiMpqVCeeGOmjU3L3bNXqcOzGLOJhORrxwkJyOdnEPprHjczWxGt3IYN2Mzppg15GWls3f/QdJWDaMjJpaNW8yRPyaSnp3O3qyDpL81zbpUxNt89JmGsOmZ5Ly7iAT1Ll0qopwIJh86SN6hg+TtTyd9ydCaQIvNm4Wl6Np6U5KTb13HF4KCTZSkW/763rQ7H/OfZ1ravjkKT+sf5bHdn2Dyjj3sfTuNvVOCOb3zVYeX17hjWDeRl/7lz/isPezNyCRvVyrJMQBGTKZABrw2BtjFnBU5aPQbLPV/KwrFyXgwrJvIS3n+PJZlaefyHia2zZhVu/6qWxF0D1YoOmibHQaQz55PTXZLOWjp9ZZl32kjtBQsnc4rdqVdMXxnIuDRDSTbpQ1espgB7T3RtB7Ectt52TjGroQTbxykyDOC8VmZZC5xNmOw1uD4EQyYnsgo+8Qei3hmWCAajT+91lqPechSr1eWvk7JvdNIz8okfYJ/zXINyS/PJMwXPEOnkW6tZ+bz3dCYtUQuz7Ts46MNdNfsY9OzOUAI/R8fyuDJtjFrlZ5PiXYQ6Rnp5Lw3jQC7VUAzPjhKeduh5GRlkpZkF0UyrWHlpgoeWJtJ5ruZLI8ysW3pRst6naOHkvD4QKCMGRv2Qe815GRlsryHifeXrsHgpk0X45U5Y5k05/WL+2DHpYZfz66vEzs7k5ixzki72Xus52U7g4PAdNb9tfHm3kLK7xluOb8LAzF80ZAPFXJYsHQH9Fht2e6jNfx99EA6qko5G/tQxtyRi8ltMYhV/7bUJ/2lKFp9bXbfv27GUKNUeRP9Xt3ruEF9DKAfxuCEqYweaZdmyuEjg47H3rb1vfWaWGD5AAQUOj1juV7SF0ZQ+cY8ZlziM7TEWI5Ob70GDx0k593VLIxyF4V182wpMlLZMoKE57vBztmsy1GI3WjZ74a/KNTEX3sM4/GEEQx41GHHF8nVM9COm3NeehrCZtvuY3vYu2URk4PdtNENw9LFbDOGMnn/QfIOZTLl7qqaRRJcPfcaz8SypWso0iaSdshynGeS+zFYdZ24vh7dPQdd9aWJuc9t5vT9M8k8dJC8XTMJ+OWiG3DN82gf3LFanahm+PKEOumKezIhju27XC9jLoQQQgghrm/9ej7Ay5sucboQgG8gsf5QcrgYh289+wYS28ZMQd5FzmZtrEYeTzd2A5mjvcl6Kq52jV7Fj0ifisu7lAPeRIb7Y/40v8GzRt0Jah/CneeKyVLVUecfQsfmpWQ4zC5TCAsNRHMiv97ZSjX0q8nRlzJj4BKyUQgLHcaUlIFULujOyMbOTFX8iA6CY3XOiaVPKG1EvewpfkTfo+Er+zHnG0h0C2Ntvzgr40wjx02D1LytfjbHQgOhqDHn3pvIUIWSy1GfhvYBQ0nNiODD2LENCkhbWMZWw9tmKe9Vrhq7bWeSucTMnP5LVOsOexMZ7sNpZ3V3d86cjTlfP8LOWtftBTf7bmybfm2NvJ4bdP7d7NNVPzvr4wZxcyyof+y7qo9Lrs5zY7kZFw3o4wmvpBO2O474NHWO5T6oOWV/TSSSfiSEI53iebN9CK0qXPVVY/ix8N01+PxDb1laxTeQwRMXkRywj+BHUtSF63D2bNH5hxBwtrZuzsoABPn7YSpt6PlqGFfPQAtn53wQqVmDMEzSM6PQUteE2S/Qq3IeEYm7wE393XFZj4u+PpyrexzVdeLuePWMz7r75rL/P8tvqXVrt/P/3ZJgrxBCCCGEuCZdtmDvNWc4q96OoZO/H54fpxA3anPdP5CuY2HJaay6P485w5eQYYIg/Qo2TPRh92PxzL3E2WXXjZpgb1KDZy3/dkJY+NZMAg7MQl/zMp9fyyDWZwzC/PKIBnytXIhrT+z0VKYEHWKO0+VwnKkN9s5VZ120QaRmPwUr9cSnVYASwsL1LxBpmEfEJEuws0lrO5XMzaEUjB/BjDwT+A5i/eZEfN4fRtyKy7KYs7hKXUqwV5ZxEEIIIYQQ4lrS+gfMJhPH3lnCJAn01pG7cjFvloby9117yNmfyeq4KrYljpVAb2OUl1HySRmn1elXpXxWjoz/DQK9AB8w5xG9BHpFk5WxeizxDQ70Ahgp+czJDM1LspXnFnyA5vE08vbvIefdaegK5jHiegj0Ylmjf+W6UoKezyRn/x5yNvbF/MZExkmgV7ghM3uFEEIIIcQ16fqd2SuEEEIIIZoymdkrhBBCCCGEEEIIIYQQ1zkJ9gohhBBCCCGEEEIIIUQTIMFeIYQQQgghhBBCCCGEaAIk2CuEEEIIIYQQQgghhBBNgAR7hRBCCCGEEEIIIYQQogmQYK8QQgghhBBCCCGEEEI0ARLsFUIIIYQQQjSQQlh7P3TqZGHhG0ikrzpRCCGEEOLX49E+uGO1OlHN8OUJddIV92RCnDpJCCGEEEIIBy9vSlcnXYJE0o+EcKRTPHPVWU3ORbRVGUpqRiJBzaowbB9G3KIydQmri9j3xWg7nNQXozA8G8+Mw+rMX9+A2Wk83U8L5Xms65nEK+oC1ww/Jr/yApHH5xO3KF+dee3pPY/MsX9gW+zVfE7cXTN2eY0c8wkpexjNYiISd6mzLoNrcJxMSaWwdznLHkpikzrvcmjk+bmyrsHz48bct3LplBdG3FJImJ9GgrKVqMSt6mKNd03cH8T1qnXri/9o/aoO9m7f9ZE6WQghhBBCCAD69XzgEoO93Vj1XiK8GMe43dQTcGlqLqKtozdQOKTSSaDkN+pHpS/LX+xG+YokFhSqMxtKXfeLFcKqjNXo9lqCEdc2hYRnXiDy1AuMXH1UnXn16zGPzL/Bpj6zeBMgeAzrk7zJ/tt8NpnUha8W7q4Zu7xGjvkrG+y9BsfJlQ72NvL8XFnX4Plxwz7YGzl2BRN895E0ZwcGdcH6TNhA3v35hA5fY/n9mrg/iOvVpQR7m7Xyvb3u80TF9P336qQrLqRTEMVfNPrSFUIIIYQQ14mgtnfy74IidXLD+Yby6NAINF9+htls5vPTwQx+SsvXa7M4GxpMxzYKzb6qoNxuk6D2IYTdpeWW740YquwybHwDie3gz913eHLWeAbL347eRIb6ct54nnZ19tuQvGZ0Cr+HP/5s5MSPgOJH9P1tCfIy8/npc5alFUKD0Zns6uQbSOwfseQ7rVO4ta3vst+6iT1n7QzqOYxBd1XxedFxvrPvl8vcjzr/EDppjJz4g6Xet1vbbdnGbl9V5Zw99Rn//ncF5fX1Y3gbWth+V/yI7tgSs/EMpjp1P1dTh8ggbc2xLSz93LGNk3orETyaEIHX//7DqS9NlJzXEt22GSU/+RJ9f1t8rOWd7lfxs5Q93YLI8Hu413ZerefNsQ4utrE/t7a8eo6t8w/hgVtMlJyxNcTWT0YM3xj5/N9HHOvoMObsyzvpV/ttHMad3f4a02anx8daB8f2Bz0wCH3nFhiKTvG7n42cOF3O2dJCckvPYHI7Tuz2d6eWu+/Ucvedtnw3596Bu/276y/b9ejsmrG7Vh3GvJWbfu4Uk8D/kcOr//kdsR38+eON9ufbTb86u2fUGVdllJysHSdB7UMI+MkIOidj3O6asvSrtqYutnuBurxT7url7FoAu+tWoVnLcAZ1MPOvf+yioCZffc7t61J3fGG9fzltq+r8uCznUK/a49aOrYu5p6ufF2X8t0R1HTvbrt7+q71f17lvORs7NWrbZ7+duz5xl/fQoFHcbnyFNw/B+fIKThzJp8Dh3nUP99ZcrxY1Y87+WfnwIAb4m/jyy7OW/j5jf3+orYf6+aR+LqmvJad9JMQluvlmL3VSg8nMXiGEEEIIcU261Jm9q947SGRrTzhbhbl4M6HDPUk/8jCaYtA0M4HWD+X4GuIe24iBDszd8gK9vCswmBR0vuW8Pzyeucdr9xc9JZVn+imcPmlC0zoQr7xZREzaZZ2Z1xcvI3DWiNknEJ9Tm5n0SArZ9eZ1g88UWjWv4MhOPQvKF5E6OwJKSy1ljZuZNORVIl/LJNowkainLV/XTUjZw3jNWp4+3tdNnZzNJOzA3C1rGKA1UlKuoPOHgmf1jDw7jcz5PdFpwHz2KO92HsECsM6MnXdZ+zEhZQ/j2xgxKRoqz2sJaFlM9mFvOt1dRWULP3xO2PZl3wZLX2mKNU6OqWpr7xXkTIF1D+0grE7dXydheRrj7zdhMEKruxSOzIth3E4/5m55nV7eRgwV0Mq7lHU9p9fMDkzeeJDB93mCuQrD3lnE5PQlZ4oPp81avM4Wk7toOsf0aUwOh5IyE60CtZx+YwxxS49a6jNdx2mTguYs+LRVKN2dh0+oP5XfKQRoy9gUPYJl9tGX3ivISQ7CXAVmowmNnx+avPnoE3dg6L3C6bHVbVrQZgOZvY3MiJ1Fhm2f0zX8I3IsOrtZdLp+zsacbXw669ckNrVNJP21QXgZS6ls5oNX6RqiJu1wrH8D26zrt4jU5BDMXxhB648mbx5RU3dBcCLpq/ridcpIpaJFV7GDcRt9eKZmnFZR+kYX9KV29XI3TpS+rH93GgEn8yhRggnTVlFSls/7Qzagc3PuHdV3PbvorwZv57gPXb95pE7vBsZSKhUtrT5NIcKunxNS9jA+oAJTc6g0eRPgbyI7MY5xB1z3q8v7WJ1xNZafkmrHydy3cnmwWTGaFhpON7Nct7ZzGDklleX9NBR8bCIgvAMaYzElH6aQ1Xoek+83UWIEL62ZD+PjmXvSvj9rua2XbSydMeEV6If5n0lEPZ2jup950srXB+V8nmpm7xjSDg3FHwBPNC1M5M7szsgTiaSvHUqrU8WcbuFPADksGzidTSZ3bXU8P67LdWDuW2vo0SyPY6YgOgZ7Ul5cyEcvJjH3MIBC8kXd0x2fF6f/Unt+Lrb/kl97gcFtKigxQis/M7sfieeV+52PnVp9WZ81jXamUk6f90HXupz3h1ru83PfyqVHSyOcr+K02ZsArZFto+KZW1h/Xs0yDvYz1oPHkLZqOLryYk6jRVe1g5GPpBC2PJPRwSYM34DXXVoq3xxG3C+zyHu8AxqqMJvLyZ0TxziN/XXo4vm33eT4XFJdSwnLMxs8joVojEuZ2SsvaBNCCCGEENelcX22UoKJ3AVdar/SiSeGLXqiBuqJWpRDVdtQBgC6sRPppexjUk89cQNjeP6wDw+OCHHYX9bLY9FHxhE3JJ6YpTkQ3o8JNbkmjsyJI2pgPDF9Uiht3ZfR+obk+aApmEhEfz3jNkSQPDYCc9oYa9n5FGgHMWEsLDhYjE/HfsQC0JPIjlCwc2s9dapLN3YiA7R5LOtjaeekNBNhY2cyYPcsYrYWw5kcXqoJ9ALsu+z9CKA5X8icaD1xPReTe64D7Uzz0cfqiVmaQ1VgFwarNwBAcXpM15zUvetMEkJLea2PnrghevSbjITphwP96NTeyJ6BlvQIVbBvwfB55J6Bkq1diHl6nyWxpZby1XFE9R/LDM1MEiJMZDwVY9nvs3m0GjSRZMW6A08omhFDzMAYXjpoJijoB5b2iSOu/2JyzwXywKN2B6th5P1HYogZoifqqR2Yuw5jQltrlvrYTtpkWH2QIu8O9Aq3bJLQIxg+3q5at9LVmLNV3IW4EAKM24kfGE9c/xjHQK9Ng9ocQfLYEAwr44gZEk9Mn82Uh+uZjELylEFodk8kamA8cT1nk+vdjQST/Tjtgn6l+qC4HicjBxF2Zjvxw5MYOTCF3Cozhhdn8Uo9574ud9ezO43dLoLksd0wb7ecm7iecejn1O1njTnPci31jyPjuJaArn5u+rWe+5j9uHKyJq3m663oY23XbSCd+gF0I+FhLQVL9YxMHEH8tjIU017iV3gTeb8nuSss/Rr1kPsAmdt62cbSED36tDJ87u1GWJ37WRz6nUbHnQKwBn3nLoR2juHN41WYDqYwZ6dlfLX6eD76/vHE9RxDhimChNkRNVs5b2tdTsv1GEYP3zzWDUxi5HA9e0o1mPbbAr0Apou8p9s/L2oS4RL6b3CbQkv/DdGj7zmWuSddj51aO5jTP4aI/vHEDdSz52QgDwzxq8k151nu53H949j0mT+9xg5qUF5dCslThqIrsJ6n/nHEjUyhAHhljp64aEu947cZCYgaRtjKEbx00ATHtxLaue7SPS6ff9b8mueSw7XUs1HjWIhfiwR7hRBCCCGEqGHkq+3WKZTngRYK3kB0ez80zYOZvCWV9C2pPHaPJz5/rP3DHwCTJ5FJK0h7N5OcpFAU67bWTCrzrPs1beazkwpKm4bklfLRIut6i0pnAnyNFNjWXzTtIPsTM7rAzrBuLwUtrYG73g/TzpxPxs766lRXdHs/TJ/sq1m7MHt1HgZfLcHqgvW6hH4ETIZCsgEwA1B5Jt+yNuPOMk5rPHEeanR+zMbQhfvjg5buL1vqt6qHFo02iMG8yrt7FWL3Z5K+ZAwDfNVbOnGmkGxrfXTh/viczGOldR1Pw/Z9HDvnR0BXa9lzBo7Z1vg8D5wtJ8MEsIuvTnni6eybnFWVlJ+y/rswj5Iz3vgEWX9XH9tpmzaSXehDu34h1mBSFUe2qdZ2dTfm3Hl5K7nKIDKz0lg1NgJbtRw0pM1KZwJ8QRe7mvQtqaS/3A2dp5Z2PboR0Aa87ptmSd/yFO1aaNH92fEQzrkYJz9WgUZDAICi4OVpK9/Yc+/uenankdupzw0mDPazv22pNdeSiZ/Og5d3oJt+reeeYTeunDldZltD1Qx4otwOYMJcpcGrpeWqDWhh69hdbNpppOPsPezdOJUEf+dXdQ139bIbS4bzZlC8aQdEBzrezwznLfcTZ3SjX2CwNo91T+/AQDcC2pg5ttvWnqOszDPi06b2gynnba3LaTlTFWZPL3wUAD88NeqtuMh7ut3zQs3ddq76T/U8MJhM7seOvaCHWb42jb1ZaUS2to47q5r7OSaWfVqK5hZtg/LqUp8n+2sgkAHPrCY9I5O0GG1Nm9xRt1f9/HN6LTV2HAvxK5FgrxBCCCGEEA1gKs1k3Ysplp9npzNj/ut2uQqTX9vO5PvKePOpGCKW5jmuUerAD68W6jQbd3kuNNMAm/mwUKFdvxASegRjLtzBtkbVyR0NN13Gv1/d9+NVoDy/tn6LZjHj6TVkY2LTpBhiBq7hiNKTp3ekMtc2i/ZSNFMnXCwfNDXBSSectglW7i5E6diP2N4P085cyJ4D6g1daOYsOmXHtIORPbszcmUOXr0XkbolkTB1mQar4Iit7i8u5rnEWaw7DGCmZKctPYWlU8eybIt620ZYt4asZn1ZnpFG5rtPoBx4lQWHgUs69xdxPcMlbNcYzvr1ct0z7OWz4J1i/P+WRubbmSyMqODNpRsByF4aT2jsbN49Eczot9NY31u9rc2VqJedtomselxLwcrZ7l/S1cyTi/9StZ3Di3m/OJCEd9NI3/UCD5RvZuU6daHLeU+/2O1ccTZ27LK7ziP1paF4HZhGfHQMu8vs8lTCWrh+uLjLcy+CVW+9QH9lHwseiSFiZ6m6QCPU//xr+DgW4tcjwV4hhBBCCHGdqqTK9UQvB1mFZXjeHUyrT/PJOJxPxqfFFHxWYVeiM+3amCl4J4VtpyDI1wvH2JuCV6jlL0ZdjzF00pZRVLPcsLs8O6ZDlBj96JjUwfK7MpTojhoMhZZlA17ZW4xy71Aig0wceyenAXWqK6uwDM+ODzPB+sdtZFIEOmMxuW4jA5ezH68UH1p1BVBI6BFkNzPYse6Gw8WUa4N58Ky1foeLKSgqw0AHBnTxxlC6g7lPvUrBOS13Op2u6pzhcDHl/hFMtk4R0w17mI7Nyzi2V12yETy98LHOMg0aG0XQ+WI+2qku5K5NQNo+ipQODHg4CPPHW9mm3rieMeeyX4MjGOBrInd7CvEv51Gl9at3Vp1TpkOUGLW062q21j2fgk+LyTXt49gJDQH3aSmwS88+BVS6fHuaez0G8cDZrcTF6omJ7k7M07bZgs7PvS5qOHMfDXES/HN3PbvoLyxpXrbxUWc7J2znZoqtDh2ItR63Xi77tfH3jIYY1d2PgmctS3ZERI9gQSGAQmyXEHSnclg5ZyzZpdaZ6Uo3JicNItahKRdXr6yTRpR7u5GgYDmedXaxIz/mPjuIVnlrmFMza9kyvjr2HlrTt5O7+FH++SHrmLhUTxDpn8eyaD1xPbsTMdzy4Yva5binW1zcdpbnQT8mW+8zutAQIl2OHbsNQ/3wOZnDnDfKMCghdT648GppHbNKNxLCtRgKtjcory7reYobav32gEJsaAcgBJ3WSO6creSaHM/76bOuH2YX9/xzP44j1cWF+JVIsFcIIYQQQlyn3uajzzSETc8k591FJKiz7RjWTeSlf/kzPmsPezMyyduVSnKMfYldZOdVETY7k70Ze9jQ3VM1c0qh0zOZ5B06SPrCCCrfmMeMmpeSucuzl8O4GZsx91hD3qGD5GWNQZe3mKR11iOlbSbXM4Kwqnw2HaaeOhkxmQIZ8NqYmhRs7czz57Gsg+QdOsjyHia2zZhVNwjo4HL245WwnSOfKUQuP0jeoXT6VxXVvK29Tt0PzGLOJhORrxwkJyOdnEPprHhcC6FRDJ6/nbyMNNJ3JdLRuINNTgKrLh2YxZw3LPvNO3SQ9An+FCyY6PjStUbT0usty/7SRmgpWDpdtd6ulas2AbCVdw9rCOti5sgGy4ugHLkbc677Nbb7E0zesYe9b6exd0owp3e+Ws86t65Yjm+KWUNeVjp79x8kbdUwOmJi2bjFHPljIunZ6ezNOkj6W9Msazm/cZAizwjGZ2WSuaSneoeulRo53XoQ6YcsfZqXlUbq9G7oXJz7wfEjGDA9kVHq/bi8nl33F1i+5d9uoXV8OGzn/FqFHBYs3QE9Vlvq/NEa/j56IB1VpZxz1a/u7hkXr/Q0hM229uuhPezdsojJPR6m1/RFpGelkf5uOt2Vfbz7MqAfxuCEqYweab+Hi6uXYelithlDmbz/IHmHMplyd5V1YRg7I2bRq70nnqHTas592gTL+CpoM6ambyNNm5nzdI5664tURDkRTLaNtf3ppC8ZWjcw2Kh7ujsXt51h3Qu8+amWhAzruFw+jf4xrsaOnfR8SrSDSM9IJ+e9aQTg+AGMJnSmpV+zFhF2ZjMLFtRO/XWVZ/jORMCjG0i22w+YWLZ0DUXaRNIOWc7xM8n9GMx2jnymJTYjnb1Z6UwIsC0IBBkfHKW87VBysjJJS3IM/l/U86+1i3E88gkSEoYy2LomuhC/No/2wR2r1Ylqhi9PqJOuuCcT4ti+6yN1shBCCCGEEAD06/kAL29yN/WtIRTCQgPRnMi3zAqsj+JH9D0avjpcTJE6D9D5h9COYrJK7f+Urn1D+5vtQ2hVYX8sd3muNK7OzusE+AYS3cJYN92aF9vGTEGedQZovRpXp/r68UoIctm/zuruTWS4P+ZP8x1mdQW1DyGAUjIudjby5Wp3b9sb5GdzLDQQihzr6ZzzNjWMsz6ycN2vluNR6iyv8YLah3DnubrjWOcfQsfmqnNyEf0clpzGCu3r6MfvwIA3kTETeeZ5Pz7sFM9cp+d+KKkZEXwYO9YuyF7/9ey6vyyc5ru7Vt2cm4Zw1q8u7xkXZRCpWYMwTNIzo9Cy74TZL9Crch4Ribss58+n3OFeM+GVdMJ2xxGf5rini62XszY21KVs65J+NTn6UmYMXEI2CmGhw5iSMpDKBd0Z2YAPkS62Hy52O1fPA/d9401kuA+nVdfg3Ldy6ZQXRlxqILHe5Q7Xrbs88CYyVKHExTPJWV2cXku4bk+N+vKdqDuOFcL8PcktvchnhRBA69Z1vzvSUBLsFUIIIYQQ16TLE+z9NdQGgOaqs9zmCeFCTbA36SJnzAq1UWv38DhrGfHUVopQiJ2+mmd6GHkperqTPg5h4VszCTgwC33NC9KQ69mZtlPJ3BxKwfgRzMgzge8g1m9OxOf9YcStqLuYa+z0VKYEHWKOi6UNmoKw5DRW3Z/HnOFLyDBBkH4FGyb6sPuxeOY6/VZH01ET0F2qznGfJ8T16FKCvbKMgxBCNCEeHh70iY1mwTPTWb5oNvFD/orG7RtbhBBCXHlGSj5zNUPIXZ4QLpSXUfJJGafV6eKivTJnMdmaoWzI3kPO/jQm+BeybKizQC9APitHxqsCvcj17MzxtaxcV0rQ85nk7N9Dzsa+mN+YyDgngV6AjNVjiW/CgV6A3JWLebM0lL/v2kPO/kxWx1WxLXFskw/0Ahg+L6bExQXiLk8I0Tgys1cIIZqQPz/wfyQMG8ibW3fww49neWRgHw7nfsw772aoiwohxDXv2pnZK4QQQgghRMPJzF4hhLjGDGoL/+wDv7vMd+HWd96BwfA1+z/8F/n/LuCTT4v54yU8JIQQQgghhBBCCHHtuMxhBiGEEPUJ9oZp98Edv4cXu1zegK+npyc/njtX8/uPP57lpptucigjhBBCCCGEEEKIpukyhhiEEELU595b4ZVu8H4ZlP8ErZrD6gcvb8BXCCGEEEIIIYQQ1ycJLwghxK/E3wtejYKUQsj+H1yohuF7oaUGFv9ZXVoIIYQQQgghhBCicSTYK4QQv5IzZpidC1v+W5v2w88wYh8cMNqXbJzWd97BvNlTuOEG97f0cU8mEBbSSZ0shBBCCCGEEEKIJsJ9ZEAIIcRl850ZPjihTgVTFWwvVac23C0tW9KqlTeeN96oznJw220+3HabjzpZCCGEEEIIIYQQTYQEe4UQ4hr3faUJgBa/b87Zc+e4/TYfPDw8ANBqffnx7FkA/vD7Fnz/faXDtkIIIYQQQgghhGg6JNgrhBBXWJc74Lbm6lTnuungFo061T2D8WsuXLjA7be14pNPivDyUnh66jjGPvkY7YPu5ugnRSh/+D2//30LvjxhUG8uhBBCCCGEEEKIJkKCvUIIcYWNvRd6tHZMq/gJ/nPaMQ1gcidLcLgxfvnlFz7K+w+9Yrrz3+OlrHjxFQzGU1w4f4GNr6Wxd38OMT27YTB+zcmvLmFxYCGEEEIIIYQQQlzVJNgrhBC/gSPlMOWQOvXivZOeyR13+JI0YSS//HKe1za/zdr1r3PyKyPDH9PTrWtnUre8Q3V1tXpTIYQQQgghhBBCNBES7BVCiCvs7C+gaaZOde73N8KPv6hT6/fDDz+yLOVlWra8mamTniJl2TOkLH2G2ckTufeeQFa//BplX36l3kwIIYQQQgghhBBNiEf74I71TvMyfOnk9fFX2JMJcWzf9ZE6WQghrjl9/CD5/6DoO3WOoz/cCDdroM/7YD6vzm2YG2/8HUF/astfoh/k1ltbsi09k/9+XoLphx/VRYUQ4prXr+cDvLwpXZ0shBBCCCHENa11a506qcFkZq8QQlxh75VBz/dg9Sfuf+blQ88dFx/oBbj1lpZ8c7qcb789w9mz5/jK8D9atGjg2+GEEEIIIYQQQghxTZOZvUII0US0C7qbxHFPqJMBSH9vFx/8c786WQghrmkys1cIIYQQQjRFlzKzV4K9QgghhBDimiTBXiGEEEII0RRdSrBXlnEQQgghhBBCCCGEEEKIJkCCvUIIIYQQQgghhBBCCNEESLBXCCGEEEIIIYQQQgghmgAJ9gohhBBCCCGEEEIIIUQTIMFeIYQQQgghhBBCCCGEaAIk2CuEEEIIIYQQQgghhBBNwFUb7P32u0paed+sThZCCCGEEIJW3jfz7XeV6mQhhBBCCCGua1dtsPfIJ/+lY3s/CfgKIYQQQggHrbxvpmN7P4588l91VuMofkSHhxDpW5NAWGgIseGBBDmWbLSg9iHEhlt/Qv3Q2WeGzyTz37lkPhNin/qrmrx2D4WHVjNZnXGRHNrr0KfuhLD83VwK35tJrDpLCCGEEEJcFI/2wR2r1Ylqhi9PqJN+FXcH3Emne//Erbd4qbOEEEIIIcR16tvvKjnyyX/5vOQrdVbj9F5BzvwITr8eRtxSCEtKZVWCP4bXxxC39Ki6dKPMfSuXAYF2CWeN5L48i5GbjkLb4aSu1UPaGOLXldkV+vUkzE9j9D3FrOs/i03qzItQp71UYchczMind2CwT3bgx+RX1jCANJJGbSRXne2gG6vem0fAJ7OIeXqfOlMIIYQQoklp3dphqkCjXNXBXiGEEEIIIa4Y+2DvPxNJf2UorfLmox9fG6AM6jKUwaFQmL6DbaUm8A0k1l+h/NN8ck1YZwMH4lVeTFapqWbXc9/KZYBvDsseSiLLvy+Tl08j2r+crMfimFToTWS4P5Tmk33Kuo+ovsSGemJ4bwevfFZh3Uttemna22xycnydfwgdfcopyCvDAOj8uzEgLgTlyx1s21ZMEYBvIKP69UV3Np+M7fucbldzrI52bcU6+/keDV8dNuLVbyADfCvY9sYOa9tr2bd3E96MWpvGhHATWQPjmHS8tg6tyu3qZZ0RHEApGZ9VWOrUvJSMkwoJjw5Cd2orm7aXYcCbyPAnmJwyiFaFa3huwyFKDhdT5KRdQgghhBBNgQR7hRBCCCGEaCxbsPeN+ZT3mEkYOSzrn8QmE5ZZpxs3kHCfgvlsFZoWULJpGHHvDSLz7UGQpidmQRm0nUrm24Mwv96duKXOg72bANrOJPPtvmgyxxL1dATpR4aCdUZxwvJMJkd51xynaJ0e/Wpv5m5Zw4D2nmAGNBVkT4ph3P2pFA7TkjuzOyN3QkLKHiZ3LLQcp/cicuZ3QzlbhVnjCZ9uZOSznjyzeSgBVGHGE82ZXcztOQvFfjsiWPj2ImLbemI2g0ZTRYltdnPvFeTMD6LyuIKurScA5s82M25IisNM3DrtnVJbz3UtV7AwKQKfX6pA4wmn9rFs4HQ2mazbsZngR1IsbQmqwKD4oWsBUGXp8xX9SD8ylICaoxWzbWA+nZy1a34ao8MNvNk/iZUS/BVCCCHENepSgr1X7Zq9QgghhBBC/Bp0A6YR5gOcq6DEFiDUT2PwfZA7pzuhnbuw8rCZgB4jiD2+lYJS0IUOIgzQxQWjo5gjL9cTWTxeSvlZ0ChaVUZPIu/3xnx4CaGduxDTfxizVxsJS57FgPZmilbEEPxAGPqewxi3V7WpSkKPEBRTPis7dyH0IT3jpq6hIC6EAE0ZGX27EPpADCOfWsw21XZhyYnEtjVb2vpADG8WQsCgiSQrthIKfDqLmE7d2fSfKjTtQ1yssavBJzyE2AGJpD0cCOYyju0cxIQnI/Ap3cq4B7oQM3Uf5b7dSJgdod7YoqUnx2Z2JzgyhaKzngSE9wNSiOu0mRLAdGAWwZ3imeuiXT46LYqPP/73qHcshBBCCHF9kGCvEEIIIYS4rmk0JnJ3H8Xc+mEmJ/kBMDjcHw0Q8Mhq0rek0qsN4K2lHWWs3H0U/CMY3FYhIdgfCg/xSj2xXvBBaQGcN6vSd5GdV4EmfCqFWWkkR/lgAmLv9YMzhby/ybKkQ9Ep29IOrm3anU95ixAm/DuT9Nnd8DkLpOdTctaP2Iw97N34BAFn61bUdqzs7SagggWFpaDxI6CrrYQZQ94+DJgor1TX307LEBLWrmbh7KHoqo6S8fRElvUIIUABQ95asgHD7r2UnAGfNi5eTneulIK9JjCVU1mlzrTjol3L/jaMcf3jmXRYvYEQQgghxPVBgr1CCCGEEOK6Vr59GiOnvsCe4xAwYBoTFKg0m4EKjryYwroXU1j37HRmjJ/Hm4Bh9QcUmLS0G/IEnQKh6PDrbl5CZqEbFogOqDxVrM5i09QY9ONTyPrGm8gJq1mfbAk446mhUa8p3jmdqNgkXtlZTquoMSx8bSphx1OI66ln2evFmAMHkfz2akapNjP9UtX4YzlzJodlncII7hRGROwIZuw1gakKM4DGW1360rhql6mMbLu1k4UQQgghrjcS7BVCCCGEENe1StNR4CgzVu6jXAlh8DM9yfigmHL8eGCYPyWH8yk4pcWrhe1lZlt593AFuqi++FPMkVRXwUXLsgajklaTOiEEjSmfjJVlqjJ+TJ49lU6fb2bSk69SdBa8fAPJ+LgYWgQTO78nQXgzOGkMCQBfmzCjQXd/N6KjEul1f81aC4SNnsnC+4tYOWcs2wqr4BYt7XonslzvQ9aKsSzYbQRFQb0C3JsHizG3CKb79BB0vj1ZFRUIpmI+2qkqeDEOf8AxI+geTGSUr0LsBD0dW4KhYLu6ZIN4evmgUxR0LtqVMD+NnKwVTKjtFiGEEEKI64oEe4UQQgghhAA4sJh380woUWNY/t181m0rxvPPU0k7kkvmuzOZ0K9vTdFt7+RT3lJBU5xvfaGbE9ZlDSYMC0Zzch+bxo2t+9Kw1t1oF9GP5F25FGYnEkQxuzfsInfpYjYdNqHrPY+0I5kkD+tJWA/g9e0UlHuiG7CI5UseprLAFjzWEnlPBN2fz6TwyB4S7oOSna9ivieEB8auJvNILqv6aSnfu5VXVFUwrHuBNw+bCHh0NZm75hHZ0kj20ul1yl2cHBYs3UGJJoIJu/awcEQHqv6zkQUL1EHv+rxK9n+q0NyXSGZ2Ki91d94uWbNXCCGEENc7j/bBHavViWqGL0+ok4QQQgghhLgOeBMZ7o/503xy7QO1XRexNyWC8pVd0G+wS79IOv8QOvqUU5Bnmz1s5RtIbBuzKt1FnQAUP6Lv8aHSIc9SntJ8sk85Fnfg9FiXi0JYaCCaE/XUoR5B7UO481wxWaUm5+1S/Ij0qZClHIQQQghxTWvdWv1drIaTYK8QQgghhBAN1o2Fr4wh7B4/fCp2MK7PfLLVRYQQQgghhLgElxLslWUchBBCCCGEaDATP5nNGHLWsGCIBHqFEEIIIcTVRWb2CiGEEEIIIYQQQgghxFVCZvYKIYQQQgghhBBCCCHEdU6CvUIIIYQQQgghhBBCCNEESLBXCCGEEEIIIYQQQgghmgAJ9gohhBBCCCGEEEIIIUQTcNW/oM3L62Zat/bH29uHG2/0VGcLIcQ16+efq6ioKOfkyVIqK79XZ1+0DjdX87jfBbr4XOBWuW0KIZqQb6vgYPkN/KPsBo5+76HOFkIIIYQQokm4lBe0XdXBXq22Ne3adVAnCyFEk3Ps2FGMxpPq5EYbeOcFnu9wXp0shBBNztNHm/H2V/IlNSGEEEII0fRcSrD3qv0/ZC+vmyXQK4S4brRr1wEvr5vVyY3S4eZqCfQKIa4bz3c4T4eb652zIIQQQgghxHXlqg32tm7tr04SQogm7VLve4/7XVAnCSFEkyb3PSGEEEIIIRxdtcFeb28fdZIQQjRpl3rf6+IjQQ8hxPVF7ntCCCGEEEI4umqDvfIyNiHE9eZS73vyMjYhxPVG7ntCCCGEEEI4umqDvUIIIYQQQgghhBBCCCEaToK9QgghhBBCiFq+gUT6qhOFsND5BxKmqFOFEEIIcbWQYK8QQgghhBCN4RtIbKgfOnX6VSYhZQ85KT3VyW4NmJ1GXsYGlr+2glHqTDsXs+/f3JRUCvevIEGdfiX1XkHOkVTmqtMbqvc8MjPcn4tfj8KEtXvIfHMDq9ZPJUydfS1pO5zUjFQWhqszrlFX1TgRQgjxW5NgrxBCCCGEuE75MeH5VPZm51L4VqI606XJS1az8KUXSG4qgaIaIXQP98PwRhdCeybxin3WhA3kbRxjnyJ+DSeMGE5VUHVJM2m7seq9dFb1UKc31kAi74PceV0IfWQJuersa8mpCk6fKqfyrDrjGtFjHpnvzWOw7ffLMk6EEEI0FRLsFUIIIYQQ16kKPtqWwtK9ZeoMN4bzgH8Z2YcV2vULqUnV+YdYlj7wDSQ2PIRo/9qoS0PydP4hDrOFg9qHEBseUvt1ed9AYtt712wHCmGhtnzLv2PDrcdpCMWP6PAQx30q/ni1AI3iWEdQCLtFQdPS27FOOG8TuNi/Azd1tu6z5sduH3X6BW8iQ/3Q1ewvkKCa0tgdJ5CgyiqHHPAm0noM18sSOCtT3zHt6mn6AbMqDydjwtYHlu3s9leayZurXyfLhPvjKn5E2882tx8vvj54KQpeberrS7vz5mzmuq8PigY0vrVjtkFjtzHttaPepvHXjf01YmXrF1MO7768lm2FtVk6f2fj0ZvI+vrZxThX17++Njs/vvNrKcjHGy/FG52tvP04UfyIbu9dO3brnMvaMW35sdbF3bkXQghxTfFoH9yxWp2oZvjyhDrpiuvePVadJMTl0+ouOvkrcOYER/77rTpXiN/Mnj0Z6qQG+zzmZ3WSEJeXTzjVfj5w5ggex79S516ce3pTXbkTj5PqDCEa5u7MG9VJjTcllcLQfIIfSVHn1DV6A3ndC4lL9SNtQhXLek5nm3VZg/H3VFGFidNnFHT+UPCsnpHbTfXmjb7diFmrYC7KYeWoDwjbsoYBWiMl5XZlz8xk72If3n9gBMsAlETS90dQ9Mjr+KydRjtTKafP+6BrXc77Q+OZe9xSp9EsJiJxl0MTdP0WkTo7AkpLMfsE4mPczKQhKURuPMjg+zzBXIVh7yxint5n2WDCBvIe74CGKszmcnLnxJHbew/jAyowNYdKkzcB/iayE+MYd8C6/+QQzF8YQeuPJm8eUVPt69CX9VnO66zrt4K06UEY8opQ7o/A52wxhrwdxM0sYu6WF+jlXYHBpKDzLef94fHMPZ5I+pFuaIo1aJqZQOuHcnwNcY9txEAH5tb0pSetfH1Qzuex7KEkNt2XSPqLffE6ZaRSoyWgeT7Loqezya6WuCzj7pgKCcvTmBwOJWUmWmm98Wxp5P1O8Q5LOSSk7GF8GyMmRUPleS0BLYvJPuxNp7urqGzhh88J6/56ryBnCqx7KMn9cR3K2Y/pQla9N4/I1p5wtgpz8WZChx903pckkv7aILyMpVQ288GrdA1Rk3ZYa9zNYT9Fb3Th/bYNHLu2a6Ah7VX3UaOuG3WbxlI1O5Now0Sins6v3admLaFPaUk/EsKRTvHMVZ+zQC2n3xhD3NKjQKJdOeuyHLZ+7r2InGdCqCw2YvbWwgfxxK0wOta/QW22HH/8/SYMRmh1l8KReTGM2+niWvpnFzLn90SnAfPZKkrf6IK+1L5eK8iZruO0SUFzxoRXoB/mfyYR9XQOKH1Z/+40Ak7mUaIEE6atoqQsn/dnl9PL5bkXQgjxW2jd+uI/epOZvaJJ8/5TJI+MHcvs5ElMGduPrm2sGR2iiH9sMPEP36PaQgghmro7qe42n+qknVyY+gbV/YeoC7h2z5NUD1lC9V8u+bvAFiEvcmHEEqpHb6TeT56FuEpM7hJIecF2DDsPUdIimO5d7TJPpjEiWk/cwBgm7TQT9vhTteuauslTfMvZFBtHzKglFIydyABtHsv6WMummQgbO5MBB7ZyrCKQB0ZbttE9GULA8RxWHt/BnP4xRPSPJ26gnj0nA3lgiJ/tqE5EkDw2AnPaGKIGxhPTZz4F2kFMGKuwYPg8cs9AydYutYFegJUjeOmgCY5vJbRzHON2W5I15jzmROuJ6x9HxnEtAV39rPsPwbAyjpgh8cT02Ux5uJ7JtXsDXNd51CMRVG7Xo09MImZpDlXny1g3cyu6sRPppexjUk9Lvzx/2IcHR9hmVisYtuiJGqgnalEOVW1DGQDoHPoyDv3O2kAc/3mVcbHdiRoYT1yfFHJ/F0H0sNrs+ss4PyZdZ5IQYSLjqRjihuiJWFGIej6xjeZ8oaX/ei4m91wH2pnmo4/VW9od2KX2K/oOXBzXpX2M67OVEkzkLuhC6PA1rvsyLoQA43biB8YT1z9GFexz3I9+pSW1QWPXuoeLam8jrpu6bQpkwcFifDr2wzKNqCeRHaFg51aHQ6jPmf7ZPFoNmkiyy9neFgk9QuBfKcQMiSeuZ3eHQK9Ng9rcdSYJoaW81kdvOf4mI2H64a6vpd2ziNlaDGdyeKlz7blw4AlFM2KIGaJHn1aGz73dLP02chBhZ7YTPzyJkQNTyK0yY3hxFq+4PfdCCCGuNRLsFU3WvcMmMftvvejavg23t7qVgPZ/5pGkSQzrqC4phBDXj+pRb1Dd569U6+6EVp2ojpzFhXm/UbA1/z94nPwCjn+IhzWpesYxLjz3G9VHiPooY3gg0Iy5xSAWTveDM9606x1Rk11VaaTI+u/svFJMt2hp14A8U8EHbDJZ/h3d3g/TJ/tqfs9enYfBV0sw+Ww6XE5Ql+GAwqhQf4oOvGqZCRn0MMvXprE3K43I1uDlHWjdsxNKZwJ8jRSsPmr53bSD7E/M6AI7q0vWy2QoJNvyL346bz2u0pkAX9DFriZ9SyrpL3dD56mlnfozIhd1Nv1ShaaFJfCra+mFp7V4dHs/NM2DmbwllfQtqTx2jyc+f7T1vZGvtls77DzQQsEbiA507EvDefsFFUwoXRJZvzGNvfsT6ah4otxul11vGefHJFSLz8k8VtqWBzhvK19Xbf9Z6lV5Jt9yPneWcVrjifNYo4vjNoLLvnx5K7nKIDKz0lg1NqLOMgPONGzsWsteRHsbc904bdO6vRS07ECvcKD3w7Qz55Ox07Z3C124v8M5M2zfx7FzfgTYf5DjxKYNOzh9/0zyMjawsJ/zpQ8a0mZduD8+aOn+sqXuq3po0WiDGNzQa8mZcwaO2dpz3gyKt6XffqwCjYYAAEXBy3aBXcS5F0IIcfWSYK9omsKHMyz8Nm784QRZ/1jM2KSZzPpHNkf+k0dmgbqwnVZ30SmsE6EdtOocvP90L6Eu8ixuJfA+d/lCCPFbm0Z1oA+YDuOxsBM3TGvHDe/uxOPjjTXB1gZr/SDVIb2pbnunOseqnSX/Htuf5XdS3bE31R3t32i1EY8Vvblh00br7+HwO7tsIa4yuvgQgs4UU2QLHn5pxCe4r/OZlS01NYHKOtzlOaXhJgVyt+RgCOzCZOUJOvkVcyTVBF3nkfrSULwOTCM+OobdjVl+2F4zjTrlElRw5MUU1r2YwroXF/Nc4izWHbbLdlPnZSv3Qe8X2Pt2Gmkjvcl9eTG2BY5MpZnWfaaw7tnpzJj/eu2GjaQbvYHU5GC+2jCGqIcWU2A9p40tc61y2pemHYzs2Z2RK3Pw6r2I1C2JtTPTL5pl7F4W9Vw3TtvEZj4stKyvndAjGHPhDrapN3SlmTpBpTCFuM4xPL+llKApaaQt76ku0XDl+bV1XzSLGU+vsQaJ67mWGmvdGrKa9WV5RhqZ7z6BcuBVFhy2fOhz+c+9EEKI34oEe0WT9PCf78KLXzh5+E225VvW5K3If591r2VToS5sde+gv7Fs1ihGPzaY4U/+jZTkv3IvQFAvpsyfz7y/DWO4Og8YNmshq5eMZey0CSSOGMzwXvc57lgIIa4aXpYn/+9ugubWpOypeLz7oeXfXTdyYfkxLowdbs0cTvVzTmba+g7hQuLLliUdxu7kwuAHrRnzLdsnbeTCc9ss+SPe4MKQRKqTd1KdsITqhI1cmDrfuj9r+RnzrcfaSPWtQPNwqmvShfgtdGBU0nAGOwSpFBLCAyn/+FVmLFpi+Rm/naKWtUs5eHpprTPivEn+SzAUHapZA9Zdnr2swjI8Oz7MBOuxI5Mi0BmLyTUBx7dSYPTjgeeD0RUfZIEJCPXD52QOc94ow6CE4NVCvUcV0yFKjH50TOpg+V0ZSnRHDYZCu2UbnDh9toGRTtMhSoxa2nU1k3E4n4zD+RR8aq2/jZs6D340BHNaHFED9URExjHOOos1q7AMz7uDafWpZZ8ZnxZT8Jmr/6uzyDppRLm3GwnWl3XFtqw9odH3+FFVsJ25ByvAV1s7w9FOQ8rUUVSOqXUoE6zTWYN8a2cnX1HmKmjuhU4B6MDcUH+7zEqq7CY1u+zL4AgG+JrI3Z5C/Mt5VGn9ambRNoTbsXuRGnXdOGsT8MreYpR7hxIZZOLYOznqTTEcLqbcP4LJ1nOmG/YwHZuXcWyvrYQPrbpiuQf0CKqZgawLjSBWqWDb6/MZt7MMRXdx82ENh4sp1wbz4Flr3Q8XU1BUhsHdtVTnZYMN1GMQD5zdSlysnpjo7sQ8vcMy09jFuY/UJzI56nJF64UQQvxaJNgrmqBOBLT6HfAtJ9Mb+PK18OEM66rld6eP8PrixaRknuAXbSh9Bt0KRd9QUWHk0I7XWf3yO+Sdhhu199I9ym775m2499bvOLTjTV7Psn3ZTAghrjZr8Sg1QfNOVCce4ULSi1Tf72pmrhvNwSNzKh7vvgNmDYTNovpPdvmt74FPU/B4fzdc0EDIU1Sf24nHlrVwDrjjAbAvD8BuSNtoyT93BI8tU/F4b7O6kBCXV495ZB46SN6gQGg7iLxDB0mbAPQYxuMJIxjwqF1ZZRidAss5tt3yoieLtzlSrNQu5dBaz4ZDB8k7lMlg3xxemmq3Nqi7PDuGdRN5Kc+fx7IOknfoIMt7mNg2Y5Z1NmIZK3eXEdQlkKJ/WmfEp+dToh1EekY6Oe9NI8BuhdhjpypQukxklcPXvnMYN2Mz5h5ryDt0kLysMejyFpO0zn1ELuODo5S3HUpOViZpSe6CP5b9m2LWkJeVzt79B0lbNQyHVbTc1LnEWI5On2mp26GD5Ly7moVRiqVf/uXP+Kw97M3IJG9XKskx9juty7B0MduMoUzeb+n3KXdXWb9MD5t252P+80xyMtLJ2RyFp5PmN6RMHTtnsy5HIXajpf4b/qLQkM0u2e69HDnTgcFZB8k7NI+7TpTWtBXe5qPPNIRNzyTn3UVEu+jL2O5PMHnHHva+ncbeKcGc3vmq08CqK+7H7kVqzHXjpE0ApG0m1zOCsKp8NjmbFXtgFnPeMBH5iqXe6RP8KVgwkWUmgO0c+UwhcvlB8g6l07+qiHIAtMTqp/JMViaZW9JIG6CQu+VV9Z4b5sAs5myyHD8nI52cQ+mseFzr/lp64yBFnhGMz8okc0kjZhSXGjndehDp1usrLyuN1OndGOb03HcjYfRQEh4fqN6LEEKIq5xH++CO9S6LZ/jyhDrpiuve3bKMvhCNF0nikl4ENv+GQ+OX4/QLflGjWDbgLvjv+0x+MZuu4+fwSNAvfLLtffJ+APgjXR/5M62Ne0lc9k8AvP8USsT/3cXtfwyk053N+frwDJ593TKzt7OviU9ef47Vzv4HUohG2LPH9mXVxvs85md1khBOtKN6yHNUd2gHtm9t/3cxN6zdaJnZ2y8cji/mhtUbrbNtp1HNYTz+PhyPOvlQ/VQu1X9S4EA7btg+nwvL/wrfvMMNC2dalm5I3k21zxd4TOqNB1A9NpfqtuCxPQyPA+ryquM51FsI5+7OvFGddFkE+fthKi2zzHprgISUPYxmMREzi4kOgmN5tdu6y3PJN5DYNmYKGlIWbyLDfTh9uLhmfVMLhbBQLZV56nRbXiCaE/lkn1LnudCoOkFQ+xDuPFdMVqmzcKezOvux8N01+PxDz8jtJvANZPDERSQH7CP4kRRLEcWP6Hs0fFWnra65rIfiV//5aEgZJ3T+IXRsXkpGPbOPLy/LOaUo38lsWifn22lfehMZ7g+ljRgXao0cJ65c1HXjtE0N5GbboPYhtKpw0ieKH9H3+FD5qbM+byxL35ud7MvpGHZTX1fCktNYoX0d/fgdGPAmMmYizzzvx4ed4pnr7Nz7+hF2tqxOfYQQQlx5rVs7Ww2+YWRmr2iCPqXyB4Df0aKB36a68QYAhXsHDLYs1fDYnwnQwI033git/szoWfOZ97e/0r1jG27/g7M/Ks9RKYFeIcQ14RgeWwZww9M98PjnbstM2j8lcuEv6nIN43HmG8s/bMtCOPgKLqjThLg2FDUi0OvAVEaWq6CUuzy1U8VkNLQsFWQ7DfiYyHUa6MWa5yR45U6j6gRFn+XXDbDWcFbnUHQ+nmg01oUPzip0CvDBZLArZSojq8527rmsR0POR0PKOGEozf+VA73UnFPngTkn59tpX1aQfbiR40KtkeOkXo05B07b1EButi36zEWfmMrIOuyqzxvL0vfO9uV0DLuprysd23jD7zTWpSiqUIL9UMqNlILzc39KAr1CCHEtkmCvaIK+5ZOvTcCtBHQNdchp3cb5y9N+vgBwjuJtMxg73u5n4fsE9o2kky+c3PsiiTMW82xRA5eGEEKIq1D1/b2t//oKjw/+xg2ffgVo4FZVwQaq9r3T8pbxM+ocIa4vp8sKKSlzHtxzlyfsbeW5BR+geTyNvP17yHl3GrqCeYyYtEtdUFwH5Lq5/F6Zs5hszVA2ZO8hZ38aE/wLWTZ0eqOW6xBCCHH1k2CvaJLytn1I8Q/g1aEf85IG0/cvf2F40jSmTBlFYvStcN5S7nc33UpgKziQf4JKmhP4l7E80rUToWF/5pFRfyUauL15c8ssYa8/Etq1H4lBFxkREUKI31q3jVQPW8KFmW9Q3f8pqvu/zIV77rQEfj8G/medvqPrQ3XkU1RPHUO1sxm7ur9S3b031f3foPqPGjj3Hzw+UBe6GKfhF0BzJ0T2pvr+cHUBIa5aGSuSiF9hv55vLXd5wlFR5hL0sd0Jfag7EdF64hfsatTMRdF0yHVzBZzaxYzhcUREdifioRhinlrCm85mLAshhLimSbBXNE2ns0nZnE1xBXjf1YmH+0YRetet/PJ1MUcKvoUDRZz4AW5s82ceiQYOb+T1rBNUtmhD10GDGf5YP7oG3UVAVzhw4BO+/hm8Q/oxfFAoPpXfck59PCGEuBYcfQeP419By05URyZSHfkgaMph3zw8/gv8dwse/zND83ZU90+k+sbDeFhXaXDwLVT3WkJ1ZCf45Ss83p91mdbX3QmffgE33El1/yVUxw6h3hcLCCGEEEIIIYSoIS9oE01fm0BCb7+RytJPKD5tl97qLjr5N+ecQ/qtBN7XBq9fviHvqNGusJZ7w27lZ/U+hLjM5AVt4tdxJ9UdOwHleBQ4WXD8nt5U8wUenx5T59TyCafazxOP/A/VOZeu9YNU+2qgbDcelteeC+HSlXpBmxBCCCGEEL+VS3lBmwR7hRDiKiLBXiGEaBwJ9gohhBBCiKbmUoK9soyDEEIIIYQQQgghhBBCNAES7BVCCCGEEEIIIYQQQogmQIK9QgghhBBCCCGEEEII0QRIsFcIIYQQQgghhBBCCCGaAAn2CiGEEEIIIYQQQgghRBMgwV4hhBBCCCGEEEIIIYRoAiTYK4QQQgghhBBCCCGEEE3AVRvs/fnnKnWSEEI0aZd63/v20jYXQohrjtz3ri06/0DCFHWqEEI0LXKvu075BhLpq068VN5EtvdWJwpRr6s22FtRUa5OEkKIJu1S73sHy6/aW7oQQlwRl+e+501keAjR/g35y9xSNtb208A/wBJS9pCT0lOdDOq83vPIzFjBKHUhF+a+lUv6FHXqZdLIurinMGHtHjLf3MCq9VMJU2c3iKrvrT8XH1DxY/Ir6aRPD1FnNJKlXup6BLVv6Jiyajuc1IxUFoarM34lvoGX2J+g8w8hNtQPXQPTL6spqRTuX0GCOr3BFMJC7cbWla7vVa8n6/fb7i+X61r5tfyW9b0c97qL5a7d9ufzt3Blju/u2dpYl7qvAbPTyMvYwPLX6j43df4hxIYHEqRKr1fXmWQeymT5+tdZr1dn/tauzDkVl8/l+D/kK+LkyVJ1khBCNGmXet/7R9lVe0sXQogr4lLve0H6RWRmp/HMlGk88/Ye9i7vW0+AZxiT1y5iyt8SGf23RCa8mEnhrkUkXEKAzMEJI4ZTFVRdrv1distal4FE3ge587oQ+sgSctXZDRJFf2u/j/5bIqOnL2Lh2pkMvkddrqEqKDdWUG4yqzMayZ/Bs1eT/KR9R0UwYcULJESY7NLqcaqC06fKqTyrzrjSvBm1JJ2ctxcxevo81u/PZH0/W1sSST+SS96hg9afdFb1UG0OgELC8kzSX5vJhPkbSN+SSKQ1J2F+OumvzWN08hrSszaQHKza9KrRmdFLXuDv063j65kNZB5KZW599e0xj8z35jHY1e9NwuW6Vn4tv2V9L8e97mL9lu3+lUzYQN7GMerUq0AI3cP9MLzRhdCeSbxSk96B5I17SH9tJqOnv0Dav+3vrxa6fivIOeI8aBr7cAi6k5sJ7RzDyDR1bgNctf0lfg2X9n/IV1Bl5fccO3ZUnSyEEE3SsWNHqaz8Xp3cKEe/9+Dpo83UyUII0SQ9fbQZR7/3UCc3SqvmRrImxRE1UE/E8B2Yo55gclt1KTUjHw6JJ25IPDHRI8iq6kb/JxVQ/Ii2nw3oG1h35q919qTLGZ+lmby5+nWy7GKElhlBbraxK6P++qjz9NoZjPbpOn/L7zWzMB3q4k1kqB+6mm3VM5TUs25V+b4+KBrQ+NodU/Ejus7saNtxLPtzrPdWJln7PW7IYo5UaTDtXcOkw3bbOpmRGdTeMlu1bl+Y+OitNWx6x/L3hutyFrZ020/t+cjn/QIjAaFP1B43/GHatSzmyOuqbe3aWqe/TTm8+/JathXaSrg/Ty7Hkq1fnfRDrNOZu1q8jNt5LjaOuP4xjNtuJmxkYu2MxLP5vNa5C6GduxDaOY5xux23BiB8GgkRRt6NjSOm50SylaFMnqJA26kM7lHBu7ExxPWPYdKH3vRPGq7a2JtI+/HicB3VN+7s0ivrrulSt83uxheAmWMbrGMsNo5Nxf70GFE706/u/iDIxxsvxRuddX/q3y2cjE3Fj+j23jXnq+55wXV9nV47tWOi5seWb0t3GBMu9l0zrn7AXPPBw+W6Vmycj237PIf+sL+XOmtLnb50rC8ursGa7ZydH7C7rzmOO1ftBhf3Ohf7t/VjUHsn59JtH1nVudbrttv5+bRyNY7qOwd29/t6ry13x7exnVN1PerUTyHsFgVNS2/VsV3fD52edyvb9ey8fy194DSvTr0AxR+vFqBR1HUooyR9MfGRlvvr3L0Q9sgTdtv15ZkJ/phP2W1SQ0F3iwKeisPYcXYfwum4dNNftnwnY6y+67tB59TpGKlV5/lnTXfeNvf1dH79CACP9sEdq9WJaoYvT6iTfjVeXjfTurU/3t4+3HijpzpbCCGuWT//XEVFRTknT5ZecqDXXoebq3nc7wJdfC5wq9w2hRBNyLdVlqUb/lF2wyUHeusaSuqhQVROimNcTQBRLZH0IyEc6RTPXGvK3LdyeeBTPTEfJ5IzBdY9lMQmrF8rD80n+JEUElL2MP6eKqowcfqMgs4fCp7VM3K7iYSUPYxmMRGJu6D3Crt9KCQ8n8r4rhoMZSa8WiuULI1h5Pba2sx9K5cHPYvRaDScbqYloGUxm6JHsMykkLA8jfH3mzAYodVdCkfmxTBuZ1/WZ02jnamU0+d90LUu5/2h8cw9bvkK6+jbjZi1CuaiHFZu1/L3mrokkn6kG5piDZpmJtD6oRxfQ9xjGzEofVn/7jQCTuZRogQTpq2ipCyf94fMss5u6saq9+YR2doTzlZR9EYXkk4sInV2BJSWYvYJxMe4mUlDUsi2HofPFFo1r+DITj3jNtS21yYsKZVVvcp5qX8Sm0xAcCLpa4fS6lQxp1v4E0AOywZOZ5PJ2kfNitG0UPeRJa9TXhhxS92Xi5ySyvJ+Ggo+NhEQ3gGNsZiSD1OIX5FvqVD4PPamaHn/gREsA8Jmp7O+7XaCH9vIqOWZjA42YfgGvO7SUvnmMOJWlNXt71FVjK4ZW+7P0/g2RkyKhkqTNwH+JrIT4xh3AHT95pE6vRsYS6lUtLT6NIWISaXM3fICvbwrMJgUdL7lvD/csi+nhq0mb0gFc2JnkdFjETl/96wd067YjXXs258W7HhNtJ1J5npv3nTYn+qacrgG3Iw7OjB3yxoGaI2UlHvSytcH5Xweyx5KYhMdXLTZ3fjqyfr902Bpd0butKQkpOxhfItXCR1VaHcsu+v37DQy5/dEpwHz2SpKD+9DibD7/Y0u6A8kkr6qL16njFQqWnQVOxg3JIXc3ivImeLDabMWr7PF5C4ay4w695269V1QvojU5BDMXxhB648mbx5RU3eh67eCtOlBGPKKUO6PwOdsMYa8Hbx0pi/P9FM4fdKEpnUgXnmziJi0y/W+bddlSy0+ioJha+31cVmuFTdj20Ih+bVMog0TiXrask1Cyh7Ga9by9HEXbXHSlz8l1dbX1TVI7xXkTNdx2qSgOWPCK9AP8z+TiHo6B4LHkLZqOLryYk6jRVe1g5GPvEonp/dVW93r3uv0B9zfmx44X4zXbRpO/yeTuKkbG9hHQNtE0l8bhJexlMpmPniVriFq0g6H86Tr5/p86vo5H0cW1nNwajpRU3PA7hyEvqwl/UXreNZoCWiez7Lo6TXXakPHU43ei8h5JoTKYiNmby18EE/cCqPz+p3Qk/d4BzRUYTaXkzsnjtzerp+tLs87HUh+7QUGt6mgxAit/MzsfiSe0im1z+LIKaksf9juGWPl0Kd2z67IjQcZfJ8nmKsw7J1FzNP77BppozDhlXQeO7+W0Ke2AgoJL6Ux+vwasm+fSZD1vNWYsKG2vcZ9LOuTRpDT+5qL572/3fbW/qr9sM71GHN3fbsbUzXuS3QxRmrVff59QJjTtrmvp/Prp2lp3dr9983cueqDvUIIIYQQQlxputEbSP9rOc/3nM42dWaNRNKPRFDy1GKyAK/7n2DCCC1HJsUxTrEPUjkJ9nq9TvzwjRQBkc+ksyo4h5H9l9DOVbC36yL2LvYnd5SeGYWAoqAzmTDY1WbuW7n0OD0f/fgdGOjL+v3T0KzvQvzJReydr/Bu7FhWmkA3dgPp4QcJfWwjOkXBYDIBCgvf3kPHQj0xz1qCj5M7Fuo7UMQAAAuxSURBVLKsj/WP2zpBt76Uz4lj5HYT9FtBznQNb3Yey8qkVAojCokZuMRahycwz1AHzK3bz+zOyJ0RrNq1iIC9Y4hZdBSUvqx/bxpeaTHoVz9B+pFBaN6w5jmjDCctaxiVC6x1sQYmelUutvZDBxa+vYawUkugwmUfvV43gOW8XDfWZ82Eld0ZuR1009PIvCeT4Mfs/7C0tMkrtQvxr/ux8N3XCdgdg361yeG86aankdmlkJF95tNO3d+qoKfb86T7gHEDl5DtkKdz7FcUdIoJ4jeQHlPKpD7zyQYGLMlk/C+zagJpjhQmv5ZJL1uQp/cKcp4JhVPl4O2DZ9kHLBs5nzdVq1MkpOxhdLMUIsbvsCTYxv5ISN/fjZKn4pmU58ngZ9YwuZ+Z9+0+LFG3u6HjbtvYDWTqK2v6Tzc9jcwYA8seSiJrrKs2R7gZX5ZgryZ1Om98CmgfZsL0hzFtiCHpdy84HCtyehqrokqZ23M626akUti73Bpktra95nfL2Iw8PoaYZ49ax8lUmBPHOJ8V5MwP5pitbU4lquobwapdz+KVGkf86yZQxpCWEcJHkSNQtuTyQEF3YhZZ++nJH3gudhYZ9vcN++vW6b7tx09f1u+fic9O58Hei79WXI/tGqM3UBhnZEbsLDLsg/AHXLSld92+tK+vq2swt/cKcmbpyLbeY2vzUoh2uKdYriXD/a7vq7Xs73X135sGNNtqvZYd1dtHU1IpDLfdd2vVttvd+XQ9jpbZdqRfTd4Ik/V5aCmvWWlpk04xYTBh3aft3DduPNnUuXeA23FenrKHybfvqPlgyd2zNdfFeTeo7x3WvrZ98Krf+zBp033Y/VQ8c2u+aWGrl6tnV2fW759Xp302o+an0ut+La3OfsDK4Ut402RZviFtQhXroqfjbz9e7STYtVfn6r72wSCX49J+ezVXY8z19d2wc2p79tQdI7Ul1P+/4bJtT+e7raer66cpuZRg71W7jIMQQgghhBC/iuBEVj2upWD1fDeBXhs/Ipe/wDMLZzI41Mz7U+MZd0Bdpq6qSiNF1n9n55ViukVLO1UZe7pwf3xO5rHS9semKtBrc7pshzXdDHii3G7dFi3dX04lfUsqq3po0WiDLOuIBj3M8rVp7M1KI7I1eHkH1uzLVPCBwywmR0a+sgWlzgMtFLwBfqwCjYYALEEVr/q+UaJ0JsDXSMFqa7DNtIPsT8zoAjtbC5TyUZ1AnI3C5FUj0OWlMKcmQNaNgDZmju229cNRVuYZ8WlT+5IiZ33kjPNyJsxVGrxaWr5XGtDCWQNz2FNoIqjLIFD6EdSmlM9SbfULZMAzq0nPyCQtRguKd815d9vf7s6TodD6x62Jn85b89T9iuWP7ej2fmiaBzN5i2UsPHaPJ//f3v3HRH3fcRx/Ucxdc/FQQxsqB8ySpXJEIV102cCLrtMg1iglFTu3xW2YpTVxhuq0bebStRtD5y/sZiMtW5jz4m4ddUYhxAbnbi6N8McgMjFboBkwMwpxuUvOcBl1f9yBd1+4uy9kWO76fCT88f18vnz5/nh/Pt9833y+n+9jnyud3FYk1/7TeiG7Q01vhEbz6dIPVb2pXKWbKlS6/pC6Fm/Rzj3LtKnmhC6Et3e2ZpXGxoOyWKd5V9dfr2On+uX8Was6/vIbVY7c1sw/RTt93K1fvkz+m1cnz9/Q+IN5SuMfc/z4Kq4+qR8df1N7ns/WrWMvqarBr/WF0X/Le7pDQ1nZSjSdbyg2pYynD4TP14ty2rLl+HK4+j/d8sZM9E6I2F97ifKzJMem06HtnfmKHJZsOTdI/v8GZbUtkyQ5FmdoMkr9FrlqTsjzfquu16yWfaLdGretVXJExU/8eV9n31bix7YkqaFdXYtX6tkvSdq8Uc6xTrVcSnAscc9l7Daoe0O6Fe5jh8bHwnXGPiXUluL2q9Mybmdq39T3YYxEVaJzdOZ3umHfptYPPPrF7tJpPvwV53rGiaNJnnO6oVV6rkrS2i1yKnwN5Jd9zV69+yuP2v+4V8X2yP505vHU1HhRH3/hB+poaVTd1vDr/Gb2L0Lse+v0193YnkOJxLD01arbX6qx9npDotfMvSs2r7tely906uPHNuq7B8vC0zcU6NbJ6BGv8cTq12Yel2FxYmz69m3umsaPkYi1Iu5/sY5Nir+fMdsPJJK9AAAA+Eyzb9G7x7dJzQfijLCLdFuXS9Zo9boKVXy7RrXtZn7HYLH1QTJmrox0quFUfejn8CG98urb8q59U2d//nVlXDugb64v15WIgWKz1vC2PkjfouMtHrW+/x3Zr/1StVNeRzch3WosmcLxjcN64clunX914kE0jnRLgo/tmdWp2t/f1pPf86j1vVbVlY7q/NGpr4s2X+tVsKBEO79WJMc/OvWOX6GRYL89qefsV1W7vVyll0x+iPX/fJ38/a0PYuGNg3rlxxFDrMIcW0+orlK6/HLka8t+9f47vOC/Ku/f/crIWq6uC+cmt9d04bbOD4zIuiR78nx/0WbXmC+U1vU21ah83RqtLilX1T8XKuNfHz2Uj1aZOeap/LpRG9rX8h279bInVlJYkqx6dJr89lRj6rsU3o9T9Tr6/d065jauMxOj+uvEcZ06op/sPaSGD6Vjb12VNp9U+3seeXZl6saZI2qRXft+/Qfte/ojnX+xXKVHOzSL3moGTLQVU7F9Tn/qtsu5dZV2bijSWPdFNc/6WGbZBmOZrl81rpNIor7JzDnyX9Susq9q11vXlbH5sM66I+bZNmX6OHrgupqujSr/mW3atHG51H1RzeE3YM6+VqTBxpf0zLoj6jJ3EWLrrldFSbl+6u5XwX6PPMcn5shOtH8xTN5bZ3fd7Ws2yt7eprG1B1SX+L85ISbuXb1/69Q7DQdVcaJb1g1V2vetrSq22VX8Wujjl89+Xsrf9md59hh/M1rMfm2mcWkmxmZptjEy7bHN4X5+FkyZxuGRT8YjFwEAAAAAAAAASYCRvQAAAAAAAACQAkj2AgAAAAAAAEAKmPE0DjabTZWVlVqyZIncbrdGR0eNq0RxuVwqKChQRkaGsQoAAAAAAMxjPp9Pvb298nrjzgQKAJgDmZmZ2rFjh+7evavm5mYFAgHjKlOkP571xOuRBWn3o3K/UVasWKHKykotWrRI6enpkqT+/tiTXW/fvl1Op1NWa+JJqwEAAAAAwPxitVrlcDiUl5ennp4eYzUAYA6VlJTI4XBo4cKFKi4uViAQ0PDwsHG1KKamcbDZbKqqqlJZWZksFnPfDna5XMrJyTEWAwAAAACAJJOTkyOXy2UsBgA8JBaLRWVlZaqqqpLNZjNWT0qY7LXZbKqurlZubq6xKq6CggJjEQAAAAAASFI85wPApy83N1fV1dXKzMw0Vklmkr35+fmmR/NGYo5eAAAAAABSB8/5ADA/WCwWLV261FgsmUn29vX1KRgMGosT8vl8xiIAAAAAAJCkeM4HgPkhGAzqzp07xmLJTLI3EAiosbFRAwMDxqq4ent7jUUAAAAAACBJ8ZwPAJ++gYEBNTY2anR01FglmUn2Kpzw9Xg8amtrMz3K1+v1anBw0FgMAAAAAACSzODgoLxer7EYAPCQBINBtbW1yePxKBAIGKsnpT+e9cTrkQVp9+9HLkYZHh7WzZs3lZeXJ4vFoitXrujevXvG1Sb19PRowYIFstvtslqtxmoAAAAAADCP+Xw+dXV1qaWlxVgFAJhjPp9PhYWFGhkZkdvt1tDQkHGVKdIKi4qjsruPfDIeuQgAAAAAAAAASAKmpnEAAAAAAAAAAMxvJHsBAAAAAAAAIAWkPeUsjD1JLwAAAAAAAAAgKaQVrFhJshcAAAAAAAAAklyac2URyV4AAAAAAAAASHJphUXFJHsBAAAAAAAAIMnxgTYAAAAAAAAASAEkewEAAAAAAAAgBZDsBQAAAAAAAIAUQLIXAAAAAAAAAFIAyV4AAAAAAAAASAEkewEAAAAAAAAgBZDsBQAAAAAAAIAU8D8IPgRD2owpDgAAAABJRU5ErkJggg=="}, "image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["![image.png](attachment:image.png)\n", "\n", "![image-2.png](attachment:image-2.png)"], "outputs": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}