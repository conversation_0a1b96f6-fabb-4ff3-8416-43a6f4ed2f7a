{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "machine_shape": "hm", "gpuType": "A100"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "widgets": {"application/vnd.jupyter.widget-state+json": {"1d1fe06ac632475086ed5964ed000360": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c138f597c98c4944b54d36510ecc8e0b", "IPY_MODEL_bef2531516164e85bb79b86a791dd00d", "IPY_MODEL_1cb9fc011950479a8d4832bc52c3399c"], "layout": "IPY_MODEL_974e8f7f05ef472d85d5ea71425e6c39"}}, "c138f597c98c4944b54d36510ecc8e0b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_696090959af8499e9a38777e664b85c1", "placeholder": "​", "style": "IPY_MODEL_973bcc9740b4426da4c680d11f3c1f7e", "value": "tokenizer_config.json: 100%"}}, "bef2531516164e85bb79b86a791dd00d": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3cb5d8fdb5fb4b6a99f6733c00df8378", "max": 2103, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_58f4369c68434d569d5eb1bc36e71775", "value": 2103}}, "1cb9fc011950479a8d4832bc52c3399c": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a05df972876941e3b6faab56cc30a4b8", "placeholder": "​", "style": "IPY_MODEL_9c61d90b63dd4fb5a481282d6d6eb8e8", "value": " 2.10k/2.10k [00:00&lt;00:00, 182kB/s]"}}, "974e8f7f05ef472d85d5ea71425e6c39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "696090959af8499e9a38777e664b85c1": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "973bcc9740b4426da4c680d11f3c1f7e": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "3cb5d8fdb5fb4b6a99f6733c00df8378": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "58f4369c68434d569d5eb1bc36e71775": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "a05df972876941e3b6faab56cc30a4b8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "9c61d90b63dd4fb5a481282d6d6eb8e8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2b71f87a02a540488a9e07f072f8807a": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_548cd7e9fab54470bc52810f27784760", "IPY_MODEL_9c5eb078ece84a57aa9c402c9cad3b0b", "IPY_MODEL_ee00a9f599db4affabb7bf1c4df6ca1a"], "layout": "IPY_MODEL_52bd638607bf4e1aaf224ebdcfa3693d"}}, "548cd7e9fab54470bc52810f27784760": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_771619a5acd343c788b8189167af09d4", "placeholder": "​", "style": "IPY_MODEL_09a1b30b5659452f95ebb2e72466c750", "value": "tokenizer.model: 100%"}}, "9c5eb078ece84a57aa9c402c9cad3b0b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_145a1f1032a44079a262db381e60d401", "max": 493443, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_99888ad83b51485f959f977ba4418119", "value": 493443}}, "ee00a9f599db4affabb7bf1c4df6ca1a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_ec0854c2ea9a4c9280b6876df365db9d", "placeholder": "​", "style": "IPY_MODEL_dac5892c85214f69a5d75d5dc4858dfe", "value": " 493k/493k [00:00&lt;00:00, 7.91MB/s]"}}, "52bd638607bf4e1aaf224ebdcfa3693d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "771619a5acd343c788b8189167af09d4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "09a1b30b5659452f95ebb2e72466c750": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "145a1f1032a44079a262db381e60d401": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "99888ad83b51485f959f977ba4418119": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "ec0854c2ea9a4c9280b6876df365db9d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "dac5892c85214f69a5d75d5dc4858dfe": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "41b669da565e4204b848b754dfa28ac8": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_e806afdada48418c9e353b94a38cd703", "IPY_MODEL_7898b7322b014e96984c3d09a29a57fb", "IPY_MODEL_d665270b05d64effba568ded85eee1b4"], "layout": "IPY_MODEL_df087de9ade24058b1cf32e1556f7cb6"}}, "e806afdada48418c9e353b94a38cd703": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_584330ab439b4887b1050a7f14dc5d7c", "placeholder": "​", "style": "IPY_MODEL_880b32d3bd1d4af8b5d0b449aab87e8b", "value": "tokenizer.json: 100%"}}, "7898b7322b014e96984c3d09a29a57fb": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_97d09f016e274cca93927f3bd8329352", "max": 1795188, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_d87ef5878c0f4211809716674d0d8413", "value": 1795188}}, "d665270b05d64effba568ded85eee1b4": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_556109848b1c4ebc99a6cc7c0be519e0", "placeholder": "​", "style": "IPY_MODEL_8d6cdfd75e3f4a628c9e785d3c469d98", "value": " 1.80M/1.80M [00:00&lt;00:00, 24.9MB/s]"}}, "df087de9ade24058b1cf32e1556f7cb6": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "584330ab439b4887b1050a7f14dc5d7c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "880b32d3bd1d4af8b5d0b449aab87e8b": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "97d09f016e274cca93927f3bd8329352": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d87ef5878c0f4211809716674d0d8413": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "556109848b1c4ebc99a6cc7c0be519e0": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8d6cdfd75e3f4a628c9e785d3c469d98": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "fb1ff6f4482143c39be1cca57ec2fc8b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_83e6421843ad487c91bc75510b90f198", "IPY_MODEL_9e74a7b74e1a4b119af5b95d572bac3c", "IPY_MODEL_080c34ad56c84c229b1555b15b354aad"], "layout": "IPY_MODEL_d968bf43e8574d9090326b31c9a7fd93"}}, "83e6421843ad487c91bc75510b90f198": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_e78b05f33ee54c968fd87b77a2470bce", "placeholder": "​", "style": "IPY_MODEL_79a201f7ab7e49efa9e3e1504012dec2", "value": "special_tokens_map.json: 100%"}}, "9e74a7b74e1a4b119af5b95d572bac3c": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6e5d431074de4955a97d4ea36621ae36", "max": 414, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bfc581362fbc4aca85df7b2a943dd5e4", "value": 414}}, "080c34ad56c84c229b1555b15b354aad": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_bc9b585bfd2847bb9f22c4720bd19033", "placeholder": "​", "style": "IPY_MODEL_8addd2418c3049f3be32465cc9a408d4", "value": " 414/414 [00:00&lt;00:00, 52.5kB/s]"}}, "d968bf43e8574d9090326b31c9a7fd93": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "e78b05f33ee54c968fd87b77a2470bce": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "79a201f7ab7e49efa9e3e1504012dec2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "6e5d431074de4955a97d4ea36621ae36": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bfc581362fbc4aca85df7b2a943dd5e4": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "bc9b585bfd2847bb9f22c4720bd19033": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "8addd2418c3049f3be32465cc9a408d4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "c7b5bb9ef22f4ebe9969d4d10d63d24c": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_d8c3f3ec329743f6b2f21d21601f092a", "IPY_MODEL_2fee19152ef34eeaba541d559b9a0bc0", "IPY_MODEL_2740de6be1ae4e3bacc642c39828883b"], "layout": "IPY_MODEL_4104813265f34db0ab09c9d6c148ba29"}}, "d8c3f3ec329743f6b2f21d21601f092a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_6d2dbad5a0984f8382abd18910c14343", "placeholder": "​", "style": "IPY_MODEL_32285185818f40a6b07c6d6f6175b70c", "value": "config.json: 100%"}}, "2fee19152ef34eeaba541d559b9a0bc0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_79da3c26e0fb4405a198c2255df9ec00", "max": 571, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c95bea4e04ff49078821a5dd67f0c28a", "value": 571}}, "2740de6be1ae4e3bacc642c39828883b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3695b9dde85348efb683e31e5d52e210", "placeholder": "​", "style": "IPY_MODEL_1d982bed2d4645b8a19295b7812cef49", "value": " 571/571 [00:00&lt;00:00, 72.5kB/s]"}}, "4104813265f34db0ab09c9d6c148ba29": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6d2dbad5a0984f8382abd18910c14343": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "32285185818f40a6b07c6d6f6175b70c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "79da3c26e0fb4405a198c2255df9ec00": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c95bea4e04ff49078821a5dd67f0c28a": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3695b9dde85348efb683e31e5d52e210": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1d982bed2d4645b8a19295b7812cef49": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "32c58f50bb1c44e085ae3663004fcfff": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_c4df70cf509541828d3a06c380fdfe3d", "IPY_MODEL_abd2737f597f48b0846a74c743307917", "IPY_MODEL_a2a52b5e3c104e1cbec513a9f8744db2"], "layout": "IPY_MODEL_ba57460b8ee24f4e96f8a603914b7073"}}, "c4df70cf509541828d3a06c380fdfe3d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d17cd0e49fa94361894660c0645ec9a8", "placeholder": "​", "style": "IPY_MODEL_6cd364a43f6f4ea793b05bf14ee9d687", "value": "model.safetensors.index.json: 100%"}}, "abd2737f597f48b0846a74c743307917": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_a80f72a5e41047f1898d5b6f00a2c69b", "max": 25125, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_c6f6fca0f35b44fbb9037337a5bc0431", "value": 25125}}, "a2a52b5e3c104e1cbec513a9f8744db2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_3d07e648a5644742b8112146e952c44a", "placeholder": "​", "style": "IPY_MODEL_bff978fcc6f94f55bf605c6d9c23cfd2", "value": " 25.1k/25.1k [00:00&lt;00:00, 2.73MB/s]"}}, "ba57460b8ee24f4e96f8a603914b7073": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d17cd0e49fa94361894660c0645ec9a8": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "6cd364a43f6f4ea793b05bf14ee9d687": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "a80f72a5e41047f1898d5b6f00a2c69b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "c6f6fca0f35b44fbb9037337a5bc0431": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "3d07e648a5644742b8112146e952c44a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bff978fcc6f94f55bf605c6d9c23cfd2": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "eca24e648bcf4cc684f15da684e2791d": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_dc82b611b8c145eb8ebc7b80073e9ae1", "IPY_MODEL_f3e6040a241c4ac7b715bb07a9ec6d6b", "IPY_MODEL_e310ab9f4338443e82d257ddc21f48bb"], "layout": "IPY_MODEL_9dd0e53a7a2a4d668c5640d938b71c9f"}}, "dc82b611b8c145eb8ebc7b80073e9ae1": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_1fc933b90fa546c884181136373ad005", "placeholder": "​", "style": "IPY_MODEL_94f3ee73e2c04092ac5522c6ef038ea1", "value": "Fetching 2 files: 100%"}}, "f3e6040a241c4ac7b715bb07a9ec6d6b": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_81d8563026e04f5ab00eced0da89a7ef", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_95f081aaf9e84c2f91c82a4e2f183009", "value": 2}}, "e310ab9f4338443e82d257ddc21f48bb": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_965cfc093b5040bbaec177820e45ec95", "placeholder": "​", "style": "IPY_MODEL_d328397d81f343e28dd1a6e52c5f0ae7", "value": " 2/2 [00:46&lt;00:00, 46.46s/it]"}}, "9dd0e53a7a2a4d668c5640d938b71c9f": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1fc933b90fa546c884181136373ad005": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "94f3ee73e2c04092ac5522c6ef038ea1": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "81d8563026e04f5ab00eced0da89a7ef": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "95f081aaf9e84c2f91c82a4e2f183009": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "965cfc093b5040bbaec177820e45ec95": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d328397d81f343e28dd1a6e52c5f0ae7": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f73f9c7f341c4a99b00585343bf4d4bd": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_2a11e010825b42d4a949ad64ae0d1933", "IPY_MODEL_15b769156f6a4d2988f1c09f3820f7ef", "IPY_MODEL_a0484e3846c647b892d2de3797496605"], "layout": "IPY_MODEL_cb042f80aaf04bf1963d637d1771741e"}}, "2a11e010825b42d4a949ad64ae0d1933": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_852ba7d4221a475488411f5014362496", "placeholder": "​", "style": "IPY_MODEL_38dc7c1e65324e3097d8738532272e32", "value": "model-00001-of-00002.safetensors: 100%"}}, "15b769156f6a4d2988f1c09f3820f7ef": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_613da14abc24460db3bb337886cb407c", "max": 9942981696, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_37a495a5836f413ea5f662538d51a939", "value": 9942981696}}, "a0484e3846c647b892d2de3797496605": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_9c61322c006f465385df301121462e82", "placeholder": "​", "style": "IPY_MODEL_d93d0bb6ebc943a1be6902bd88cef441", "value": " 9.94G/9.94G [00:46&lt;00:00, 246MB/s]"}}, "cb042f80aaf04bf1963d637d1771741e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "852ba7d4221a475488411f5014362496": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "38dc7c1e65324e3097d8738532272e32": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "613da14abc24460db3bb337886cb407c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "37a495a5836f413ea5f662538d51a939": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "9c61322c006f465385df301121462e82": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d93d0bb6ebc943a1be6902bd88cef441": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "50a2a1bd13db4045a4ae01138470c42b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_ad7cba643d1742cdb47c433bf50072f9", "IPY_MODEL_57ef5d067e7343239525a6da237b29eb", "IPY_MODEL_7567388a58a340d4a0f384f79ee13ddc"], "layout": "IPY_MODEL_52c2896ab41a4d2592484084cb501e5a"}}, "ad7cba643d1742cdb47c433bf50072f9": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_22957622a42345b991371153c29583c4", "placeholder": "​", "style": "IPY_MODEL_d34c879607b041739a2cc6273509e330", "value": "model-00002-of-00002.safetensors: 100%"}}, "57ef5d067e7343239525a6da237b29eb": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d1e7bdd4faac4765862fc809017c4856", "max": 4540516344, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_fcb0ad846398455faccf0d797549f589", "value": 4540516344}}, "7567388a58a340d4a0f384f79ee13ddc": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b381226552c9462d858051fcb7240727", "placeholder": "​", "style": "IPY_MODEL_94e630795bc247e08e6af434c5924cdd", "value": " 4.54G/4.54G [00:23&lt;00:00, 248MB/s]"}}, "52c2896ab41a4d2592484084cb501e5a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "22957622a42345b991371153c29583c4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d34c879607b041739a2cc6273509e330": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "d1e7bdd4faac4765862fc809017c4856": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "fcb0ad846398455faccf0d797549f589": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "b381226552c9462d858051fcb7240727": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "94e630795bc247e08e6af434c5924cdd": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "2b496c218e2049ff9156ff5b3bbdb90b": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_62d3b35a3924417894094d3bbf993932", "IPY_MODEL_41737448e98a48dcbe117351645395de", "IPY_MODEL_e83735cd79674a3482f0b90d4c9a3e3d"], "layout": "IPY_MODEL_eff6ca539e2947e9b2987977f143de9a"}}, "62d3b35a3924417894094d3bbf993932": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_aa75292545a649eda8cb7bab0ac9bbcd", "placeholder": "​", "style": "IPY_MODEL_22c0e2213505435eaeebdfe330b8fbb8", "value": "Loading checkpoint shards: 100%"}}, "41737448e98a48dcbe117351645395de": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7de820edeeaf4210af68c721bab3082d", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_66ef664b717343bdaf8e5c4610b2a678", "value": 2}}, "e83735cd79674a3482f0b90d4c9a3e3d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f09534cbda8c4e91b2e073c0eca0cb96", "placeholder": "​", "style": "IPY_MODEL_7d8b5a2a52aa4957bc5905021898d8f4", "value": " 2/2 [00:17&lt;00:00,  8.24s/it]"}}, "eff6ca539e2947e9b2987977f143de9a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "aa75292545a649eda8cb7bab0ac9bbcd": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "22c0e2213505435eaeebdfe330b8fbb8": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "7de820edeeaf4210af68c721bab3082d": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "66ef664b717343bdaf8e5c4610b2a678": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "f09534cbda8c4e91b2e073c0eca0cb96": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7d8b5a2a52aa4957bc5905021898d8f4": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "1c68a822580a4960acad93be9fd48ce3": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_6df81f91b17f41dc91fc9f367fa0afab", "IPY_MODEL_17742936c9ac46e588d1ce42235745d0", "IPY_MODEL_17f0cd6f05184164b48ef906f192505a"], "layout": "IPY_MODEL_936a67f2de2e44728b83600f4fa0569c"}}, "6df81f91b17f41dc91fc9f367fa0afab": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_d5ad82f6b9654a8cb888613caaaaa097", "placeholder": "​", "style": "IPY_MODEL_b014979e237344129545ff2c384c1c1c", "value": "generation_config.json: 100%"}}, "17742936c9ac46e588d1ce42235745d0": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_b99c12d57d4a4eab84aefbef58452c32", "max": 116, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_5923bbdcf6334393ad832765f129bdec", "value": 116}}, "17f0cd6f05184164b48ef906f192505a": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_260ac8c28531450bba1deac4e4669dc4", "placeholder": "​", "style": "IPY_MODEL_067959a4ef614c498c28bb83c10e16de", "value": " 116/116 [00:00&lt;00:00, 15.6kB/s]"}}, "936a67f2de2e44728b83600f4fa0569c": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "d5ad82f6b9654a8cb888613caaaaa097": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "b014979e237344129545ff2c384c1c1c": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b99c12d57d4a4eab84aefbef58452c32": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5923bbdcf6334393ad832765f129bdec": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "260ac8c28531450bba1deac4e4669dc4": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "067959a4ef614c498c28bb83c10e16de": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "b5dd409cf6e04764adbb7c2a49b7be86": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_65187b4ebb2041b39778268e8b4d6b0d", "IPY_MODEL_33317cac10ca4a98bf4433c1eff43435", "IPY_MODEL_f81f5402902c4c04b10895782287e908"], "layout": "IPY_MODEL_c471914fe0d34ae8967bac2820637d5b"}}, "65187b4ebb2041b39778268e8b4d6b0d": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_7aead6f6cffa40a383f1b8c64943329e", "placeholder": "​", "style": "IPY_MODEL_f24fe57d8e164fd68185b4c117e7c097", "value": "Loading checkpoint shards: 100%"}}, "33317cac10ca4a98bf4433c1eff43435": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_f913ca9ab6d44ab1b788a36bd964ed39", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_ed34016801264a05bb3697eca2ac22ef", "value": 2}}, "f81f5402902c4c04b10895782287e908": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_fe622254072540fda3b0dd6b2cab6e4a", "placeholder": "​", "style": "IPY_MODEL_5d95bdea47594e21855a6e564d0760da", "value": " 2/2 [00:17&lt;00:00,  8.01s/it]"}}, "c471914fe0d34ae8967bac2820637d5b": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "7aead6f6cffa40a383f1b8c64943329e": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "f24fe57d8e164fd68185b4c117e7c097": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "f913ca9ab6d44ab1b788a36bd964ed39": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "ed34016801264a05bb3697eca2ac22ef": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "fe622254072540fda3b0dd6b2cab6e4a": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5d95bdea47594e21855a6e564d0760da": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "9f9defc39ac5437e9512e5fad810b409": {"model_module": "@jupyter-widgets/controls", "model_name": "HBoxModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HBoxModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HBoxView", "box_style": "", "children": ["IPY_MODEL_1c126dfdc51c438b9b48c8a65e549ae2", "IPY_MODEL_741d800130ea4830b9266f467fa6a0bf", "IPY_MODEL_73c0a01f1693471c9c017143e9e9058b"], "layout": "IPY_MODEL_ab8174c1337b43048e05aeca72ca18ef"}}, "1c126dfdc51c438b9b48c8a65e549ae2": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_5e5a992d86434e62a25fc9b7f75f4b16", "placeholder": "​", "style": "IPY_MODEL_1507b1310f5045c9b691fdb102cc1686", "value": "Loading checkpoint shards: 100%"}}, "741d800130ea4830b9266f467fa6a0bf": {"model_module": "@jupyter-widgets/controls", "model_name": "FloatProgressModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "FloatProgressModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "ProgressView", "bar_style": "success", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_8a8e81f9d3a54ce49b367f8e984b4a06", "max": 2, "min": 0, "orientation": "horizontal", "style": "IPY_MODEL_bab02b1f092b40c8983cd6440f7eaf16", "value": 2}}, "73c0a01f1693471c9c017143e9e9058b": {"model_module": "@jupyter-widgets/controls", "model_name": "HTMLModel", "model_module_version": "1.5.0", "state": {"_dom_classes": [], "_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "HTMLModel", "_view_count": null, "_view_module": "@jupyter-widgets/controls", "_view_module_version": "1.5.0", "_view_name": "HTMLView", "description": "", "description_tooltip": null, "layout": "IPY_MODEL_94f30dc2653a4f178c9c2ef454d24644", "placeholder": "​", "style": "IPY_MODEL_a508625ef12d4a639fa9773484507709", "value": " 2/2 [00:17&lt;00:00,  8.07s/it]"}}, "ab8174c1337b43048e05aeca72ca18ef": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "5e5a992d86434e62a25fc9b7f75f4b16": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "1507b1310f5045c9b691fdb102cc1686": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}, "8a8e81f9d3a54ce49b367f8e984b4a06": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "bab02b1f092b40c8983cd6440f7eaf16": {"model_module": "@jupyter-widgets/controls", "model_name": "ProgressStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "ProgressStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "bar_color": null, "description_width": ""}}, "94f30dc2653a4f178c9c2ef454d24644": {"model_module": "@jupyter-widgets/base", "model_name": "LayoutModel", "model_module_version": "1.2.0", "state": {"_model_module": "@jupyter-widgets/base", "_model_module_version": "1.2.0", "_model_name": "LayoutModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "LayoutView", "align_content": null, "align_items": null, "align_self": null, "border": null, "bottom": null, "display": null, "flex": null, "flex_flow": null, "grid_area": null, "grid_auto_columns": null, "grid_auto_flow": null, "grid_auto_rows": null, "grid_column": null, "grid_gap": null, "grid_row": null, "grid_template_areas": null, "grid_template_columns": null, "grid_template_rows": null, "height": null, "justify_content": null, "justify_items": null, "left": null, "margin": null, "max_height": null, "max_width": null, "min_height": null, "min_width": null, "object_fit": null, "object_position": null, "order": null, "overflow": null, "overflow_x": null, "overflow_y": null, "padding": null, "right": null, "top": null, "visibility": null, "width": null}}, "a508625ef12d4a639fa9773484507709": {"model_module": "@jupyter-widgets/controls", "model_name": "DescriptionStyleModel", "model_module_version": "1.5.0", "state": {"_model_module": "@jupyter-widgets/controls", "_model_module_version": "1.5.0", "_model_name": "DescriptionStyleModel", "_view_count": null, "_view_module": "@jupyter-widgets/base", "_view_module_version": "1.2.0", "_view_name": "StyleView", "description_width": ""}}}}}, "cells": [{"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "Pv8FH9BMgskk", "outputId": "00cd7f02-2556-4850-b599-1ddec83f7cd9"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.4/76.4 kB\u001b[0m \u001b[31m3.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m112.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m96.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m55.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m11.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m44.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m20.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m3.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m109.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m76.1/76.1 MB\u001b[0m \u001b[31m28.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25h\u001b[31mERROR: pip's dependency resolver does not currently take into account all the packages that are installed. This behaviour is the source of the following dependency conflicts.\n", "google-genai 1.12.1 requires httpx<1.0.0,>=0.28.1, but you have httpx 0.27.2 which is incompatible.\u001b[0m\u001b[31m\n", "\u001b[0mRequirement already satisfied: bitsandbytes in /usr/local/lib/python3.11/dist-packages (0.45.5)\n", "Requirement already satisfied: torch<3,>=2.0 in /usr/local/lib/python3.11/dist-packages (from bitsandbytes) (2.6.0+cu124)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from bitsandbytes) (2.0.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (2025.3.2)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.3.1.170)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch<3,>=2.0->bitsandbytes) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch<3,>=2.0->bitsandbytes) (3.0.2)\n", "Requirement already satisfied: transformers in /usr/local/lib/python3.11/dist-packages (4.51.3)\n", "Requirement already satisfied: accelerate in /usr/local/lib/python3.11/dist-packages (1.6.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from transformers) (3.18.0)\n", "Requirement already satisfied: huggingface-hub<1.0,>=0.30.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.30.2)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2.0.2)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from transformers) (24.2)\n", "Requirement already satisfied: pyyaml>=5.1 in /usr/local/lib/python3.11/dist-packages (from transformers) (6.0.2)\n", "Requirement already satisfied: regex!=2019.12.17 in /usr/local/lib/python3.11/dist-packages (from transformers) (2024.11.6)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from transformers) (2.32.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.21.1)\n", "Requirement already satisfied: safetensors>=0.4.3 in /usr/local/lib/python3.11/dist-packages (from transformers) (0.5.3)\n", "Requirement already satisfied: tqdm>=4.27 in /usr/local/lib/python3.11/dist-packages (from transformers) (4.67.1)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from accelerate) (5.9.5)\n", "Requirement already satisfied: torch>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from accelerate) (2.6.0+cu124)\n", "Requirement already satisfied: fsspec>=2023.5.0 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (2025.3.2)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub<1.0,>=0.30.0->transformers) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.1.6)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.3.1.170)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=2.0.0->accelerate) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=2.0.0->accelerate) (1.3.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.4.1)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (3.10)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests->transformers) (2025.4.26)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=2.0.0->accelerate) (3.0.2)\n"]}], "source": ["!pip install -q requests torch bitsandbytes transformers sentencepiece accelerate openai httpx==0.27.2\n", "!pip install -U bitsandbytes\n", "!pip install -U transformers accelerate"]}, {"cell_type": "code", "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI\n", "from google.colab import drive\n", "from huggingface_hub import login\n", "from google.colab import userdata\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, TextStreamer, BitsAndBytesConfig, pipeline, TextGenerationPipeline\n", "import torch"], "metadata": {"id": "u0qdj2ynjjRz"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["# Models\n", "GPT = 'gpt2'\n", "FALCON = \"tiiuae/falcon-rw-1b\"\n", "MISTRAL = \"mistralai/Mistral-7B-Instruct-v0.1\"\n", "Databricks = \"databricks/dolly-v2-3b\"\n"], "metadata": {"id": "a_sHgTj_jpDE"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["# Sign in to <PERSON>ggingFace Hub\n", "\n", "hf_token = userdata.get('HF_TOKEN')\n", "login(hf_token, add_to_git_credential=True)"], "metadata": {"id": "JYjtu3cPj2Th"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["# Flatten the messages into a single plain prompt\n", "# prompt = \"\"\"\n", "# Generate {{n}} fake job postings for a {{role}} position.\n", "\n", "# Only output a JSON array like:\n", "# [\n", "#   {{\n", "#     \"title\": \"Software Engineer\",\n", "#     \"description\": \"Develop backend APIs and services.\",\n", "#     \"requirements\": [\"Python\", \"FastAPI\", \"MongoDB\"],\n", "#     \"location\": \"San Francisco\",\n", "#     \"company_name\": \"TechCorp\"\n", "#   }},\n", "#   ...\n", "# ]\n", "# Return valid JSON only. No markdown. No explanations.\n", "# \"\"\"\n", "\n", "# prompt = \"\"\"\n", "# Generate exactly {{n}} fake job postings for a {{role}}.\n", "\n", "# Each posting must be a JSON object with:\n", "# - title\n", "# - description (5-10 sentences)\n", "# - requirements (array of 3-5 strings)\n", "# - location\n", "# - company_name\n", "\n", "# Return a single JSON array with {n} items. No explanations. No markdown.\n", "# ONLY the JSON array as output.\n", "# \"\"\"\n", "\n", "prompt = \"\"\"\n", "Generate one fake job posting for a {{role}}.\n", "\n", "Return only a single JSON object with:\n", "- title\n", "- description (5-10 sentences)\n", "- requirements (array of 4-6 strings)\n", "- location\n", "- company_name\n", "\n", "No explanations, no extra text.\n", "Only the JSON object.\n", "\"\"\"\n", "\n", "\n", "\n"], "metadata": {"id": "7IUshG1fkQ7k"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["!pip install safetensors"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-9nzEpDd-dkd", "outputId": "484ed145-951f-4950-f9ba-bf7ed6e30a13"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: safetensors in /usr/local/lib/python3.11/dist-packages (0.5.3)\n"]}]}, {"cell_type": "code", "source": ["import os\n", "os.makedirs(\"/tmp/dolly_offload\", exist_ok=True)"], "metadata": {"id": "D13qucmC-qGr"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "source": ["bnb_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,\n", "    bnb_4bit_use_double_quant=True,\n", "    bnb_4bit_compute_dtype=torch.bfloat16,\n", "    bnb_4bit_quant_type=\"nf4\"\n", ")"], "metadata": {"id": "4qf967BtEqqx"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["def load_model_and_tokenizer():\n", "  tokenizer = AutoTokenizer.from_pretrained(MISTRAL, trust_remote_code=True)\n", "\n", "  model = AutoModelForCausalLM.from_pretrained(\n", "  MISTRAL,\n", "  device_map={\"\": \"cuda\"},\n", "  trust_remote_code=True,\n", "  offload_folder=\"/tmp/dolly_offload\",\n", "  quantization_config=bnb_config\n", "  )\n", "\n", "  return model, tokenizer\n"], "metadata": {"id": "GjV7joEMjujM"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["# generator = pipeline(\"text-generation\", model=Databricks, device_map=\"auto\", trust_remote_code=True, offload_folder=\"/tmp/dolly_offload\")\n", "\n", "def generate_job(role=\"Software Engineer\", model=None, tokenizer=None):\n", "    # prompt = prompt.format(role=role, n=n)\n", "    # outputs = generator(prompt, max_new_tokens=500, do_sample=True, temperature=0.9)\n", "    # return outputs[0]['generated_text']\n", "\n", "    # Apply chat template formatting\n", "    # inputs = tokenizer.apply_chat_template(messages, return_tensors=\"pt\").to(model.device)\n", "    inputs = tokenizer(prompt.format(role=role), return_tensors=\"pt\")\n", "    inputs = {k: v.to(model.device) for k, v in inputs.items()}\n", "\n", "\n", "    # Generate output\n", "    outputs = model.generate(\n", "        **inputs,\n", "        max_new_tokens=600,\n", "        do_sample=True,\n", "        temperature=0.2,\n", "        top_p=0.9,\n", "        pad_token_id=tokenizer.eos_token_id\n", "    )\n", "\n", "    # Decode and return\n", "    result = tokenizer.decode(outputs[0], skip_special_tokens=True)\n", "    return result\n", "\n"], "metadata": {"id": "5w89B0MwkJWo"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["\n", "def generate_jobs(role=\"Software Engineer\", n=5):\n", "  model, tokenizer = load_model_and_tokenizer()\n", "  role = \"Software Engineer\"\n", "  fake_jobs = []\n", "  for i in range(n):\n", "      fake_jobs.append(generate_job(role=role, model=model, tokenizer=tokenizer))\n", "  return fake_jobs"], "metadata": {"id": "ULhKrRe7XZmW"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["print(generate_jobs(role=\"Software Engineer\", n=10))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 406, "referenced_widgets": ["1d1fe06ac632475086ed5964ed000360", "c138f597c98c4944b54d36510ecc8e0b", "bef2531516164e85bb79b86a791dd00d", "1cb9fc011950479a8d4832bc52c3399c", "974e8f7f05ef472d85d5ea71425e6c39", "696090959af8499e9a38777e664b85c1", "973bcc9740b4426da4c680d11f3c1f7e", "3cb5d8fdb5fb4b6a99f6733c00df8378", "58f4369c68434d569d5eb1bc36e71775", "a05df972876941e3b6faab56cc30a4b8", "9c61d90b63dd4fb5a481282d6d6eb8e8", "2b71f87a02a540488a9e07f072f8807a", "548cd7e9fab54470bc52810f27784760", "9c5eb078ece84a57aa9c402c9cad3b0b", "ee00a9f599db4affabb7bf1c4df6ca1a", "52bd638607bf4e1aaf224ebdcfa3693d", "771619a5acd343c788b8189167af09d4", "09a1b30b5659452f95ebb2e72466c750", "145a1f1032a44079a262db381e60d401", "99888ad83b51485f959f977ba4418119", "ec0854c2ea9a4c9280b6876df365db9d", "dac5892c85214f69a5d75d5dc4858dfe", "41b669da565e4204b848b754dfa28ac8", "e806afdada48418c9e353b94a38cd703", "7898b7322b014e96984c3d09a29a57fb", "d665270b05d64effba568ded85eee1b4", "df087de9ade24058b1cf32e1556f7cb6", "584330ab439b4887b1050a7f14dc5d7c", "880b32d3bd1d4af8b5d0b449aab87e8b", "97d09f016e274cca93927f3bd8329352", "d87ef5878c0f4211809716674d0d8413", "556109848b1c4ebc99a6cc7c0be519e0", "8d6cdfd75e3f4a628c9e785d3c469d98", "fb1ff6f4482143c39be1cca57ec2fc8b", "83e6421843ad487c91bc75510b90f198", "9e74a7b74e1a4b119af5b95d572bac3c", "080c34ad56c84c229b1555b15b354aad", "d968bf43e8574d9090326b31c9a7fd93", "e78b05f33ee54c968fd87b77a2470bce", "79a201f7ab7e49efa9e3e1504012dec2", "6e5d431074de4955a97d4ea36621ae36", "bfc581362fbc4aca85df7b2a943dd5e4", "bc9b585bfd2847bb9f22c4720bd19033", "8addd2418c3049f3be32465cc9a408d4", "c7b5bb9ef22f4ebe9969d4d10d63d24c", "d8c3f3ec329743f6b2f21d21601f092a", "2fee19152ef34eeaba541d559b9a0bc0", "2740de6be1ae4e3bacc642c39828883b", "4104813265f34db0ab09c9d6c148ba29", "6d2dbad5a0984f8382abd18910c14343", "32285185818f40a6b07c6d6f6175b70c", "79da3c26e0fb4405a198c2255df9ec00", "c95bea4e04ff49078821a5dd67f0c28a", "3695b9dde85348efb683e31e5d52e210", "1d982bed2d4645b8a19295b7812cef49", "32c58f50bb1c44e085ae3663004fcfff", "c4df70cf509541828d3a06c380fdfe3d", "abd2737f597f48b0846a74c743307917", "a2a52b5e3c104e1cbec513a9f8744db2", "ba57460b8ee24f4e96f8a603914b7073", "d17cd0e49fa94361894660c0645ec9a8", "6cd364a43f6f4ea793b05bf14ee9d687", "a80f72a5e41047f1898d5b6f00a2c69b", "c6f6fca0f35b44fbb9037337a5bc0431", "3d07e648a5644742b8112146e952c44a", "bff978fcc6f94f55bf605c6d9c23cfd2", "eca24e648bcf4cc684f15da684e2791d", "dc82b611b8c145eb8ebc7b80073e9ae1", "f3e6040a241c4ac7b715bb07a9ec6d6b", "e310ab9f4338443e82d257ddc21f48bb", "9dd0e53a7a2a4d668c5640d938b71c9f", "1fc933b90fa546c884181136373ad005", "94f3ee73e2c04092ac5522c6ef038ea1", "81d8563026e04f5ab00eced0da89a7ef", "95f081aaf9e84c2f91c82a4e2f183009", "965cfc093b5040bbaec177820e45ec95", "d328397d81f343e28dd1a6e52c5f0ae7", "f73f9c7f341c4a99b00585343bf4d4bd", "2a11e010825b42d4a949ad64ae0d1933", "15b769156f6a4d2988f1c09f3820f7ef", "a0484e3846c647b892d2de3797496605", "cb042f80aaf04bf1963d637d1771741e", "852ba7d4221a475488411f5014362496", "38dc7c1e65324e3097d8738532272e32", "613da14abc24460db3bb337886cb407c", "37a495a5836f413ea5f662538d51a939", "9c61322c006f465385df301121462e82", "d93d0bb6ebc943a1be6902bd88cef441", "50a2a1bd13db4045a4ae01138470c42b", "ad7cba643d1742cdb47c433bf50072f9", "57ef5d067e7343239525a6da237b29eb", "7567388a58a340d4a0f384f79ee13ddc", "52c2896ab41a4d2592484084cb501e5a", "22957622a42345b991371153c29583c4", "d34c879607b041739a2cc6273509e330", "d1e7bdd4faac4765862fc809017c4856", "fcb0ad846398455faccf0d797549f589", "b381226552c9462d858051fcb7240727", "94e630795bc247e08e6af434c5924cdd", "2b496c218e2049ff9156ff5b3bbdb90b", "62d3b35a3924417894094d3bbf993932", "41737448e98a48dcbe117351645395de", "e83735cd79674a3482f0b90d4c9a3e3d", "eff6ca539e2947e9b2987977f143de9a", "aa75292545a649eda8cb7bab0ac9bbcd", "22c0e2213505435eaeebdfe330b8fbb8", "7de820edeeaf4210af68c721bab3082d", "66ef664b717343bdaf8e5c4610b2a678", "f09534cbda8c4e91b2e073c0eca0cb96", "7d8b5a2a52aa4957bc5905021898d8f4", "1c68a822580a4960acad93be9fd48ce3", "6df81f91b17f41dc91fc9f367fa0afab", "17742936c9ac46e588d1ce42235745d0", "17f0cd6f05184164b48ef906f192505a", "936a67f2de2e44728b83600f4fa0569c", "d5ad82f6b9654a8cb888613caaaaa097", "b014979e237344129545ff2c384c1c1c", "b99c12d57d4a4eab84aefbef58452c32", "5923bbdcf6334393ad832765f129bdec", "260ac8c28531450bba1deac4e4669dc4", "067959a4ef614c498c28bb83c10e16de"]}, "id": "kKsErltXXwy1", "outputId": "683c2e5e-16d8-4fe3-efdd-664c385c71e7"}, "execution_count": 19, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["tokenizer_config.json:   0%|          | 0.00/2.10k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1d1fe06ac632475086ed5964ed000360"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.model:   0%|          | 0.00/493k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2b71f87a02a540488a9e07f072f8807a"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["tokenizer.json:   0%|          | 0.00/1.80M [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "41b669da565e4204b848b754dfa28ac8"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["special_tokens_map.json:   0%|          | 0.00/414 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "fb1ff6f4482143c39be1cca57ec2fc8b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["config.json:   0%|          | 0.00/571 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "c7b5bb9ef22f4ebe9969d4d10d63d24c"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model.safetensors.index.json:   0%|          | 0.00/25.1k [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "32c58f50bb1c44e085ae3663004fcfff"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Fetching 2 files:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "eca24e648bcf4cc684f15da684e2791d"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00001-of-00002.safetensors:   0%|          | 0.00/9.94G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "f73f9c7f341c4a99b00585343bf4d4bd"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["model-00002-of-00002.safetensors:   0%|          | 0.00/4.54G [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "50a2a1bd13db4045a4ae01138470c42b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "2b496c218e2049ff9156ff5b3bbdb90b"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["generation_config.json:   0%|          | 0.00/116 [00:00<?, ?B/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "1c68a822580a4960acad93be9fd48ce3"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["['\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a highly skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining software systems, with a strong understanding of programming languages such as Java or Python. The successful candidate will work collaboratively with our team of developers, designers, and project managers to deliver high-quality software solutions that meet the needs of our clients.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"Minimum of 2 years experience in software development\", \"Strong understanding of programming languages such as Java or Python\", \"Experience with software testing and debugging\", \"Excellent problem-solving skills\", \"Ability to work collaboratively in a team environment\"],\\n  \"location\": \"New York, NY\",\\n  \"company_name\": \"ABC Corporation\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a software engineer to join our team and help develop new features for our products. The ideal candidate will have experience with programming languages such as Java or Python and be familiar with software development methodologies such as Agile. Responsibilities include writing clean, maintainable, and efficient code, collaborating with cross-functional teams, and actively participating in code reviews. This is a full-time position located in San Francisco.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software development\", \"Strong proficiency in Java or Python\", \"Experience with software development methodologies such as Agile\", \"Excellent communication and collaboration skills\", \"Ability to work independently and as part of a team\"],\\n  \"location\": \"San Francisco\",\\n  \"company_name\": \"Acme Inc.\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a highly skilled software engineer to join our team and develop innovative solutions. The ideal candidate will have experience in designing, coding, and testing software systems. Responsibilities include collaborating with cross-functional teams, writing clean and maintainable code, and ensuring timely delivery of projects. Must have a strong understanding of programming languages such as Java, Python, or C++. Knowledge of software development methodologies and tools is a plus. This is a full-time position located in San Francisco, CA.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software engineering\", \"Strong proficiency in Java, Python, or C++\", \"Experience with software development methodologies such as Agile or Scrum\", \"Excellent problem-solving and communication skills\", \"Ability to work independently and as part of a team\", \"Strong attention to detail and ability to manage multiple projects simultaneously\"],\\n  \"location\": \"San Francisco, CA\",\\n  \"company_name\": \"ABC Tech\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a software engineer to join our team and help develop new features for our products. The ideal candidate will have experience with programming languages such as Java or Python and be familiar with software development methodologies such as Agile. Responsibilities include writing clean, maintainable code, collaborating with cross-functional teams, and actively participating in code reviews. This is a full-time position located in our office in San Francisco.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"2+ years of experience in software development\", \"Strong proficiency in Java or Python\", \"Familiarity with software development methodologies such as Agile\", \"Excellent communication and collaboration skills\"],\\n  \"location\": \"San Francisco\",\\n  \"company_name\": \"Acme Inc.\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a software engineer to join our team in developing and maintaining our software applications. The ideal candidate will have experience in programming languages such as Java, Python, or C++ and be familiar with software development methodologies such as Agile and Scrum. Responsibilities include writing clean and efficient code, collaborating with cross-functional teams, and actively participating in code reviews. This is a full-time position located in San Francisco, CA.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software development\", \"Strong proficiency in Java, Python, or C++\", \"Experience with Agile and Scrum methodologies\", \"Excellent communication and collaboration skills\"],\\n  \"location\": \"San Francisco, CA\",\\n  \"company_name\": \"Acme Inc.\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n    \"title\": \"Software Engineer\",\\n    \"description\": \"We are looking for a highly skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining complex software systems. Responsibilities include writing clean and efficient code, collaborating with cross-functional teams, and actively participating in code reviews. Must have a strong understanding of data structures, algorithms, and software design patterns. This is a full-time position located in San Francisco, CA.\",\\n    \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software development\", \"Strong proficiency in Java or C++\", \"Experience with agile development methodologies\", \"Excellent problem-solving skills\", \"Ability to work independently and as part of a team\", \"Strong communication and interpersonal skills\"],\\n    \"location\": \"San Francisco, CA\",\\n    \"company_name\": \"Acme Inc.\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a talented software engineer to join our team in developing and maintaining our web applications. The ideal candidate will have a strong background in computer science and experience with various programming languages such as Java, Python, and JavaScript. Responsibilities include writing clean and efficient code, collaborating with cross-functional teams, and actively participating in code reviews. This is a full-time position located in our office in New York City.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software development\", \"Strong proficiency in Java, Python, and JavaScript\", \"Experience with web development frameworks such as React and Angular\", \"Good understanding of data structures and algorithms\", \"Excellent problem-solving skills\", \"Ability to work independently and within a team environment\", \"Strong communication and interpersonal skills\"],\\n  \"location\": \"New York City\",\\n  \"company_name\": \"ABC Company\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a software engineer to join our team in developing and maintaining our web applications. The ideal candidate will have experience in front-end and back-end development, proficiency in JavaScript, HTML, CSS, and SQL. They will also be responsible for ensuring the functionality and performance of our applications. This is a full-time position located in New York City.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software development\", \"Strong knowledge of JavaScript, HTML, CSS, and SQL\", \"Experience with front-end frameworks such as React or Angular\", \"Familiarity with back-end technologies such as Node.js or Python\", \"Ability to work independently and as part of a team\", \"Excellent problem-solving skills\", \"Strong communication and interpersonal skills\"],\\n  \"location\": \"New York City\",\\n  \"company_name\": \"ABC Company\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a skilled software engineer to join our team in developing and maintaining our software applications. The ideal candidate will have experience in programming languages such as Java, Python, and C++, and will be able to work independently or as part of a team. Responsibilities include writing clean and efficient code, testing and debugging software, and collaborating with other team members. This is a full-time position located in New York City.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"3+ years of experience in software development\", \"Strong proficiency in Java, Python, and C++\", \"Experience with agile development methodologies\", \"Excellent problem-solving and communication skills\"],\\n  \"location\": \"New York City\",\\n  \"company_name\": \"ABC Corporation\"\\n}\\n```', '\\nGenerate one fake job posting for a {role}.\\n\\nReturn only a single JSON object with:\\n- title\\n- description (5-10 sentences)\\n- requirements (array of 4-6 strings)\\n- location\\n- company_name\\n\\nNo explanations, no extra text.\\nOnly the JSON object.\\n\\n```json\\n{\\n  \"title\": \"Software Engineer\",\\n  \"description\": \"We are looking for a software engineer to join our team and help develop new features for our products. The ideal candidate will have experience with programming languages such as Java or Python and be familiar with software development methodologies such as Agile. Responsibilities include writing clean, maintainable code, collaborating with cross-functional teams, and actively participating in code reviews. This is a full-time position located in our office in San Francisco.\",\\n  \"requirements\": [\"Bachelor\\'s degree in Computer Science or related field\", \"2+ years of experience in software development\", \"Strong proficiency in Java or Python\", \"Experience with Agile software development methodologies\", \"Excellent communication and collaboration skills\"],\\n  \"location\": \"San Francisco\",\\n  \"company_name\": \"Acme Inc.\"\\n}\\n```']\n"]}]}, {"cell_type": "code", "source": ["!pip install -U bitsandbytes"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "OYEsZ10YSgHv", "outputId": "67dc4a86-56ef-4fe8-8c3c-c0b5e13ac76a"}, "execution_count": 20, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Requirement already satisfied: bitsandbytes in /usr/local/lib/python3.11/dist-packages (0.45.5)\n", "Requirement already satisfied: torch<3,>=2.0 in /usr/local/lib/python3.11/dist-packages (from bitsandbytes) (2.6.0+cu124)\n", "Requirement already satisfied: numpy>=1.17 in /usr/local/lib/python3.11/dist-packages (from bitsandbytes) (2.0.2)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.4.2)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (2025.3.2)\n", "Requirement already satisfied: nvidia-cuda-nvrtc-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-runtime-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-cuda-cupti-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-cudnn-cu12==9.1.0.70 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (9.1.0.70)\n", "Requirement already satisfied: nvidia-cublas-cu12==12.4.5.8 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.5.8)\n", "Requirement already satisfied: nvidia-cufft-cu12==11.2.1.3 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (11.2.1.3)\n", "Requirement already satisfied: nvidia-curand-cu12==10.3.5.147 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (10.3.5.147)\n", "Requirement already satisfied: nvidia-cusolver-cu12==11.6.1.9 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (11.6.1.9)\n", "Requirement already satisfied: nvidia-cusparse-cu12==12.3.1.170 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.3.1.170)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: nvidia-nvjitlink-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (12.4.127)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch<3,>=2.0->bitsandbytes) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch<3,>=2.0->bitsandbytes) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch<3,>=2.0->bitsandbytes) (3.0.2)\n"]}]}, {"cell_type": "code", "source": ["import re\n", "import json\n", "import ast\n", "\n", "\n", "\n", "def extract_json_objects_from_text_block(texts):\n", "    \"\"\"\n", "    Accepts either a single string or a list of strings.\n", "    Extracts all valid JSON objects from messy text blocks.\n", "    \"\"\"\n", "    if isinstance(texts, str):\n", "        texts = [texts]  # wrap in list if single string\n", "\n", "    pattern = r\"\\{[\\s\\S]*?\\}\"\n", "    results = []\n", "\n", "    for raw_text in texts:\n", "        matches = re.findall(pattern, raw_text)\n", "        for match in matches:\n", "            try:\n", "                obj = json.loads(match)\n", "                results.append(obj)\n", "            except json.JSONDecodeError:\n", "                continue\n", "\n", "    return results\n", "\n", "text = generate_jobs(role=\"Software Engineer\", n=10)\n", "print(extract_json_objects_from_text_block(text))"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 86, "referenced_widgets": ["b5dd409cf6e04764adbb7c2a49b7be86", "65187b4ebb2041b39778268e8b4d6b0d", "33317cac10ca4a98bf4433c1eff43435", "f81f5402902c4c04b10895782287e908", "c471914fe0d34ae8967bac2820637d5b", "7aead6f6cffa40a383f1b8c64943329e", "f24fe57d8e164fd68185b4c117e7c097", "f913ca9ab6d44ab1b788a36bd964ed39", "ed34016801264a05bb3697eca2ac22ef", "fe622254072540fda3b0dd6b2cab6e4a", "5d95bdea47594e21855a6e564d0760da"]}, "id": "1uzTM2G1oqDs", "outputId": "08e88ab0-ca17-46d3-8f9c-6a595863aeba"}, "execution_count": 22, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "b5dd409cf6e04764adbb7c2a49b7be86"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["[{'title': 'Software Engineer', 'description': 'We are looking for a highly skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining complex software systems. Responsibilities include writing clean, efficient, and testable code, collaborating with cross-functional teams, and actively participating in code reviews. Must have a strong understanding of data structures, algorithms, and software design patterns. This is a full-time position located in San Francisco, CA.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", '3+ years of experience in software development', 'Strong proficiency in Java or C++', 'Experience with agile development methodologies', 'Excellent problem-solving and analytical skills', 'Ability to work independently and as part of a team', 'Strong written and verbal communication skills'], 'location': 'San Francisco, CA', 'company_name': 'Acme Inc.'}, {'title': 'Software Engineer', 'description': 'We are looking for a highly skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining complex software systems. Responsibilities include writing clean, efficient, and testable code, collaborating with cross-functional teams, and actively participating in code reviews. Must have a strong understanding of software design patterns and best practices. This is a full-time position located in San Francisco.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", '5+ years of experience in software development', 'Strong proficiency in Java or C++', 'Experience with agile development methodologies', 'Excellent problem-solving skills', 'Ability to work independently and as part of a team', 'Strong communication and interpersonal skills'], 'location': 'San Francisco', 'company_name': 'Acme Inc.'}, {'title': 'Software Engineer', 'description': 'We are looking for a highly skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining complex software systems. Responsibilities include writing clean, efficient, and testable code, collaborating with cross-functional teams, and actively participating in code reviews. This is a full-time position located in San Francisco.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", '5+ years of experience in software development', 'Strong proficiency in Java or C++', 'Experience with agile development methodologies', 'Excellent problem-solving and communication skills'], 'location': 'San Francisco', 'company_name': 'Acme Inc.'}, {'title': 'Software Engineer', 'description': 'We are looking for a talented software engineer to join our team. The ideal candidate will have experience in developing and maintaining software systems, with a strong background in computer science or a related field. Responsibilities include writing clean and efficient code, collaborating with cross-functional teams, and actively participating in code reviews. This is an excellent opportunity for a self-starter who is passionate about technology and eager to learn and grow.', 'requirements': [\"Bachelor's degree in Computer Science or a related field\", '2+ years of experience in software development', 'Strong proficiency in Java or C++', 'Experience with agile development methodologies', 'Excellent problem-solving skills', 'Ability to work independently and as part of a team'], 'location': 'New York, NY', 'company_name': 'ABC Corporation'}, {'title': 'Software Engineer', 'description': 'We are looking for a highly skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining complex software systems. Responsibilities include writing clean and efficient code, collaborating with cross-functional teams, and actively participating in code reviews. Must have a strong understanding of data structures, algorithms, and software design patterns. This is a full-time position located in San Francisco, CA.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", '3+ years of experience in software development', 'Strong proficiency in Java or C++', 'Experience with agile development methodologies', 'Excellent problem-solving skills', 'Ability to work independently and as part of a team'], 'location': 'San Francisco, CA', 'company_name': 'Acme Inc.'}, {'title': 'Software Engineer', 'description': 'We are seeking a highly skilled software engineer to join our team and develop innovative software solutions. The ideal candidate will have a strong background in computer science and experience with various programming languages. Responsibilities include designing, coding, testing and maintaining software systems, as well as collaborating with cross-functional teams. Must have excellent problem-solving skills and the ability to work independently in a fast-paced environment. This is a full-time position located in San Francisco.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", 'Minimum of 3 years experience in software development', 'Strong proficiency in Java, Python, or C++', 'Experience with agile development methodologies', 'Excellent communication and collaboration skills'], 'location': 'San Francisco', 'company_name': 'Acme Inc.'}, {'title': 'Software Engineer', 'description': 'We are looking for a skilled software engineer to join our team. The ideal candidate will have experience in developing and maintaining software systems, with a strong understanding of programming languages such as Java or Python. Responsibilities include writing clean and efficient code, collaborating with cross-functional teams, and ensuring the timely delivery of high-quality software products. This is an excellent opportunity for a self-starter who is passionate about technology and eager to grow in their career.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", 'Minimum of 2 years experience in software development', 'Strong proficiency in Java or Python', 'Experience with agile development methodologies', 'Excellent problem-solving and analytical skills', 'Ability to work independently and as part of a team'], 'location': 'New York', 'company_name': 'ABC Corporation'}, {'title': 'Software Engineer', 'description': 'We are looking for a skilled software engineer to join our team and contribute to the development of innovative software solutions. The ideal candidate will have experience in designing, developing and testing software systems, and be able to work collaboratively with cross-functional teams. Responsibilities include writing clean, efficient and testable code, as well as actively participating in code reviews and continuous integration processes. This is an excellent opportunity for a self-starter with a passion for technology and a desire to grow in their career.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", '2+ years of experience in software development', 'Strong proficiency in Java or C++', 'Experience with agile development methodologies', 'Good understanding of data structures and algorithms', 'Excellent problem-solving skills', 'Ability to work independently and as part of a team', 'Strong written and verbal communication skills'], 'location': 'New York', 'company_name': 'ABC Technologies'}, {'title': 'Software Engineer', 'description': 'We are seeking a highly skilled software engineer to join our team in developing and maintaining cutting-edge software systems. The ideal candidate will have a strong background in computer science and be proficient in multiple programming languages. Responsibilities include designing, coding, testing, and debugging software applications, as well as collaborating with cross-functional teams. This is an exciting opportunity for a creative and innovative problem solver to make a significant impact in the tech industry.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", 'Minimum of 2 years experience in software development', 'Strong proficiency in Java, Python, or C++', 'Experience with agile development methodologies', 'Excellent analytical and problem-solving skills'], 'location': 'New York, NY', 'company_name': 'ABC Tech Inc.'}, {'title': 'Software Engineer', 'description': 'We are looking for a talented software engineer to join our team. Responsibilities include developing and maintaining software systems, collaborating with cross-functional teams, and ensuring timely delivery of high-quality products. The ideal candidate will have a strong background in computer science and experience with programming languages such as Java, Python, or C++. Must be able to work independently and within a team environment. Strong problem-solving and communication skills required. This is a full-time position located in New York.', 'requirements': [\"Bachelor's degree in Computer Science or related field\", '3+ years of experience in software development', 'Strong proficiency in Java, Python, or C++', 'Experience with agile development methodologies', 'Excellent problem-solving and communication skills', 'Ability to work independently and within a team environment'], 'location': 'New York', 'company_name': 'ABC Company'}]\n"]}]}, {"cell_type": "code", "source": ["!pip install gradio"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "lsLBgN1TMj0i", "outputId": "a1e1a45d-62a1-4704-8b86-fb155e68e684"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting gradio\n", "  Downloading gradio-5.29.0-py3-none-any.whl.metadata (16 kB)\n", "Collecting aiofiles<25.0,>=22.0 (from gradio)\n", "  Downloading aiofiles-24.1.0-py3-none-any.whl.metadata (10 kB)\n", "Requirement already satisfied: anyio<5.0,>=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (4.9.0)\n", "Collecting fastapi<1.0,>=0.115.2 (from gradio)\n", "  Downloading fastapi-0.115.12-py3-none-any.whl.metadata (27 kB)\n", "Collecting ffmpy (from gradio)\n", "  Downloading ffmpy-0.5.0-py3-none-any.whl.metadata (3.0 kB)\n", "Collecting gradio-client==1.10.0 (from gradio)\n", "  Downloading gradio_client-1.10.0-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting groovy~=0.1 (from gradio)\n", "  Downloading groovy-0.1.2-py3-none-any.whl.metadata (6.1 kB)\n", "Requirement already satisfied: httpx>=0.24.1 in /usr/local/lib/python3.11/dist-packages (from gradio) (0.27.2)\n", "Requirement already satisfied: huggingface-hub>=0.28.1 in /usr/local/lib/python3.11/dist-packages (from gradio) (0.30.2)\n", "Requirement already satisfied: jinja2<4.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (3.1.6)\n", "Requirement already satisfied: markupsafe<4.0,>=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (3.0.2)\n", "Requirement already satisfied: numpy<3.0,>=1.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (2.0.2)\n", "Requirement already satisfied: orjson~=3.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (3.10.17)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from gradio) (24.2)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (2.2.2)\n", "Requirement already satisfied: pillow<12.0,>=8.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (11.2.1)\n", "Requirement already satisfied: pydantic<2.12,>=2.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (2.11.3)\n", "Collecting pydub (from gradio)\n", "  Downloading pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting python-multipart>=0.0.18 (from gradio)\n", "  Downloading python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: pyyaml<7.0,>=5.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (6.0.2)\n", "Collecting ruff>=0.9.3 (from gradio)\n", "  Downloading ruff-0.11.8-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (25 kB)\n", "Collecting safehttpx<0.2.0,>=0.1.6 (from gradio)\n", "  Downloading safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)\n", "Collecting semantic-version~=2.0 (from gradio)\n", "  Downloading semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Collecting starlette<1.0,>=0.40.0 (from gradio)\n", "  Downloading starlette-0.46.2-py3-none-any.whl.metadata (6.2 kB)\n", "Collecting tomlkit<0.14.0,>=0.12.0 (from gradio)\n", "  Downloading tomlkit-0.13.2-py3-none-any.whl.metadata (2.7 kB)\n", "Requirement already satisfied: typer<1.0,>=0.12 in /usr/local/lib/python3.11/dist-packages (from gradio) (0.15.3)\n", "Requirement already satisfied: typing-extensions~=4.0 in /usr/local/lib/python3.11/dist-packages (from gradio) (4.13.2)\n", "Collecting uvicorn>=0.14.0 (from gradio)\n", "  Downloading uvicorn-0.34.2-py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from gradio-client==1.10.0->gradio) (2025.3.2)\n", "Requirement already satisfied: websockets<16.0,>=10.0 in /usr/local/lib/python3.11/dist-packages (from gradio-client==1.10.0->gradio) (15.0.1)\n", "Requirement already satisfied: idna>=2.8 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio) (3.10)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5.0,>=3.0->gradio) (1.3.1)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from httpx>=0.24.1->gradio) (2025.4.26)\n", "Requirement already satisfied: httpcore==1.* in /usr/local/lib/python3.11/dist-packages (from httpx>=0.24.1->gradio) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in /usr/local/lib/python3.11/dist-packages (from httpcore==1.*->httpx>=0.24.1->gradio) (0.16.0)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from huggingface-hub>=0.28.1->gradio) (3.18.0)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from huggingface-hub>=0.28.1->gradio) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in /usr/local/lib/python3.11/dist-packages (from huggingface-hub>=0.28.1->gradio) (4.67.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas<3.0,>=1.0->gradio) (2025.2)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<2.12,>=2.0->gradio) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in /usr/local/lib/python3.11/dist-packages (from pydantic<2.12,>=2.0->gradio) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<2.12,>=2.0->gradio) (0.4.0)\n", "Requirement already satisfied: click>=8.0.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in /usr/local/lib/python3.11/dist-packages (from typer<1.0,>=0.12->gradio) (13.9.4)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio) (1.17.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /usr/local/lib/python3.11/dist-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.19.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface-hub>=0.28.1->gradio) (3.4.1)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests->huggingface-hub>=0.28.1->gradio) (2.4.0)\n", "Requirement already satisfied: mdurl~=0.1 in /usr/local/lib/python3.11/dist-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio) (0.1.2)\n", "Downloading gradio-5.29.0-py3-none-any.whl (54.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m54.1/54.1 MB\u001b[0m \u001b[31m46.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gradio_client-1.10.0-py3-none-any.whl (322 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m322.9/322.9 kB\u001b[0m \u001b[31m34.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading aiofiles-24.1.0-py3-none-any.whl (15 kB)\n", "Downloading fastapi-0.115.12-py3-none-any.whl (95 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m95.2/95.2 kB\u001b[0m \u001b[31m10.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading groovy-0.1.2-py3-none-any.whl (14 kB)\n", "Downloading python_multipart-0.0.20-py3-none-any.whl (24 kB)\n", "Downloading ruff-0.11.8-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (11.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m11.5/11.5 MB\u001b[0m \u001b[31m131.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading safehttpx-0.1.6-py3-none-any.whl (8.7 kB)\n", "Downloading semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)\n", "Downloading starlette-0.46.2-py3-none-any.whl (72 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m72.0/72.0 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading tomlkit-0.13.2-py3-none-any.whl (37 kB)\n", "Downloading uvicorn-0.34.2-py3-none-any.whl (62 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m62.5/62.5 kB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ffmpy-0.5.0-py3-none-any.whl (6.0 kB)\n", "Downloading pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Installing collected packages: pydub, uvicorn, tomlkit, semantic-version, ruff, python-multipart, groovy, ffmpy, aiofiles, starlette, safehttpx, gradio-client, fastapi, gradio\n", "Successfully installed aiofiles-24.1.0 fastapi-0.115.12 ffmpy-0.5.0 gradio-5.29.0 gradio-client-1.10.0 groovy-0.1.2 pydub-0.25.1 python-multipart-0.0.20 ruff-0.11.8 safehttpx-0.1.6 semantic-version-2.10.0 starlette-0.46.2 tomlkit-0.13.2 uvicorn-0.34.2\n"]}]}, {"cell_type": "code", "source": ["import gradio as gr\n", "import json\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "import re\n", "\n", "def generate_ui(role, n):\n", "    try:\n", "        raw_jobs = generate_jobs(role, n)\n", "        parsed_jobs = extract_json_objects_from_text_block(raw_jobs)\n", "\n", "        if not isinstance(parsed_jobs, list) or not all(isinstance(item, dict) for item in parsed_jobs):\n", "            print(\"[ERROR] Parsed result is not a list of dicts\")\n", "            return gr.update(value=[], visible=True), None\n", "\n", "        filename = f\"{role.replace(' ', '_').lower()}_jobs.json\"\n", "        with open(filename, \"w\") as f:\n", "            json.dump(parsed_jobs, f, indent=2)\n", "\n", "        print(f\"[INFO] Returning {len(parsed_jobs)} jobs -> {filename}\")\n", "        return parsed_jobs, filename\n", "\n", "    except Exception as e:\n", "        print(f\"[FATAL ERROR] {e}\")\n", "        return gr.update(value=[], visible=True), None\n", "\n", "if __name__ == \"__main__\":\n", "  with gr.<PERSON><PERSON>() as demo:\n", "      gr.Markdown(\"# 🧠 Synthetic Job Dataset Generator\")\n", "      gr.Mark<PERSON>(\"Generate a structured dataset of job postings for a specific role.\")\n", "\n", "      with gr.<PERSON>():\n", "          role_input = gr.Textbox(label=\"Job Role\", placeholder=\"e.g. Software Engineer\", value=\"Software Engineer\")\n", "          n_input = gr.Number(label=\"Number of Samples\", value=5, precision=0)\n", "\n", "      generate_button = gr.<PERSON><PERSON>(\"🚀 Generate\")\n", "      output_table = gr.J<PERSON>(label=\"Generated Dataset\")\n", "      download_button = gr.File(label=\"Download JSON\")\n", "\n", "      generate_button.click(\n", "          generate_ui,\n", "          inputs=[role_input, n_input],\n", "          outputs=[output_table, download_button]\n", "      )\n", "\n", "  demo.launch(debug=True)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 730, "referenced_widgets": ["9f9defc39ac5437e9512e5fad810b409", "1c126dfdc51c438b9b48c8a65e549ae2", "741d800130ea4830b9266f467fa6a0bf", "73c0a01f1693471c9c017143e9e9058b", "ab8174c1337b43048e05aeca72ca18ef", "5e5a992d86434e62a25fc9b7f75f4b16", "1507b1310f5045c9b691fdb102cc1686", "8a8e81f9d3a54ce49b367f8e984b4a06", "bab02b1f092b40c8983cd6440f7eaf16", "94f30dc2653a4f178c9c2ef454d24644", "a508625ef12d4a639fa9773484507709"]}, "id": "FEByigZTo5cv", "outputId": "e452754b-e155-4b57-eced-7af37996f1f0"}, "execution_count": 25, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["It looks like you are running Gradio on a hosted a Jupyter notebook. For the Gradio app to work, sharing must be enabled. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. This cell will run indefinitely so that you can see errors and logs. To turn off, set debug=False in launch().\n", "* Running on public URL: https://bf27145eb99f8caadd.gradio.live\n", "\n", "This share link expires in 1 week. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://bf27145eb99f8caadd.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"], "application/vnd.jupyter.widget-view+json": {"version_major": 2, "version_minor": 0, "model_id": "9f9defc39ac5437e9512e5fad810b409"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["[INFO] Returning 5 jobs -> software_engineer_jobs.json\n", "Keyboard interruption in main thread... closing server.\n", "Killing tunnel 127.0.0.1:7860 <> https://bf27145eb99f8caadd.gradio.live\n"]}]}, {"cell_type": "code", "source": ["import os\n", "\n", "# Get list of all .ipynb files in /content\n", "notebooks = [f for f in os.listdir(\"/content\") if f.endswith(\".ipynb\")]\n", "print(notebooks)\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZfPQJw0Z5UD9", "outputId": "0e4ba82b-e23b-4faa-8b29-eaf87fdf9500"}, "execution_count": 27, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["[]\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "Y88jqI_u5WEL"}, "execution_count": null, "outputs": []}]}