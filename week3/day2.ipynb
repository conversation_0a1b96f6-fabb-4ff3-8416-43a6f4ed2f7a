{"cells": [{"cell_type": "markdown", "id": "1f2ed49d-7873-4a03-bdc8-bda1c23cb979", "metadata": {}, "source": ["# HuggingFace pipelines\n", "\n", "For this session we head to Google Colab and use this Notebook to explore the HuggingFace High Level API, pipelines.\n", "\n", "https://colab.research.google.com/drive/1aMaEw8A56xs0bRM4lu8z7ou18jqyybGm?usp=sharing\n", "\n", "You can use a low cost (or free) T4 GPU runtime for this notebook - and the results look great!\n", "\n", "There are instructions in the notebook for setting up your HuggingFace Token and including it as a secret in the notebook."]}, {"cell_type": "code", "execution_count": null, "id": "d5d84b78-0a02-4a35-8f10-e3f7451d352f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}