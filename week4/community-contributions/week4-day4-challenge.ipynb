{"cells": [{"cell_type": "markdown", "id": "4a6ab9a2-28a2-445d-8512-a0dc8d1b54e9", "metadata": {}, "source": ["# Code Generator\n", "\n", "The requirement: use an Open Source model to generate high performance C++ code from Python code\n", "\n", "To replicate this, you'll need to set up a HuggingFace endpoint as I do in the video. It's simple to do, and it's quite satisfying to see the results!\n", "\n", "It's also an important part of your learning; this is the first example of deploying an open source model to be behind an API. We'll return to this in Week 8, but this should plant a seed in your mind for what's involved in moving open source models into production."]}, {"cell_type": "markdown", "id": "22e1567b-33fd-49e7-866e-4b635d15715a", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h1 style=\"color:#900;\">Important - Pause Endpoints when not in use</h1>\n", "            <span style=\"color:#900;\">\n", "            If you do decide to use HuggingFace endpoints for this project, you should stop or pause the endpoints when you are done to avoid accruing unnecessary running cost. The costs are very low as long as you only run the endpoint when you're using it. Navigate to the HuggingFace endpoint UI <a href=\"https://ui.endpoints.huggingface.co/\">here,</a> open your endpoint, and click Pause to put it on pause so you no longer pay for it.  \n", "Many thanks to student <PERSON> for raising this.\n", "<br/><br/>\n", "In week 8 we will use Modal instead of HuggingFace endpoints; with Modal you only pay for the time that you use it and you should get free credits.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 51, "id": "e610bf56-a46e-4aff-8de1-ab49d62b1ad3", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import io\n", "import sys\n", "import json\n", "import requests\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import google.generativeai\n", "import anthropic\n", "from IPython.display import Markdown, display, update_display\n", "import gradio as gr\n", "import subprocess\n", "import platform"]}, {"cell_type": "code", "execution_count": 52, "id": "4f672e1c-87e9-4865-b760-370fa605e614", "metadata": {}, "outputs": [], "source": ["# environment\n", "\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['ANTHROPIC_API_KEY'] = os.getenv('ANTHROPIC_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['HF_TOKEN'] = os.getenv('HF_TOKEN', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": 53, "id": "8aa149ed-9298-4d69-8fe2-8f5de0f667da", "metadata": {}, "outputs": [], "source": ["# initialize\n", "\n", "openai = OpenAI()\n", "claude = anthropic.Anthropic()\n", "OPENAI_MODEL = \"gpt-4o\"\n", "CLAUDE_MODEL = \"claude-3-5-sonnet-20240620\""]}, {"cell_type": "code", "execution_count": 166, "id": "6896636f-923e-4a2c-9d6c-fac07828a201", "metadata": {}, "outputs": [], "source": ["# Define the different actions available\n", "\n", "prompt_options = [\"Convert to C\", \"Add comments\", \"Write unit tests\"]\n", "\n", "system_prompts = {\n", "    prompt_options[0]:  \"\"\"\n", "You are an assistant that reimplements Python code in high performance C++.\n", "Respond only with C++ code; use comments sparingly and do not provide any explanation other than occasional comments.\n", "The C++ response needs to produce an identical output in the fastest possible time. Keep implementations of random number generators identical so that results match exactly.\n", "\"\"\",\n", "    \n", "    prompt_options[1]:  \"\"\"\n", "You are an assistant that adds succinct comments and docstrings to Python code. Respond only with valid Python code.\n", "\"\"\",\n", "    \n", "    prompt_options[2]:  \"\"\"\n", "You are an assistant that creates unit tests for Python code. Respond only with valid Python code.\n", "\"\"\"\n", "}\n", "\n", "user_prompts = {\n", "    prompt_options[0]: \"\"\"\n", "Rewrite this Python code in C++ with the fastest possible implementation that produces identical output in the least time. \n", "Respond only with C++ code; do not explain your work other than a few comments.\n", "Pay attention to number types to ensure no int overflows. Remember to #include all necessary C++ packages such as iomanip.\\n\\n\n", "\"\"\",\n", "    \n", "    prompt_options[1]:  \"\"\"\n", "Keep this Python code but insert appropriate comments and docstrings.\n", "\"\"\",\n", "    \n", "    prompt_options[2]:  \"\"\"\n", "Create unit tests for this Python code.\n", "\"\"\"\n", "}"]}, {"cell_type": "code", "execution_count": 157, "id": "a1cbb778-fa57-43de-b04b-ed523f396c38", "metadata": {}, "outputs": [], "source": ["python_sample_options = [\"Hello, World\", \"Calculate pi\", \"<PERSON><PERSON><PERSON>'s Algorithm\", \"Si<PERSON> of Eratosthenes\"]\n", "\n", "python_code_samples = {\n", "    python_sample_options[0]: \"\"\"\n", "import time\n", "\n", "start_time = time.time()\n", "\n", "print(\"Hello, world\")\n", "\n", "end_time = time.time()\n", "\n", "print(f\"Execution Time: {(end_time - start_time):.6f} seconds\")\n", "\"\"\",\n", "\n", "    python_sample_options[1]:  \"\"\"\n", "import time\n", "\n", "def calculate(iterations, param1, param2):\n", "    result = 1.0\n", "    for i in range(1, iterations+1):\n", "        j = i * param1 - param2\n", "        result -= (1/j)\n", "        j = i * param1 + param2\n", "        result += (1/j)\n", "    return result\n", "\n", "start_time = time.time()\n", "result = calculate(100_000_000, 4, 1) * 4\n", "end_time = time.time()\n", "\n", "print(f\"Result: {result:.12f}\")\n", "print(f\"Execution Time: {(end_time - start_time):.6f} seconds\")\n", "\"\"\",\n", "\n", "    python_sample_options[2]:  \"\"\"\n", "# Be careful to support large number sizes\n", "\n", "def lcg(seed, a=1664525, c=1013904223, m=2**32):\n", "    value = seed\n", "    while True:\n", "        value = (a * value + c) % m\n", "        yield value\n", "        \n", "def max_subarray_sum(n, seed, min_val, max_val):\n", "    lcg_gen = lcg(seed)\n", "    random_numbers = [next(lcg_gen) % (max_val - min_val + 1) + min_val for _ in range(n)]\n", "    max_sum = float('-inf')\n", "    for i in range(n):\n", "        current_sum = 0\n", "        for j in range(i, n):\n", "            current_sum += random_numbers[j]\n", "            if current_sum > max_sum:\n", "                max_sum = current_sum\n", "    return max_sum\n", "\n", "def total_max_subarray_sum(n, initial_seed, min_val, max_val):\n", "    total_sum = 0\n", "    lcg_gen = lcg(initial_seed)\n", "    for _ in range(20):\n", "        seed = next(lcg_gen)\n", "        total_sum += max_subarray_sum(n, seed, min_val, max_val)\n", "    return total_sum\n", "\n", "# Parameters\n", "n = 10000         # Number of random numbers\n", "initial_seed = 42 # Initial seed for the LCG\n", "min_val = -10     # Minimum value of random numbers\n", "max_val = 10      # Maximum value of random numbers\n", "\n", "# Timing the function\n", "import time\n", "start_time = time.time()\n", "result = total_max_subarray_sum(n, initial_seed, min_val, max_val)\n", "end_time = time.time()\n", "\n", "print(\"Total Maximum Subarray Sum (20 runs):\", result)\n", "print(\"Execution Time: {:.6f} seconds\".format(end_time - start_time))\n", "\"\"\",\n", "\n", "    python_sample_options[3]:  \"\"\"\n", "import time\n", "start_time = time.time()\n", "stop_at=100_000_000\n", "prime = [True] * (stop_at + 1)\n", "p = 2\n", "\n", "while p * p <= stop_at:\n", "    # If prime[p] is True, then p is a prime\n", "    if prime[p]:\n", "        # Mark all multiples of p as non-prime\n", "        for i in range(p * p, stop_at + 1, p):\n", "            prime[i] = False\n", "    p += 1\n", "\n", "# Collect all prime numbers\n", "primes = [p for p in range(2, stop_at + 1) if prime[p]]\n", "\n", "end_time = time.time()\n", "print(\"Maximum prime:, {:,}\".format(primes[-1]))\n", "print(\"Execution Time: {:.6f} seconds\".format(end_time - start_time))\n", "\"\"\"\n", "}"]}, {"cell_type": "code", "execution_count": 56, "id": "e33565c0-cba8-46d3-a0c5-9440d7fe4d2c", "metadata": {}, "outputs": [], "source": ["# Create a complete user prompt given descriptive text plus the python code to convert.\n", "def create_user_prompt(user_prompt, python_code):\n", "    return user_prompt + '\\n' + python_code\n", "\n", "# Create the list the GPT. <PERSON> doesn't need this because it does not combine the system and user prompts.\n", "def create_messages_for_gpt(system_prompt, user_prompt):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]"]}, {"cell_type": "code", "execution_count": 57, "id": "71e1ba8c-5b05-4726-a9f3-8d8c6257350b", "metadata": {}, "outputs": [], "source": ["def write_cpp_file(filename_base, cpp_code):\n", "    code_to_write = cpp.replace(\"```cpp\",\"\").replace(\"```\",\"\")\n", "    with open(f\"{filename_base}.cpp\", \"w\") as f:\n", "        f.write(code)"]}, {"cell_type": "code", "execution_count": 164, "id": "e7d2fea8-74c6-4421-8f1e-0e76d5b201b9", "metadata": {}, "outputs": [], "source": ["# This is where additional models can be easily added. \n", "# Just update the model_options list, add a streaming function, and update the call_llm function. \n", "\n", "model_options = [\"GPT\", \"Claude\"]\n", "# model_options = [\"GP<PERSON>\", \"<PERSON>\", \"CodeQwen\"]\n", "default_model = model_options[0]\n", "\n", "def stream_gpt(system_prompt, user_prompt, python_code):    \n", "    stream = openai.chat.completions.create(model=OPENAI_MODEL, messages=create_messages_for_gpt(system_prompt, create_user_prompt(user_prompt, python_code)), stream=True)\n", "    reply = \"\"\n", "    for chunk in stream:\n", "        fragment = chunk.choices[0].delta.content or \"\"\n", "        reply += fragment\n", "        yield reply.replace('```cpp\\n','').replace('```','')\n", "\n", "def stream_claude(system_prompt, user_prompt, python_code):\n", "    result = claude.messages.stream(\n", "        model=CLAUDE_MODEL,\n", "        max_tokens=2000,\n", "        system=system_prompt,\n", "        messages=[{\"role\": \"user\", \"content\": create_user_prompt(user_prompt, python_code)}],\n", "    )\n", "    reply = \"\"\n", "    with result as stream:\n", "        for text in stream.text_stream:\n", "            reply += text\n", "            yield reply.replace('```cpp\\n','').replace('```','')\n", "\n", "def call_llm(system_prompt, user_prompt, python_code, model):\n", "    if model==\"GPT\":\n", "        result = stream_gpt(system_prompt, user_prompt, python_code)\n", "    elif model==\"Claude\":\n", "        result = stream_claude(system_prompt, user_prompt, python_code)\n", "    # elif model==\"CodeQwen\":\n", "    #     result = stream_code_qwen(python)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    for stream_so_far in result:\n", "        yield stream_so_far    "]}, {"cell_type": "code", "execution_count": null, "id": "bb8c5b4e-ec51-4f21-b3f8-6aa94fede86d", "metadata": {}, "outputs": [], "source": ["from huggingface_hub import login, InferenceClient\n", "from transformers import AutoTokenizer\n", "\n", "code_qwen = \"Qwen/CodeQwen1.5-7B-Chat\"\n", "code_gemma = \"google/codegemma-7b-it\"\n", "CODE_QWEN_URL = \"https://h1vdol7jxhje3mpn.us-east-1.aws.endpoints.huggingface.cloud\"\n", "CODE_GEMMA_URL = \"https://c5hggiyqachmgnqg.us-east-1.aws.endpoints.huggingface.cloud\"\n", "\n", "hf_token = os.environ['HF_TOKEN']\n", "login(hf_token, add_to_git_credential=True)\n", "\n", "def stream_code_qwen(python):\n", "    tokenizer = AutoTokenizer.from_pretrained(code_qwen)\n", "    messages = messages_for(python)\n", "    text = tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)\n", "    client = InferenceClient(CODE_QWEN_URL, token=hf_token)\n", "    stream = client.text_generation(text, stream=True, details=True, max_new_tokens=3000)\n", "    result = \"\"\n", "    for r in stream:\n", "        result += r.token.text\n", "        yield result    "]}, {"cell_type": "code", "execution_count": 61, "id": "19bf2bff-a822-4009-a539-f003b1651383", "metadata": {}, "outputs": [], "source": ["def execute_python(code):\n", "    try:\n", "        output = io.StringIO()\n", "        sys.stdout = output\n", "        exec(code)\n", "    finally:\n", "        sys.stdout = sys.__stdout__\n", "    return output.getvalue()\n", "\n", "def execute_cpp(code):\n", "    write_output(code)\n", "    try:\n", "        compile_result = subprocess.run(compiler_cmd[2], check=True, text=True, capture_output=True)\n", "        run_cmd = [\"./optimized\"]\n", "        run_result = subprocess.run(run_cmd, check=True, text=True, capture_output=True)\n", "        return run_result.stdout\n", "    except subprocess.CalledProcessError as e:\n", "        return f\"An error occurred:\\n{e.stderr}\""]}, {"cell_type": "code", "execution_count": 62, "id": "4ba311ec-c16a-4fe0-946b-4b940704cf65", "metadata": {}, "outputs": [], "source": ["def select_python_sample(python_sample):\n", "    if python_sample in python_sample_options:\n", "        return python_code_samples[python_sample]\n", "    else:\n", "        return next(iter(donedone.values()), \"# Type in your Python program here\")"]}, {"cell_type": "code", "execution_count": 63, "id": "e42286bc-085c-45dc-b101-234308e58269", "metadata": {}, "outputs": [], "source": ["import platform\n", "\n", "VISUAL_STUDIO_2022_TOOLS = \"C:\\\\Program Files\\\\Microsoft Visual Studio\\\\2022\\\\Community\\\\Common7\\Tools\\\\VsDevCmd.bat\"\n", "VISUAL_STUDIO_2019_TOOLS = \"C:\\\\Program Files (x86)\\\\Microsoft Visual Studio\\\\2019\\\\BuildTools\\\\Common7\\\\Tools\\\\VsDevCmd.bat\"\n", "\n", "simple_cpp = \"\"\"\n", "#include <iostream>\n", "\n", "int main() {\n", "    std::cout << \"Hello\";\n", "    return 0;\n", "}\n", "\"\"\"\n", "\n", "def run_cmd(command_to_run):\n", "    try:\n", "        run_result = subprocess.run(command_to_run, check=True, text=True, capture_output=True)\n", "        return run_result.stdout if run_result.stdout else \"SUCCESS\"\n", "    except:\n", "        return \"\"\n", "\n", "def c_compiler_cmd(filename_base):\n", "    my_platform = platform.system()\n", "    my_compiler = []\n", "\n", "    try:\n", "        with open(\"simple.cpp\", \"w\") as f:\n", "            f.write(simple_cpp)\n", "            \n", "        if my_platform == \"Windows\":\n", "            if os.path.isfile(VISUAL_STUDIO_2022_TOOLS):\n", "                if os.path.isfile(\"./simple.exe\"):\n", "                    os.remove(\"./simple.exe\")\n", "                compile_cmd = [\"cmd\", \"/c\", VISUAL_STUDIO_2022_TOOLS, \"&\", \"cl\", \"simple.cpp\"]\n", "                if run_cmd(compile_cmd):\n", "                    if run_cmd([\"./simple.exe\"]) == \"Hello\":\n", "                        my_compiler = [\"Windows\", \"Visual Studio 2022\", [\"cmd\", \"/c\", VISUAL_STUDIO_2022_TOOLS, \"&\", \"cl\", f\"{filename_base}.cpp\"]]\n", "        \n", "            if not my_compiler:\n", "                if os.path.isfile(VISUAL_STUDIO_2019_TOOLS):\n", "                    if os.path.isfile(\"./simple.exe\"):\n", "                        os.remove(\"./simple.exe\")\n", "                    compile_cmd = [\"cmd\", \"/c\", VISUAL_STUDIO_2019_TOOLS, \"&\", \"cl\", \"simple.cpp\"]\n", "                    if run_cmd(compile_cmd):\n", "                        if run_cmd([\"./simple.exe\"]) == \"Hello\":\n", "                            my_compiler = [\"Windows\", \"Visual Studio 2019\", [\"cmd\", \"/c\", VISUAL_STUDIO_2019_TOOLS, \"&\", \"cl\", f\"{filename_base}.cpp\"]]\n", "    \n", "            if not my_compiler:\n", "                my_compiler=[my_platform, \"Unavailable\", []]\n", "                \n", "        elif my_platform == \"Linux\":\n", "            if os.path.isfile(\"./simple\"):\n", "                os.remove(\"./simple\")\n", "            compile_cmd = [\"g++\", \"simple.cpp\", \"-o\", \"simple\"]\n", "            if run_cmd(compile_cmd):\n", "                if run_cmd([\"./simple\"]) == \"Hello\":\n", "                    my_compiler = [\"Linux\", \"GCC (g++)\", [\"g++\", f\"{filename_base}.cpp\", \"-o\", f\"{filename_base}\" ]]\n", "    \n", "            if not my_compiler:\n", "                if os.path.isfile(\"./simple\"):\n", "                    os.remove(\"./simple\")\n", "                compile_cmd = [\"clang++\", \"simple.cpp\", \"-o\", \"simple\"]\n", "                if run_cmd(compile_cmd):\n", "                    if run_cmd([\"./simple\"]) == \"Hello\":\n", "                        my_compiler = [\"Linux\", \"Clang++\", [\"clang++\", f\"{filename_base}.cpp\", \"-o\", f\"{filename_base}\"]]\n", "        \n", "            if not my_compiler:\n", "                my_compiler=[my_platform, \"Unavailable\", []]\n", "    \n", "        elif my_platform == \"Darwin\":\n", "            if os.path.isfile(\"./simple\"):\n", "                os.remove(\"./simple\")\n", "            compile_cmd = [\"clang++\", \"-Ofast\", \"-std=c++17\", \"-march=armv8.5-a\", \"-mtune=apple-m1\", \"-mcpu=apple-m1\", \"-o\", \"simple\", \"simple.cpp\"]\n", "            if run_cmd(compile_cmd):\n", "                if run_cmd([\"./simple\"]) == \"Hello\":\n", "                    my_compiler = [\"Macintosh\", \"Clang++\", [\"clang++\", \"-Ofast\", \"-std=c++17\", \"-march=armv8.5-a\", \"-mtune=apple-m1\", \"-mcpu=apple-m1\", \"-o\", f\"{filename_base}\", f\"{filename_base}.cpp\"]]\n", "    \n", "            if not my_compiler:\n", "                my_compiler=[my_platform, \"Unavailable\", []]\n", "    except:\n", "        my_compiler=[my_platform, \"Unavailable\", []]\n", "        \n", "    if my_compiler:\n", "        return my_compiler\n", "    else:\n", "        return [\"Unknown\", \"Unavailable\", []]\n"]}, {"cell_type": "code", "execution_count": 167, "id": "f9ca2e6f-60c1-4e5f-b570-63c75b2d189b", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7916/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 167, "metadata": {}, "output_type": "execute_result"}], "source": ["css = \"\"\"\n", ".python {background-color: #306998;}\n", ".cpp {background-color: #050;}\n", "\"\"\"\n", "\n", "model = default_model\n", "selected_tab = prompt_options[0]\n", "\n", "# Determine the C (C++, really) compiler to use based on the platform\n", "compiler_cmd = c_compiler_cmd(\"optimized\")\n", "\n", "def any_tab_on_select(evt: gr.SelectData):\n", "    global selected_tab\n", "    selected_tab = evt.value\n", "\n", "def reset_prompts():\n", "    return system_prompts[selected_tab], user_prompts[selected_tab]\n", "\n", "def change_python_sample(python_sample, python_code):\n", "    if not python_sample == \"Custom\":\n", "        if python_sample in python_sample_options:\n", "            return python_code_samples[python_sample]\n", "        else:\n", "            return python_code\n", "    else:\n", "        return python_code\n", "\n", "def change_python_sample_to_custom():\n", "    return \"Custom\"\n", "\n", "# Display the interface\n", "with gr.Blocks(css=css) as ui:\n", "    with gr.Tab(prompt_options[0]) as first_tab:\n", "        gr.Markdown(\"# \" + prompt_options[0])\n", "        with gr.Group():\n", "            with gr.<PERSON>():\n", "                first_system_prompt_txt = gr.Textbox(label=\"System prompt\", value=system_prompts[prompt_options[0]], lines=10, interactive=True )\n", "                first_user_prompt_txt = gr.Textbox(label=\"User prompt\", value=user_prompts[prompt_options[0]], lines=10, interactive=True )\n", "            with gr.<PERSON>():\n", "                first_reset_prompts_btn = gr.<PERSON><PERSON>(\"Reset prompts\")\n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>():\n", "                first_sample_program_rad = gr.Radio(python_sample_options + [\"Custom\"], label=\"Sample program\", value=python_sample_options[0])\n", "                first_python_code_txt = gr.Textbox(label=\"Python code:\", value=python_code_samples[python_sample_options[0]], lines=10, interactive=True)\n", "            with gr.<PERSON>():\n", "                first_model_drp = gr.Dropdown(model_options, label=\"Select model\", value=default_model, interactive=True)\n", "                first_convert_btn = gr.<PERSON><PERSON>(\"Convert code\", interactive=True)\n", "                first_cpp_txt = gr.Textbox(label=\"C++ code:\", lines=10, interactive=True)\n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>():\n", "                with gr.Group():\n", "                    first_python_run_btn = gr.<PERSON>(\"Run Python\", interactive=True)\n", "                    first_python_out_txt = gr.TextArea(label=\"Python result:\", elem_classes=[\"python\"])\n", "            with gr.<PERSON>():\n", "                with gr.Group():\n", "                    if not compiler_cmd[1] == \"Unavailable\":\n", "                        first_cpp_run_btn = gr.<PERSON><PERSON>(\"Run C++\")\n", "                    else:\n", "                        first_cpp_run_btn = gr.<PERSON>(\"No compiler to run C++\", interactive=False)\n", "                    first_cpp_out_txt = gr.TextArea(label=\"C++ result:\", elem_classes=[\"cpp\"])\n", "                    first_architecture_rad = gr.Radio([compiler_cmd[0]], label=\"Architecture\", interactive=False, value=compiler_cmd[0])\n", "                    first_compiler_rad = gr.Radio([compiler_cmd[1]], label=\"Compiler\", interactive=False, value=compiler_cmd[1])\n", "            \n", "    with gr.Tab(prompt_options[1]) as second_tab:\n", "        gr.Markdown(\"# \" + prompt_options[1])\n", "        with gr.Group():\n", "            with gr.<PERSON>():\n", "                second_system_prompt_txt = gr.Textbox(label=\"System prompt\", value=system_prompts[prompt_options[1]], lines=10, interactive=True )\n", "                second_user_prompt_txt = gr.Textbox(label=\"User prompt\", value=user_prompts[prompt_options[1]], lines=10, interactive=True )\n", "            with gr.<PERSON>():\n", "                second_reset_prompts_btn = gr.<PERSON><PERSON>(\"Reset prompts\")\n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>():\n", "                second_sample_program_rad = gr.Radio(python_sample_options + [\"Custom\"], label=\"Sample program\", value=python_sample_options[1])\n", "                second_python_code_txt = gr.Textbox(label=\"Python code:\", value=python_code_samples[python_sample_options[1]], lines=10)\n", "            with gr.<PERSON>():\n", "                second_model_drp = gr.Dropdown(model_options, label=\"Select model\", value=default_model)\n", "                second_comment_btn = gr.<PERSON>(\"Comment code\")\n", "                second_python_code_comments_txt = gr.Textbox(label=\"Commented code\", lines=20)\n", "\n", "                    \n", "    with gr.Tab(prompt_options[2]) as third_tab:\n", "        gr.Markdown(\"# \" + prompt_options[2])\n", "        with gr.Group():\n", "            with gr.<PERSON>():\n", "                third_system_prompt_txt = gr.Textbox(label=\"System prompt\", value=system_prompts[prompt_options[2]], lines=10, interactive=True )\n", "                third_user_prompt_txt = gr.Textbox(label=\"User prompt\", value=user_prompts[prompt_options[2]], lines=10, interactive=True )\n", "            with gr.<PERSON>():\n", "                third_reset_prompts_btn = gr.<PERSON><PERSON>(\"Reset prompts\")\n", "        with gr.<PERSON>():\n", "            with gr.<PERSON>():\n", "                third_sample_program_rad = gr.Radio(python_sample_options + [\"Custom\"], label=\"Sample program\", value=python_sample_options[1])\n", "                third_python_code_txt = gr.Textbox(label=\"Python code:\", value=python_code_samples[python_sample_options[1]], lines=10)\n", "            with gr.<PERSON>():\n", "                third_model_drp = gr.Dropdown(model_options, label=\"Select model\", value=default_model)\n", "                third_unit_test_btn = gr.<PERSON><PERSON>(\"Create unit tests\")\n", "                third_python_unit_tests_txt = gr.Textbox(label=\"Unit tests\", lines=20)\n", "\n", "    first_tab.select(any_tab_on_select)\n", "    second_tab.select(any_tab_on_select)\n", "    third_tab.select(any_tab_on_select)\n", "    \n", "    first_reset_prompts_btn.click(reset_prompts, outputs=[first_system_prompt_txt, first_user_prompt_txt])\n", "    second_reset_prompts_btn.click(reset_prompts, outputs=[second_system_prompt_txt, second_user_prompt_txt])\n", "    third_reset_prompts_btn.click(reset_prompts, outputs=[third_system_prompt_txt, third_user_prompt_txt])\n", "\n", "    first_sample_program_rad.input(change_python_sample, inputs=[first_sample_program_rad, first_python_code_txt], outputs=[first_python_code_txt])\n", "    first_python_code_txt.input(change_python_sample_to_custom, inputs=[], outputs=[first_sample_program_rad])\n", "    first_convert_btn.click(call_llm, inputs=[first_system_prompt_txt, first_user_prompt_txt, first_python_code_txt, first_model_drp], outputs=[first_cpp_txt])\n", "    first_python_run_btn.click(execute_python, inputs=[first_python_code_txt], outputs=[first_python_out_txt])\n", "    first_cpp_run_btn.click(execute_cpp, inputs=[first_cpp_txt], outputs=[first_cpp_out_txt])\n", "\n", "    second_sample_program_rad.input(change_python_sample, inputs=[second_sample_program_rad, second_python_code_txt], outputs=[second_python_code_txt])\n", "    second_python_code_txt.input(change_python_sample_to_custom, inputs=[], outputs=[second_sample_program_rad])\n", "    second_comment_btn.click(call_llm, inputs=[second_system_prompt_txt, second_user_prompt_txt, second_python_code_txt, second_model_drp], outputs=[second_python_code_comments_txt])\n", "\n", "    third_sample_program_rad.input(change_python_sample, inputs=[third_sample_program_rad, third_python_code_txt], outputs=[third_python_code_txt])\n", "    third_python_code_txt.input(change_python_sample_to_custom, inputs=[], outputs=[second_sample_program_rad])\n", "    third_unit_test_btn.click(call_llm, inputs=[third_system_prompt_txt, third_user_prompt_txt, third_python_code_txt, third_model_drp], outputs=[third_python_unit_tests_txt])\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": 152, "id": "9d0ad093-425b-488e-8c3f-67f729dd9c06", "metadata": {}, "outputs": [], "source": ["import time\n", "\n", "start_time = time.time()\n", "    \n", "print(\"Hello, world\")\n", "    \n", "end_time = time.time()\n", "    \n", "\n", "print(f\"Execution Time: {(end_time - start_time):.6f} seconds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}