{"cells": [{"cell_type": "markdown", "id": "3d3cb3c4-9046-4f64-9188-ee20ae324fd1", "metadata": {}, "source": ["# Code Generator\n", "\n", "The requirement: use a Frontier model to generate high performance C++ code from Python code\n", "\n", "# Important Note\n", "Used an open-source model gemini-1.5-pro ,can try 2.0 flash too\n"]}, {"cell_type": "code", "execution_count": null, "id": "6f2c3e03-f38a-4bf2-98e8-696fb3d428c9", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import io\n", "import sys\n", "from dotenv import load_dotenv\n", "import google.generativeai\n", "from IPython.display import Markdown, display, update_display\n", "import gradio as gr\n", "import subprocess"]}, {"cell_type": "code", "execution_count": null, "id": "e437f3d1-39c4-47fd-919f-c2119d602d72", "metadata": {}, "outputs": [], "source": ["# environment\n", "\n", "load_dotenv()\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "if google_api_key:\n", "    print(f\"Google API Key exists\")\n", "else:\n", "    print(\"Google API Key not set\")"]}, {"cell_type": "code", "execution_count": null, "id": "1724ddb6-0059-46a3-bcf9-587c0c93cb2a", "metadata": {}, "outputs": [], "source": ["google.generativeai.configure()\n"]}, {"cell_type": "code", "execution_count": null, "id": "b62738c1-9857-40fc-91e8-dfd46483ea50", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that reimplements Python code in high performance C++ for an Windows system. \"\n", "system_message += \"Respond only with C++ code; use comments sparingly and do not provide any explanation other than occasional comments. \"\n", "system_message += \"The C++ response needs to produce an identical output in the fastest possible time.\""]}, {"cell_type": "code", "execution_count": null, "id": "bd431141-8602-4c68-9a1d-a7c0a6f13fa3", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(python):\n", "    user_prompt = \"Rewrite this Python code in C++ with the fastest possible implementation that produces identical output in the least time. \"\n", "    user_prompt += \"Respond only with C++ code; do not explain your work other than a few comments. \"\n", "    user_prompt += \"Pay attention to number types to ensure no int overflows. Remember to #include all necessary C++ packages such as iomanip.\\n\\n\"\n", "    user_prompt += python\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "d5f48451-4cd4-46ea-a41d-531a3c7db2a8", "metadata": {}, "outputs": [], "source": ["def messages_for(python):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(python)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "83fd2170-14ea-4fb6-906e-c3c5cfce1ecc", "metadata": {}, "outputs": [], "source": ["# write to a file called optimized.cpp\n", "\n", "def write_output(cpp):\n", "    code = cpp.replace(\"```cpp\",\"\").replace(\"```\",\"\")\n", "    with open(\"optimized.cpp\", \"w\") as f:\n", "        f.write(code)"]}, {"cell_type": "code", "execution_count": null, "id": "1ff08067-c9df-4981-8ab5-99eb2c2fd2c7", "metadata": {}, "outputs": [], "source": ["def optimize_google(python):\n", "    # Initialize empty reply string\n", "    reply = \"\"\n", "    \n", "    # The API for Gemini has a slightly different structure\n", "    gemini = google.generativeai.GenerativeModel(\n", "        model_name='gemini-1.5-pro',\n", "        system_instruction=system_message\n", "    )\n", "    \n", "    response = gemini.generate_content(\n", "        user_prompt_for(python),\n", "        stream=True\n", "    )\n", "    \n", "    # Process the stream\n", "    for chunk in response:\n", "        # Extract text from the chunk\n", "        if chunk.text:\n", "            reply += chunk.text\n", "            print(chunk.text, end=\"\", flush=True)\n", "    \n", "    # Write the complete response to output\n", "    write_output(reply)\n", "    \n", "    # return reply"]}, {"cell_type": "code", "execution_count": null, "id": "8e8c7ba2-4ee9-4523-b0f1-cc7a91798bba", "metadata": {}, "outputs": [], "source": ["pi = \"\"\"\n", "import time\n", "\n", "def calculate(iterations, param1, param2):\n", "    result = 1.0\n", "    for i in range(1, iterations+1):\n", "        j = i * param1 - param2\n", "        result -= (1/j)\n", "        j = i * param1 + param2\n", "        result += (1/j)\n", "    return result\n", "\n", "start_time = time.time()\n", "result = calculate(100_000_000, 4, 1) * 4\n", "end_time = time.time()\n", "\n", "print(f\"Result: {result:.12f}\")\n", "print(f\"Execution Time: {(end_time - start_time):.6f} seconds\")\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "78d1afb7-ed6b-4a03-b36d-4ce8249c592e", "metadata": {}, "outputs": [], "source": ["exec(pi)"]}, {"cell_type": "code", "execution_count": null, "id": "1fe1d0b6-7cc7-423b-bc4b-741a0c48c106", "metadata": {}, "outputs": [], "source": ["optimize_google(pi)"]}, {"cell_type": "code", "execution_count": null, "id": "d28b4ac9-0909-4b35-aee1-97613a133e8e", "metadata": {}, "outputs": [], "source": ["exec(pi) #Execution Time: 16.209231 seconds"]}, {"cell_type": "markdown", "id": "7d0443a3-3ca2-4a7a-a6c3-c94d0aa54603", "metadata": {}, "source": ["# Compiling C++ and executing\n", "\n", "This next cell contains the command to compile a C++ file on Windows system.  \n", "It compiles the file `optimized.cpp` into an executable called `optimized`  \n", "Then it runs the program called `optimized`\n", "\n", "The way to compile for mac users is \\\n", "!clang++ -O3 -std=c++17 -march=armv8.3-a -o optimized optimized.cpp \\\n", "!./optimized"]}, {"cell_type": "code", "execution_count": null, "id": "9b5cfc70-df1f-44a7-b4ae-fd934f715930", "metadata": {}, "outputs": [], "source": ["!g++ -o optimized optimized.cpp\n", "!.\\optimized #Execution Time: 3.661196 seconds"]}, {"cell_type": "code", "execution_count": null, "id": "e30fcbdf-82cf-4d50-9690-92dae69d5127", "metadata": {}, "outputs": [], "source": ["python_hard = \"\"\"\n", "def lcg(seed, a=1664525, c=1013904223, m=2**32):\n", "    value = seed\n", "    while True:\n", "        value = (a * value + c) % m\n", "        yield value\n", "        \n", "def max_subarray_sum(n, seed, min_val, max_val):\n", "    lcg_gen = lcg(seed)\n", "    random_numbers = [next(lcg_gen) % (max_val - min_val + 1) + min_val for _ in range(n)]\n", "    max_sum = float('-inf')\n", "    for i in range(n):\n", "        current_sum = 0\n", "        for j in range(i, n):\n", "            current_sum += random_numbers[j]\n", "            if current_sum > max_sum:\n", "                max_sum = current_sum\n", "    return max_sum\n", "\n", "def total_max_subarray_sum(n, initial_seed, min_val, max_val):\n", "    total_sum = 0\n", "    lcg_gen = lcg(initial_seed)\n", "    for _ in range(20):\n", "        seed = next(lcg_gen)\n", "        total_sum += max_subarray_sum(n, seed, min_val, max_val)\n", "    return total_sum\n", "\n", "# Parameters\n", "n = 10000         # Number of random numbers\n", "initial_seed = 42 # Initial seed for the LCG\n", "min_val = -10     # Minimum value of random numbers\n", "max_val = 10      # Maximum value of random numbers\n", "\n", "# Timing the function\n", "import time\n", "start_time = time.time()\n", "result = total_max_subarray_sum(n, initial_seed, min_val, max_val)\n", "end_time = time.time()\n", "\n", "print(\"Total Maximum Subarray Sum (20 runs):\", result)\n", "print(\"Execution Time: {:.6f} seconds\".format(end_time - start_time))\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "2e8e111c-6f69-4ed0-8f86-8ed5982aa065", "metadata": {}, "outputs": [], "source": ["exec(python_hard) #Execution Time: 62.297366 seconds"]}, {"cell_type": "code", "execution_count": null, "id": "38038ac1-5cdf-49d7-a286-a5871d5af583", "metadata": {}, "outputs": [], "source": ["optimize_google(python_hard)"]}, {"cell_type": "code", "execution_count": null, "id": "08cb9619-b8ae-42e7-9375-4b3918c37fd0", "metadata": {}, "outputs": [], "source": ["!g++ -o optimized optimized.cpp\n", "!.\\optimized"]}, {"cell_type": "code", "execution_count": null, "id": "acd17a0d-f9f1-45a6-8151-916d8e6b9e4f", "metadata": {}, "outputs": [], "source": ["def stream_google(python):\n", "    # Initialize empty reply string\n", "    reply = \"\"\n", "    \n", "    # The API for Gemini has a slightly different structure\n", "    gemini = google.generativeai.GenerativeModel(\n", "        model_name='gemini-1.5-pro',\n", "        system_instruction=system_message\n", "    )\n", "    \n", "    response = gemini.generate_content(\n", "        user_prompt_for(python),\n", "        stream=True\n", "    )\n", "    \n", "    # Process the stream\n", "    for chunk in response:\n", "        # Extract text from the chunk\n", "        if chunk.text:\n", "            reply += chunk.text\n", "            yield reply.replace('```cpp\\n','').replace('```','')\n"]}, {"cell_type": "code", "execution_count": null, "id": "c3177229-d6cf-4df2-81a7-9e1f3b229c19", "metadata": {}, "outputs": [], "source": ["def optimize(python, model):\n", "    result=stream_google(python)\n", "    for stream_so_far in result:\n", "        yield stream_so_far    "]}, {"cell_type": "code", "execution_count": null, "id": "c2476c2d-9218-4d30-bcc9-9cc5271c3a00", "metadata": {}, "outputs": [], "source": ["with gr.Blocks() as ui:\n", "    with gr.<PERSON>():\n", "        python = gr.Textbox(label=\"Python code:\", lines=10, value=pi)\n", "        cpp = gr.Textbox(label=\"C++ code:\", lines=10)\n", "    with gr.<PERSON>():\n", "        model = gr.Dropdown([\"Google\"], label=\"Select model\", value=\"Google\")\n", "        convert = gr.<PERSON><PERSON>(\"Convert code\")\n", "\n", "    convert.click(optimize, inputs=[python, model], outputs=[cpp])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "a30de175-af4e-428a-8942-1c41997c01f1", "metadata": {}, "outputs": [], "source": ["def execute_python(code):\n", "        try:\n", "            output = io.StringIO()\n", "            sys.stdout = output\n", "            exec(code)\n", "        finally:\n", "            sys.stdout = sys.__stdout__\n", "        return output.getvalue()"]}, {"cell_type": "code", "execution_count": null, "id": "20c6316d-b090-42c5-9be9-7d5a178b97b3", "metadata": {}, "outputs": [], "source": ["def execute_cpp(code):\n", "        write_output(code)\n", "        try:\n", "            # compile_cmd = [\"clang++\", \"-Ofast\", \"-std=c++17\", \"-march=armv8.5-a\", \"-mtune=apple-m1\", \"-mcpu=apple-m1\", \"-o\", \"optimized\", \"optimized.cpp\"]\n", "            compile_cmd = [\"g++\", \"-o\", \"optimized\", \"optimized.cpp\"]\n", "            compile_result = subprocess.run(compile_cmd, check=True, text=True, capture_output=True)\n", "            run_cmd = [\"./optimized\"]\n", "            run_result = subprocess.run(run_cmd, check=True, text=True, capture_output=True)\n", "            return run_result.stdout\n", "        except subprocess.CalledProcessError as e:\n", "            return f\"An error occurred:\\n{e.stderr}\""]}, {"cell_type": "code", "execution_count": null, "id": "950a459f-3ef6-4afd-9e83-f01c032aa21b", "metadata": {}, "outputs": [], "source": ["css = \"\"\"\n", ".python {background-color: #306998;}\n", ".cpp {background-color: #050;}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "bc3d90ba-716c-4b8f-989f-46c2447c42fa", "metadata": {}, "outputs": [], "source": ["with gr.Blocks(css=css) as ui:\n", "    gr.Markdown(\"## Convert code from Python to C++\")\n", "    with gr.<PERSON>():\n", "        python = gr.Textbox(label=\"Python code:\", value=pi, lines=10)\n", "        cpp = gr.Textbox(label=\"C++ code:\", lines=10)\n", "    with gr.<PERSON>():\n", "        model = gr.Dropdown([\"Google\"], label=\"Select model\", value=\"Google\")\n", "    with gr.<PERSON>():\n", "        convert = gr.<PERSON><PERSON>(\"Convert code\")\n", "    with gr.<PERSON>():\n", "        python_run = gr.<PERSON><PERSON>(\"Run Python\")\n", "        cpp_run = gr.<PERSON><PERSON>(\"Run C++\")\n", "    with gr.<PERSON>():\n", "        python_out = gr.<PERSON>(label=\"Python result:\", elem_classes=[\"python\"])\n", "        cpp_out = gr.<PERSON><PERSON>(label=\"C++ result:\", elem_classes=[\"cpp\"])\n", "\n", "    convert.click(optimize, inputs=[python, model], outputs=[cpp])\n", "    python_run.click(execute_python, inputs=[python], outputs=[python_out])\n", "    cpp_run.click(execute_cpp, inputs=[cpp], outputs=[cpp_out])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c12f6115-e8a9-494e-95ce-2566854c0aa2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}