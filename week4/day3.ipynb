{"cells": [{"cell_type": "markdown", "id": "4a6ab9a2-28a2-445d-8512-a0dc8d1b54e9", "metadata": {}, "source": ["# Code Generator\n", "\n", "The requirement: use a Frontier model to generate high performance C++ code from Python code\n"]}, {"cell_type": "markdown", "id": "d5ccb926-7b49-44a4-99ab-8ef20b5778c0", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">Reminder: fetch latest code</h2>\n", "            <span style=\"color:#f71;\">I'm continually improving these labs, adding more examples and exercises.\n", "            At the start of each week, it's worth checking you have the latest code.<br/>\n", "            First do a <a href=\"https://chatgpt.com/share/6734e705-3270-8012-a074-421661af6ba9\">git pull and merge your changes as needed</a>. Any problems? Try asking ChatGPT to clarify how to merge - or contact me!<br/><br/>\n", "            After you've pulled the code, from the llm_engineering directory, in an Anaconda prompt (PC) or Terminal (Mac), run:<br/>\n", "            <code>conda env update --f environment.yml --prune</code><br/>\n", "            Or if you used virtualenv rather than Anaconda, then run this from your activated environment in a Powershell (PC) or Terminal (Mac):<br/>\n", "            <code>pip install -r requirements.txt</code>\n", "            <br/>Then restart the kernel (Kernel menu >> Restart Kernel and Clear Outputs Of All Cells) to pick up the changes.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "d90e04a2-5b8a-4fd5-9db8-27c02f033313", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h1 style=\"color:#900;\">Important Note</h1>\n", "            <span style=\"color:#900;\">\n", "            In this lab, I use GPT-4o and Claude-3.5-Sonnet, which are the slightly higher priced models. The costs are still low, but if you'd prefer to keep costs ultra low, please make the suggested switches to the models (3 cells down from here).\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "e610bf56-a46e-4aff-8de1-ab49d62b1ad3", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import io\n", "import sys\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import google.generativeai\n", "import anthropic\n", "from IPython.display import Markdown, display, update_display\n", "import gradio as gr\n", "import subprocess"]}, {"cell_type": "code", "execution_count": null, "id": "4f672e1c-87e9-4865-b760-370fa605e614", "metadata": {}, "outputs": [], "source": ["# environment\n", "\n", "load_dotenv(override=True)\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['ANTHROPIC_API_KEY'] = os.getenv('ANTHROPIC_API_KEY', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": null, "id": "8aa149ed-9298-4d69-8fe2-8f5de0f667da", "metadata": {}, "outputs": [], "source": ["# initialize\n", "# NOTE - option to use ultra-low cost models by uncommenting last 2 lines\n", "\n", "openai = OpenAI()\n", "claude = anthropic.Anthropic()\n", "OPENAI_MODEL = \"gpt-4o\"\n", "CLAUDE_MODEL = \"claude-3-5-sonnet-20240620\"\n", "\n", "# Want to keep costs ultra-low? Uncomment these lines:\n", "# OPENAI_MODEL = \"gpt-4o-mini\"\n", "# CLAUDE_MODEL = \"claude-3-haiku-20240307\""]}, {"cell_type": "code", "execution_count": null, "id": "6896636f-923e-4a2c-9d6c-fac07828a201", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that reimplements Python code in high performance C++ for an M1 Mac. \"\n", "system_message += \"Respond only with C++ code; use comments sparingly and do not provide any explanation other than occasional comments. \"\n", "system_message += \"The C++ response needs to produce an identical output in the fastest possible time.\""]}, {"cell_type": "code", "execution_count": null, "id": "8e7b3546-57aa-4c29-bc5d-f211970d04eb", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(python):\n", "    user_prompt = \"Rewrite this Python code in C++ with the fastest possible implementation that produces identical output in the least time. \"\n", "    user_prompt += \"Respond only with C++ code; do not explain your work other than a few comments. \"\n", "    user_prompt += \"Pay attention to number types to ensure no int overflows. Remember to #include all necessary C++ packages such as iomanip.\\n\\n\"\n", "    user_prompt += python\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "c6190659-f54c-4951-bef4-4960f8e51cc4", "metadata": {}, "outputs": [], "source": ["def messages_for(python):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(python)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "71e1ba8c-5b05-4726-a9f3-8d8c6257350b", "metadata": {}, "outputs": [], "source": ["# write to a file called optimized.cpp\n", "\n", "def write_output(cpp):\n", "    code = cpp.replace(\"```cpp\",\"\").replace(\"```\",\"\")\n", "    with open(\"optimized.cpp\", \"w\") as f:\n", "        f.write(code)"]}, {"cell_type": "code", "execution_count": null, "id": "e7d2fea8-74c6-4421-8f1e-0e76d5b201b9", "metadata": {}, "outputs": [], "source": ["def optimize_gpt(python):    \n", "    stream = openai.chat.completions.create(model=OPENAI_MODEL, messages=messages_for(python), stream=True)\n", "    reply = \"\"\n", "    for chunk in stream:\n", "        fragment = chunk.choices[0].delta.content or \"\"\n", "        reply += fragment\n", "        print(fragment, end='', flush=True)\n", "    write_output(reply)"]}, {"cell_type": "code", "execution_count": null, "id": "7cd84ad8-d55c-4fe0-9eeb-1895c95c4a9d", "metadata": {}, "outputs": [], "source": ["def optimize_claude(python):\n", "    result = claude.messages.stream(\n", "        model=CLAUDE_MODEL,\n", "        max_tokens=2000,\n", "        system=system_message,\n", "        messages=[{\"role\": \"user\", \"content\": user_prompt_for(python)}],\n", "    )\n", "    reply = \"\"\n", "    with result as stream:\n", "        for text in stream.text_stream:\n", "            reply += text\n", "            print(text, end=\"\", flush=True)\n", "    write_output(reply)"]}, {"cell_type": "code", "execution_count": null, "id": "a1cbb778-fa57-43de-b04b-ed523f396c38", "metadata": {}, "outputs": [], "source": ["pi = \"\"\"\n", "import time\n", "\n", "def calculate(iterations, param1, param2):\n", "    result = 1.0\n", "    for i in range(1, iterations+1):\n", "        j = i * param1 - param2\n", "        result -= (1/j)\n", "        j = i * param1 + param2\n", "        result += (1/j)\n", "    return result\n", "\n", "start_time = time.time()\n", "result = calculate(100_000_000, 4, 1) * 4\n", "end_time = time.time()\n", "\n", "print(f\"Result: {result:.12f}\")\n", "print(f\"Execution Time: {(end_time - start_time):.6f} seconds\")\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "7fe1cd4b-d2c5-4303-afed-2115a3fef200", "metadata": {}, "outputs": [], "source": ["exec(pi)"]}, {"cell_type": "code", "execution_count": null, "id": "105db6f9-343c-491d-8e44-3a5328b81719", "metadata": {}, "outputs": [], "source": ["optimize_gpt(pi)"]}, {"cell_type": "code", "execution_count": null, "id": "bf26ee95-0c77-491d-9a91-579a1e96a8a3", "metadata": {}, "outputs": [], "source": ["exec(pi)"]}, {"cell_type": "markdown", "id": "bf8f8018-f64d-425c-a0e1-d7862aa9592d", "metadata": {}, "source": ["# Compiling C++ and executing\n", "\n", "This next cell contains the command to compile a C++ file on my M1 Mac.  \n", "It compiles the file `optimized.cpp` into an executable called `optimized`  \n", "Then it runs the program called `optimized`\n", "\n", "In the next lab (day4), a student has contributed a full solution that compiles to efficient code on Mac, PC and Linux!\n", "\n", "You can wait for this, or you can google (or ask ChatGPT!) for how to do this on your platform, then replace the lines below.\n", "If you're not comfortable with this step, you can skip it for sure - I'll show you exactly how it performs on my Mac.\n", "\n", "\n", "OR alternatively: student <PERSON><PERSON> K.G. points out that you can run Python and C++ code online to test it out that way. Thank you Sand<PERSON>!  \n", "> Not an exact comparison but you can still get the idea of performance difference.\n", "> For example here: https://www.programiz.com/cpp-programming/online-compiler/"]}, {"cell_type": "code", "execution_count": null, "id": "4194e40c-04ab-4940-9d64-b4ad37c5bb40", "metadata": {}, "outputs": [], "source": ["# Compile C++ and run the executable\n", "\n", "!clang++ -O3 -std=c++17 -march=armv8.3-a -o optimized optimized.cpp\n", "!./optimized"]}, {"cell_type": "code", "execution_count": null, "id": "983a11fe-e24d-4c65-8269-9802c5ef3ae6", "metadata": {}, "outputs": [], "source": ["optimize_claude(pi)"]}, {"cell_type": "code", "execution_count": null, "id": "d5a766f9-3d23-4bb4-a1d4-88ec44b61ddf", "metadata": {}, "outputs": [], "source": ["# Repeat for <PERSON> - again, use the right approach for your platform\n", "\n", "!clang++ -O3 -std=c++17 -march=armv8.3-a -o optimized optimized.cpp\n", "!./optimized"]}, {"cell_type": "code", "execution_count": null, "id": "c3b497b3-f569-420e-b92e-fb0f49957ce0", "metadata": {}, "outputs": [], "source": ["python_hard = \"\"\"# Be careful to support large number sizes\n", "\n", "def lcg(seed, a=1664525, c=1013904223, m=2**32):\n", "    value = seed\n", "    while True:\n", "        value = (a * value + c) % m\n", "        yield value\n", "        \n", "def max_subarray_sum(n, seed, min_val, max_val):\n", "    lcg_gen = lcg(seed)\n", "    random_numbers = [next(lcg_gen) % (max_val - min_val + 1) + min_val for _ in range(n)]\n", "    max_sum = float('-inf')\n", "    for i in range(n):\n", "        current_sum = 0\n", "        for j in range(i, n):\n", "            current_sum += random_numbers[j]\n", "            if current_sum > max_sum:\n", "                max_sum = current_sum\n", "    return max_sum\n", "\n", "def total_max_subarray_sum(n, initial_seed, min_val, max_val):\n", "    total_sum = 0\n", "    lcg_gen = lcg(initial_seed)\n", "    for _ in range(20):\n", "        seed = next(lcg_gen)\n", "        total_sum += max_subarray_sum(n, seed, min_val, max_val)\n", "    return total_sum\n", "\n", "# Parameters\n", "n = 10000         # Number of random numbers\n", "initial_seed = 42 # Initial seed for the LCG\n", "min_val = -10     # Minimum value of random numbers\n", "max_val = 10      # Maximum value of random numbers\n", "\n", "# Timing the function\n", "import time\n", "start_time = time.time()\n", "result = total_max_subarray_sum(n, initial_seed, min_val, max_val)\n", "end_time = time.time()\n", "\n", "print(\"Total Maximum Subarray Sum (20 runs):\", result)\n", "print(\"Execution Time: {:.6f} seconds\".format(end_time - start_time))\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "dab5e4bc-276c-4555-bd4c-12c699d5e899", "metadata": {}, "outputs": [], "source": ["exec(python_hard)"]}, {"cell_type": "code", "execution_count": null, "id": "e8d24ed5-2c15-4f55-80e7-13a3952b3cb8", "metadata": {}, "outputs": [], "source": ["optimize_gpt(python_hard)"]}, {"cell_type": "code", "execution_count": null, "id": "e0b3d073-88a2-40b2-831c-6f0c345c256f", "metadata": {}, "outputs": [], "source": ["# Replace this with the right C++ compile + execute command for your platform\n", "\n", "!clang++ -O3 -std=c++17 -march=armv8.3-a -o optimized optimized.cpp\n", "!./optimized"]}, {"cell_type": "code", "execution_count": null, "id": "e9305446-1d0c-4b51-866a-b8c1e299bf5c", "metadata": {}, "outputs": [], "source": ["optimize_claude(python_hard)"]}, {"cell_type": "code", "execution_count": null, "id": "0c181036-8193-4fdd-aef3-fc513b218d43", "metadata": {}, "outputs": [], "source": ["# Replace this with the right C++ compile + execute command for your platform\n", "\n", "!clang++ -O3 -std=c++17 -march=armv8.3-a -o optimized optimized.cpp\n", "!./optimized"]}, {"cell_type": "code", "execution_count": null, "id": "0be9f47d-5213-4700-b0e2-d444c7c738c0", "metadata": {}, "outputs": [], "source": ["def stream_gpt(python):    \n", "    stream = openai.chat.completions.create(model=OPENAI_MODEL, messages=messages_for(python), stream=True)\n", "    reply = \"\"\n", "    for chunk in stream:\n", "        fragment = chunk.choices[0].delta.content or \"\"\n", "        reply += fragment\n", "        yield reply.replace('```cpp\\n','').replace('```','')"]}, {"cell_type": "code", "execution_count": null, "id": "8669f56b-8314-4582-a167-78842caea131", "metadata": {}, "outputs": [], "source": ["def stream_claude(python):\n", "    result = claude.messages.stream(\n", "        model=CLAUDE_MODEL,\n", "        max_tokens=2000,\n", "        system=system_message,\n", "        messages=[{\"role\": \"user\", \"content\": user_prompt_for(python)}],\n", "    )\n", "    reply = \"\"\n", "    with result as stream:\n", "        for text in stream.text_stream:\n", "            reply += text\n", "            yield reply.replace('```cpp\\n','').replace('```','')"]}, {"cell_type": "code", "execution_count": null, "id": "2f1ae8f5-16c8-40a0-aa18-63b617df078d", "metadata": {}, "outputs": [], "source": ["def optimize(python, model):\n", "    if model==\"GPT\":\n", "        result = stream_gpt(python)\n", "    elif model==\"Claude\":\n", "        result = stream_claude(python)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    for stream_so_far in result:\n", "        yield stream_so_far        "]}, {"cell_type": "code", "execution_count": null, "id": "f1ddb38e-6b0a-4c37-baa4-ace0b7de887a", "metadata": {}, "outputs": [], "source": ["with gr.Blocks() as ui:\n", "    with gr.<PERSON>():\n", "        python = gr.Textbox(label=\"Python code:\", lines=10, value=python_hard)\n", "        cpp = gr.Textbox(label=\"C++ code:\", lines=10)\n", "    with gr.<PERSON>():\n", "        model = gr.Dropdown([\"GPT\", \"Claude\"], label=\"Select model\", value=\"GPT\")\n", "        convert = gr.<PERSON><PERSON>(\"Convert code\")\n", "\n", "    convert.click(optimize, inputs=[python, model], outputs=[cpp])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "19bf2bff-a822-4009-a539-f003b1651383", "metadata": {}, "outputs": [], "source": ["def execute_python(code):\n", "    try:\n", "        output = io.StringIO()\n", "        sys.stdout = output\n", "        exec(code)\n", "    finally:\n", "        sys.stdout = sys.__stdout__\n", "    return output.getvalue()"]}, {"cell_type": "code", "execution_count": null, "id": "77f3ab5d-fcfb-4d3f-8728-9cacbf833ea6", "metadata": {}, "outputs": [], "source": ["# You'll need to change the code in the try block to compile the C++ code for your platform\n", "# I pasted this into <PERSON>'s chat UI with a request for it to give me a version for an Intel PC,\n", "# and it responded with something that looks perfect - you can try a similar approach for your platform.\n", "\n", "# M1 Mac version to compile and execute optimized C++ code:\n", "\n", "def execute_cpp(code):\n", "        write_output(code)\n", "        try:\n", "            compile_cmd = [\"clang++\", \"-Ofast\", \"-std=c++17\", \"-march=armv8.5-a\", \"-mtune=apple-m1\", \"-mcpu=apple-m1\", \"-o\", \"optimized\", \"optimized.cpp\"]\n", "            compile_result = subprocess.run(compile_cmd, check=True, text=True, capture_output=True)\n", "            run_cmd = [\"./optimized\"]\n", "            run_result = subprocess.run(run_cmd, check=True, text=True, capture_output=True)\n", "            return run_result.stdout\n", "        except subprocess.CalledProcessError as e:\n", "            return f\"An error occurred:\\n{e.stderr}\""]}, {"cell_type": "code", "execution_count": null, "id": "9a2274f1-d03b-42c0-8dcc-4ce159b18442", "metadata": {}, "outputs": [], "source": ["css = \"\"\"\n", ".python {background-color: #306998;}\n", ".cpp {background-color: #050;}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f1303932-160c-424b-97a8-d28c816721b2", "metadata": {}, "outputs": [], "source": ["with gr.Blocks(css=css) as ui:\n", "    gr.Markdown(\"## Convert code from Python to C++\")\n", "    with gr.<PERSON>():\n", "        python = gr.Textbox(label=\"Python code:\", value=python_hard, lines=10)\n", "        cpp = gr.Textbox(label=\"C++ code:\", lines=10)\n", "    with gr.<PERSON>():\n", "        model = gr.Dropdown([\"GPT\", \"Claude\"], label=\"Select model\", value=\"GPT\")\n", "    with gr.<PERSON>():\n", "        convert = gr.<PERSON><PERSON>(\"Convert code\")\n", "    with gr.<PERSON>():\n", "        python_run = gr.<PERSON><PERSON>(\"Run Python\")\n", "        cpp_run = gr.<PERSON><PERSON>(\"Run C++\")\n", "    with gr.<PERSON>():\n", "        python_out = gr.<PERSON>(label=\"Python result:\", elem_classes=[\"python\"])\n", "        cpp_out = gr.<PERSON><PERSON>(label=\"C++ result:\", elem_classes=[\"cpp\"])\n", "\n", "    convert.click(optimize, inputs=[python, model], outputs=[cpp])\n", "    python_run.click(execute_python, inputs=[python], outputs=[python_out])\n", "    cpp_run.click(execute_cpp, inputs=[cpp], outputs=[cpp_out])\n", "\n", "ui.launch(inbrowser=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}