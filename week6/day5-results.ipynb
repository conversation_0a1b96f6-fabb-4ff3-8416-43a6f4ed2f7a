{"cells": [{"cell_type": "markdown", "id": "db8736a7-ed94-441c-9556-831fa57b5a10", "metadata": {}, "source": ["# The Product Pricer Continued\n", "\n", "A model that can estimate how much something costs, from its description.\n", "\n", "## AT LAST - it's time for Fine Tuning!\n", "\n", "After all this data preparation, and old school machine learning, we've finally arrived at the moment you've been waiting for. Fine-tuning a model."]}, {"cell_type": "code", "execution_count": 1, "id": "681c717b-4c24-4ac3-a5f3-3c5881d6e70a", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import re\n", "import math\n", "import json\n", "import random\n", "from dotenv import load_dotenv\n", "from huggingface_hub import login\n", "from items import Item\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pickle\n", "from collections import Counter\n", "from openai import OpenAI\n", "from anthropic import Anthropic"]}, {"cell_type": "code", "execution_count": 2, "id": "21a3833e-4093-43b0-8f7b-839c50b911ea", "metadata": {}, "outputs": [], "source": ["# moved our Tester into a separate package\n", "# call it with Tester.test(function_name, test_dataset)\n", "\n", "from testing import Tester"]}, {"cell_type": "code", "execution_count": 3, "id": "36d05bdc-0155-4c72-a7ee-aa4e614ffd3c", "metadata": {}, "outputs": [], "source": ["# environment\n", "\n", "load_dotenv(override=True)\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['ANTHROPIC_API_KEY'] = os.getenv('ANTHROPIC_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['HF_TOKEN'] = os.getenv('HF_TOKEN', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": 4, "id": "4dd3aad2-6f99-433c-8792-e461d2f06622", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Token is valid (permission: write).\n", "Your token has been saved in your configured git credential helpers (osxkeychain).\n", "Your token has been saved to /Users/<USER>/.cache/huggingface/token\n", "Login successful\n"]}], "source": ["# Log in to HuggingFace\n", "\n", "hf_token = os.environ['HF_TOKEN']\n", "login(hf_token, add_to_git_credential=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "b0a6fb86-74a4-403c-ab25-6db2d74e9d2b", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": 6, "id": "c830ed3e-24ee-4af6-a07b-a1bfdcd39278", "metadata": {}, "outputs": [], "source": ["%matplotlib inline"]}, {"cell_type": "code", "execution_count": 156, "id": "5c9b05f4-c9eb-462c-8d86-de9140a2d985", "metadata": {}, "outputs": [], "source": ["# Let's avoid curating all our data again! Load in the pickle files:\n", "\n", "with open('train.pkl', 'rb') as file:\n", "    train = pickle.load(file)\n", "\n", "with open('test.pkl', 'rb') as file:\n", "    test = pickle.load(file)"]}, {"cell_type": "code", "execution_count": 157, "id": "e8367135-f40e-43e1-8f3c-09e990ab1194", "metadata": {}, "outputs": [], "source": ["# OpenAI recommends fine-tuning with populations of 50-100 examples\n", "# But as our examples are very small, I'm suggesting we go with 500 examples (and 1 epoch)\n", "\n", "fine_tune_train = train[:500]\n", "fine_tune_validation = train[500:550]"]}, {"cell_type": "markdown", "id": "8be4a889-81c3-42b1-a2fc-034cdc7321a6", "metadata": {}, "source": ["# Step 1\n", "\n", "Prepare our data for fine-tuning in JSONL (JSON Lines) format and upload to OpenAI"]}, {"cell_type": "code", "execution_count": 158, "id": "8ae2fb3c-1cff-4ce3-911e-627c970edd7b", "metadata": {}, "outputs": [], "source": ["# First let's work on a good prompt for a Frontier model\n", "# Notice that I'm removing the \" to the nearest dollar\"\n", "# When we train our own models, we'll need to make the problem as easy as possible, \n", "# but a Frontier model needs no such simplification.\n", "\n", "def messages_for(item):\n", "    system_message = \"You estimate prices of items. Reply only with the price, no explanation\"\n", "    user_prompt = item.test_prompt().replace(\" to the nearest dollar\",\"\").replace(\"\\n\\nPrice is $\",\"\")\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": user_prompt},\n", "        {\"role\": \"assistant\", \"content\": f\"Price is ${item.price:.2f}\"}\n", "    ]"]}, {"cell_type": "code", "execution_count": 159, "id": "c0e5b56c-8a0b-4d8e-a112-ce87efb4e152", "metadata": {}, "outputs": [], "source": ["# Convert the items into a list of json objects - a \"jsonl\" string\n", "# Each row represents a message in the form:\n", "# {\"messages\" : [{\"role\": \"system\", \"content\": \"You estimate prices...\n", "\n", "\n", "def make_jsonl(items):\n", "    result = \"\"\n", "    for item in items:\n", "        messages = messages_for(item)\n", "        messages_str = json.dumps(messages)\n", "        result += '{\"messages\": ' + messages_str +'}\\n'\n", "    return result.strip()"]}, {"cell_type": "code", "execution_count": 160, "id": "7734bff0-95c4-4e67-a87e-7e2254e2c67d", "metadata": {}, "outputs": [], "source": ["# Convert the items into jsonl and write them to a file\n", "\n", "def write_jsonl(items, filename):\n", "    with open(filename, \"w\") as f:\n", "        jsonl = make_jsonl(items)\n", "        f.write(jsonl)"]}, {"cell_type": "code", "execution_count": 161, "id": "393d3ad8-999a-4f99-8c04-339d9166d604", "metadata": {}, "outputs": [], "source": ["write_jsonl(fine_tune_train, \"fine_tune_train.jsonl\")"]}, {"cell_type": "code", "execution_count": 162, "id": "8e23927f-d73e-4668-ac20-abe6f14a56cb", "metadata": {}, "outputs": [], "source": ["write_jsonl(fine_tune_validation, \"fine_tune_validation.jsonl\")"]}, {"cell_type": "code", "execution_count": 163, "id": "d59ad8d2-c61a-448e-b7ed-232f1606970f", "metadata": {}, "outputs": [], "source": ["with open(\"fine_tune_train.jsonl\", \"rb\") as f:\n", "    train_file = openai.files.create(file=f, purpose=\"fine-tune\")"]}, {"cell_type": "code", "execution_count": 164, "id": "083fefba-fd54-47ce-9ff3-aabbc200846f", "metadata": {}, "outputs": [{"data": {"text/plain": ["FileObject(id='file-6nbrzujd8bzY6CJc3aWHfwBx', bytes=470371, created_at=1726782875, filename='fine_tune_train.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None)"]}, "execution_count": 164, "metadata": {}, "output_type": "execute_result"}], "source": ["train_file"]}, {"cell_type": "code", "execution_count": 165, "id": "97df3360-0760-4422-a556-5f26d23de6dc", "metadata": {}, "outputs": [], "source": ["with open(\"fine_tune_validation.jsonl\", \"rb\") as f:\n", "    validation_file = openai.files.create(file=f, purpose=\"fine-tune\")"]}, {"cell_type": "code", "execution_count": 166, "id": "a1abb8f3-9e52-4061-970c-fcf399d8ffa3", "metadata": {}, "outputs": [{"data": {"text/plain": ["FileObject(id='file-Zhltm81sv1mxTXpE7dBoE9Ca', bytes=47059, created_at=1726782877, filename='fine_tune_validation.jsonl', object='file', purpose='fine-tune', status='processed', status_details=None)"]}, "execution_count": 166, "metadata": {}, "output_type": "execute_result"}], "source": ["validation_file"]}, {"cell_type": "markdown", "id": "466052b9-9fb9-48f6-8cf9-c74e6ddc1394", "metadata": {}, "source": ["# Step 2\n", "\n", "I love Weights and Biases - a beautiful, free platform for monitoring training runs.  \n", "Weights and Biases is integrated with OpenAI for fine-tuning.\n", "\n", "First set up your weights & biases free account at:\n", "\n", "https://wandb.ai\n", "\n", "From the Avatar >> Settings menu, near the bottom, you can create an API key.\n", "\n", "Then visit the OpenAI dashboard at:\n", "\n", "https://platform.openai.com/account/organization\n", "\n", "In the integrations section, you can add your Weights & Biases key.\n", "\n", "## And now time to Fine-tune!"]}, {"cell_type": "code", "execution_count": 167, "id": "c7add1a7-a746-4d6e-a5f8-e25629b8b527", "metadata": {}, "outputs": [], "source": ["wandb_integration = {\"type\": \"wandb\", \"wandb\": {\"project\": \"gpt-pricer\"}}"]}, {"cell_type": "code", "execution_count": 168, "id": "45421b86-5531-4e42-ab19-d6abbb8f4c13", "metadata": {}, "outputs": [{"data": {"text/plain": ["FineTuningJob(id='ftjob-HkjHYhLQAfTvJYFDthWk0RCJ', created_at=**********, error=Error(code=None, message=None, param=None), fine_tuned_model=None, finished_at=None, hyperparameters=Hyperparameters(n_epochs=1, batch_size='auto', learning_rate_multiplier='auto'), model='gpt-4o-mini-2024-07-18', object='fine_tuning.job', organization_id='org-zh28s0IEJlsuAOdoWucLpzsX', result_files=[], seed=42, status='validating_files', trained_tokens=None, training_file='file-6nbrzujd8bzY6CJc3aWHfwBx', validation_file='file-Zhltm81sv1mxTXpE7dBoE9Ca', estimated_finish=None, integrations=[FineTuningJobWandbIntegrationObject(type='wandb', wandb=FineTuningJobWandbIntegration(project='gpt-pricer', entity=None, name=None, tags=None, run_id='ftjob-HkjHYhLQAfTvJYFDthWk0RCJ'))], user_provided_suffix='pricer')"]}, "execution_count": 168, "metadata": {}, "output_type": "execute_result"}], "source": ["openai.fine_tuning.jobs.create(\n", "    training_file=train_file.id,\n", "    validation_file=validation_file.id,\n", "    model=\"gpt-4o-mini-2024-07-18\",\n", "    seed=42,\n", "    hyperparameters={\"n_epochs\": 1},\n", "    integrations = [wandb_integration],\n", "    suffix=\"pricer\"\n", ")"]}, {"cell_type": "code", "execution_count": 169, "id": "aeb9de2e-542c-4e83-81c7-b6745133e48b", "metadata": {}, "outputs": [{"data": {"text/plain": ["SyncCursorPage[FineTuningJob](data=[FineTuningJob(id='ftjob-HkjHYhLQAfTvJYFDthWk0RCJ', created_at=**********, error=Error(code=None, message=None, param=None), fine_tuned_model=None, finished_at=None, hyperparameters=Hyperparameters(n_epochs=1, batch_size=1, learning_rate_multiplier=1.8), model='gpt-4o-mini-2024-07-18', object='fine_tuning.job', organization_id='org-zh28s0IEJlsuAOdoWucLpzsX', result_files=[], seed=42, status='running', trained_tokens=None, training_file='file-6nbrzujd8bzY6CJc3aWHfwBx', validation_file='file-Zhltm81sv1mxTXpE7dBoE9Ca', estimated_finish=1726783667, integrations=[FineTuningJobWandbIntegrationObject(type='wandb', wandb=FineTuningJobWandbIntegration(project='gpt-pricer', entity=None, name=None, tags=None, run_id='ftjob-HkjHYhLQAfTvJYFDthWk0RCJ'))], user_provided_suffix='pricer')], object='list', has_more=True)"]}, "execution_count": 169, "metadata": {}, "output_type": "execute_result"}], "source": ["openai.fine_tuning.jobs.list(limit=1)"]}, {"cell_type": "code", "execution_count": 170, "id": "40d24873-8ff5-413f-b0d4-8f77c28f18e1", "metadata": {}, "outputs": [], "source": ["job_id = openai.fine_tuning.jobs.list(limit=1).data[0].id"]}, {"cell_type": "code", "execution_count": 171, "id": "a32aef35-4b38-436c-ad00-d082f758efa7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'ftjob-HkjHYhLQAfTvJYFDthWk0RCJ'"]}, "execution_count": 171, "metadata": {}, "output_type": "execute_result"}], "source": ["job_id"]}, {"cell_type": "code", "execution_count": 172, "id": "a7e01247-c133-48e1-93d3-c79c399e6178", "metadata": {}, "outputs": [{"data": {"text/plain": ["FineTuningJob(id='ftjob-HkjHYhLQAfTvJYFDthWk0RCJ', created_at=**********, error=Error(code=None, message=None, param=None), fine_tuned_model=None, finished_at=None, hyperparameters=Hyperparameters(n_epochs=1, batch_size=1, learning_rate_multiplier=1.8), model='gpt-4o-mini-2024-07-18', object='fine_tuning.job', organization_id='org-zh28s0IEJlsuAOdoWucLpzsX', result_files=[], seed=42, status='running', trained_tokens=None, training_file='file-6nbrzujd8bzY6CJc3aWHfwBx', validation_file='file-Zhltm81sv1mxTXpE7dBoE9Ca', estimated_finish=1726783670, integrations=[FineTuningJobWandbIntegrationObject(type='wandb', wandb=FineTuningJobWandbIntegration(project='gpt-pricer', entity=None, name=None, tags=None, run_id='ftjob-HkjHYhLQAfTvJYFDthWk0RCJ'))], user_provided_suffix='pricer')"]}, "execution_count": 172, "metadata": {}, "output_type": "execute_result"}], "source": ["openai.fine_tuning.jobs.retrieve(job_id)"]}, {"cell_type": "code", "execution_count": 174, "id": "0f5150e1-b8de-485f-8eba-cf1e5b00c117", "metadata": {}, "outputs": [{"data": {"text/plain": ["[FineTuningJobEvent(id='ftevent-KUgnsCTWfh1lQVjoAPA7I6g2', created_at=1726783779, level='info', message='The job has successfully completed', object='fine_tuning.job.event', data={}, type='message'),\n", " FineTuningJobEvent(id='ftevent-O29FRCZPhnJGcBwM9n0enHwX', created_at=1726783775, level='info', message='Usage policy evaluations completed, model is now enabled for sampling', object='fine_tuning.job.event', data={}, type='message'),\n", " FineTuningJobEvent(id='ftevent-TMJ44u3HrnIRnxj60YGMhO7V', created_at=1726783694, level='info', message='Evaluating model against our usage policies before enabling', object='fine_tuning.job.event', data={}, type='message'),\n", " FineTuningJobEvent(id='ftevent-CV0SCQlLiLfpLxchtH8JpL2r', created_at=1726783694, level='info', message='New fine-tuned model created', object='fine_tuning.job.event', data={}, type='message'),\n", " FineTuningJobEvent(id='ftevent-sVA8wkDwkBXls9eoytwh6Yo8', created_at=1726783678, level='info', message='Step 500/500: training loss=1.00, validation loss=0.66, full validation loss=1.14', object='fine_tuning.job.event', data={'step': 500, 'train_loss': 0.9954185485839844, 'valid_loss': 0.6571156978607178, 'total_steps': 500, 'full_valid_loss': 1.142647624015808, 'train_mean_token_accuracy': 0.75, 'valid_mean_token_accuracy': 0.875, 'full_valid_mean_token_accuracy': 0.795}, type='metrics'),\n", " FineTuningJobEvent(id='ftevent-wrXdeQUacRDKUJNrgnOYPAvh', created_at=1726783663, level='info', message='Step 499/500: training loss=1.35', object='fine_tuning.job.event', data={'step': 499, 'train_loss': 1.354459524154663, 'total_steps': 500, 'train_mean_token_accuracy': 0.75}, type='metrics'),\n", " FineTuningJobEvent(id='ftevent-mbXMnAkBom6eJIxzjLCBCWKL', created_at=1726783658, level='info', message='Step 498/500: training loss=0.54', object='fine_tuning.job.event', data={'step': 498, 'train_loss': 0.5353636741638184, 'total_steps': 500, 'train_mean_token_accuracy': 0.875}, type='metrics'),\n", " FineTuningJobEvent(id='ftevent-jxOIiRdmCclPXQK1sNe6VqVy', created_at=1726783658, level='info', message='Step 497/500: training loss=1.12', object='fine_tuning.job.event', data={'step': 497, 'train_loss': 1.1181604862213135, 'total_steps': 500, 'train_mean_token_accuracy': 0.75}, type='metrics'),\n", " FineTuningJobEvent(id='ftevent-DjscZj40JQPlUPdtOV26722p', created_at=1726783658, level='info', message='Step 496/500: training loss=0.78', object='fine_tuning.job.event', data={'step': 496, 'train_loss': 0.7788112163543701, 'total_steps': 500, 'train_mean_token_accuracy': 0.75}, type='metrics'),\n", " FineTuningJobEvent(id='ftevent-QGdm9wZ8zdTizeoTh74mb9rq', created_at=1726783656, level='info', message='Step 495/500: training loss=1.17', object='fine_tuning.job.event', data={'step': 495, 'train_loss': 1.1684081554412842, 'total_steps': 500, 'train_mean_token_accuracy': 0.75}, type='metrics')]"]}, "execution_count": 174, "metadata": {}, "output_type": "execute_result"}], "source": ["openai.fine_tuning.jobs.list_events(fine_tuning_job_id=job_id, limit=10).data"]}, {"cell_type": "markdown", "id": "066fef03-8338-4526-9df3-89b649ad4f0a", "metadata": {}, "source": ["# Step 3\n", "\n", "Test our fine tuned model"]}, {"cell_type": "code", "execution_count": 175, "id": "fa4488cb-3c17-4eda-abd1-53c1c68a491b", "metadata": {}, "outputs": [], "source": ["fine_tuned_model_name = openai.fine_tuning.jobs.retrieve(job_id).fine_tuned_model"]}, {"cell_type": "code", "execution_count": 176, "id": "66ea68e8-ab1b-4f0d-aba4-a59574d8f85e", "metadata": {}, "outputs": [], "source": ["# The prompt\n", "\n", "def messages_for(item):\n", "    system_message = \"You estimate prices of items. Reply only with the price, no explanation\"\n", "    user_prompt = item.test_prompt().replace(\" to the nearest dollar\",\"\").replace(\"\\n\\nPrice is $\",\"\")\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": user_prompt},\n", "        {\"role\": \"assistant\", \"content\": \"Price is $\"}\n", "    ]"]}, {"cell_type": "code", "execution_count": 177, "id": "4ff92d61-0d27-4b0d-8b32-c9891016509b", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system',\n", "  'content': 'You estimate prices of items. Reply only with the price, no explanation'},\n", " {'role': 'user',\n", "  'content': \"How much does this cost?\\n\\nOEM AC Compressor w/A/C Repair Kit For Ford F150 F-150 V8 & Lincoln Mark LT 2007 2008 - BuyAutoParts NEW\\nAs one of the world's largest automotive parts suppliers, our parts are trusted every day by mechanics and vehicle owners worldwide. This A/C Compressor and Components Kit is manufactured and tested to the strictest OE standards for unparalleled performance. Built for trouble-free ownership and 100% visually inspected and quality tested, this A/C Compressor and Components Kit is backed by our 100% satisfaction guarantee. Guaranteed Exact Fit for easy installation 100% BRAND NEW, premium ISO/TS 16949 quality - tested to meet or exceed OEM specifications Engineered for superior durability, backed by industry-leading unlimited-mileage warranty Included in this K\"},\n", " {'role': 'assistant', 'content': 'Price is $'}]"]}, "execution_count": 177, "metadata": {}, "output_type": "execute_result"}], "source": ["# Try this out\n", "\n", "messages_for(test[0])"]}, {"cell_type": "code", "execution_count": 178, "id": "b1af1888-f94a-4106-b0d8-8a70939eec4e", "metadata": {}, "outputs": [], "source": ["# A utility function to extract the price from a string\n", "\n", "def get_price(s):\n", "    s = s.replace('$','').replace(',','')\n", "    match = re.search(r\"[-+]?\\d*\\.\\d+|\\d+\", s)\n", "    return float(match.group()) if match else 0"]}, {"cell_type": "code", "execution_count": 179, "id": "f138c5b7-bcc1-4085-aced-68dad1bf36b4", "metadata": {}, "outputs": [{"data": {"text/plain": ["99.99"]}, "execution_count": 179, "metadata": {}, "output_type": "execute_result"}], "source": ["get_price(\"The price is roughly $99.99 because blah blah\")"]}, {"cell_type": "code", "execution_count": 180, "id": "501a2a7a-69c8-451b-bbc0-398bcb9e1612", "metadata": {}, "outputs": [], "source": ["# The function for gpt-4o-mini\n", "\n", "def gpt_fine_tuned(item):\n", "    response = openai.chat.completions.create(\n", "        model=fine_tuned_model_name, \n", "        messages=messages_for(item),\n", "        seed=42,\n", "        max_tokens=7\n", "    )\n", "    reply = response.choices[0].message.content\n", "    return get_price(reply)"]}, {"cell_type": "code", "execution_count": 181, "id": "843d88b4-364a-431b-b48b-8a7c1f68b786", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["374.41\n", "174.99\n"]}], "source": ["print(test[0].price)\n", "print(gpt_fine_tuned(test[0]))"]}, {"cell_type": "code", "execution_count": 182, "id": "36bdd2c9-1859-4f99-a09f-3ec83b845b30", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[91m1: Guess: $174.99 Truth: $374.41 Error: $199.42 SLE: 0.57 Item: OEM AC Compressor w/A/C Repair Kit For F...\u001b[0m\n", "\u001b[91m2: Guess: $117.47 Truth: $225.11 Error: $107.64 SLE: 0.42 Item: Motorcraft YB3125 Fan Clutch\u001b[0m\n", "\u001b[91m3: Guess: $154.65 Truth: $61.68 Error: $92.97 SLE: 0.83 Item: Dorman 603-159 Front Washer Fluid Reserv...\u001b[0m\n", "\u001b[91m4: Guess: $277.65 Truth: $599.99 Error: $322.34 SLE: 0.59 Item: HP Premium 17.3-inch HD Plus Touchscreen...\u001b[0m\n", "\u001b[93m5: Guess: $65.65 Truth: $16.99 Error: $48.66 SLE: 1.72 Item: 5-Position Super Switch Pickup Selector ...\u001b[0m\n", "\u001b[92m6: Guess: $13.66 Truth: $31.99 Error: $18.33 SLE: 0.66 Item: Horror Bookmarks, Resin Horror Bookmarks...\u001b[0m\n", "\u001b[92m7: Guess: $139.98 Truth: $101.79 Error: $38.19 SLE: 0.10 Item: SK6241 - Stinger 4 Gauge 6000 Series Pow...\u001b[0m\n", "\u001b[92m8: Guess: $262.47 Truth: $289.00 Error: $26.53 SLE: 0.01 Item: Godox ML60Bi LED Light Kit, Handheld LED...\u001b[0m\n", "\u001b[91m9: Guess: $349.99 Truth: $635.86 Error: $285.87 SLE: 0.35 Item: Randall RG75DG3PLUS G3 Plus 100-Watt Com...\u001b[0m\n", "\u001b[93m10: Guess: $127.99 Truth: $65.99 Error: $62.00 SLE: 0.43 Item: HOLDWILL 6 Pack LED Shop Light, 4FT 24W ...\u001b[0m\n", "\u001b[93m11: Guess: $174.99 Truth: $254.21 Error: $79.22 SLE: 0.14 Item: Viking Horns V103C/1005ATK 3 Gallon Air ...\u001b[0m\n", "\u001b[91m12: Guess: $224.22 Truth: $412.99 Error: $188.77 SLE: 0.37 Item: CURT 70110 Custom Tow Bar Base Plate Bra...\u001b[0m\n", "\u001b[93m13: Guess: $143.65 Truth: $205.50 Error: $61.85 SLE: 0.13 Item: 10-Pack Solar HAMMERED BRONZE Finish Pos...\u001b[0m\n", "\u001b[93m14: Guess: $319.99 Truth: $248.23 Error: $71.76 SLE: 0.06 Item: COSTWAY Electric Tumble Dryer, Sliver\u001b[0m\n", "\u001b[91m15: Guess: $221.65 Truth: $399.00 Error: $177.35 SLE: 0.34 Item: FREE SIGNAL TV Transit 32\" 12 Volt DC Po...\u001b[0m\n", "\u001b[92m16: Guess: $322.22 Truth: $373.94 Error: $51.72 SLE: 0.02 Item: Bilstein 5100 Monotube Gas Shock Set com...\u001b[0m\n", "\u001b[92m17: Guess: $110.47 Truth: $92.89 Error: $17.58 SLE: 0.03 Item: Sangean K-200 Multi-Function Upright AM/...\u001b[0m\n", "\u001b[93m18: Guess: $127.22 Truth: $51.99 Error: $75.23 SLE: 0.78 Item: <PERSON> Lapboard Class ...\u001b[0m\n", "\u001b[91m19: Guess: $594.00 Truth: $179.00 Error: $415.00 SLE: 1.43 Item: Gigabyte AMD Radeon HD 7870 2 GB GDDR5 D...\u001b[0m\n", "\u001b[92m20: Guess: $24.66 Truth: $19.42 Error: $5.24 SLE: 0.05 Item: 3dRose LLC 8 x 8 x 0.25 Inches Bull Terr...\u001b[0m\n", "\u001b[91m21: Guess: $262.47 Truth: $539.95 Error: $277.48 SLE: 0.52 Item: ROKINON 85mm F1.4 Auto Focus Full Frame ...\u001b[0m\n", "\u001b[92m22: Guess: $110.47 Truth: $147.67 Error: $37.20 SLE: 0.08 Item: AUTOSAVER88 Headlight Assembly Compatibl...\u001b[0m\n", "\u001b[92m23: Guess: $47.95 Truth: $24.99 Error: $22.96 SLE: 0.40 Item: ASI NAUTICAL 2.5 Inches Opera Glasses Bi...\u001b[0m\n", "\u001b[91m24: Guess: $47.22 Truth: $149.00 Error: $101.78 SLE: 1.29 Item: <PERSON><PERSON><PERSON> TUBE OVERDRIVE TO100 Authentic...\u001b[0m\n", "\u001b[92m25: Guess: $18.65 Truth: $16.99 Error: $1.66 SLE: 0.01 Item: Fun Express Insect Finger Puppets - 24 f...\u001b[0m\n", "\u001b[92m26: Guess: $22.99 Truth: $7.99 Error: $15.00 SLE: 0.96 Item: WAFJAMF Roller Stamp Identity Theft Stam...\u001b[0m\n", "\u001b[91m27: Guess: $47.99 Truth: $199.99 Error: $152.00 SLE: 1.99 Item: <PERSON><PERSON><PERSON> Floor Lamp 2-Light 16\" ...\u001b[0m\n", "\u001b[91m28: Guess: $127.99 Truth: $251.45 Error: $123.46 SLE: 0.45 Item: Apple Watch Series 6 (GPS, 44mm) - Space...\u001b[0m\n", "\u001b[92m29: Guess: $276.99 Truth: $231.62 Error: $45.37 SLE: 0.03 Item: ICON 01725 Tandem Axle Fender Skirt FS17...\u001b[0m\n", "\u001b[93m30: Guess: $65.98 Truth: $135.00 Error: $69.02 SLE: 0.50 Item: SanDisk 128GB Ultra (10 Pack) MicroSD Cl...\u001b[0m\n", "\u001b[92m31: Guess: $322.22 Truth: $356.62 Error: $34.40 SLE: 0.01 Item: Velvac 2020,L,C/Hr,W,E2003,102\",Bk - 715...\u001b[0m\n", "\u001b[91m32: Guess: $127.99 Truth: $257.99 Error: $130.00 SLE: 0.49 Item: TCMT Passenger Backrest Sissy Bar & Lugg...\u001b[0m\n", "\u001b[93m33: Guess: $83.99 Truth: $27.99 Error: $56.00 SLE: 1.16 Item: Alnicov 63.5MM Brass Tremolo Block，Tremo...\u001b[0m\n", "\u001b[93m34: Guess: $127.22 Truth: $171.20 Error: $43.98 SLE: 0.09 Item: Subaru Forester Outback Legacy OEM Engin...\u001b[0m\n", "\u001b[91m35: Guess: $322.22 Truth: $225.00 Error: $97.22 SLE: 0.13 Item: Richmond Auto Upholstery - 2012 Dodge Ra...\u001b[0m\n", "\u001b[93m36: Guess: $146.99 Truth: $105.00 Error: $41.99 SLE: 0.11 Item: AP-39 Automotive Paint Primer Grey 2K Ur...\u001b[0m\n", "\u001b[91m37: Guess: $139.98 Truth: $299.99 Error: $160.01 SLE: 0.58 Item: Road Top Wireless Carplay Retrofit Kit D...\u001b[0m\n", "\u001b[92m38: Guess: $571.22 Truth: $535.09 Error: $36.13 SLE: 0.00 Item: Gibson Performance Exhaust 5658 Aluminiz...\u001b[0m\n", "\u001b[92m39: Guess: $10.99 Truth: $12.33 Error: $1.34 SLE: 0.01 Item: <PERSON> Happy Links - Baby Montessor...\u001b[0m\n", "\u001b[92m40: Guess: $110.47 Truth: $84.99 Error: $25.48 SLE: 0.07 Item: CANMORE H300 Handheld GPS Golf Device, S...\u001b[0m\n", "\u001b[92m41: Guess: $22.99 Truth: $15.99 Error: $7.00 SLE: 0.12 Item: DCPOWER AC Adapter Compatible Replacemen...\u001b[0m\n", "\u001b[92m42: Guess: $49.00 Truth: $62.44 Error: $13.44 SLE: 0.06 Item: Sharp, VX2128V, Commercial Desktop Calcu...\u001b[0m\n", "\u001b[93m43: Guess: $143.65 Truth: $82.99 Error: $60.66 SLE: 0.30 Item: <PERSON> & <PERSON>h Stork Gian...\u001b[0m\n", "\u001b[91m44: Guess: $174.00 Truth: $599.95 Error: $425.95 SLE: 1.52 Item: Sony SSCS8 2-Way 3-Driver Center Channel...\u001b[0m\n", "\u001b[93m45: Guess: $262.47 Truth: $194.99 Error: $67.48 SLE: 0.09 Item: ASUS Chromebook CX1, 14\" Full HD NanoEdg...\u001b[0m\n", "\u001b[93m46: Guess: $249.99 Truth: $344.95 Error: $94.96 SLE: 0.10 Item: FiiO X7 32GB Hi-Res Lossless Music Playe...\u001b[0m\n", "\u001b[92m47: Guess: $22.99 Truth: $37.99 Error: $15.00 SLE: 0.24 Item: TORRO Leather Case Compatible with iPhon...\u001b[0m\n", "\u001b[93m48: Guess: $174.57 Truth: $224.35 Error: $49.78 SLE: 0.06 Item: Universal Air Conditioner KT 1031 A/C Co...\u001b[0m\n", "\u001b[93m49: Guess: $572.47 Truth: $814.00 Error: $241.53 SLE: 0.12 Item: Street Series Stainless Performance Cat-...\u001b[0m\n", "\u001b[92m50: Guess: $393.69 Truth: $439.88 Error: $46.19 SLE: 0.01 Item: Lenovo IdeaPad 3 14-inch Laptop, 14.0-in...\u001b[0m\n", "\u001b[93m51: Guess: $221.65 Truth: $341.43 Error: $119.78 SLE: 0.19 Item: Access Bed Covers TonnoSport 22050219 - ...\u001b[0m\n", "\u001b[92m52: Guess: $47.95 Truth: $46.78 Error: $1.17 SLE: 0.00 Item: G.I. JOE Hasbro 3 3/4\" Wave 5 Action Fig...\u001b[0m\n", "\u001b[91m53: Guess: $262.47 Truth: $171.44 Error: $91.03 SLE: 0.18 Item: T&S Brass B-0232-BST Double Pantry Fauce...\u001b[0m\n", "\u001b[91m54: Guess: $146.99 Truth: $458.00 Error: $311.01 SLE: 1.28 Item: ZTUOAUMA Fuel Injection Pump 3090942 309...\u001b[0m\n", "\u001b[92m55: Guess: $128.66 Truth: $130.75 Error: $2.09 SLE: 0.00 Item: 2AP18AA#ABA Hp Prime Graphing Calculator...\u001b[0m\n", "\u001b[92m56: Guess: $66.47 Truth: $83.81 Error: $17.34 SLE: 0.05 Item: Lowrance 000-0119-83 Nmea 2000 25' Exten...\u001b[0m\n", "\u001b[91m57: Guess: $47.22 Truth: $386.39 Error: $339.17 SLE: 4.34 Item: Jeep Genuine Accessories 82213051 Hood L...\u001b[0m\n", "\u001b[92m58: Guess: $139.98 Truth: $169.00 Error: $29.02 SLE: 0.04 Item: GODOX CB-06 Hard Carrying Case with Whee...\u001b[0m\n", "\u001b[92m59: Guess: $47.22 Truth: $17.95 Error: $29.27 SLE: 0.87 Item: Au-Tomotive Gold, INC. Ford Black Valet ...\u001b[0m\n", "\u001b[91m60: Guess: $127.99 Truth: $269.00 Error: $141.01 SLE: 0.55 Item: Snailfly Black Roof Rack Rail + Cross Ba...\u001b[0m\n", "\u001b[92m61: Guess: $47.99 Truth: $77.77 Error: $29.78 SLE: 0.23 Item: KING SHA Anti Glare LED Track Lighting H...\u001b[0m\n", "\u001b[92m62: Guess: $110.47 Truth: $88.99 Error: $21.48 SLE: 0.05 Item: APS Compatible with Chevy Silverado 1500...\u001b[0m\n", "\u001b[92m63: Guess: $322.22 Truth: $364.41 Error: $42.19 SLE: 0.02 Item: Wilwood Engineering 14011291R Brake Cali...\u001b[0m\n", "\u001b[92m64: Guess: $154.65 Truth: $127.03 Error: $27.62 SLE: 0.04 Item: ACDelco Gold 336-1925<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>...\u001b[0m\n", "\u001b[91m65: Guess: $390.22 Truth: $778.95 Error: $388.73 SLE: 0.48 Item: UWS EC10783 69-<PERSON><PERSON> <PERSON>e Black Heavy-Wa...\u001b[0m\n", "\u001b[93m66: Guess: $127.99 Truth: $206.66 Error: $78.67 SLE: 0.23 Item: Dell Latitude E5440 14in Business Laptop...\u001b[0m\n", "\u001b[92m67: Guess: $65.98 Truth: $35.94 Error: $30.04 SLE: 0.35 Item: (Plug and Play) Spare Tire Brake Light W...\u001b[0m\n", "\u001b[92m68: Guess: $110.47 Truth: $149.00 Error: $38.53 SLE: 0.09 Item: The Ultimate Roadside Rescue Assistant\u001b[0m\n", "\u001b[92m69: Guess: $221.65 Truth: $251.98 Error: $30.33 SLE: 0.02 Item: Brand New 18\" x 8.5\" Replacement Wheel f...\u001b[0m\n", "\u001b[92m70: Guess: $128.66 Truth: $160.00 Error: $31.34 SLE: 0.05 Item: Headlight Headlamp LH Left & RH Right Pa...\u001b[0m\n", "\u001b[92m71: Guess: $54.99 Truth: $39.99 Error: $15.00 SLE: 0.10 Item: <PERSON><PERSON> And <PERSON>itch Deluxe Oversize Print La...\u001b[0m\n", "\u001b[91m72: Guess: $143.77 Truth: $362.41 Error: $218.64 SLE: 0.85 Item: AC Compressor & A/C Clutch For Hyundai A...\u001b[0m\n", "\u001b[92m73: Guess: $277.65 Truth: $344.00 Error: $66.35 SLE: 0.05 Item: House Of Troy PIN475-AB Pinnacle Collect...\u001b[0m\n", "\u001b[91m74: Guess: $127.22 Truth: $25.09 Error: $102.13 SLE: 2.54 Item: Juno T29 WH Floating Electrical Feed Sin...\u001b[0m\n", "\u001b[93m75: Guess: $110.47 Truth: $175.95 Error: $65.48 SLE: 0.21 Item: Sherman GO-PARTS - for 2013-2016 Toyota ...\u001b[0m\n", "\u001b[92m76: Guess: $117.22 Truth: $132.64 Error: $15.42 SLE: 0.02 Item: Roland RPU-3 Electronic Keyboard Pedal o...\u001b[0m\n", "\u001b[93m77: Guess: $277.65 Truth: $422.99 Error: $145.34 SLE: 0.18 Item: Rockland VMI14 12,000 Pound 12 Volt DC E...\u001b[0m\n", "\u001b[92m78: Guess: $143.65 Truth: $146.48 Error: $2.83 SLE: 0.00 Item: Max Advanced Brakes Elite XDS Front Cros...\u001b[0m\n", "\u001b[91m79: Guess: $47.22 Truth: $156.83 Error: $109.61 SLE: 1.41 Item: Quality-Built 11030 Premium Quality Alte...\u001b[0m\n", "\u001b[92m80: Guess: $219.00 Truth: $251.99 Error: $32.99 SLE: 0.02 Item: Lucida LG-510 Student Classical Guitar, ...\u001b[0m\n", "\u001b[91m81: Guess: $322.22 Truth: $940.33 Error: $618.11 SLE: 1.14 Item: Longacre 52-79800 Aluminum Turn Plates\u001b[0m\n", "\u001b[92m82: Guess: $83.99 Truth: $52.99 Error: $31.00 SLE: 0.21 Item: Motion Pro 08-0380 Adjustable Torque Wre...\u001b[0m\n", "\u001b[91m83: Guess: $127.22 Truth: $219.95 Error: $92.73 SLE: 0.30 Item: Glyph Thunderbolt 3 NVMe Dock (0 GB)\u001b[0m\n", "\u001b[91m84: Guess: $221.65 Truth: $441.03 Error: $219.38 SLE: 0.47 Item: TOYO Open Country MT Performance Radial ...\u001b[0m\n", "\u001b[93m85: Guess: $128.66 Truth: $168.98 Error: $40.32 SLE: 0.07 Item: Razer Seiren X USB Streaming Microphone ...\u001b[0m\n", "\u001b[92m86: Guess: $3.99 Truth: $2.49 Error: $1.50 SLE: 0.13 Item: Happy Birthday to Dad From Your Daughter...\u001b[0m\n", "\u001b[92m87: Guess: $127.22 Truth: $98.62 Error: $28.60 SLE: 0.06 Item: Little Tikes My Real Jam First Concert S...\u001b[0m\n", "\u001b[91m88: Guess: $47.22 Truth: $256.95 Error: $209.73 SLE: 2.81 Item: Studio M Peace and Harmony Art Pole Comm...\u001b[0m\n", "\u001b[92m89: Guess: $34.65 Truth: $30.99 Error: $3.66 SLE: 0.01 Item: MyVolts 12V Power Supply Adaptor Compati...\u001b[0m\n", "\u001b[93m90: Guess: $393.69 Truth: $569.84 Error: $176.15 SLE: 0.14 Item: Dell Latitude 7212 Rugged Extreme Tablet...\u001b[0m\n", "\u001b[91m91: Guess: $66.47 Truth: $177.99 Error: $111.52 SLE: 0.95 Item: Covermates Contour Fit Car Cover - Light...\u001b[0m\n", "\u001b[91m92: Guess: $572.47 Truth: $997.99 Error: $425.52 SLE: 0.31 Item: Westin 57-4025 Black HDX Grille Guard fi...\u001b[0m\n", "\u001b[92m93: Guess: $224.22 Truth: $219.00 Error: $5.22 SLE: 0.00 Item: Fieldpiece JL2 Job Link Wireless App Tra...\u001b[0m\n", "\u001b[92m94: Guess: $221.65 Truth: $225.55 Error: $3.90 SLE: 0.00 Item: hansgrohe Talis S Modern Premium Easy Cl...\u001b[0m\n", "\u001b[92m95: Guess: $579.99 Truth: $495.95 Error: $84.04 SLE: 0.02 Item: G-Technology G-SPEED eS PRO High-Perform...\u001b[0m\n", "\u001b[93m96: Guess: $571.22 Truth: $942.37 Error: $371.15 SLE: 0.25 Item: DreamLine SHDR-1960723L-01 Shower Door, ...\u001b[0m\n", "\u001b[92m97: Guess: $22.22 Truth: $1.94 Error: $20.28 SLE: 4.27 Item: Sanctuary Square Backplate Finish: Oiled...\u001b[0m\n", "\u001b[93m98: Guess: $221.65 Truth: $284.34 Error: $62.69 SLE: 0.06 Item: Pelican Protector 1750 Long Case - Multi...\u001b[0m\n", "\u001b[93m99: Guess: $127.22 Truth: $171.90 Error: $44.68 SLE: 0.09 Item: Brock Replacement Driver and Passenger H...\u001b[0m\n", "\u001b[91m100: Guess: $47.99 Truth: $144.99 Error: $97.00 SLE: 1.19 Item: Carlinkit Ai Box Mini, Android 11, Multi...\u001b[0m\n", "\u001b[91m101: Guess: $799.95 Truth: $470.47 Error: $329.48 SLE: 0.28 Item: StarDot NetCamLIVE2 YouTube Live Stream ...\u001b[0m\n", "\u001b[92m102: Guess: $47.22 Truth: $66.95 Error: $19.73 SLE: 0.12 Item: Atomic Compatible FILXXCAR0016 16x25x5 M...\u001b[0m\n", "\u001b[93m103: Guess: $47.22 Truth: $117.00 Error: $69.78 SLE: 0.80 Item: Bandai Awakening of S. H. s.h.figuarts s...\u001b[0m\n", "\u001b[91m104: Guess: $276.10 Truth: $172.14 Error: $103.96 SLE: 0.22 Item: Fit System 62135G Passenger Side Towing ...\u001b[0m\n", "\u001b[91m105: Guess: $221.65 Truth: $392.74 Error: $171.09 SLE: 0.33 Item: Black Horse Black Aluminum Exceed Runnin...\u001b[0m\n", "\u001b[92m106: Guess: $34.98 Truth: $16.99 Error: $17.99 SLE: 0.48 Item: Dearsun Twinkle Star Color Night Light P...\u001b[0m\n", "\u001b[92m107: Guess: $8.22 Truth: $1.34 Error: $6.88 SLE: 1.88 Item: Pokemon - Gallade Spirit Link (83/108) -...\u001b[0m\n", "\u001b[93m108: Guess: $239.99 Truth: $349.98 Error: $109.99 SLE: 0.14 Item: Ibanez GA34STCE-NT GIO Series Classical ...\u001b[0m\n", "\u001b[93m109: Guess: $277.65 Truth: $370.71 Error: $93.06 SLE: 0.08 Item: Set 2 Heavy Duty 12-16.5 12x16.5 12 Ply ...\u001b[0m\n", "\u001b[93m110: Guess: $139.98 Truth: $65.88 Error: $74.10 SLE: 0.56 Item: Hairpin Table Legs 28\" Heavy Duty Hairpi...\u001b[0m\n", "\u001b[93m111: Guess: $139.98 Truth: $229.99 Error: $90.01 SLE: 0.24 Item: Marada Racing Seat with Adjustable Slide...\u001b[0m\n", "\u001b[92m112: Guess: $29.65 Truth: $9.14 Error: $20.51 SLE: 1.22 Item: Remington Industries 24UL1007STRWHI25 24...\u001b[0m\n", "\u001b[91m113: Guess: $594.00 Truth: $199.00 Error: $395.00 SLE: 1.19 Item: Acer S3-391-6046 13.3-inch Ultrabook, In...\u001b[0m\n", "\u001b[93m114: Guess: $179.99 Truth: $109.99 Error: $70.00 SLE: 0.24 Item: ICBEAMER 7\" RGB LED Headlights Bulb Halo...\u001b[0m\n", "\u001b[91m115: Guess: $221.65 Truth: $570.42 Error: $348.77 SLE: 0.89 Item: R1 Concepts Front Rear Brakes and Rotors...\u001b[0m\n", "\u001b[91m116: Guess: $600.97 Truth: $279.99 Error: $320.98 SLE: 0.58 Item: Camplux 2.64 GPM Tankless , Outdoor Port...\u001b[0m\n", "\u001b[92m117: Guess: $33.98 Truth: $30.99 Error: $2.99 SLE: 0.01 Item: KNOKLOCK 10 Pack 3.75 Inch(96mm) Kitchen...\u001b[0m\n", "\u001b[92m118: Guess: $31.99 Truth: $31.99 Error: $0.00 SLE: 0.00 Item: Valley Enterprises Yaesu USB FTDI CT-62 ...\u001b[0m\n", "\u001b[92m119: Guess: $22.98 Truth: $15.90 Error: $7.08 SLE: 0.12 Item: G9 LED Light Bulbs，8W，75W 100W replaceme...\u001b[0m\n", "\u001b[93m120: Guess: $118.99 Truth: $45.99 Error: $73.00 SLE: 0.88 Item: ZCHAOZ 4 Lights Antique White Farmhouse ...\u001b[0m\n", "\u001b[93m121: Guess: $154.65 Truth: $113.52 Error: $41.13 SLE: 0.09 Item: Honeywell TH8320R1003 Honeywell VisionPr...\u001b[0m\n", "\u001b[92m122: Guess: $453.65 Truth: $516.99 Error: $63.34 SLE: 0.02 Item: Patriot Exhaust H8013-1 1-7/8\" Clippster...\u001b[0m\n", "\u001b[92m123: Guess: $174.99 Truth: $196.99 Error: $22.00 SLE: 0.01 Item: Fitrite Autopart New Front Left Driver S...\u001b[0m\n", "\u001b[92m124: Guess: $22.22 Truth: $46.55 Error: $24.33 SLE: 0.51 Item: Technical Precision Replacement for GE G...\u001b[0m\n", "\u001b[93m125: Guess: $262.47 Truth: $356.99 Error: $94.52 SLE: 0.09 Item: Covercraft Carhartt SeatSaver Front Row ...\u001b[0m\n", "\u001b[93m126: Guess: $219.65 Truth: $319.95 Error: $100.30 SLE: 0.14 Item: Sennheiser SD Pro 2 (506008) - Double-Si...\u001b[0m\n", "\u001b[93m127: Guess: $47.22 Truth: $96.06 Error: $48.84 SLE: 0.49 Item: Hitachi MAF0110 Mass Air Flow Sensor\u001b[0m\n", "\u001b[92m128: Guess: $221.65 Truth: $190.99 Error: $30.66 SLE: 0.02 Item: AmScope SE305R-P-LED-PS36A 10X-30X LED C...\u001b[0m\n", "\u001b[91m129: Guess: $127.22 Truth: $257.95 Error: $130.73 SLE: 0.49 Item: Front Left Driver Side Window Regulator ...\u001b[0m\n", "\u001b[93m130: Guess: $139.98 Truth: $62.95 Error: $77.03 SLE: 0.62 Item: Premium Replica Hubcap Set, Fits Nissan ...\u001b[0m\n", "\u001b[91m131: Guess: $174.99 Truth: $47.66 Error: $127.33 SLE: 1.65 Item: Excellerations Phonics Spelling Game for...\u001b[0m\n", "\u001b[92m132: Guess: $221.65 Truth: $226.99 Error: $5.34 SLE: 0.00 Item: RC4WD BigDog Dual Axle Scale Car/Truck T...\u001b[0m\n", "\u001b[93m133: Guess: $274.99 Truth: $359.95 Error: $84.96 SLE: 0.07 Item: Unknown Stage 2 Clutch Kit - Low Altitud...\u001b[0m\n", "\u001b[92m134: Guess: $47.22 Truth: $78.40 Error: $31.18 SLE: 0.25 Item: 2002-2008 Dodge Ram 1500 Mopar 4X4 Emble...\u001b[0m\n", "\u001b[93m135: Guess: $110.47 Truth: $172.77 Error: $62.30 SLE: 0.20 Item: Pro Comp Alloys Series 89 Wheel with Pol...\u001b[0m\n", "\u001b[93m136: Guess: $390.22 Truth: $316.45 Error: $73.77 SLE: 0.04 Item: Detroit Axle - Front Rear Strut & Coil S...\u001b[0m\n", "\u001b[92m137: Guess: $119.49 Truth: $87.99 Error: $31.50 SLE: 0.09 Item: ECCPP Rear Wheel Axle Replacement fit fo...\u001b[0m\n", "\u001b[91m138: Guess: $127.22 Truth: $226.63 Error: $99.41 SLE: 0.33 Item: Dell Latitude E6520 Intel i7-2720QM 2.20...\u001b[0m\n", "\u001b[92m139: Guess: $22.98 Truth: $31.49 Error: $8.51 SLE: 0.09 Item: F FIERCE CYCLE 251pcs Black Universal Mo...\u001b[0m\n", "\u001b[92m140: Guess: $219.65 Truth: $196.00 Error: $23.65 SLE: 0.01 Item: Flash Furniture 4 Pk. HERCULES Series 88...\u001b[0m\n", "\u001b[93m141: Guess: $154.65 Truth: $78.40 Error: $76.25 SLE: 0.45 Item: B&M 30287 Throttle Valve/Kickdown Cable,...\u001b[0m\n", "\u001b[92m142: Guess: $143.65 Truth: $116.25 Error: $27.40 SLE: 0.04 Item: Gates TCK226 PowerGrip Premium Timing Be...\u001b[0m\n", "\u001b[91m143: Guess: $277.65 Truth: $112.78 Error: $164.87 SLE: 0.80 Item: Monroe Shocks & Struts Quick-Strut 17149...\u001b[0m\n", "\u001b[92m144: Guess: $66.47 Truth: $27.32 Error: $39.15 SLE: 0.75 Item: Feit Electric BPMR16/GU10/930CA/6 35W EQ...\u001b[0m\n", "\u001b[93m145: Guess: $77.77 Truth: $145.91 Error: $68.14 SLE: 0.39 Item: Yellow Jacket 2806 Contractor Extension ...\u001b[0m\n", "\u001b[91m146: Guess: $262.47 Truth: $171.09 Error: $91.38 SLE: 0.18 Item: Garage-Pro Tailgate SET Compatible with ...\u001b[0m\n", "\u001b[91m147: Guess: $47.22 Truth: $167.95 Error: $120.73 SLE: 1.57 Item: 3M Perfect It Buffing and Polishing Kit ...\u001b[0m\n", "\u001b[92m148: Guess: $65.98 Truth: $28.49 Error: $37.49 SLE: 0.67 Item: Chinese Style Dollhouse Model DIY Miniat...\u001b[0m\n", "\u001b[92m149: Guess: $146.22 Truth: $122.23 Error: $23.99 SLE: 0.03 Item: Generic NRG Innovations SRK-161H Steerin...\u001b[0m\n", "\u001b[92m150: Guess: $65.65 Truth: $32.99 Error: $32.66 SLE: 0.45 Item: Learning Resources Coding Critters Range...\u001b[0m\n", "\u001b[92m151: Guess: $66.47 Truth: $71.20 Error: $4.73 SLE: 0.00 Item: Bosch Automotive 15463 Oxygen Sensor, OE...\u001b[0m\n", "\u001b[93m152: Guess: $57.65 Truth: $112.75 Error: $55.10 SLE: 0.44 Item: Case of 24-2 Inch Blue Painters Tape - 6...\u001b[0m\n", "\u001b[92m153: Guess: $154.65 Truth: $142.43 Error: $12.22 SLE: 0.01 Item: MOCA Engine Water Pump & Fan Clutch fit ...\u001b[0m\n", "\u001b[91m154: Guess: $196.99 Truth: $398.99 Error: $202.00 SLE: 0.49 Item: SAREMAS Foot Step Bars for Hyundai Palis...\u001b[0m\n", "\u001b[93m155: Guess: $599.00 Truth: $449.00 Error: $150.00 SLE: 0.08 Item: Gretsch G9210 Square Neck Boxcar Mahogan...\u001b[0m\n", "\u001b[93m156: Guess: $129.99 Truth: $189.00 Error: $59.01 SLE: 0.14 Item: NikoMaku Mirror Dash Cam Front and Rear ...\u001b[0m\n", "\u001b[92m157: Guess: $110.47 Truth: $120.91 Error: $10.44 SLE: 0.01 Item: Fenix HP25R v2.0 USB-C Rechargeable Head...\u001b[0m\n", "\u001b[93m158: Guess: $127.99 Truth: $203.53 Error: $75.54 SLE: 0.21 Item: R&L Racing Heavy Duty Roll-Up Soft Tonne...\u001b[0m\n", "\u001b[92m159: Guess: $393.69 Truth: $349.99 Error: $43.70 SLE: 0.01 Item: Garmin 010-02258-10 GPSMAP 64sx, Handhel...\u001b[0m\n", "\u001b[92m160: Guess: $30.47 Truth: $34.35 Error: $3.88 SLE: 0.01 Item: <PERSON> 5-7/8\" X 8-1/2\" X 3/16\" Thick Heav...\u001b[0m\n", "\u001b[92m161: Guess: $359.00 Truth: $384.99 Error: $25.99 SLE: 0.00 Item: GAOMON PD2200 Pen Display & 20 Pen Nibs ...\u001b[0m\n", "\u001b[91m162: Guess: $393.69 Truth: $211.00 Error: $182.69 SLE: 0.39 Item: VXMOTOR for 97-03 Ford F150/F250 Lightdu...\u001b[0m\n", "\u001b[92m163: Guess: $139.98 Truth: $129.00 Error: $10.98 SLE: 0.01 Item: HP EliteBook 2540p Intel Core i7-640LM X...\u001b[0m\n", "\u001b[91m164: Guess: $10.49 Truth: $111.45 Error: $100.96 SLE: 5.20 Item: Green EPX Mixing Nozzles 100-Pack-fits 3...\u001b[0m\n", "\u001b[92m165: Guess: $66.47 Truth: $81.12 Error: $14.65 SLE: 0.04 Item: Box Partners 6 1/4 x 3 1/8\" 13 Pt. Manil...\u001b[0m\n", "\u001b[93m166: Guess: $277.65 Truth: $457.08 Error: $179.43 SLE: 0.25 Item: Vixen Air 1/2\" NPT Air Ride Suspension H...\u001b[0m\n", "\u001b[92m167: Guess: $69.99 Truth: $49.49 Error: $20.50 SLE: 0.12 Item: Smart Floor Lamp, 2700-6500K+RGBPink Mul...\u001b[0m\n", "\u001b[92m168: Guess: $47.99 Truth: $80.56 Error: $32.57 SLE: 0.26 Item: SOZG 324mm Wheelbase Body Shell RC Car B...\u001b[0m\n", "\u001b[92m169: Guess: $262.47 Truth: $278.39 Error: $15.92 SLE: 0.00 Item: <PERSON> ET Street S/S Racing Rad...\u001b[0m\n", "\u001b[91m170: Guess: $146.22 Truth: $364.50 Error: $218.28 SLE: 0.83 Item: Pirelli 275/40R20 106W XL RFT P0 PZ4-LUX...\u001b[0m\n", "\u001b[91m171: Guess: $174.99 Truth: $378.99 Error: $204.00 SLE: 0.59 Item: Torklift C3212 Rear Tie Down\u001b[0m\n", "\u001b[92m172: Guess: $127.22 Truth: $165.28 Error: $38.06 SLE: 0.07 Item: Cardone 78-4226 Remanufactured Ford Comp...\u001b[0m\n", "\u001b[91m173: Guess: $154.65 Truth: $56.74 Error: $97.91 SLE: 0.98 Item: Kidde AccessPoint 001798 Supra TouchPoin...\u001b[0m\n", "\u001b[93m174: Guess: $221.65 Truth: $307.95 Error: $86.30 SLE: 0.11 Item: 3M Protecta 3100414 Self Retracting Life...\u001b[0m\n", "\u001b[91m175: Guess: $127.22 Truth: $38.00 Error: $89.22 SLE: 1.42 Item: Plantronics 89435-01 <PERSON>d Headset, Blac...\u001b[0m\n", "\u001b[92m176: Guess: $76.99 Truth: $53.00 Error: $23.99 SLE: 0.14 Item: Logitech K750 Wireless Solar Keyboard fo...\u001b[0m\n", "\u001b[92m177: Guess: $499.98 Truth: $498.00 Error: $1.98 SLE: 0.00 Item: Olympus PEN E-PL9 Body Only with 3-Inch ...\u001b[0m\n", "\u001b[91m178: Guess: $154.65 Truth: $53.99 Error: $100.66 SLE: 1.08 Item: Beck/Arnley 051-6066 Hub & Bearing Assem...\u001b[0m\n", "\u001b[91m179: Guess: $194.99 Truth: $350.00 Error: $155.01 SLE: 0.34 Item: Eibach Pro-Kit Performance Springs E10-6...\u001b[0m\n", "\u001b[93m180: Guess: $390.22 Truth: $299.95 Error: $90.27 SLE: 0.07 Item: LEGO DC Batman 1989 Batwing 76161 Displa...\u001b[0m\n", "\u001b[93m181: Guess: $143.65 Truth: $94.93 Error: $48.72 SLE: 0.17 Item: Kingston Brass KS3608PL Restoration 4-In...\u001b[0m\n", "\u001b[92m182: Guess: $349.00 Truth: $379.00 Error: $30.00 SLE: 0.01 Item: Polk Vanishing Series 265-LS In-Wall 3-W...\u001b[0m\n", "\u001b[93m183: Guess: $221.09 Truth: $299.95 Error: $78.86 SLE: 0.09 Item: Spec-D Tuning LED Projector Headlights G...\u001b[0m\n", "\u001b[92m184: Guess: $10.99 Truth: $24.99 Error: $14.00 SLE: 0.60 Item: RICHMOND & FINCH Airpod Pro Case, Green ...\u001b[0m\n", "\u001b[92m185: Guess: $66.47 Truth: $41.04 Error: $25.43 SLE: 0.22 Item: LFA Industries 43B-5A-33JT 1/16-1/2-1.5-...\u001b[0m\n", "\u001b[91m186: Guess: $47.99 Truth: $327.90 Error: $279.91 SLE: 3.63 Item: SAUTVS LED Headlight Assembly for Slings...\u001b[0m\n", "\u001b[92m187: Guess: $22.69 Truth: $10.99 Error: $11.70 SLE: 0.46 Item: 2 Pack Combo Womens Safety Glasses Impac...\u001b[0m\n", "\u001b[92m188: Guess: $14.98 Truth: $14.99 Error: $0.01 SLE: 0.00 Item: Arepa - Venezuelan cuisine - Venezuela P...\u001b[0m\n", "\u001b[92m189: Guess: $49.65 Truth: $84.95 Error: $35.30 SLE: 0.28 Item: Schlage Lock Company KS23D2300 Padlock, ...\u001b[0m\n", "\u001b[91m190: Guess: $196.99 Truth: $111.00 Error: $85.99 SLE: 0.32 Item: Techni Mobili White Sit to Stand Mobile ...\u001b[0m\n", "\u001b[92m191: Guess: $127.22 Truth: $123.73 Error: $3.49 SLE: 0.00 Item: Special Lite Products Contemporary Wall ...\u001b[0m\n", "\u001b[93m192: Guess: $393.69 Truth: $557.38 Error: $163.69 SLE: 0.12 Item: Tascam DP-24SD 24-Track Digital Portastu...\u001b[0m\n", "\u001b[92m193: Guess: $66.47 Truth: $95.55 Error: $29.08 SLE: 0.13 Item: Glow Lighting 636CC10SP Vista Crystal Fl...\u001b[0m\n", "\u001b[92m194: Guess: $146.22 Truth: $154.00 Error: $7.78 SLE: 0.00 Item: Z3 <PERSON>, <PERSON>, <PERSON><PERSON>, Wi...\u001b[0m\n", "\u001b[91m195: Guess: $499.98 Truth: $198.99 Error: $300.99 SLE: 0.84 Item: Olympus E-20 5MP Digital Camera w/ 4x Op...\u001b[0m\n", "\u001b[91m196: Guess: $139.98 Truth: $430.44 Error: $290.46 SLE: 1.25 Item: PHYNEDI 1:1000 World Trade Center (1973-...\u001b[0m\n", "\u001b[92m197: Guess: $10.99 Truth: $45.67 Error: $34.68 SLE: 1.85 Item: YANGHUAN Unstable Unicorns Adventure Car...\u001b[0m\n", "\u001b[91m198: Guess: $127.22 Truth: $249.00 Error: $121.78 SLE: 0.45 Item: Interlogix NX-1820E NetworX Touch Screen...\u001b[0m\n", "\u001b[92m199: Guess: $57.99 Truth: $42.99 Error: $15.00 SLE: 0.09 Item: Steering Damper,Universal Motorcycle Han...\u001b[0m\n", "\u001b[91m200: Guess: $262.47 Truth: $181.33 Error: $81.14 SLE: 0.14 Item: Amprobe TIC 410A Hot Stick Attachment\u001b[0m\n", "\u001b[92m201: Guess: $8.22 Truth: $6.03 Error: $2.19 SLE: 0.07 Item: MyCableMart 3.5mm Plug/Jack, 4 Conductor...\u001b[0m\n", "\u001b[92m202: Guess: $22.69 Truth: $29.99 Error: $7.30 SLE: 0.07 Item: OtterBox + Pop Symmetry Series Case for ...\u001b[0m\n", "\u001b[92m203: Guess: $726.65 Truth: $899.00 Error: $172.35 SLE: 0.05 Item: Dell XPS X8700-1572BLK Desktop ( Intel C...\u001b[0m\n", "\u001b[91m204: Guess: $224.95 Truth: $399.99 Error: $175.04 SLE: 0.33 Item: Franklin Iron Works Sperry Industrial Br...\u001b[0m\n", "\u001b[92m205: Guess: $18.65 Truth: $4.66 Error: $13.99 SLE: 1.55 Item: Avery Legal Dividers, Standard Collated ...\u001b[0m\n", "\u001b[91m206: Guess: $127.22 Truth: $261.41 Error: $134.19 SLE: 0.51 Item: Moen 8346 Commercial Posi-Temp Pressure ...\u001b[0m\n", "\u001b[92m207: Guess: $128.66 Truth: $136.97 Error: $8.31 SLE: 0.00 Item: Carlisle Versa Trail ATR All Terrain Rad...\u001b[0m\n", "\u001b[93m208: Guess: $139.98 Truth: $79.00 Error: $60.98 SLE: 0.32 Item: SUNWAYFOTO 44mm Tripod Ball Head Arca Co...\u001b[0m\n", "\u001b[91m209: Guess: $164.50 Truth: $444.99 Error: $280.49 SLE: 0.98 Item: NanoBeam AC NBE-5AC-Gen2-US 4 Units 5GHz...\u001b[0m\n", "\u001b[92m210: Guess: $390.22 Truth: $411.94 Error: $21.72 SLE: 0.00 Item: WULF 4\" Front 2\" Rear Leveling Lift Kit ...\u001b[0m\n", "\u001b[93m211: Guess: $196.43 Truth: $148.40 Error: $48.03 SLE: 0.08 Item: Alera ALEVABFMC Valencia Series Mobile B...\u001b[0m\n", "\u001b[93m212: Guess: $154.98 Truth: $244.99 Error: $90.01 SLE: 0.21 Item: YU-GI-OH! Ignition Assault Booster Box\u001b[0m\n", "\u001b[93m213: Guess: $143.65 Truth: $86.50 Error: $57.15 SLE: 0.25 Item: 48\" x 36\" Extra-Large Framed Magnetic Bl...\u001b[0m\n", "\u001b[91m214: Guess: $147.77 Truth: $297.95 Error: $150.18 SLE: 0.49 Item: Dell Latitude D620 Renewed Notebook PC\u001b[0m\n", "\u001b[92m215: Guess: $393.69 Truth: $399.99 Error: $6.30 SLE: 0.00 Item: acer Aspire 5 Laptop, AMD Ryzen 3 5300U ...\u001b[0m\n", "\u001b[91m216: Guess: $174.00 Truth: $599.00 Error: $425.00 SLE: 1.52 Item: Elk 31080/6RC-GRN 30 by 6-Inch Viva 6-Li...\u001b[0m\n", "\u001b[91m217: Guess: $10.99 Truth: $105.99 Error: $95.00 SLE: 4.79 Item: Barbie Top Model Doll\u001b[0m\n", "\u001b[92m218: Guess: $674.00 Truth: $689.00 Error: $15.00 SLE: 0.00 Item: <PERSON><PERSON> 20-In. Electric Range wit...\u001b[0m\n", "\u001b[91m219: Guess: $174.99 Truth: $404.99 Error: $230.00 SLE: 0.70 Item: FixtureDisplays® Metal Truss Podium Doub...\u001b[0m\n", "\u001b[93m220: Guess: $154.65 Truth: $207.76 Error: $53.11 SLE: 0.09 Item: ACDelco 13597235 GM Original Equipment A...\u001b[0m\n", "\u001b[91m221: Guess: $276.10 Truth: $171.82 Error: $104.28 SLE: 0.22 Item: EBC S1KF1135 Stage-1 Premium Street Brak...\u001b[0m\n", "\u001b[91m222: Guess: $594.99 Truth: $293.24 Error: $301.75 SLE: 0.50 Item: FXR Men's Boost FX Jacket (Black/Orange/...\u001b[0m\n", "\u001b[93m223: Guess: $262.47 Truth: $374.95 Error: $112.48 SLE: 0.13 Item: SuperATV Scratch Resistant 3-in-1 Flip W...\u001b[0m\n", "\u001b[93m224: Guess: $65.98 Truth: $111.99 Error: $46.01 SLE: 0.27 Item: SBU 3 Layer All Weather Mini Van Car Cov...\u001b[0m\n", "\u001b[92m225: Guess: $30.47 Truth: $42.99 Error: $12.52 SLE: 0.11 Item: 2 Pack Outdoor Brochure Holder Advertisi...\u001b[0m\n", "\u001b[91m226: Guess: $277.65 Truth: $116.71 Error: $160.94 SLE: 0.74 Item: Monroe Shocks & Struts Quick-Strut 17158...\u001b[0m\n", "\u001b[91m227: Guess: $221.65 Truth: $118.61 Error: $103.04 SLE: 0.39 Item: Elements of Design Magellan EB235AL Thre...\u001b[0m\n", "\u001b[92m228: Guess: $146.22 Truth: $147.12 Error: $0.90 SLE: 0.00 Item: GM Genuine Parts 15-62961 Air Conditioni...\u001b[0m\n", "\u001b[92m229: Guess: $139.98 Truth: $119.99 Error: $19.99 SLE: 0.02 Item: Baseus 17-in-1 USB C Docking Station to ...\u001b[0m\n", "\u001b[93m230: Guess: $262.47 Truth: $369.98 Error: $107.51 SLE: 0.12 Item: Whitehall™ Personalized Whitehall Capito...\u001b[0m\n", "\u001b[92m231: Guess: $277.65 Truth: $315.55 Error: $37.90 SLE: 0.02 Item: Pro Circuit Works Pipe PY05250 for 02-19...\u001b[0m\n", "\u001b[91m232: Guess: $109.99 Truth: $190.99 Error: $81.00 SLE: 0.30 Item: HYANKA 15 \"1200W Professional DJ Speaker...\u001b[0m\n", "\u001b[92m233: Guess: $139.98 Truth: $155.00 Error: $15.02 SLE: 0.01 Item: Bluetooth X6BT Card Reader Writer Encode...\u001b[0m\n", "\u001b[92m234: Guess: $322.22 Truth: $349.99 Error: $27.77 SLE: 0.01 Item: AIRAID Cold Air Intake System by K&N: In...\u001b[0m\n", "\u001b[91m235: Guess: $139.98 Truth: $249.99 Error: $110.01 SLE: 0.33 Item: Bostingner Shower Faucets Sets Complete,...\u001b[0m\n", "\u001b[91m236: Guess: $127.99 Truth: $42.99 Error: $85.00 SLE: 1.16 Item: PIT66 Front Bumper Turn Signal Lights, C...\u001b[0m\n", "\u001b[92m237: Guess: $10.99 Truth: $17.99 Error: $7.00 SLE: 0.21 Item: Caseology Bumpy Compatible with Google P...\u001b[0m\n", "\u001b[91m238: Guess: $202.65 Truth: $425.00 Error: $222.35 SLE: 0.54 Item: Fleck 2510 Timer Mechanical Filter Contr...\u001b[0m\n", "\u001b[92m239: Guess: $219.98 Truth: $249.99 Error: $30.01 SLE: 0.02 Item: Haloview MC7108 Wireless RV Backup Camer...\u001b[0m\n", "\u001b[91m240: Guess: $22.22 Truth: $138.23 Error: $116.01 SLE: 3.21 Item: <PERSON> - <PERSON>\u001b[0m\n", "\u001b[93m241: Guess: $277.65 Truth: $414.99 Error: $137.34 SLE: 0.16 Item: Corsa 14333 Tip Kit (Ford Mustang GT)\u001b[0m\n", "\u001b[93m242: Guess: $221.65 Truth: $168.28 Error: $53.37 SLE: 0.08 Item: Hoshizaki FM116A Fan Motor Kit 1\u001b[0m\n", "\u001b[93m243: Guess: $136.99 Truth: $199.99 Error: $63.00 SLE: 0.14 Item: BAINUO <PERSON>tler Chandelier Lighting,6 Ligh...\u001b[0m\n", "\u001b[92m244: Guess: $100.69 Truth: $126.70 Error: $26.01 SLE: 0.05 Item: DNA MOTORING HL-OH-FEXP06-SM-AM Smoke Le...\u001b[0m\n", "\u001b[92m245: Guess: $22.69 Truth: $5.91 Error: $16.78 SLE: 1.52 Item: <PERSON>ra St<PERSON>less 3840/1 TS 2.5mm Hex Inser...\u001b[0m\n", "\u001b[91m246: Guess: $390.22 Truth: $193.06 Error: $197.16 SLE: 0.49 Item: Celestron - PowerSeeker 127EQ Telescope ...\u001b[0m\n", "\u001b[91m247: Guess: $129.99 Truth: $249.99 Error: $120.00 SLE: 0.42 Item: NHOPEEW 10.1inch Android Car Radio Carpl...\u001b[0m\n", "\u001b[93m248: Guess: $139.65 Truth: $64.12 Error: $75.53 SLE: 0.59 Item: Other Harmonica (Suzuki-2Timer24- A)\u001b[0m\n", "\u001b[92m249: Guess: $128.99 Truth: $114.99 Error: $14.00 SLE: 0.01 Item: Harley Air Filter Venturi Intake Air Cle...\u001b[0m\n", "\u001b[93m250: Guess: $726.65 Truth: $926.00 Error: $199.35 SLE: 0.06 Item: Elite Screens Edge Free Ambient Light Re...\u001b[0m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["Tester.test(gpt_fine_tuned, test)"]}, {"cell_type": "code", "execution_count": null, "id": "03ff4b48-3788-4370-9e34-6592f23d1bce", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}