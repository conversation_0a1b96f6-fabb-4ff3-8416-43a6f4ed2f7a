{"cells": [{"cell_type": "markdown", "id": "d9b9eaa6-a12f-4cf8-a4c5-e8ac2c15d15b", "metadata": {}, "source": ["# 🔍 Predicting Item Prices from Descriptions (Part 2)\n", "---\n", "- Data Curation & Preprocessing\n", "- ➡️ Model Benchmarking – Traditional ML vs LLMs\n", "- E5 Embeddings & RAG\n", "- Fine-Tuning GPT-4o Mini\n", "- Evaluating LLaMA 3.1 8B Quantized\n", "- Fine-Tuning LLaMA 3.1 with QLoRA\n", "- Evaluating Fine-Tuned LLaMA \n", "- Summary & Leaderboard\n", "\n", "--- \n", "\n", "# ⚔️ Part 2: Traditional ML vs LLMs\n", "\n", "- Tasks:\n", "    - Vectorize text (BoW, Word2Vec)\n", "    - Train SVR, LR, XGBoost models\n", "    - Predict with LLMs (GPT<PERSON><PERSON><PERSON>, <PERSON>, LLaMA…)\n", "    - Compare traditional ML vs LLMs\n", "  \n", "📊 Which model predicts prices best? Let’s find out.\n", "\n", "- 🧑‍💻 Skill Level: Advanced\n", "- ⚙️ Hardware: ✅ CPU is sufficient — no GPU required\n", "- 🛠️ Requirements: 🔑 HF Token, Open API Key, Anthropic API key, Groq API key\n", "\n", "⚠️ This notebook assumes you're familiar with NLP techniques (e.g., converting text to vectors using Bag-of-Words or Word2Vec) and traditional ML models (like SVR, Logistic Regression, XGBoost) along with basic evaluation metrics.\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"]}, {"cell_type": "code", "execution_count": null, "id": "4ce6a892-b357-4132-b9c0-a3142a0244c8", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import importlib\n", "import re\n", "import csv\n", "import tiktoken\n", "import math\n", "from datasets import load_dataset\n", "import pandas as pd\n", "import numpy as np\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "from gensim.models import Word2Vec\n", "from gensim.utils import simple_preprocess\n", "from sklearn.svm import LinearSVR\n", "import xgboost as xgb\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from anthropic import Anthropic"]}, {"cell_type": "markdown", "id": "6f82b230-2e03-4b1e-9be5-926fcd19acbe", "metadata": {}, "source": ["## 📥 Load Dataset"]}, {"cell_type": "code", "execution_count": null, "id": "4292a45d", "metadata": {}, "outputs": [], "source": ["# #If you face NotImplementedError: Loading a dataset cached in a LocalFileSystem is not supported run:\n", "# %pip install -U datasets"]}, {"cell_type": "code", "execution_count": null, "id": "55f1495b-f343-4152-8739-3a99f5ac405d", "metadata": {}, "outputs": [], "source": ["HF_USER = \"lisekarimi\"\n", "DATASET_NAME = f\"{HF_USER}/pricer-data\"\n", "\n", "dataset = load_dataset(DATASET_NAME)\n", "train = dataset['train']\n", "test = dataset['test']"]}, {"cell_type": "code", "execution_count": null, "id": "85880d79-f1ba-4ee8-a039-b6acea84562c", "metadata": {}, "outputs": [], "source": ["print(train[0][\"text\"])"]}, {"cell_type": "code", "execution_count": null, "id": "88842541-d73b-4fae-a550-6dedf8fab633", "metadata": {}, "outputs": [], "source": ["print(train[0][\"price\"])"]}, {"cell_type": "markdown", "id": "1e3501c5-a52d-4ace-a988-b86b7e7dbb31", "metadata": {}, "source": ["## 🛠️ Prepare Data for models"]}, {"cell_type": "code", "execution_count": null, "id": "0a87cd82-127b-4a66-9ad9-90978a2376b5", "metadata": {}, "outputs": [], "source": ["def mask_price_value(text):\n", "    return re.sub(r\"(\\n\\nPrice is \\$).*\", r\"\\1\", text)"]}, {"cell_type": "code", "execution_count": null, "id": "84ad6155-2708-4810-80a6-7efcf3bbd886", "metadata": {}, "outputs": [], "source": ["# Extract prices\n", "prices = np.array([float(datapoint[\"price\"]) for datapoint in train])\n", "\n", "# Extract cleaned prompts\n", "documents = [mask_price_value(datapoint[\"text\"]) for datapoint in train]\n", "\n", "# Set random seed for reproducibility\n", "np.random.seed(42)"]}, {"cell_type": "code", "execution_count": null, "id": "c1c82371-5e92-4354-a064-38db1b6a8339", "metadata": {}, "outputs": [], "source": ["print(documents[0])"]}, {"cell_type": "markdown", "id": "f05dd862-cc64-43d3-a0c3-c3a16d66e1bf", "metadata": {}, "source": ["## 📊 Model Evaluation with testing.py\n", "\n", "- Runs predictions and computes errors on test data\n", "- Metrics: Absolute error, RMSLE, and hit rate\n", "- Visual: Scatter plot of predicted vs. actual prices (color-coded)"]}, {"cell_type": "code", "execution_count": null, "id": "45965754-7107-4023-bb33-81730b73db2e", "metadata": {}, "outputs": [], "source": ["import helpers.testing\n", "importlib.reload(helpers.testing)\n", "\n", "from helpers.testing import Tester  # noqa: E402\n", "\n", "results = {}  # Store each model's tester to compare and find the best performer"]}, {"cell_type": "markdown", "id": "2d8b08a8-f0a3-468f-91ea-7da60aecc32a", "metadata": {}, "source": ["## 🎯 Price Prediction with Traditional ML"]}, {"cell_type": "markdown", "id": "35475efe-0751-443a-9605-89e2025c3eb4", "metadata": {}, "source": ["## Bag-of-Words + Linear Regression"]}, {"cell_type": "code", "execution_count": null, "id": "ded239d6-4dca-439b-8748-67aa2d2fa2a9", "metadata": {}, "outputs": [], "source": ["# Use the CountVectorizer for a Bag of Words model\n", "vectorizer = CountVectorizer(max_features=1000, stop_words='english')\n", "X = vectorizer.fit_transform(documents)\n", "regressor = LinearRegression()\n", "regressor.fit(X, prices)\n", "\n", "def bow_lr_pricer(datapoint):\n", "    x = vectorizer.transform([mask_price_value(datapoint[\"text\"])])\n", "    return max(regressor.predict(x)[0], 0)\n", "\n", "tester = Tester(bow_lr_pricer, test)\n", "tester.run()\n", "results[\"Bag of Words LR\"] = tester"]}, {"cell_type": "markdown", "id": "4b861fe5", "metadata": {}, "source": ["Bow Lr Pricer Error=$121.23 RMSLE=0.98 Hits=27.2%"]}, {"cell_type": "markdown", "id": "25dfc7c6-a258-4b56-8c02-f01003c4674d", "metadata": {}, "source": ["## Word2Vec + Linear Regression"]}, {"cell_type": "code", "execution_count": null, "id": "efa22fd1-e81d-4142-b0a1-f1399c7a98a3", "metadata": {}, "outputs": [], "source": ["# Preprocess the documents\n", "processed_docs = [simple_preprocess(doc) for doc in documents]\n", "\n", "# Train Word2Vec model\n", "w2v_model = Word2Vec(sentences=processed_docs, vector_size=400, window=5, min_count=1, workers=4)\n", "\n", "# This step of averaging vectors across the document is a weakness in our approach\n", "\n", "def document_vector(doc):\n", "    doc_words = simple_preprocess(doc)\n", "    word_vectors = [w2v_model.wv[word] for word in doc_words if word in w2v_model.wv]\n", "    return np.mean(word_vectors, axis=0) if word_vectors else np.zeros(w2v_model.vector_size)\n", "\n", "# Create feature matrix\n", "X_w2v = np.array([document_vector(doc) for doc in documents])\n", "\n", "# Run Linear Regression on word2vec\n", "\n", "word2vec_lr_regressor = LinearRegression()\n", "word2vec_lr_regressor.fit(X_w2v, prices)\n", "\n", "def word2vec_lr_pricer(datapoint):\n", "    doc = mask_price_value(datapoint[\"text\"])\n", "    vec = document_vector(doc)\n", "    return max(0, word2vec_lr_regressor.predict([vec])[0])\n", "\n", "tester = Tester(word2vec_lr_pricer, test)\n", "tester.run()\n", "results[\"Word2Vec LR\"] = tester"]}, {"cell_type": "markdown", "id": "daaf6101", "metadata": {}, "source": ["Word2Vec Lr Pricer Error=$127.42 RMSLE=0.97 Hits=27.6%"]}, {"cell_type": "markdown", "id": "5f1fe808-f80e-4d15-8ec7-d31710cf68c5", "metadata": {}, "source": ["## Word2Vec + Linear SVR"]}, {"cell_type": "code", "execution_count": null, "id": "35d01455-6619-4f29-8f95-90c03763407e", "metadata": {}, "outputs": [], "source": ["svr_regressor = LinearSVR()\n", "svr_regressor.fit(X_w2v, prices)\n", "\n", "def svr_pricer(datapoint):\n", "    np.random.seed(42)\n", "    doc = mask_price_value(datapoint[\"text\"])\n", "    doc_vector = document_vector(doc)\n", "    return max(float(svr_regressor.predict([doc_vector])[0]),0)\n", "\n", "tester = Tester(svr_pricer, test)\n", "tester.run()\n", "results[\"Word2Vec SVR\"] = tester"]}, {"cell_type": "markdown", "id": "48cb9c88", "metadata": {}, "source": ["Svr Pricer Error=$124.24 RMSLE=0.98 Hits=28.4%"]}, {"cell_type": "markdown", "id": "469ca205-3e5e-4aca-8b77-53f6acd92e40", "metadata": {}, "source": ["## Word2Vec + XGBoost "]}, {"cell_type": "markdown", "id": "a55acfe0-9633-45aa-a4c4-96b434a5a43b", "metadata": {}, "source": ["I initially tried Random Forest, but it struggled with high training time and didn’t scale well with this data.\n", "That’s why I opted for XGBoost — it’s faster, handles large datasets efficiently, and often delivers better performance on structured data."]}, {"cell_type": "code", "execution_count": null, "id": "b0e3e1d7-2e62-4866-924e-7ed4483db8bc", "metadata": {}, "outputs": [], "source": ["xgb_model = xgb.XGBRegressor(n_estimators=100, random_state=42, n_jobs=-1, verbosity=0)\n", "xgb_model.fit(X_w2v, prices)\n", "\n", "def xgboost_pricer(datapoint):\n", "    doc = mask_price_value(datapoint[\"text\"])\n", "    doc_vector = document_vector(doc)\n", "    return max(0, xgb_model.predict([doc_vector])[0])\n", "\n", "tester = Tester(xgboost_pricer, test)\n", "tester.run()\n", "results[\"Word2Vec XGBoost\"] = tester\n"]}, {"cell_type": "markdown", "id": "d35050fa", "metadata": {}, "source": ["Xgboost Pricer Error=$107.97 RMSLE=0.84 Hits=29.2%"]}, {"cell_type": "markdown", "id": "4db1051d-9a7e-4cec-87fc-0d77fd858ced", "metadata": {}, "source": ["## 🚀 Price Prediction with Frontier LLMs"]}, {"cell_type": "code", "execution_count": null, "id": "0ef3fa58-87b7-4c30-8088-1a4999f0d25a", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "\n", "# Get API keys from environment\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if not openai_api_key:\n", "   print(\"❌ OPENAI_API_KEY is missing\")\n", "\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "if not anthropic_api_key:\n", "   print(\"❌ ANTHROPIC_API_KEY is missing\")\n", "\n", "groq_api_key = os.getenv('GROQ_API_KEY')\n", "if not groq_api_key:\n", "   print(\"❌ GROQ_API_KEY is missing\")\n", "\n", "# Initialize clients\n", "openai = OpenAI(api_key=openai_api_key)\n", "claude = Anthropic(api_key=anthropic_api_key)\n", "groq = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\")"]}, {"cell_type": "code", "execution_count": null, "id": "d86b3bca-513b-4621-8c66-4b89c134b895", "metadata": {}, "outputs": [], "source": ["def messages_for(datapoint):\n", "    system_message = \"You estimate prices of items. Reply only with the price, no explanation\"\n", "    user_prompt = mask_price_value(datapoint[\"text\"]).replace(\" to the nearest dollar\", \"\").replace(\"\\n\\nPrice is $\",\"\")\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": user_prompt},\n", "        {\"role\": \"assistant\", \"content\": \"Price is $\"}\n", "    ]\n", "\n", "messages_for(train[0])"]}, {"cell_type": "code", "execution_count": null, "id": "f502d428-98aa-4160-bebe-726efcce5c65", "metadata": {}, "outputs": [], "source": ["# A utility function to extract the price from a string\n", "\n", "def get_price(s):\n", "    s = s.replace('$','').replace(',','')\n", "    match = re.search(r\"[-+]?\\d*\\.\\d+|\\d+\", s)\n", "    return float(match.group()) if match else 0\n", "\n", "get_price(\"The price is roughly $99.99 because blah blah\") # Testing"]}, {"cell_type": "code", "execution_count": null, "id": "3845eda0-d37d-4605-a00f-83b1d8fc6945", "metadata": {}, "outputs": [], "source": ["# A utility function to Count the tokens before passing the prompt to the model\n", "\n", "def count_tokens(messages):\n", "    encoding = tiktoken.get_encoding(\"cl100k_base\")\n", "    token_count = sum(len(encoding.encode(message['content'])) for message in messages)\n", "    return token_count\n"]}, {"cell_type": "markdown", "id": "4737e678-5d57-4dee-984b-ae5c56f9542d", "metadata": {}, "source": ["### gpt-4o-mini"]}, {"cell_type": "code", "execution_count": null, "id": "dca067d0-a4ff-4a48-bb74-d2914f3704b7", "metadata": {}, "outputs": [], "source": ["# Count tokens once before running\n", "total_tokens = 0\n", "for datapoint in train:\n", "    messages = messages_for(datapoint)\n", "    total_tokens += count_tokens(messages)\n", "print(f\"Total tokens: {total_tokens}\")\n", "\n", "def gpt_4o_mini(datapoint):\n", "    messages = messages_for(datapoint)\n", "    response = openai.chat.completions.create(\n", "        model=\"gpt-4o-mini\",\n", "        messages=messages,\n", "        seed=42,\n", "        max_tokens=5\n", "    )\n", "    reply = response.choices[0].message.content\n", "    return get_price(reply)\n", "\n", "tester = Tester(gpt_4o_mini, test)\n", "tester.run()\n", "results[\"gpt 4o mini\"] = tester"]}, {"cell_type": "markdown", "id": "5c4c8ee4", "metadata": {}, "source": ["Gpt 4o Mini Error=$99.30 RMSLE=0.75 Hits=44.8%"]}, {"cell_type": "markdown", "id": "00a72937-9cde-472c-bd22-84996a42ab4c", "metadata": {}, "source": ["### gpt 4o (the big guy 😎)"]}, {"cell_type": "code", "execution_count": null, "id": "20d18e1a-ccbf-4481-84ed-16b1c5760176", "metadata": {}, "outputs": [], "source": ["def gpt_4o_frontier(datapoint):\n", "    response = openai.chat.completions.create(\n", "        model=\"gpt-4o\",\n", "        messages=messages_for(datapoint),\n", "        seed=42,\n", "        max_tokens=5\n", "    )\n", "    reply = response.choices[0].message.content\n", "    return get_price(reply)\n", "\n", "tester = Tester(gpt_4o_frontier, test)\n", "tester.run()\n", "results[\"gpt 4o (the big guy)\"] = tester"]}, {"cell_type": "markdown", "id": "0c307928", "metadata": {}, "source": ["Gpt 4O Frontier Error=$87.68 RMSLE=1.01 Hits=51.2%"]}, {"cell_type": "markdown", "id": "20af42a7-8889-4091-bee9-80aeaf63816f", "metadata": {}, "source": ["### claude 3.7 Sonnet"]}, {"cell_type": "code", "execution_count": null, "id": "f4e343ef-2097-4395-86b2-90c489f133fe", "metadata": {}, "outputs": [], "source": ["def claude_3_point_7_sonnet(datapoint):\n", "    messages = messages_for(datapoint)\n", "    system_message = messages[0]['content']\n", "    messages = messages[1:]\n", "    response = claude.messages.create(\n", "        model=\"claude-3-7-sonnet-20250219\",\n", "        max_tokens=5,\n", "        system=system_message,\n", "        messages=messages\n", "    )\n", "    reply = response.content[0].text\n", "    return get_price(reply)\n", "\n", "tester = Tester(claude_3_point_7_sonnet, test)\n", "tester.run()\n", "results[\"claude 3.7 sonnet\"] = tester"]}, {"cell_type": "markdown", "id": "fdbba849", "metadata": {}, "source": ["Claude 3 Point 7 Sonnet Error=$110.26 RMSLE=0.60 Hits=46.0%"]}, {"cell_type": "markdown", "id": "0ff3a6bd-99b8-438e-abc1-295bf0bb9961", "metadata": {}, "source": ["### groq model"]}, {"cell_type": "code", "execution_count": null, "id": "58a0c852-0811-4156-9c08-fa5bf4b54cd2", "metadata": {}, "outputs": [], "source": ["def llama3_groq_pricer(datapoint):\n", "    response = groq.chat.completions.create(\n", "        model=\"llama3-70b-8192\",\n", "        messages=messages_for(datapoint),\n", "        max_tokens=5,\n", "        seed=42\n", "    )\n", "    reply = response.choices[0].message.content\n", "    return get_price(reply)\n", "\n", "tester = Tester(llama3_groq_pricer, test)\n", "tester.run()\n", "results[\"llama3-70b-8192\"] = tester"]}, {"cell_type": "markdown", "id": "daf7f96c", "metadata": {}, "source": ["Llama3 Groq Pricer Error=$122.95 RMSLE=0.73 Hits=44.8%"]}, {"cell_type": "code", "execution_count": null, "id": "b4cd8f25-9a8d-4227-ba58-c163b4d601cb", "metadata": {}, "outputs": [], "source": ["def deepseek_qwen_pricer(datapoint):\n", "    response = groq.chat.completions.create(\n", "        model=\"deepseek-r1-distill-qwen-32b\",\n", "        messages=messages_for(datapoint),\n", "        max_tokens=5,\n", "        seed=42\n", "    )\n", "    reply = response.choices[0].message.content\n", "    return get_price(reply)\n", "\n", "tester = Tester(deepseek_qwen_pricer, test)\n", "tester.run()\n", "results[\"deepseek-qwen-32b\"] = tester"]}, {"cell_type": "markdown", "id": "7280870e", "metadata": {}, "source": ["Deepseek Qwen <PERSON>r Error=$178.96 RMSLE=0.83 Hits=33.2%"]}, {"cell_type": "markdown", "id": "af7d0190-d89b-4525-8a34-21033e99abb0", "metadata": {}, "source": ["## 🕵️ Human Judgement Baseline (Ed)\n", "\n", "We include a human baseline from our instructor <PERSON>, who manually estimated prices based on item descriptions (💪 thanks <PERSON> for taking on this exhausting task!). This allows us to compare model performance against human intuition."]}, {"cell_type": "code", "execution_count": null, "id": "4d63970d-a2d2-4329-8fe7-d0bdc2ff1bcb", "metadata": {}, "outputs": [], "source": ["human_predictions = []\n", "\n", "with open('data/human_output.csv', 'r', encoding=\"utf-8\") as csvfile:\n", "    reader = csv.reader(csvfile)\n", "    for row in reader:\n", "        human_predictions.append(float(row[1]))\n", "\n", "def human_pricer(datapoint):\n", "    # `Tester` runs in order, so use the index from Tester itself\n", "    idx = human_pricer.counter\n", "    human_pricer.counter += 1\n", "    return human_predictions[idx]\n", "\n", "human_pricer.counter = 0  # initialize counter\n", "\n", "tester = Tester(human_pricer, test)\n", "tester.run()\n", "results[\"Human Predictions\"] = tester"]}, {"cell_type": "markdown", "id": "08c0d367-d596-43e6-81af-5889691fa34b", "metadata": {}, "source": ["## 🥇 Benchmark Showdown: ML, LLMs, and Ed"]}, {"cell_type": "code", "execution_count": null, "id": "830ae9a5-a185-41af-b17f-8d6a3f3752b7", "metadata": {}, "outputs": [], "source": ["def truncate(x, decimals=2):\n", "    factor = 10 ** decimals\n", "    return math.floor(x * factor) / factor\n", "\n", "df_results = []\n", "\n", "for model_name, tester in results.items():\n", "    avg_error = truncate(sum(tester.errors) / tester.size)\n", "    hit_percent = truncate(sum(1 for c in tester.colors if c == \"green\") / tester.size * 100)\n", "    rmsle = truncate(math.sqrt(sum(tester.sles) / tester.size))\n", "\n", "    df_results.append({\n", "        \"model\": model_name,\n", "        \"avrg_error\": avg_error,\n", "        \"rmsle\": rmsle,\n", "        \"accuracy_%\": hit_percent\n", "    })\n", "\n", "df_results = pd.DataFrame(df_results)\n", "df_results = df_results.sort_values(by=\"avrg_error\")\n", "\n", "# Display with .2f formatting\n", "print(df_results.to_string(index=False, float_format=\"{:.2f}\".format))\n"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "e78ddc21-1ffc-431b-902b-4562bdd4e789", "metadata": {}, "source": ["![image.png](attachment:image.png)\n", "\n", "🏁 **GPT-4o, GPT-4o Mini and XGBoost** clearly outperformed both LLMs (like Claude 3.7, LLaMA3-70B, DeepSeek-32B) and traditional ML approaches (LR, SVR).\n", "\n", "Now let’s take the top-performing frontier LLM — **GPT-4o Mini** — to test if retrieval (RAG) boosts its performance, and the best ML model — **XGBoost** — to see if contextual embeddings enhance its predictions.\n", "\n", "Let’s find out.\n", "\n", "🔜 See you in the [next notebook](https://github.com/lisek75/nlp_llms_notebook/blob/main/09_part3_e5embeddings_rag.ipynb)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}