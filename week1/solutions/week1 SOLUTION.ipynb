{"cells": [{"cell_type": "markdown", "id": "fe12c203-e6a6-452c-a655-afb8a03a4ff5", "metadata": {}, "source": ["# End of week 1 solution\n", "\n", "To demonstrate your familiarity with OpenAI API, and also Ollama, build a tool that takes a technical question,  \n", "and responds with an explanation. This is a tool that you will be able to use yourself during the course!\n", "\n", "After week 2 you'll be able to add a User Interface to this tool, giving you a valuable application."]}, {"cell_type": "code", "execution_count": null, "id": "c1070317-3ed9-4659-abe3-828943230e03", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI\n", "import ollama"]}, {"cell_type": "code", "execution_count": null, "id": "4a456906-915a-4bfd-bb9d-57e505c5093f", "metadata": {}, "outputs": [], "source": ["# constants\n", "\n", "MODEL_GPT = 'gpt-4o-mini'\n", "MODEL_LLAMA = 'llama3.2'"]}, {"cell_type": "code", "execution_count": null, "id": "a8d7923c-5f28-4c30-8556-342d7c8497c1", "metadata": {}, "outputs": [], "source": ["# set up environment\n", "\n", "load_dotenv()\n", "openai = OpenAI()\n"]}, {"cell_type": "code", "execution_count": null, "id": "3f0d0137-52b0-47a8-81a8-11a90a010798", "metadata": {}, "outputs": [], "source": ["# here is the question; type over this to ask something new\n", "\n", "question = \"\"\"\n", "Please explain what this code does and why:\n", "yield from {book.get(\"author\") for book in books if book.get(\"author\")}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "8595807b-8ae2-4e1b-95d9-e8532142e8bb", "metadata": {}, "outputs": [], "source": ["# prompts\n", "\n", "system_prompt = \"You are a helpful technical tutor who answers questions about python code, software engineering, data science and LLMs\"\n", "user_prompt = \"Please give a detailed explanation to the following question: \" + question"]}, {"cell_type": "code", "execution_count": null, "id": "9605cbb6-3d3f-4969-b420-7f4cae0b9328", "metadata": {}, "outputs": [], "source": ["# messages\n", "\n", "messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt}\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "60ce7000-a4a5-4cce-a261-e75ef45063b4", "metadata": {}, "outputs": [], "source": ["# Get gpt-4o-mini to answer, with streaming\n", "\n", "stream = openai.chat.completions.create(model=MODEL_GPT, messages=messages,stream=True)\n", "    \n", "response = \"\"\n", "display_handle = display(Markdown(\"\"), display_id=True)\n", "for chunk in stream:\n", "    response += chunk.choices[0].delta.content or ''\n", "    response = response.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "    update_display(Markdown(response), display_id=display_handle.display_id)"]}, {"cell_type": "code", "execution_count": null, "id": "8f7c8ea8-4082-4ad0-8751-3301adcf6538", "metadata": {}, "outputs": [], "source": ["# Get Llama 3.2 to answer\n", "\n", "response = ollama.chat(model=MODEL_LLAMA, messages=messages)\n", "reply = response['message']['content']\n", "display(<PERSON><PERSON>(reply))"]}, {"cell_type": "markdown", "id": "7e14bcdb-b928-4b14-961e-9f7d8c7335bf", "metadata": {}, "source": ["# Congratulations!\n", "\n", "You could make it better by taking in the question using  \n", "`my_question = input(\"Please enter your question:\")`\n", "\n", "And then creating the prompts and making the calls interactively."]}, {"cell_type": "code", "execution_count": null, "id": "da663d73-dd2a-4fff-84df-2209cf2b330b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}