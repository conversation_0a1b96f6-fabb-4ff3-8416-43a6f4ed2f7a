{"cells": [{"cell_type": "code", "execution_count": null, "id": "0a512c2a-55e7-40e1-ab17-88b7034ca09a", "metadata": {}, "outputs": [], "source": ["# Imports\n", "import openai\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "id": "1aa8dd82-6b5e-4dbd-a2ee-8367e796a51f", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - head over to the troubleshooting notebook!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj... make sure you using the right key (Check troubleshooting notebook)\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like white space was found in beginning or end. (Check troubleshooting notebook)\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": null, "id": "2acd579b-846c-4aa6-ba6c-1cc1a5a2eeb6", "metadata": {}, "outputs": [], "source": ["# Input the system prompt\n", "system_prompt = \"\"\"you are top notched AI music expert that have knowledge of all genres, songs, and artists. You need to google search lyrics. You have the following rules:\\\n", "1. Carefully break down what type of recommendation the user wants and the context.\\\n", "2. If asked to recommend genres similar to a song or artists please identify the top 3 genres.\\\n", "3. If asked to recommend artists from songs or genres then recommend the top 5 artists.\n", "4. If asked to recommend songs from genres or artist than recommend the top 10 songs.\n", "5. If asked for a general recommendation give them the top 5 songs based off of context.\\\n", "6. Be flexible and adaptable with recommendations and consider the context the user might ask.\n", "7. always respond in markdown.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3c1cf212-538c-4e9a-8da5-337bd7b6197c", "metadata": {}, "outputs": [], "source": ["# music recommender function\n", "def music_recommender(user_prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]\n", "    \n", "    response = openai.chat.completions.create(\n", "        model=\"gpt-4\",\n", "        messages=messages,\n", "        max_tokens=300\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "4f277561-af8b-4715-90e7-6ebaadeb15d0", "metadata": {}, "outputs": [], "source": ["# User prompt (Change this to fit your needs!)\n", "user_prompt = \"Can you recommend me songs from <PERSON>\"\n", "\n", "# Example usage\n", "response = music_recommender(user_prompt)\n", "display(Markdown(response))"]}, {"cell_type": "code", "execution_count": null, "id": "bb869d36-de14-4e46-9087-223d6b257efa", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}