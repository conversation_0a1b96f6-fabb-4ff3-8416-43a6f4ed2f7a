{"cells": [{"cell_type": "code", "execution_count": 1, "id": "50ed5733", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": 2, "id": "a3b173a9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key found and looks good so far!\n"]}], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "191c7214", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "code", "execution_count": 4, "id": "50adea39", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"Generate a detailed travel recommendation.Include the following information: \\\n", "    1.**Overview**: A brief introduction to the destination, highlighting its unique characteristics and appeal.\\\n", "    2.**Cost Breakdown**: - Average cost of accommodation (budget, mid-range, luxury options).\\\n", "        - Estimated daily expenses (food, transportation, activities).\\\n", "        - Total estimated cost for a typical 5-day trip for a solo traveler and a family of four.\\\n", "    3.**Best Time to Visit**: \\\n", "        - Identify the peak, shoulder, and off-peak seasons.\\\n", "        - Highlight the pros and cons of visiting during each season, including weather conditions and local events.\\\n", "    4.**Hidden Gems**: - List at least five lesser-known attractions or experiences that are must-sees.\\\n", "        - Provide a brief description of each hidden gem, including why it is special and any tips for visiting.\\\n", "            5.**Local Tips**: \\\n", "                - Suggest local customs or etiquette that travelers should be aware of.\\\n", "                - Recommend local dishes to try and where to find them.Make sure the recommendation is engaging and informative, appealing to a diverse range of travelers.\"\"\""]}, {"cell_type": "code", "execution_count": 5, "id": "aaac13d8", "metadata": {}, "outputs": [], "source": ["def messages_for(user_prompt):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt }\n", "    ]\n", "\n", "def recommender():\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(f\"Create a travel recommendation for couple in the Netherlands\")\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 8, "id": "efad902a", "metadata": {}, "outputs": [], "source": ["def display_result():\n", "    recommendendation = recommender()\n", "    display(Markdown(recommendendation))"]}, {"cell_type": "code", "execution_count": 9, "id": "5564c22c", "metadata": {}, "outputs": [{"data": {"text/markdown": ["### Travel Recommendation: Exploring the Netherlands as a Couple\n", "\n", "#### Overview \n", "The Netherlands, with its charming canals, stunning tulip fields, and vibrant cities, is a romantic destination for couples seeking beauty and culture. Whether you're wandering hand-in-hand through the cobbled streets of Amsterdam, enjoying a serene boat ride in Giethoorn, or indulging in local delicacies at quaint cafés, the Netherlands combines history, art, and picturesque landscapes in a unique blend. The country not only boasts iconic landmarks such as the Rijksmuseum and the Anne Frank House but also an extensive network of cycling paths that allow you to discover hidden treasures together.\n", "\n", "#### Cost Breakdown \n", "- **Accommodation**:\n", "  - **Budget**: €50-€100 per night (Hostels like Stayokay Amsterdam Stadsdoelen)\n", "  - **Mid-Range**: €100-€200 per night (Hotels such as Hotel Estheréa in Amsterdam)\n", "  - **Luxury**: €200-€500+ per night (Luxury options like The Dylan in Amsterdam)\n", "\n", "- **Estimated Daily Expenses**:\n", "  - **Food**: €30-€70 per person (Cafés and local restaurants)\n", "  - **Transportation**: €10-€20 per person (Train or bike rental)\n", "  - **Activities**: €15-€50 per person (Entry to museums, parks, and attractions)\n", "  \n", "- **Total Estimated Cost**:\n", "  - **Solo Traveler (5-day trip)**: Approx. €500-€1,250\n", "    - Accommodation: €250-€1,000\n", "    - Daily expenses: €250-€500\n", "  \n", "  - **Family of Four (5-day trip)**: Approx. €1,800-€3,500\n", "    - Accommodation: €500-€1,500\n", "    - Daily expenses: €1,300-€2,000\n", "\n", "#### Best Time to Visit\n", "- **Peak Season (June-August)**:\n", "  - **Pros**: Warm weather, lively festivals, and vibrant outdoor activities.\n", "  - **Cons**: Crowded tourist spots and higher prices.\n", "  \n", "- **Shoulder Season (April-May & September-October)**:\n", "  - **Pros**: Mild weather, stunning tulip blooms (April), fewer crowds, and lower prices.\n", "  - **Cons**: Possible rain and some attractions may have reduced hours.\n", "  \n", "- **Off-Peak Season (November-March)**:\n", "  - **Pros**: Lower prices, festive holiday vibes, fewer tourists.\n", "  - **Cons**: Cold and wet weather which might limit outdoor activities.\n", "\n", "#### Hidden Gems\n", "1. **Giethoorn**: Often called the \"Venice of the North,\" Giethoorn is a picturesque village without roads. Rent a \"whisper boat\" for a serene experience gliding through the canals and enjoy the quaint thatched-roof houses.\n", "\n", "2. **<PERSON><PERSON><PERSON>**: Located near Amsterdam, this charming neighborhood showcases traditional Dutch windmills, wooden houses, and artisan workshops. Spend a day wandering and even tour a functioning windmill.\n", "\n", "3. **Haarlem**: Only 15 minutes from Amsterdam, Haarlem is a historic city with stunning architecture, cozy cafés, and the impressive Frans Hals Museum that houses works from the Dutch Golden Age.\n", "\n", "4. **Edam**: Famous for its cheese, the lovely town of Edam invites you to taste samples at local markets and explore cobbled streets lined with historical buildings. Don’t miss the Edam Museum for a taste of local history.\n", "\n", "5. **Kinderdijk**: A UNESCO World Heritage site known for its iconic windmills, Kinderdijk offers a scenic bike ride and walking trails amidst the charming countryside. Visiting at sunset can be particularly romantic.\n", "\n", "#### Local Tips\n", "- **Customs and Etiquette**: The Dutch are known for being direct but polite. Keep conversations respectful and avoid raising your voice. It’s customary to greet people with a handshake or a friendly smile.\n", "\n", "- **Local Dishes to Try**: \n", "  - **<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>**: A beloved Dutch treat; find them fresh from markets.\n", "  - **Haring**: Raw herring fish served with onions and pickles; try it at local fish stalls in Amsterdam.\n", "  - **Bitterballen**: A popular Dutch snack; pair them with a local beer at a cozy café.\n", "  - **Po<PERSON><PERSON>jes**: Small fluffy pancakes, perfect for sharing as a dessert or snack; find them at street vendors or markets.\n", "  \n", "By choosing the Netherlands as your travel destination, you will immerse yourselves in a tapestry of art, history, and picturesque landscapes while creating unforgettable memories. Happy travels!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_result()"]}, {"cell_type": "code", "execution_count": null, "id": "c66b461d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}