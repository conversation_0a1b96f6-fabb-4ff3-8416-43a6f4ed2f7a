{"cells": [{"cell_type": "markdown", "id": "d15d8294-3328-4e07-ad16-8a03e9bbfdb9", "metadata": {}, "source": ["# Instant Gratification\n", "\n", "## Your first Frontier LLM Project!\n", "\n", "Let's build a useful LLM solution - in a matter of minutes.\n", "\n", "By the end of this course, you will have built an autonomous Agentic AI solution with 7 agents that collaborate to solve a business problem. All in good time! We will start with something smaller...\n", "\n", "Our goal is to code a new kind of Web Browser. Give it a URL, and it will respond with a summary. The Reader's Digest of the internet!!\n", "\n", "Before starting, you should have completed the setup for [PC](../SETUP-PC.md) or [Mac](../SETUP-mac.md) and you hopefully launched this jupyter lab from within the project root directory, with your environment activated.\n", "\n", "## If you're new to Jupyter Lab\n", "\n", "Welcome to the wonderful world of Data Science experimentation! Once you've used Jupyter Lab, you'll wonder how you ever lived without it. Simply click in each \"cell\" with code in it, such as the cell immediately below this text, and hit Shift+Return to execute that cell. As you wish, you can add a cell with the + button in the toolbar, and print values of variables, or try out variations.  \n", "\n", "I've written a notebook called [Guide to <PERSON><PERSON><PERSON>](Guide%20to%20Jupyter.ipynb) to help you get more familiar with Jupyter Labs, including adding Markdown comments, using `!` to run shell commands, and `tqdm` to show progress.\n", "\n", "## If you'd prefer to work in IDEs\n", "\n", "If you're more comfortable in IDEs like VSCode or Pycharm, they both work great with these lab notebooks too.  \n", "If you'd prefer to work in VSCode, [here](https://chatgpt.com/share/676f2e19-c228-8012-9911-6ca42f8ed766) are instructions from an AI friend on how to configure it for the course.\n", "\n", "## If you'd like to brush up your Python\n", "\n", "I've added a notebook called [Intermediate Python](Intermediate%20Python.ipynb) to get you up to speed. But you should give it a miss if you already have a good idea what this code does:    \n", "`yield from {book.get(\"author\") for book in books if book.get(\"author\")}`\n", "\n", "## I am here to help\n", "\n", "If you have any problems at all, please do reach out.  \n", "I'm available through the platform, <NAME_EMAIL>, or at https://www.linkedin.com/in/eddonner/ if you'd like to connect (and I love connecting!)\n", "\n", "## More troubleshooting\n", "\n", "Please see the [troubleshooting](troubleshooting.ipynb) notebook in this folder to diagnose and fix common problems. At the very end of it is a diagnostics script with some useful debug info.\n", "\n", "## If this is old hat!\n", "\n", "If you're already comfortable with today's material, please hang in there; you can move swiftly through the first few labs - we will get much more in depth as the weeks progress.\n", "\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Please read - important note</h2>\n", "            <span style=\"color:#900;\">The way I collaborate with you may be different to other courses you've taken. I prefer not to type code while you watch. Rather, I execute Jupyter Labs, like this, and give you an intuition for what's going on. My suggestion is that you do this with me, either at the same time, or (perhaps better) right afterwards. Add print statements to understand what's going on, and then come up with your own variations. If you have a Github account, use this to showcase your variations. Not only is this essential practice, but it demonstrates your skills to others, including perhaps future clients or employers...</span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business value of these exercises</h2>\n", "            <span style=\"color:#181;\">A final thought. While I've designed these notebooks to be educational, I've also tried to make them enjoyable. We'll do fun things like have LLMs tell jokes and argue with each other. But fundamentally, my goal is to teach skills you can apply in business. I'll explain business implications as we go, and it's worth keeping this in mind: as you build experience with models and techniques, think of ways you could put this into action at work today. Please do contact me if you'd like to discuss more or if you have ideas to bounce off me.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 2, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "markdown", "id": "6900b2a8-6384-4316-8aaa-5e519fca4254", "metadata": {}, "source": ["# Connecting to OpenAI\n", "\n", "The next cell is where we load in the environment variables in your `.env` file and connect to OpenAI.\n", "\n", "## Troubleshooting if you have problems:\n", "\n", "Head over to the [troubleshooting](troubleshooting.ipynb) notebook in this folder for step by step code to identify the root cause and fix it!\n", "\n", "If you make a change, try restarting the \"Kernel\" (the python process sitting behind this notebook) by Kernel menu >> Restart Kernel and Clear Outputs of All Cells. Then try this notebook again, starting at the top.\n", "\n", "Or, contact me! Message me <NAME_EMAIL> and we will get this to work.\n", "\n", "Any concerns about API costs? See my notes in the README - costs should be minimal, and you can control it at every point. You can also use Ollama as a free alternative, which we discuss during Day 2."]}, {"cell_type": "code", "execution_count": 3, "id": "7b87cadb-d513-4303-baee-a37b6f938e4d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key found and looks good so far!\n"]}], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "019974d9-f3ad-4a8a-b5f9-0a3719aea2d3", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "markdown", "id": "442fc84b-0815-4f40-99ab-d9a5da6bda91", "metadata": {}, "source": ["# Let's make a quick call to a Frontier model to get started, as a preview!"]}, {"cell_type": "code", "execution_count": 5, "id": "a58394bf-1e45-46af-9bfd-01e24da6f49a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello! I’m glad to hear from you! How can I assist you today?\n"]}], "source": ["# To give you a preview -- calling OpenAI with these messages is this easy. Any problems, head over to the Troubleshooting notebook.\n", "\n", "message = \"Hello, <PERSON><PERSON>! This is my first ever message to you! Hi!\"\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=[{\"role\":\"user\", \"content\":message}])\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "2aa190e5-cb31-456a-96cc-db109919cd78", "metadata": {}, "source": ["## OK onwards with our first project"]}, {"cell_type": "code", "execution_count": 6, "id": "c5e793b2-6775-426a-a139-4848291d0463", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/1******** Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": 7, "id": "2ef960cf-6dc2-4cda-afb3-b38be12f4c97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Home - <PERSON>\n", "Home\n", "Outsmart\n", "An arena that pits LLMs against each other in a battle of diplomacy and deviousness\n", "About\n", "Posts\n", "Well, hi there.\n", "I’m <PERSON>. I like writing code and experimenting with LLMs, and hopefully you’re here because you do too. I also enjoy DJing (but I’m badly out of practice), amateur electronic music production (\n", "very\n", "amateur) and losing myself in\n", "Hacker News\n", ", nodding my head sagely to things I only half understand.\n", "I’m the co-founder and CTO of\n", "Nebula.io\n", ". We’re applying AI to a field where it can make a massive, positive impact: helping people discover their potential and pursue their reason for being. Recruiters use our product today to source, understand, engage and manage talent. I’m previously the founder and CEO of AI startup untapt,\n", "acquired in 2021\n", ".\n", "We work with groundbreaking, proprietary LLMs verticalized for talent, we’ve\n", "patented\n", "our matching model, and our award-winning platform has happy customers and tons of press coverage.\n", "Connect\n", "with me for more!\n", "December 21, 2024\n", "Welcome, SuperDataScientists!\n", "November 13, 2024\n", "Mastering AI and LLM Engineering – Resources\n", "October 16, 2024\n", "From Software Engineer to AI Data Scientist – resources\n", "August 6, 2024\n", "Outsmart LLM Arena – a battle of diplomacy and deviousness\n", "Navigation\n", "Home\n", "Outsmart\n", "An arena that pits LLMs against each other in a battle of diplomacy and deviousness\n", "About\n", "Posts\n", "Get in touch\n", "ed [at] edwarddonner [dot] com\n", "www.edwarddonner.com\n", "Follow me\n", "LinkedIn\n", "Twitter\n", "Facebook\n", "Subscribe to newsletter\n", "Type your email…\n", "Subscribe\n"]}], "source": ["# Let's try one out. Change the website and add print statements to follow along.\n", "\n", "ed = Website(\"https://edwarddonner.com\")\n", "print(ed.title)\n", "print(ed.text)"]}, {"cell_type": "markdown", "id": "6a478a0c-2c53-48ff-869c-4d08199931e1", "metadata": {}, "source": ["## Types of prompts\n", "\n", "You may know this already - but if not, you will get very familiar with it!\n", "\n", "Models like GPT4o have been trained to receive instructions in a particular way.\n", "\n", "They expect to receive:\n", "\n", "**A system prompt** that tells them what task they are performing and what tone they should use\n", "\n", "**A user prompt** -- the conversation starter that they should reply to"]}, {"cell_type": "code", "execution_count": 8, "id": "abdb8417-c5dc-44bc-9bee-2e059d162699", "metadata": {}, "outputs": [], "source": ["# Define our system prompt - you can experiment with this later, changing the last sentence to '<PERSON><PERSON><PERSON> in markdown in Spanish.\"\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": 9, "id": "f0275b1b-7cfe-4f9d-abfa-7650d378da0c", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": 10, "id": "26448ec4-5c00-4204-baec-7df91d11ff2e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are looking at a website titled Home - <PERSON>\n", "The contents of this website is as follows; please provide a short summary of this website in markdown. If it includes news or announcements, then summarize these too.\n", "\n", "Home\n", "Outsmart\n", "An arena that pits LLMs against each other in a battle of diplomacy and deviousness\n", "About\n", "Posts\n", "Well, hi there.\n", "I’m <PERSON>. I like writing code and experimenting with LLMs, and hopefully you’re here because you do too. I also enjoy DJing (but I’m badly out of practice), amateur electronic music production (\n", "very\n", "amateur) and losing myself in\n", "Hacker News\n", ", nodding my head sagely to things I only half understand.\n", "I’m the co-founder and CTO of\n", "Nebula.io\n", ". We’re applying AI to a field where it can make a massive, positive impact: helping people discover their potential and pursue their reason for being. Recruiters use our product today to source, understand, engage and manage talent. I’m previously the founder and CEO of AI startup untapt,\n", "acquired in 2021\n", ".\n", "We work with groundbreaking, proprietary LLMs verticalized for talent, we’ve\n", "patented\n", "our matching model, and our award-winning platform has happy customers and tons of press coverage.\n", "Connect\n", "with me for more!\n", "December 21, 2024\n", "Welcome, SuperDataScientists!\n", "November 13, 2024\n", "Mastering AI and LLM Engineering – Resources\n", "October 16, 2024\n", "From Software Engineer to AI Data Scientist – resources\n", "August 6, 2024\n", "Outsmart LLM Arena – a battle of diplomacy and deviousness\n", "Navigation\n", "Home\n", "Outsmart\n", "An arena that pits LLMs against each other in a battle of diplomacy and deviousness\n", "About\n", "Posts\n", "Get in touch\n", "ed [at] edwarddonner [dot] com\n", "www.edwarddonner.com\n", "Follow me\n", "LinkedIn\n", "Twitter\n", "Facebook\n", "Subscribe to newsletter\n", "Type your email…\n", "Subscribe\n"]}], "source": ["print(user_prompt_for(ed))"]}, {"cell_type": "markdown", "id": "ea211b5f-28e1-4a86-8e52-c0b7677cadcc", "metadata": {}, "source": ["## Messages\n", "\n", "The API from OpenAI expects to receive messages in a particular structure.\n", "Many of the other APIs share this structure:\n", "\n", "```\n", "[\n", "    {\"role\": \"system\", \"content\": \"system message goes here\"},\n", "    {\"role\": \"user\", \"content\": \"user message goes here\"}\n", "]\n", "\n", "To give you a preview, the next 2 cells make a rather simple call - we won't stretch the might GPT (yet!)"]}, {"cell_type": "code", "execution_count": 11, "id": "f25dcd35-0cd0-4235-9f64-ac37ed9eaaa5", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a snarky assistant\"},\n", "    {\"role\": \"user\", \"content\": \"What is 2 + 2?\"}\n", "]"]}, {"cell_type": "code", "execution_count": 12, "id": "21ed95c5-7001-47de-a36d-1d6673b403ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Oh, we're starting with the basics, huh? Well, 2 + 2 equals 4. Shocking, I know!\n"]}], "source": ["# To give you a preview -- calling OpenAI with system and user messages:\n", "\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=messages)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "d06e8d78-ce4c-4b05-aa8e-17050c82bb47", "metadata": {}, "source": ["## And now let's build useful messages for GPT-4o-mini, using a function"]}, {"cell_type": "code", "execution_count": 12, "id": "0134dfa4-8299-48b5-b444-f2a8c3403c88", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "code", "execution_count": 13, "id": "36478464-39ee-485c-9f3f-6a4e458dbc9c", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'system',\n", "  'content': 'You are an assistant that analyzes the contents of a website and provides a short summary, ignoring text that might be navigation related. Respond in markdown.'},\n", " {'role': 'user',\n", "  'content': 'You are looking at a website titled Home - <PERSON>\\nThe contents of this website is as follows; please provide a short summary of this website in markdown. If it includes news or announcements, then summarize these too.\\n\\nHome\\nOutsmart\\nAn arena that pits LLMs against each other in a battle of diplomacy and deviousness\\nAbout\\nPosts\\nWell, hi there.\\nI’m Ed. I like writing code and experimenting with LLMs, and hopefully you’re here because you do too. I also enjoy DJing (but I’m badly out of practice), amateur electronic music production (\\nvery\\namateur) and losing myself in\\nHacker News\\n, nodding my head sagely to things I only half understand.\\nI’m the co-founder and CTO of\\nNebula.io\\n. We’re applying AI to a field where it can make a massive, positive impact: helping people discover their potential and pursue their reason for being. Recruiters use our product today to source, understand, engage and manage talent. I’m previously the founder and CEO of AI startup untapt,\\nacquired in 2021\\n.\\nWe work with groundbreaking, proprietary LLMs verticalized for talent, we’ve\\npatented\\nour matching model, and our award-winning platform has happy customers and tons of press coverage.\\nConnect\\nwith me for more!\\nDecember 21, 2024\\nWelcome, SuperDataScientists!\\nNovember 13, 2024\\nMastering AI and LLM Engineering – Resources\\nOctober 16, 2024\\nFrom Software Engineer to AI Data Scientist – resources\\nAugust 6, 2024\\nOutsmart LLM Arena – a battle of diplomacy and deviousness\\nNavigation\\nHome\\nOutsmart\\nAn arena that pits LLMs against each other in a battle of diplomacy and deviousness\\nAbout\\nPosts\\nGet in touch\\ned [at] edwarddonner [dot] com\\nwww.edwarddonner.com\\nFollow me\\nLinkedIn\\nTwitter\\nFacebook\\nSubscribe to newsletter\\nType your email…\\nSubscribe'}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Try this out, and then try for a few more websites\n", "\n", "messages_for(ed)"]}, {"cell_type": "markdown", "id": "16f49d46-bf55-4c3e-928f-68fc0bf715b0", "metadata": {}, "source": ["## Time to bring it together - the API for OpenAI is very simple!"]}, {"cell_type": "code", "execution_count": 14, "id": "905b9919-aba7-45b5-ae65-81b3d1d78e34", "metadata": {}, "outputs": [], "source": ["# And now: call the OpenAI API. You will get very familiar with this!\n", "\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 15, "id": "05e38d41-dfa4-4b20-9c96-c46ea75d9fb5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'# Summary of <PERSON>\\'s Website\\n\\nEdward <PERSON>\\'s website serves as a platform for sharing his interests and expertise in coding, large language models (LLMs), and AI. He is the co-founder and CTO of Nebula.io, a company focused on leveraging AI to enhance talent discovery and management. Previously, he founded the AI startup untapt, which was acquired in 2021.\\n\\n## Key Content\\n\\n- **Personal Introduction**: <PERSON> shares his passion for coding, experimenting with LLMs, DJing, and music production.\\n- **Professional Background**: He highlights his role at Nebula.io and his prior experience with untapt.\\n- **Innovative Work**: Mention of proprietary LLMs tailored for talent management and a patented matching model.\\n\\n## News and Announcements\\n\\n- **December 21, 2024**: Welcoming \"SuperDataScientists.\"\\n- **November 13, 2024**: Resources for mastering AI and LLM engineering.\\n- **October 16, 2024**: Transitioning from software engineering to AI data science resources.\\n- **August 6, 2024**: Introduction to the Outsmart LLM Arena, a competition focusing on strategy among LLMs.\\n\\nThe website encourages connections and offers resources for individuals interested in AI and LLMs.'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["summarize(\"https://edwarddonner.com\")"]}, {"cell_type": "code", "execution_count": 16, "id": "3d926d59-450e-4609-92ba-2d6f244f1342", "metadata": {}, "outputs": [], "source": ["# A function to display this nicely in the Ju<PERSON><PERSON> output, using markdown\n", "\n", "def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": 17, "id": "3018853a-445f-41ff-9560-d925d1774b2f", "metadata": {}, "outputs": [{"data": {"text/markdown": ["# Summary of <PERSON>'s Website\n", "\n", "The website belongs to <PERSON>, a coder and LLM (Large Language Model) enthusiast, who is also a co-founder and CTO of Nebula.io. Nebula.io focuses on leveraging AI to help individuals discover their potential in recruitment through its innovative platform. <PERSON> also shares his background in the AI field, having previously founded the startup untapt, which was acquired in 2021.\n", "\n", "## Recent News and Announcements\n", "1. **December 21, 2024**: Welcome message for SuperDataScientists.\n", "2. **November 13, 2024**: Resources for mastering AI and LLM engineering.\n", "3. **October 16, 2024**: Resources for transitioning from Software Engineer to AI Data Scientist.\n", "4. **August 6, 2024**: Introduction to the \"Outsmart LLM Arena,\" a competitive platform where LLMs engage in diplomacy and strategy.\n", "\n", "<PERSON> expresses a passion for technology, music, and engaging in community discussions through platforms like Hacker News."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary(\"https://edwarddonner.com\")"]}, {"cell_type": "markdown", "id": "b3bcf6f4-adce-45e9-97ad-d9a5d7a3a624", "metadata": {}, "source": ["# Let's try more websites\n", "\n", "Note that this will only work on websites that can be scraped using this simplistic approach.\n", "\n", "Websites that are rendered with Javascript, like React apps, won't show up. See the community-contributions folder for a Selenium implementation that gets around this. You'll need to read up on installing Selenium (ask ChatGPT!)\n", "\n", "Also Websites protected with CloudFront (and similar) may give 403 errors - many thanks <PERSON> for pointing this out.\n", "\n", "But many websites will work just fine!"]}, {"cell_type": "code", "execution_count": 18, "id": "45d83403-a24c-44b5-84ac-961449b4008f", "metadata": {}, "outputs": [{"data": {"text/markdown": ["# CNN Website Summary\n", "\n", "CNN is a leading news platform that provides comprehensive coverage across a wide range of categories including US and world news, politics, business, health, entertainment, and more. The website features breaking news articles, videos, and live updates on significant global events.\n", "\n", "### Recent Headlines:\n", "- **Politics**: \n", "  - <PERSON> announced his resignation as Canada's Prime Minister, sharing his \"one regret.\"\n", "  - Analysis of <PERSON>'s influence in Congress and recent legal battles related to his actions.\n", "  \n", "- **Global Affairs**: \n", "  - Rising tensions in Venezuela as the opposition leader urges military action against <PERSON><PERSON>.\n", "  - Sudanese authorities announced the transfer of 11 Yemeni detainees from Guantanamo Bay to Oman.\n", "  \n", "- **Weather**: A major winter storm impacted Washington, DC, causing power outages and stranded drivers.\n", "\n", "- **Health**: \n", "  - FDA issues new draft guidance on improving pulse oximeter readings for individuals with darker skin.\n", "\n", "### Additional Features:\n", "CNN includes segments dedicated to sports, science, climate, and travel. There are also various podcasts available, offering deeper insights into current events and specialized topics. \n", "\n", "The site encourages user feedback on ads and technical issues, emphasizing its commitment to enhancing user experience. \n", "\n", "Overall, CNN serves as a crucial resource for staying updated with local and international news."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary(\"https://cnn.com\")"]}, {"cell_type": "code", "execution_count": 19, "id": "75e9fd40-b354-4341-991e-863ef2e59db7", "metadata": {}, "outputs": [{"data": {"text/markdown": ["# Anthropic Website Summary\n", "\n", "Anthropic is an AI safety and research company that prioritizes safety in the development of AI technologies. The main focus of the site is on their AI model, Claude, which includes the latest version, Claude 3.5 Sonnet, as well as additional offerings like Claude 3.5 Haiku. The company emphasizes the creation of AI-powered applications and custom experiences through its API.\n", "\n", "## Recent Announcements\n", "- **Claude 3.5 Sonnet Launch**: Announced on October 22, 2024, featuring significant advancements in AI capabilities.\n", "- **New AI Models**: Introduction of <PERSON> 3.5 Sonnet and <PERSON> 3.5 <PERSON><PERSON>.\n", "\n", "<PERSON><PERSON><PERSON>'s work spans various domains including machine learning, policy, and product development, aimed at generating reliable and beneficial AI systems. They also highlight career opportunities within the organization."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary(\"https://anthropic.com\")"]}, {"cell_type": "code", "execution_count": 21, "id": "8070c4c3-1ef1-4c7a-8c2d-f6b4b9b4aa8e", "metadata": {}, "outputs": [{"data": {"text/markdown": ["# Summary of CPP Investments Website\n", "\n", "## Overview\n", "The CPP Investments website serves as a comprehensive resource for information regarding the management and performance of the Canada Pension Plan (CPP) Fund. It emphasizes its long-standing commitment to ensuring financial security for over 22 million Canadians who rely on the benefits of the CPP.\n", "\n", "## Key Sections\n", "- **About Us**: Details the governance, leadership, and investment programs available within CPP Investments.\n", "- **The Fund**: Offers an overview of the fund's performance, sustainability, and transparency in its operations.\n", "- **Investment Strategies**: Explanation of CPP's investment beliefs and strategies, emphasizing a global mindset and sustainable investing practices.\n", "- **Insights Institute**: A dedicated section for reports and analyses on relevant investment topics, including emerging trends and strategies.\n", "\n", "## Recent News and Announcements\n", "- **2024 CEO Letter** (May 22, 2024): Reflects on the 25th anniversary of CPP Investments and its mission to manage funds in the best interest of Canadians.\n", "- **Article on CPP Benefits** (September 18, 2024): Highlights why the CPP is regarded as one of the best pension plans globally.\n", "- **Report on AI Integration and Human Capital** (October 31, 2024): Discusses how institutional investors can engage with boards and leadership on AI adaptation strategies.\n", "- **Stake Sales** (January 3, 2025): Announcements regarding the sale of stakes in various partnerships and joint ventures, including a significant logistics partnership in North America and real estate ventures in Hong Kong.\n", "\n", "This website underscores CPP Investments' ongoing commitment to transparency, strong financial performance, and its role in supporting the financial security of Canadians as they prepare for retirement."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary('https://cppinvestments.com')"]}, {"cell_type": "markdown", "id": "c951be1a-7f1b-448f-af1f-845978e47e2c", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business applications</h2>\n", "            <span style=\"color:#181;\">In this exercise, you experienced calling the Cloud API of a Frontier Model (a leading model at the frontier of AI) for the first time. We will be using APIs like OpenAI at many stages in the course, in addition to building our own LLMs.\n", "\n", "More specifically, we've applied this to Summarization - a classic Gen AI use case to make a summary. This can be applied to any business vertical - summarizing the news, summarizing financial performance, summarizing a resume in a cover letter - the applications are limitless. Consider how you could apply Summarization in your business, and try prototyping a solution.</span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Before you continue - now try yourself</h2>\n", "            <span style=\"color:#900;\">Use the cell below to make your own simple commercial example. Stick with the summarization use case for now. Here's an idea: write something that will take the contents of an email, and will suggest an appropriate short subject line for the email. That's the kind of feature that might be built into a commercial email tool.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 33, "id": "00743dac-0e70-45b7-879a-d7293a6f68a6", "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Subject:** Request for Annual Sales Report (2024)\n", "\n", "**Email:**\n", "\n", "Dear <PERSON><PERSON><PERSON><PERSON>,\n", "\n", "I hope this email finds you in good health and high spirits. As we step into a new year and begin reviewing our plans and strategies, it is crucial for us to analyze the performance metrics from the previous year. In this regard, I would like to kindly request a copy of the Annual Sales Report for 2024.\n", "\n", "This report will play an integral role in understanding our achievements, challenges, and areas for improvement over the past year. It will also serve as a foundation for aligning our goals and preparing a roadmap for the upcoming quarters. Please ensure that the report includes key performance indicators such as:\n", "\n", "- Total revenue generated\n", "- Region-wise sales performance\n", "- Product/service-wise contribution\n", "- Month-by-month trend analysis\n", "- Customer retention and acquisition metrics\n", "\n", "If there are any additional insights or observations from your side that you feel would be helpful for us to review, please feel free to include them as well. Your expertise and detailed input are always highly valued.\n", "\n", "Kindly let me know if the report is already prepared or if there is an expected timeline for its completion. In case you require any assistance, data inputs, or clarification from my end to finalize the report, do not hesitate to reach out.\n", "\n", "Thank you in advance for prioritizing this request. I appreciate your support and look forward to receiving the report soon.\n", "\n", "Best regards,  \n", "<PERSON><PERSON>\n", "\n", "**Tone:** Professional and Collaborative"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"You are an AI assistant email reviewer. All you need is to identify the meaning of the context in the text given and provide the subject line and email. and in the end of text, please provide the tone info.\"\n", "user_prompt = \"\"\"\n", "    Dear <PERSON><PERSON><PERSON><PERSON>,\n", "\n", "I hope this email finds you in good health and high spirits. As we step into a new year and begin reviewing our plans and strategies, it is crucial for us to analyze the performance metrics from the previous year. In this regard, I would like to kindly request a copy of the Annual Sales Report for 2024.\n", "\n", "This report will play an integral role in understanding our achievements, challenges, and areas for improvement over the past year. It will also serve as a foundation for aligning our goals and preparing a roadmap for the upcoming quarters. Please ensure that the report includes key performance indicators such as:\n", "\n", "Total revenue generated\n", "Region-wise sales performance\n", "Product/service-wise contribution\n", "Month-by-month trend analysis\n", "Customer retention and acquisition metrics\n", "If there are any additional insights or observations from your side that you feel would be helpful for us to review, please feel free to include them as well. Your expertise and detailed input are always highly valued.\n", "\n", "Kindly let me know if the report is already prepared or if there is an expected timeline for its completion. In case you require any assistance, data inputs, or clarification from my end to finalize the report, do not hesitate to reach out.\n", "\n", "Thank you in advance for prioritizing this request. I appreciate your support and look forward to receiving the report soon.\n", "\n", "Best regards,\n", "<PERSON><PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\":\"system\", \"content\": system_prompt},\n", "    {\"role\":\"user\", \"content\": user_prompt}\n", "    \n", "] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=messages\n", ")\n", "\n", "# Step 4: print the result\n", "\n", "display(Markdown(response.choices[0].message.content))"]}, {"cell_type": "code", "execution_count": 14, "id": "d4d641a5-0103-44a5-b5c2-70e80976d1f1", "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Subject:** Addressing Sales Performance Concerns\n", "\n", "<PERSON> <PERSON><PERSON><PERSON>,\n", "\n", "I wanted to touch base with you about your sales performance over the last two quarters. I’ve noticed that you haven’t been hitting the targets, and it’s something we need to address seriously.\n", "\n", "I know you’re capable of much more, and I want to see you succeed. That said, it’s crucial that you meet your sales targets this quarter. If there isn’t a significant improvement, we may have to consider other options, including letting you go, which I truly hope we can avoid.\n", "\n", "If there’s anything holding you back or if you need additional support, let me know. I’m here to help, but ultimately, it’s up to you to turn things around.\n", "\n", "Let’s make this quarter count! Let me know if you want to discuss this further or need help strategizing.\n", "\n", "Best regards,  \n", "<PERSON><PERSON>\n", "\n", "**Tone:** Serious yet supportive"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"You are an AI assistant email reviewer. All you need is to identify the meaning of the context in the text given and provide the subject line and email. and in the end of text, please provide the tone info.\"\n", "user_prompt = \"\"\"\n", "<PERSON> <PERSON><PERSON><PERSON>,\n", "\n", "I wanted to touch base with you about your sales performance over the last two quarters. I’ve noticed that you haven’t been hitting the targets, and it’s something we need to address seriously.\n", "\n", "I know you’re capable of much more, and I want to see you succeed. That said, it’s crucial that you meet your sales targets this quarter. If there isn’t a significant improvement, we may have to consider other options, including letting you go, which I truly hope we can avoid.\n", "\n", "If there’s anything holding you back or if you need additional support, let me know. I’m here to help, but ultimately, it’s up to you to turn things around.\n", "\n", "Let’s make this quarter count! Let me know if you want to discuss this further or need help strategizing.\n", "\n", "Best regards,\n", "<PERSON><PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\":\"system\", \"content\": system_prompt},\n", "    {\"role\":\"user\", \"content\": user_prompt}\n", "    \n", "] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=messages\n", ")\n", "\n", "# Step 4: print the result\n", "\n", "display(Markdown(response.choices[0].message.content))"]}, {"cell_type": "markdown", "id": "36ed9f14-b349-40e9-a42c-b367e77f8bda", "metadata": {}, "source": ["## An extra exercise for those who enjoy web scraping\n", "\n", "You may notice that if you try `display_summary(\"https://openai.com\")` - it doesn't work! That's because OpenAI has a fancy website that uses Javascript. There are many ways around this that some of you might be familiar with. For example, Selenium is a hugely popular framework that runs a browser behind the scenes, renders the page, and allows you to query it. If you have experience with Se<PERSON><PERSON>, <PERSON><PERSON> or similar, then feel free to improve the Website class to use them. In the community-contributions folder, you'll find an example Selenium solution from a student (thank you!)"]}, {"cell_type": "markdown", "id": "eeab24dc-5f90-4570-b542-b0585aca3eb6", "metadata": {}, "source": ["# Sharing your code\n", "\n", "I'd love it if you share your code afterwards so I can share it with others! You'll notice that some students have already made changes (including a Selenium implementation) which you will find in the community-contributions folder. If you'd like add your changes to that folder, submit a Pull Request with your new versions in that folder and I'll merge your changes.\n", "\n", "If you're not an expert with git (and I am not!) then GPT has given some nice instructions on how to submit a Pull Request. It's a bit of an involved process, but once you've done it once it's pretty clear. As a pro-tip: it's best if you clear the outputs of your Jupyter notebooks (Edit >> Clean outputs of all cells, and then Save) for clean notebooks.\n", "\n", "Here are good instructions courtesy of an AI friend:  \n", "https://chatgpt.com/share/677a9cb5-c64c-8012-99e0-e06e88afd293"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}