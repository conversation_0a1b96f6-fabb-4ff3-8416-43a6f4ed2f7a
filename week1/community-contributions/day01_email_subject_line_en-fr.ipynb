{"cells": [{"cell_type": "code", "execution_count": null, "id": "d25b0aef-3e5e-4026-90ee-2b373bf262b7", "metadata": {}, "outputs": [], "source": ["# Step 0: Import libraries and load environment variables\n", "import os\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "if not api_key:\n", "    print(\"No API key was found!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it does not start with 'sk-proj-'! Please ensure you are using the right key.\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end! Please remove them.\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n", "\n", "# Step 1: Create prompts\n", "print(\"[INFO] Creating system prompt ...\")\n", "system_prompt = \"You are an assistant that analyzes the contents of \\\n", "    email texts and suggests short subject lines for the email based \\\n", "    on the requested tone and language. Respond in markdown.\"\n", "\n", "print(\"[INFO] Creating user prompt ...\")\n", "user_prompt = \"\"\"\n", "    The text below is an e-mail text for which you are required to \\\n", "    provide subject lines. Please provide two snarky, two funny, and \\\n", "    two formal short subject lines for the email text. Each of the six \\\n", "    subject lines should be presented in both English and French \\\n", "    languages, making a total of 12 subject lines. Please provide your \\\n", "    answer in markdown.\\\n", "    \n", "    \\n\\n\n", "    \n", "    Welcome to arXiv!\n", "\n", "    Thank you for creating an account and joining the arXiv community. We look\n", "    forward to receiving your contribution.\n", "\n", "    Help Pages\n", "    An overview on how to navigate and use arXiv can be found here:\n", "    https://arxiv.org/help\n", "    https://arxiv.org/about\n", "\n", "    If you would like to know more about the submission process, please go here:\n", "    https://arxiv.org/help/submit\n", "\n", "    Before Submitting to arXiv\n", "    The arXiv.org e-print archive is fully automated and processes nearly\n", "    1,000 new submissions per day. To help us keep the process running smoothly\n", "    and efficiently please check your submission carefully for mistakes, typos\n", "    and layout issues. Once you have submitted your work please check your account\n", "    frequently for verification messages and other communication from arXiv.\n", "\n", "    Contacting arXiv\n", "    We have provided extensive help pages to guide you through the process and\n", "    to answer the most common questions. If you have problems with the submission\n", "    process please contact us here:\n", "    https://arxiv.org/help/contact\n", "    We aim to assist submitters within one business day, but during times of high\n", "    volume or maintenance work we may be slightly delayed in our response.\n", "\n", "    Thank you for your cooperation.\n", "\"\"\"\n", "\n", "# Step 2: Make messages list\n", "print(\"[INFO] Making messages list ...\")\n", "messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt}\n", "]\n", "\n", "# Step 3: Call OpenAI\n", "print(\"[INFO] Calling OpenAI ...\")\n", "openai = OpenAI()\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=messages\n", "    )\n", "\n", "# Step 4: Print result\n", "print(\"[INFO] Print result ...\")\n", "display(Markdown(response.choices[0].message.content))\n"]}, {"cell_type": "code", "execution_count": null, "id": "b0a6676e-fb43-4725-9389-2acd74c13c4e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}