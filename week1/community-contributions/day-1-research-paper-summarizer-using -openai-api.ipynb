{"cells": [{"cell_type": "code", "execution_count": 3, "id": "52dc600c-4c45-4803-81cb-f06347f4b2c3", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 4, "id": "4082f16f-d843-41c7-9137-cdfec093b2d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key found and looks good so far\n"]}], "source": ["load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if not api_key:\n", "    print('No API key was found')\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"API key is found but is not in the proper format\")\n", "else:\n", "    print(\"API key found and looks good so far\")"]}, {"cell_type": "code", "execution_count": 5, "id": "16c295ce-c57d-429e-8c03-f6610a8ddd42", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": 16, "id": "9a548a52-0f7e-4fdf-ad68-0138b2445935", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"You are a research summarizer. That summarizes the content of the research paper in no more than 1000 words. The research summary that you provide should include the following:\n", "1) Title and Authors - Identify the study and contributors.\n", "2) Objective/Problem - State the research goal or question.\n", "3) Background - Briefly explain the context and significance.\n", "4) Methods - Summarize the approach or methodology.\n", "5) Key Findings - Highlight the main results or insights.\n", "6) Conclusion - Provide the implications or contributions of the study.\n", "7) Future Directions - Suggest areas for further research or exploration.\n", "8) Limitations - Highlight constraints or challenges in the study.\n", "9) Potential Applications - Discuss how the findings can be applied in real-world scenarios.\n", "Keep all points concise, clear, and focused and generate output in markdown.\"\"\""]}, {"cell_type": "code", "execution_count": 7, "id": "66b4411f-172e-46be-b6cd-a9e5b857fb28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ipywidgets in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (8.1.5)\n", "Requirement already satisfied: pdfplumber in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (0.11.4)\n", "Requirement already satisfied: comm>=0.1.3 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipywidgets) (0.2.2)\n", "Requirement already satisfied: ipython>=6.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipywidgets) (8.30.0)\n", "Requirement already satisfied: traitlets>=4.3.1 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipywidgets) (5.14.3)\n", "Requirement already satisfied: widgetsnbextension~=4.0.12 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipywidgets) (4.0.13)\n", "Requirement already satisfied: jupyterlab_widgets~=3.0.12 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipywidgets) (3.0.13)\n", "Requirement already satisfied: pdfminer.six==20231228 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from pdfplumber) (20231228)\n", "Requirement already satisfied: Pillow>=9.1 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from pdfplumber) (11.0.0)\n", "Requirement already satisfied: pypdfium2>=4.18.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from pdfplumber) (4.30.0)\n", "Requirement already satisfied: charset-normalizer>=2.0.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber) (3.4.0)\n", "Requirement already satisfied: cryptography>=36.0.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from pdfminer.six==20231228->pdfplumber) (44.0.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.4.6)\n", "Requirement already satisfied: decorator in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (5.1.1)\n", "Requirement already satisfied: jedi>=0.16 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.19.2)\n", "Requirement already satisfied: matplotlib-inline in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.1.7)\n", "Requirement already satisfied: prompt_toolkit<3.1.0,>=3.0.41 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (3.0.48)\n", "Requirement already satisfied: pygments>=2.4.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (2.18.0)\n", "Requirement already satisfied: stack_data in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (0.6.3)\n", "Requirement already satisfied: typing_extensions>=4.6 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from ipython>=6.1.0->ipywidgets) (4.12.2)\n", "Requirement already satisfied: cffi>=1.12 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber) (1.17.1)\n", "Requirement already satisfied: parso<0.9.0,>=0.8.4 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from jedi>=0.16->ipython>=6.1.0->ipywidgets) (0.8.4)\n", "Requirement already satisfied: wcwidth in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from prompt_toolkit<3.1.0,>=3.0.41->ipython>=6.1.0->ipywidgets) (0.2.13)\n", "Requirement already satisfied: executing>=1.2.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (2.1.0)\n", "Requirement already satisfied: asttokens>=2.1.0 in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (3.0.0)\n", "Requirement already satisfied: pure_eval in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from stack_data->ipython>=6.1.0->ipywidgets) (0.2.3)\n", "Requirement already satisfied: pycparser in c:\\users\\<USER>\\anaconda3\\envs\\research_summary\\lib\\site-packages (from cffi>=1.12->cryptography>=36.0.0->pdfminer.six==20231228->pdfplumber) (2.22)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install ipywidgets pdfplumber"]}, {"cell_type": "code", "execution_count": 8, "id": "d8cd8556-ad86-4949-9f15-09de2b8c712b", "metadata": {}, "outputs": [], "source": ["import pdfplumber\n", "from ipywidgets import widgets\n", "from io import BytesIO"]}, {"cell_type": "code", "execution_count": 9, "id": "0eba3cee-d85c-4d75-9b27-70c8cd7587b1", "metadata": {}, "outputs": [], "source": ["from IPython.display import display, Markdown"]}, {"cell_type": "code", "execution_count": 10, "id": "53e270e1-c2e6-4bcc-9ada-90c059cd5a51", "metadata": {}, "outputs": [], "source": ["def messages_for(user_prompt):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]"]}, {"cell_type": "code", "execution_count": 11, "id": "2f1807ec-c10b-4d26-9bee-89bd7a4bbb95", "metadata": {}, "outputs": [], "source": ["def summarize(user_prompt):\n", "    # Generate messages using the user_prompt\n", "    messages = messages_for(user_prompt)\n", "    try:\n", "        response = openai.chat.completions.create(\n", "            model=\"gpt-4o-mini\",  # Correct model name\n", "            messages=messages,\n", "            max_tokens = 1000 # Pass the generated messages\n", "        )\n", "        # Return the content from the API response correctly\n", "        return response.choices[0].message.content\n", "    except Exception as e:\n", "        # Instead of printing, return an error message that can be displayed\n", "        return f\"Error in OpenAI API call: {e}\""]}, {"cell_type": "code", "execution_count": 12, "id": "0dee8345-4eec-4a9c-ac4e-ad70e13cea44", "metadata": {}, "outputs": [], "source": ["upload_widget = widgets.FileUpload(\n", "    accept='.pdf', \n", "    multiple=False,\n", "    description='Upload PDF',\n", "    layout=widgets.Layout(width='300px',height = '100px', border='2px dashed #cccccc', padding='10px')\n", ")"]}, {"cell_type": "code", "execution_count": 17, "id": "1ff9c7b9-1a3a-4128-a33f-0e5bb2a93d33", "metadata": {}, "outputs": [], "source": ["def extract_text_and_generate_summary(change):\n", "    print(\"extracting text\")\n", "    if upload_widget.value:\n", "        # Extract the first uploaded file\n", "        uploaded_file = list(upload_widget.value)[0]\n", "        pdf_file = uploaded_file['content']\n", "\n", "        # Extract text from the PDF\n", "        try:\n", "            with pdfplumber.open(BytesIO(pdf_file)) as pdf:\n", "                extracted_text = \"\\n\".join(page.extract_text() for page in pdf.pages)\n", "\n", "            # Generate the user prompt\n", "            user_prompt = (\n", "                f\"You are looking at the text from a research paper. Summarize it in no more than 1000 words. \"\n", "                f\"The output should be in markdown.\\n\\n{extracted_text}\"\n", "            )\n", "\n", "            # Get the summarized response\n", "            response = summarize(user_prompt)\n", "            \n", "            if response:\n", "                # Use IPython's display method to show markdown below the cell\n", "                display(Markdown(response))\n", "        \n", "        except Exception as e:\n", "            # If there's an error, display it using Markdown\n", "            display(Markdown(f\"**Error:** {str(e)}\"))\n", "\n", "    # Reset the upload widget\n", "    upload_widget.value = ()"]}, {"cell_type": "code", "execution_count": 18, "id": "0c16fe3f-704e-4a87-acd9-42c4e6b0d2fa", "metadata": {}, "outputs": [], "source": ["upload_widget.observe(extract_text_and_generate_summary, names='value')"]}, {"cell_type": "code", "execution_count": 19, "id": "c2c2d2b2-1264-42d9-9271-c4700b4df80a", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7304350377d845e78a9a758235e5eba1", "version_major": 2, "version_minor": 0}, "text/plain": ["FileUpload(value=(), accept='.pdf', description='Upload PDF', layout=Layout(border_bottom='2px dashed #cccccc'…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(upload_widget)"]}, {"cell_type": "code", "execution_count": null, "id": "70c76b90-e626-44b3-8d1f-6e995e8a938d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}