{"dashboard": {"id": null, "uid": "mock-sys-metrics", "title": "Mock System Metrics Dashboard", "timezone": "browser", "panels": [{"id": 1, "title": "CPU Usage (%)", "type": "stat", "datasource": "MockData", "targets": [], "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}, {"color": "orange", "value": 70}, {"color": "red", "value": 90}]}}}, "options": {"reduceOptions": {"calcs": ["mean"]}}}, {"id": 2, "title": "Memory Usage", "type": "gauge", "datasource": "MockData", "fieldConfig": {"defaults": {"unit": "bytes", "max": 16000000000.0, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "orange", "value": 12000000000.0}, {"color": "red", "value": 14000000000.0}]}}}}, {"id": 3, "title": "Disk Read Errors", "type": "stat", "datasource": "MockData", "fieldConfig": {"defaults": {"unit": "short", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "orange", "value": 5}, {"color": "red", "value": 10}]}}}}, {"id": 4, "title": "GPU Usage (%)", "type": "gauge", "datasource": "MockData", "fieldConfig": {"defaults": {"unit": "percent", "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "orange", "value": 75}, {"color": "red", "value": 90}]}}}}], "schemaVersion": 30, "version": 1, "refresh": "30s"}, "overwrite": true}