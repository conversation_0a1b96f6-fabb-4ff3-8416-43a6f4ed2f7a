{"cells": [{"cell_type": "markdown", "id": "a98030af-fcd1-4d63-a36e-38ba053498fa", "metadata": {}, "source": ["# A full business solution\n", "\n", "## Now we will take our project from Day 1 to the next level\n", "\n", "### BUSINESS CHALLENGE:\n", "\n", "Create a product that builds a Brochure for a company to be used for prospective clients, investors and potential recruits.\n", "\n", "We will be provided a company name and their primary website.\n", "\n", "See the end of this notebook for examples of real-world business applications.\n", "\n", "And remember: I'm always available if you have problems or ideas! Please do reach out."]}, {"cell_type": "code", "execution_count": null, "id": "d5b08506-dc8b-4443-9201-5f1848161363", "metadata": {}, "outputs": [], "source": ["# imports\n", "# If these fail, please check you're running from an 'activated' environment with (llms) in the command prompt\n", "\n", "import os\n", "import requests\n", "import json\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "fc5d8880-f2ee-4c06-af16-ecbc0262af61", "metadata": {}, "outputs": [], "source": ["# Initialize and constants\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if api_key and api_key.startswith('sk-proj-') and len(api_key)>10:\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? Please visit the troubleshooting notebook!\")\n", "    \n", "MODEL = 'gpt-4o-mini'\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "106dd65e-90af-4ca8-86b6-23a41840645b", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "e30d8128-933b-44cc-81c8-ab4c9d86589a", "metadata": {}, "outputs": [], "source": ["ed = Website(\"https://edwarddonner.com\")\n", "ed.links"]}, {"cell_type": "markdown", "id": "1771af9c-717a-4fca-bbbe-8a95893312c3", "metadata": {}, "source": ["## First step: Have GPT-4o-mini figure out which links are relevant\n", "\n", "### Use a call to gpt-4o-mini to read the links on a webpage, and respond in structured JSON.  \n", "It should decide which links are relevant, and replace relative links such as \"/about\" with \"https://company.com/about\".  \n", "We will use \"one shot prompting\" in which we provide an example of how it should respond in the prompt.\n", "\n", "This is an excellent use case for an LLM, because it requires nuanced understanding. Imagine trying to code this without LLMs by parsing and analyzing the webpage - it would be very hard!\n", "\n", "Sidenote: there is a more advanced technique called \"Structured Outputs\" in which we require the model to respond according to a spec. We cover this technique in Week 8 during our autonomous Agentic AI project."]}, {"cell_type": "code", "execution_count": null, "id": "6957b079-0d96-45f7-a26a-3487510e9b35", "metadata": {}, "outputs": [], "source": ["oneshot_system_prompt = \"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the company or freelancer offering their services, \\\n", "such as links to an About page, or a Company page, or Careers/Jobs pages.\\n\"\n", "oneshot_system_prompt += \"You should respond in JSON as in this example:\"\n", "oneshot_system_prompt += \"\"\"\n", "{\n", "    \"links\": [\n", "        {\"type\": \"about page\", \"url\": \"https://full.url/goes/here/about\"},\n", "        {\"type\": \"careers page\": \"url\": \"https://another.full.url/careers\"}\n", "    ]\n", "}\n", "\"\"\"\n", "oneshot_system_prompt += \"Make sure not to miss any relevant pages.\""]}, {"cell_type": "code", "execution_count": null, "id": "f5a8b688-b153-41a6-8b18-f6198f3df2c9", "metadata": {}, "outputs": [], "source": ["fewshot_system_prompt = \"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the company or freelancer offering their services, \\\n", "such as links to an About page, or a Company page, or Careers/Jobs pages.\\n You should respond in JSON as in the following examples:\"\n", "fewshot_system_prompt += \"\"\"\n", "    Example 1\n", "    ['https://great-comps.com/about-me', 'https://www.linkedin.com/in/great-comp/', 'mailto:<EMAIL>', 'https://great-comps.com/news', '/case-studies', 'https://patents.google.com/patent/US20210049536A1/', 'https://great-comps.com/workshop-ai']\n", "\n", "    Links:\n", "    {\n", "        \"links\": [\n", "            {\"type\": \"about page\", \"url\": \"https://great-comps.de/about-me\"},\n", "            {\"type\": \"news page\": \"url\": \"https://great-comps.de/news\"},\n", "            {\"type\": \"case studies page\": \"url\": \"https://great-comps.de/case-studies\"},\n", "            {\"type\": \"workshop page\": \"url\": \"https://great-comps.de/workshop-ai\"},\n", "        ]\n", "    }\n", "\n", "    Example 2\n", "    ['mailto:<EMAIL>','https://wahlen-robbie.at/ueber-mich', 'https://www.linkedin.com/in/robbie-doodle/', 'https://news.ycombinator.com', 'https://wahlen-robbie.at/neuigkeiten', 'https://twitter.com/robbie-d', '/whitepapers', 'https://patents.google.com/patent/US20210049536A1/', 'https://wahlen-robbie.at/services']\n", "\n", "    Links:\n", "    {\n", "        \"links\": [\n", "            {\"type\": \"über mich\", \"url\": \"https://wahlen-robbie.at/ueber-mich\"},\n", "            {\"type\": \"aktuelles\": \"url\": \"https://wahlen-robbie.at/neuigkeiten\"},\n", "            {\"type\": \"whitepaper\": \"url\": \"https://wahlen-robbie.at/whitepapers\"},\n", "            {\"type\": \"services\": \"url\": \"https://wahlen-robbie.at/services\"}\n", "        ]\n", "    }\n", "    \"\"\"\n", "fewshot_system_prompt += \"Make sure not to miss any relevant pages.\""]}, {"cell_type": "code", "execution_count": null, "id": "b97e4068-97ed-4120-beae-c42105e4d59a", "metadata": {}, "outputs": [], "source": ["print(f\"Oneshot system prompt:\\n{oneshot_system_prompt}\")\n", "print(f\"\\n\\n\\nFewshot system prompt:\\n{fewshot_system_prompt}\")"]}, {"cell_type": "code", "execution_count": null, "id": "8e1f601b-2eaf-499d-b6b8-c99050c9d6b3", "metadata": {}, "outputs": [], "source": ["def get_links_user_prompt(website):\n", "    user_prompt = f\"Here is the list of links on the website of {website.url} - \"\n", "    user_prompt += \"please decide which of these are relevant web links for a brochure about the company or person offering their services, respond with the full https URL in JSON format. \\\n", "Do not include Terms of Service, Privacy, email links or social media links.\\n\"\n", "    user_prompt += \"Links (some might be relative links):\\n\"\n", "    user_prompt += \"\\n\".join(website.links)\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "6bcbfa78-6395-4685-b92c-22d592050fd7", "metadata": {}, "outputs": [], "source": ["print(get_links_user_prompt(ed))"]}, {"cell_type": "code", "execution_count": null, "id": "a29aca19-ca13-471c-a4b4-5abbfa813f69", "metadata": {}, "outputs": [], "source": ["def get_links(url, system_prompt=oneshot_system_prompt):\n", "    \n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_links_user_prompt(website)}\n", "      ],\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", " \n", "    result = response.choices[0].message.content \n", "    print(f\"Response: {result}\")\n", "    return json.loads(result)"]}, {"cell_type": "code", "execution_count": null, "id": "2dc4150a-0042-4f5d-a7bf-158a0f9147a6", "metadata": {}, "outputs": [], "source": ["get_links(ed_url)"]}, {"cell_type": "code", "execution_count": null, "id": "74a827a0-2782-4ae5-b210-4a242a8b4cc2", "metadata": {}, "outputs": [], "source": ["# Anthropic has made their site harder to scrape, so I'm using HuggingFace..\n", "hf = \"https://huggingface.co\"\n", "\n", "huggingface = Website(hf)\n", "huggingface.links"]}, {"cell_type": "code", "execution_count": null, "id": "d3d583e2-dcc4-40cc-9b28-1e8dbf402924", "metadata": {}, "outputs": [], "source": ["ed_url = \"https://edwarddonner.com\"\n", "hf_url = \"https://huggingface.co\"\n", "\n", "print(f\"Links generated with oneshot prompt for {ed_url}:\\n\")\n", "get_links(ed_url)\n", "\n", "print(f\"\\n\\nLinks generated with fewshot prompt for {ed_url}:\\n\")\n", "get_links(ed_url, fewshot_system_prompt)\n", "\n", "print(50*\"*\")\n", "print(f\"\\nLinks generated with oneshot prompt for {hf_url}:\\n\")\n", "get_links(hf_url)\n", "\n", "print(f\"\\n\\nLinks generated with fewshot prompt for {hf_url}:\\n\")\n", "get_links(hf_url, fewshot_system_prompt)"]}, {"cell_type": "markdown", "id": "0d74128e-dfb6-47ec-9549-288b621c838c", "metadata": {}, "source": ["## Second step: make the brochure!\n", "\n", "Assemble all the details into another prompt to GPT4-o"]}, {"cell_type": "code", "execution_count": null, "id": "85a5b6e2-e7ef-44a9-bc7f-59ede71037b5", "metadata": {}, "outputs": [], "source": ["def get_all_details(url, type=fewshot_system_prompt):\n", "    result = \"Landing page:\\n\"\n", "    result += Website(url).get_contents()\n", "\n", "    links = get_links(url, type)\n", "    print(\"Found links:\", links)\n", "    for link in links[\"links\"]:\n", "        result += f\"\\n\\n{link['type']}\\n\"\n", "        result += Website(link[\"url\"]).get_contents()\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "5099bd14-076d-4745-baf3-dac08d8e5ab2", "metadata": {}, "outputs": [], "source": ["print(get_all_details(ed_url))"]}, {"cell_type": "code", "execution_count": null, "id": "9b863a55-f86c-4e3f-8a79-94e24c1a8cf2", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "and creates a short brochure about the company for prospective customers, investors and recruits. \\\n", "The brochure should be a bit unusual in terms of tone and style, it should astound the reader and pique their interest. Respond in markdown.\\\n", "Include details of company culture, customers and careers/jobs if you have the information.\"\n", "\n", "# Or uncomment the lines below for a more humorous brochure - this demonstrates how easy it is to incorporate 'tone':\n", "\n", "# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short humorous, entertaining, jokey brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "6ab83d92-d36b-4ce0-8bcc-5bb4c2f8ff23", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure of the company in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:5_000] # Truncate if more than 5,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "05d07160-7910-4da2-92ac-36aa849fcc68", "metadata": {}, "outputs": [], "source": ["# get_brochure_user_prompt(\"<PERSON>\", ed_url)"]}, {"cell_type": "code", "execution_count": null, "id": "cd909e0b-1312-4ce2-a553-821e795d7572", "metadata": {}, "outputs": [], "source": ["# get_brochure_user_prompt(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": null, "id": "e44de579-4a1a-4e6a-a510-20ea3e4b8d46", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "6b0de762-f343-44d9-85d5-9bffba3c0ae8", "metadata": {}, "outputs": [], "source": ["brochure_ed = create_brochure(\"<PERSON>\", ed_url)"]}, {"cell_type": "code", "execution_count": null, "id": "e093444a-9407-42ae-924a-145730591a39", "metadata": {}, "outputs": [], "source": ["brochure_hf = create_brochure(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": null, "id": "0d00b012-3901-492c-b985-a0340750c011", "metadata": {}, "outputs": [], "source": ["display(Markdown(brochure_ed))"]}, {"cell_type": "code", "execution_count": null, "id": "e33cb2e9-3b8c-4ef3-a6cb-70b3188b9120", "metadata": {}, "outputs": [], "source": ["display(Markdown(brochure_hf))"]}, {"cell_type": "code", "execution_count": null, "id": "dea955ad-24a6-490b-8191-f066bff1b595", "metadata": {}, "outputs": [], "source": ["def translate_brochure(brochure_content, language=\"German\"):\n", "    system_prompt = f\"You are a skilled translator. Translate the following brochure text into {language}.\\\n", "    Make sure to translate into a idiomatic {language}, matching the target language's natural structure, wording and expressions, so it can't be recognised as a translation.\\\n", "    Be sure to also meet an appropriate tone, eg a good marketing language in other languages will probably be a bit less boastful than in English.\\\n", "    Output the translated brochure in Markdown format.\"\n", "    \n", "    response = openai.chat.completions.create(\n", "        model = MODEL,\n", "        messages = [{\"role\": \"system\", \"content\": system_prompt}, {\"role\": \"user\", \"content\": brochure_content}]\n", "    )\n", "\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "9b6bdd4f-7518-4780-9da9-47f90aab974b", "metadata": {}, "outputs": [], "source": ["translation = translate_brochure(brochure_ed, language=\"German\")\n", "display(<PERSON><PERSON>(translation))"]}, {"cell_type": "code", "execution_count": null, "id": "f1dd96f2-0980-4a30-a152-1f38c0e319bb", "metadata": {}, "outputs": [], "source": ["translation = translate_brochure(brochure_hf, language=\"German\")\n", "display(<PERSON><PERSON>(translation))"]}, {"cell_type": "markdown", "id": "61eaaab7-0b47-4b29-82d4-75d474ad8d18", "metadata": {}, "source": ["## Finally - a minor improvement\n", "\n", "With a small adjustment, we can change this so that the results stream back from OpenAI,\n", "with the familiar typewriter animation"]}, {"cell_type": "code", "execution_count": null, "id": "51db0e49-f261-4137-aabe-92dd601f7725", "metadata": {}, "outputs": [], "source": ["def stream_brochure(company_name, url):\n", "    stream = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "        stream=True\n", "    )\n", "    \n", "    response = \"\"\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        response = response.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "        update_display(Markdown(response), display_id=display_handle.display_id)"]}, {"cell_type": "code", "execution_count": null, "id": "56bf0ae3-ee9d-4a72-9cd6-edcac67ceb6d", "metadata": {}, "outputs": [], "source": ["stream_brochure(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": null, "id": "fdb3f8d8-a3eb-41c8-b1aa-9f60686a653b", "metadata": {}, "outputs": [], "source": ["# Try changing the system prompt to the humorous version when you make the Brochure for Hugging Face:\n", "\n", "stream_brochure(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "markdown", "id": "a27bf9e0-665f-4645-b66b-9725e2a959b5", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business applications</h2>\n", "            <span style=\"color:#181;\">In this exercise we extended the Day 1 code to make multiple LLM calls, and generate a document.\n", "\n", "This is perhaps the first example of Agentic AI design patterns, as we combined multiple calls to LLMs. This will feature more in Week 2, and then we will return to Agentic AI in a big way in Week 8 when we build a fully autonomous Agent solution.\n", "\n", "Generating content in this way is one of the very most common Use Cases. As with summarization, this can be applied to any business vertical. Write marketing content, generate a product tutorial from a spec, create personalized email content, and so much more. Explore how you can apply content generation to your business, and try making yourself a proof-of-concept prototype. See what other students have done in the community-contributions folder -- so many valuable projects -- it's wild!</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "14b2454b-8ef8-4b5c-b928-053a15e0d553", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Before you move to Week 2 (which is tons of fun)</h2>\n", "            <span style=\"color:#900;\">Please see the week1 EXERCISE notebook for your challenge for the end of week 1. This will give you some essential practice working with Frontier APIs, and prepare you well for Week 2.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "17b64f0f-7d33-4493-985a-033d06e8db08", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">A reminder on 3 useful resources</h2>\n", "            <span style=\"color:#f71;\">1. The resources for the course are available <a href=\"https://edwarddonner.com/2024/11/13/llm-engineering-resources/\">here.</a><br/>\n", "            2. I'm on LinkedIn <a href=\"https://www.linkedin.com/in/eddonner/\">here</a> and I love connecting with people taking the course!<br/>\n", "            3. I'm trying out X/<PERSON> and I'm at <a href=\"https://x.com/edwarddonner\">@edwarddonner<a> and hoping people will teach me how it's done..  \n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "6f48e42e-fa7a-495f-a5d4-26bfc24d60b6", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../thankyou.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#090;\">Finally! I have a special request for you</h2>\n", "            <span style=\"color:#090;\">\n", "                My editor tells me that it makes a MASSIVE difference when students rate this course on Udemy - it's one of the main ways that Udemy decides whether to show it to others. If you're able to take a minute to rate this, I'd be so very grateful! And regardless - always please reach out to <NAME_EMAIL> if I can help at any point.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "b8d3e1a1-ba54-4907-97c5-30f89a24775b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}