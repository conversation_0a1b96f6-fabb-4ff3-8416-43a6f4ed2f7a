{"cells": [{"cell_type": "code", "execution_count": 3, "id": "425f8a60-d923-4c15-8c24-7f0a5cc7579c", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": 5, "id": "c00a960b-c9b0-4b9a-a19b-333ba4cc7d95", "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": 7, "id": "e5106d3b-20c0-44a3-9148-4caa0ef45341", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ‹ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ™ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ¹ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ¸ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ¼ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ´ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ¦ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â § \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ‡ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â � \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ‹ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ™ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest â ¹ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest \u001b[K\n", "pulling dde5aa3fc5ff... 100% â–•â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–� 2.0 GB                         \u001b[K\n", "pulling 966de95ca8a6... 100% â–•â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–� 1.4 KB                         \u001b[K\n", "pulling fcc5a6bec9da... 100% â–•â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–� 7.7 KB                         \u001b[K\n", "pulling a70ff7e570d9... 100% â–•â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–� 6.0 KB                         \u001b[K\n", "pulling 56bb8bd477a5... 100% â–•â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–�   96 B                         \u001b[K\n", "pulling 34bb5ab01051... 100% â–•â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–�  561 B                         \u001b[K\n", "verifying sha256 digest \u001b[K\n", "writing manifest \u001b[K\n", "success \u001b[K\u001b[?25h\u001b[?2026l\n"]}], "source": ["# Let's just make sure the model is loaded\n", "\n", "!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": 97, "id": "664ce0aa-fc53-4ead-88ae-7899ba2fa9d0", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        try:\n", "            self.url = url\n", "            response = requests.get(url, headers=headers)\n", "            self.body = response.content\n", "            soup = BeautifulSoup(self.body, 'html.parser')\n", "            self.title = soup.title.string if soup.title else \"No title found\"\n", "            if soup.body:\n", "                for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                    irrelevant.decompose()\n", "                self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "            else:\n", "                self.text = \"\"\n", "            links = [link.get('href') for link in soup.find_all('a')]\n", "            self.links = [link for link in links if link]\n", "        except:\n", "            print(\"website error\")\n", "    def get_contents(self):\n", "        try:\n", "            return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\"\n", "        except:\n", "            return \"\""]}, {"cell_type": "code", "execution_count": 11, "id": "37a829c6-e2c2-4af3-ae78-7f5d0c9929e9", "metadata": {}, "outputs": [], "source": ["link_system_prompt = \"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the company, \\\n", "such as links to an About page, or a Company page, or Careers/Jobs pages.\\n\"\n", "link_system_prompt += \"You should respond in JSON as in this example:\"\n", "link_system_prompt += \"\"\"\n", "{\n", "    \"links\": [\n", "        {\"type\": \"about page\", \"url\": \"https://full.url/goes/here/about\"},\n", "        {\"type\": \"careers page\": \"url\": \"https://another.full.url/careers\"}\n", "    ]\n", "}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 13, "id": "c5bfc644-3d3e-4a8f-8962-09efadc89272", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are provided with a list of links found on a webpage. You are able to decide which of the links would be most relevant to include in a brochure about the company, such as links to an About page, or a Company page, or Careers/Jobs pages.\n", "You should respond in JSON as in this example:\n", "{\n", "    \"links\": [\n", "        {\"type\": \"about page\", \"url\": \"https://full.url/goes/here/about\"},\n", "        {\"type\": \"careers page\": \"url\": \"https://another.full.url/careers\"}\n", "    ]\n", "}\n", "\n"]}], "source": ["print(link_system_prompt)"]}, {"cell_type": "code", "execution_count": 15, "id": "40c62f22-2b8e-452f-892b-d4ec53b0d9cf", "metadata": {}, "outputs": [], "source": ["def get_links_user_prompt(website):\n", "    user_prompt = f\"Here is the list of links on the website of {website.url} - \"\n", "    user_prompt += \"please decide which of these are relevant web links for a brochure about the company, respond with the full https URL in JSON format. \\\n", "Do not include Terms of Service, Privacy, email links.\\n\"\n", "    user_prompt += \"Links (some might be relative links):\\n\"\n", "    user_prompt += \"\\n\".join(website.links)\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": 49, "id": "233c1d40-0e3a-4b1d-b532-53f11c45e071", "metadata": {}, "outputs": [{"data": {"text/plain": ["['/',\n", " '/models',\n", " '/datasets',\n", " '/spaces',\n", " '/posts',\n", " '/docs',\n", " '/enterprise',\n", " '/pricing',\n", " '/login',\n", " '/join',\n", " '/spaces',\n", " '/models',\n", " '/deepseek-ai/DeepSeek-V3-0324',\n", " '/Qwen/Qwen2.5-Omni-7B',\n", " '/manycore-research/SpatialLM-Llama-1B',\n", " '/ByteDance/InfiniteYou',\n", " '/ds4sd/SmolDocling-256M-preview',\n", " '/models',\n", " '/spaces/ByteDance/InfiniteYou-FLUX',\n", " '/spaces/Trudy/gemini-codrawing',\n", " '/spaces/3DAIGC/LHM',\n", " '/spaces/stabilityai/stable-virtual-camera',\n", " '/spaces/tencent/Hunyuan-T1',\n", " '/spaces',\n", " '/datasets/nvidia/Llama-Nemotron-Post-Training-Dataset-v1',\n", " '/datasets/glaiveai/reasoning-v1-20m',\n", " '/datasets/FreedomIntelligence/medical-o1-reasoning-SFT',\n", " '/datasets/facebook/collaborative_agent_bench',\n", " '/datasets/a-m-team/AM-DeepSeek-R1-Distilled-1.4M',\n", " '/datasets',\n", " '/join',\n", " '/pricing#endpoints',\n", " '/pricing#spaces',\n", " '/pricing',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/allenai',\n", " '/facebook',\n", " '/amazon',\n", " '/google',\n", " '/Intel',\n", " '/microsoft',\n", " '/grammarly',\n", " '/Writer',\n", " '/docs/transformers',\n", " '/docs/diffusers',\n", " '/docs/safetensors',\n", " '/docs/huggingface_hub',\n", " '/docs/tokenizers',\n", " '/docs/trl',\n", " '/docs/transformers.js',\n", " '/docs/smolagents',\n", " '/docs/peft',\n", " '/docs/datasets',\n", " '/docs/text-generation-inference',\n", " '/docs/accelerate',\n", " '/models',\n", " '/datasets',\n", " '/spaces',\n", " '/tasks',\n", " 'https://ui.endpoints.huggingface.co',\n", " '/chat',\n", " '/huggingface',\n", " '/brand',\n", " '/terms-of-service',\n", " '/privacy',\n", " 'https://apply.workable.com/huggingface/',\n", " 'mailto:<EMAIL>',\n", " '/learn',\n", " '/docs',\n", " '/blog',\n", " 'https://discuss.huggingface.co',\n", " 'https://status.huggingface.co/',\n", " 'https://github.com/huggingface',\n", " 'https://twitter.com/huggingface',\n", " 'https://www.linkedin.com/company/huggingface/',\n", " '/join/discord',\n", " 'https://www.zhihu.com/org/huggingface',\n", " 'https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/blog/chinese-language-blog/wechat.jpg']"]}, "execution_count": 49, "metadata": {}, "output_type": "execute_result"}], "source": ["ed = Website(\"https://huggingface.co\")\n", "ed.links"]}, {"cell_type": "code", "execution_count": 29, "id": "4f0a8cd3-1053-45fe-9f59-783c7f91d160", "metadata": {}, "outputs": [], "source": ["from openai import OpenAI\n", "openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')"]}, {"cell_type": "code", "execution_count": 51, "id": "fbe0ccac-d46f-4da2-8dbc-dde484ddacfc", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here is the list of links on the website of https://huggingface.co - please decide which of these are relevant web links for a brochure about the company, respond with the full https URL in JSON format. Do not include Terms of Service, Privacy, email links.\n", "Links (some might be relative links):\n", "/\n", "/models\n", "/datasets\n", "/spaces\n", "/posts\n", "/docs\n", "/enterprise\n", "/pricing\n", "/login\n", "/join\n", "/spaces\n", "/models\n", "/deepseek-ai/DeepSeek-V3-0324\n", "/Qwen/Qwen2.5-Omni-7B\n", "/manycore-research/SpatialLM-Llama-1B\n", "/ByteDance/InfiniteYou\n", "/ds4sd/SmolDocling-256M-preview\n", "/models\n", "/spaces/ByteDance/InfiniteYou-FLUX\n", "/spaces/Trudy/gemini-codrawing\n", "/spaces/3DAIGC/LHM\n", "/spaces/stabilityai/stable-virtual-camera\n", "/spaces/tencent/Hunyuan-T1\n", "/spaces\n", "/datasets/nvidia/Llama-Nemotron-Post-Training-Dataset-v1\n", "/datasets/glaiveai/reasoning-v1-20m\n", "/datasets/FreedomIntelligence/medical-o1-reasoning-SFT\n", "/datasets/facebook/collaborative_agent_bench\n", "/datasets/a-m-team/AM-DeepSeek-R1-Distilled-1.4M\n", "/datasets\n", "/join\n", "/pricing#endpoints\n", "/pricing#spaces\n", "/pricing\n", "/enterprise\n", "/enterprise\n", "/enterprise\n", "/enterprise\n", "/enterprise\n", "/enterprise\n", "/enterprise\n", "/allenai\n", "/facebook\n", "/amazon\n", "/google\n", "/Intel\n", "/microsoft\n", "/grammarly\n", "/Writer\n", "/docs/transformers\n", "/docs/diffusers\n", "/docs/safetensors\n", "/docs/huggingface_hub\n", "/docs/tokenizers\n", "/docs/trl\n", "/docs/transformers.js\n", "/docs/smolagents\n", "/docs/peft\n", "/docs/datasets\n", "/docs/text-generation-inference\n", "/docs/accelerate\n", "/models\n", "/datasets\n", "/spaces\n", "/tasks\n", "https://ui.endpoints.huggingface.co\n", "/chat\n", "/huggingface\n", "/brand\n", "/terms-of-service\n", "/privacy\n", "https://apply.workable.com/huggingface/\n", "mailto:<EMAIL>\n", "/learn\n", "/docs\n", "/blog\n", "https://discuss.huggingface.co\n", "https://status.huggingface.co/\n", "https://github.com/huggingface\n", "https://twitter.com/huggingface\n", "https://www.linkedin.com/company/huggingface/\n", "/join/discord\n", "https://www.zhihu.com/org/huggingface\n", "https://huggingface.co/datasets/huggingface/documentation-images/resolve/main/blog/chinese-language-blog/wechat.jpg\n"]}], "source": ["print(get_links_user_prompt(ed))"]}, {"cell_type": "code", "execution_count": 33, "id": "a3cc38a3-dd28-46bf-977d-df7edb6909d6", "metadata": {}, "outputs": [], "source": ["def get_links(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": link_system_prompt},\n", "            {\"role\": \"user\", \"content\": get_links_user_prompt(website)}\n", "      ],\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", "    result = response.choices[0].message.content\n", "    return json.loads(result)"]}, {"cell_type": "code", "execution_count": 35, "id": "73499a57-025d-490c-b62e-c007885d00a9", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["['/',\n", " '/models',\n", " '/datasets',\n", " '/spaces',\n", " '/posts',\n", " '/docs',\n", " '/enterprise',\n", " '/pricing',\n", " '/login',\n", " '/join',\n", " '/spaces',\n", " '/models',\n", " '/deepseek-ai/DeepSeek-V3-0324',\n", " '/Qwen/Qwen2.5-Omni-7B',\n", " '/manycore-research/SpatialLM-Llama-1B',\n", " '/ByteDance/InfiniteYou',\n", " '/ds4sd/SmolDocling-256M-preview',\n", " '/models',\n", " '/spaces/ByteDance/InfiniteYou-FLUX',\n", " '/spaces/Trudy/gemini-codrawing',\n", " '/spaces/3DAIGC/LHM',\n", " '/spaces/stabilityai/stable-virtual-camera',\n", " '/spaces/tencent/Hunyuan-T1',\n", " '/spaces',\n", " '/datasets/nvidia/Llama-Nemotron-Post-Training-Dataset-v1',\n", " '/datasets/glaiveai/reasoning-v1-20m',\n", " '/datasets/FreedomIntelligence/medical-o1-reasoning-SFT',\n", " '/datasets/facebook/collaborative_agent_bench',\n", " '/datasets/a-m-team/AM-DeepSeek-R1-Distilled-1.4M',\n", " '/datasets',\n", " '/join',\n", " '/pricing#endpoints',\n", " '/pricing#spaces',\n", " '/pricing',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/enterprise',\n", " '/allenai',\n", " '/facebook',\n", " '/amazon',\n", " '/google',\n", " '/Intel',\n", " '/microsoft',\n", " '/grammarly',\n", " '/Writer',\n", " '/docs/transformers',\n", " '/docs/diffusers',\n", " '/docs/safetensors',\n", " '/docs/huggingface_hub',\n", " '/docs/tokenizers',\n", " '/docs/trl',\n", " '/docs/transformers.js',\n", " '/docs/smolagents',\n", " '/docs/peft',\n", " '/docs/datasets',\n", " '/docs/text-generation-inference',\n", " '/docs/accelerate',\n", " '/models',\n", " '/datasets',\n", " '/spaces',\n", " '/tasks',\n", " 'https://ui.endpoints.huggingface.co',\n", " '/chat',\n", " '/huggingface',\n", " '/brand',\n", " '/terms-of-service',\n", " '/privacy',\n", " 'https://apply.workable.com/huggingface/',\n", " 'mailto:<EMAIL>',\n", " '/learn',\n", " '/docs',\n", " '/blog',\n", " 'https://discuss.huggingface.co',\n", " 'https://status.huggingface.co/',\n", " 'https://github.com/huggingface',\n", " 'https://twitter.com/huggingface',\n", " 'https://www.linkedin.com/company/huggingface/',\n", " '/join/discord']"]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["# Anthropic has made their site harder to scrape, so I'm using HuggingFace..\n", "\n", "huggingface = Website(\"https://huggingface.co\")\n", "huggingface.links"]}, {"cell_type": "code", "execution_count": 79, "id": "d8b6e56d-cbaf-47f8-a055-c08293564af3", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'links': [{'type': 'About page', 'url': 'https://graphql.huggingface.co/'},\n", "  {'type': 'Company page', 'url': 'https://apply.workable.com/huggingface/'},\n", "  {'type': 'Careers/Jobs page',\n", "   'url': \"https://apply.workable.com/huggingface/'\"},\n", "  {'type': 'Blog', 'url': 'https://blog.huggingface.co/'}]}"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["get_links(\"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": 81, "id": "fd58cc22-8f4a-444a-a53f-0287581b1153", "metadata": {}, "outputs": [], "source": ["def get_all_details(url):\n", "    result = \"Landing page:\\n\"\n", "    result += Website(url).get_contents()\n", "    links = get_links(url)\n", "    print(\"Found links:\", links)\n", "    for link in links[\"links\"]:\n", "        result += f\"\\n\\n{link['type']}\\n\"\n", "        result += Website(link[\"url\"]).get_contents()\n", "    return result"]}, {"cell_type": "code", "execution_count": 89, "id": "a6ca5912-d370-48d4-9125-69a62bff453b", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found links: {'links': [{'type': 'About page', 'url': 'https://huggingface.co'}, {'type': 'Company page', 'url': 'https://huggingface.co/brand'}, {'type': 'Allenai team member (likely CEO)', 'url': 'https://github.com/huggingface'}, {'type': 'Microsoft partnership', 'url': 'https://www.linkedin.com/company/huggingface/'}, {'type': 'Intel partnership', 'url': 'https://www.linkedin.com/company/huggingface/'}, {'type': 'Amazon partnership', 'url': 'https://www.linkedin.com/company/huggingface/'}, {'type': 'Grammarly integration', 'url': 'https://grammarly.github.io/'}, {'type': 'Writer product page', 'url': 'https://writer.huggingface.co/'}, {'type': 'GitHub repository', 'url': 'https://github.com/huggingface'}, {'type': 'Discord server for community discussion', 'url': 'https://join.discord.huggingface.co/ '}]}\n", "website error\n", "website error\n", "Landing page:\n", "Webpage Title:\n", "Hugging Face – The AI community building the future.\n", "Webpage Contents:\n", "Hugging Face\n", "Models\n", "Datasets\n", "Spaces\n", "Posts\n", "Docs\n", "Enterprise\n", "Pricing\n", "Log In\n", "Sign Up\n", "The AI community building the future.\n", "The platform where the machine learning community collaborates on models, datasets, and applications.\n", "Explore AI Apps\n", "or\n", "Browse 1M+ models\n", "Trending on\n", "this week\n", "Models\n", "deepseek-ai/DeepSeek-V3-0324\n", "Updated\n", "1 day ago\n", "•\n", "32.5k\n", "•\n", "1.88k\n", "Qwen/Qwen2.5-Omni-7B\n", "Updated\n", "1 day ago\n", "•\n", "3.03k\n", "•\n", "662\n", "manycore-research/SpatialLM-Llama-1B\n", "Updated\n", "7 days ago\n", "•\n", "6.14k\n", "•\n", "754\n", "ByteDance/InfiniteYou\n", "Updated\n", "3 days ago\n", "•\n", "436\n", "ds4sd/SmolDocling-256M-preview\n", "Updated\n", "5 days ago\n", "•\n", "40.8k\n", "•\n", "994\n", "Browse 1M+ models\n", "Spaces\n", "Running\n", "on\n", "Zero\n", "473\n", "473\n", "InfiniteYou-FLUX\n", "📸\n", "Flexible Photo Recrafting While Preserving Your Identity\n", "Running\n", "323\n", "323\n", "Gemini Co-Drawing\n", "✏\n", "Gemini 2.0 native image generation co-doodling\n", "Running\n", "on\n", "Zero\n", "204\n", "204\n", "LHM\n", "⚡\n", "Large Animatable Human Model\n", "Running\n", "on\n", "L40S\n", "325\n", "325\n", "Stable Virtual Camera\n", "⚡\n", "Generate virtual camera views from input images\n", "Running\n", "163\n", "163\n", "Hunyuan T1\n", "💬\n", "Hunyuan T1模型体验\n", "Browse 400k+ applications\n", "Datasets\n", "nvidia/Llama-Nemotron-Post-Training-Dataset-v1\n", "Updated\n", "10 days ago\n", "•\n", "6.96k\n", "•\n", "256\n", "glaiveai/reasoning-v1-20m\n", "Updated\n", "9 days ago\n", "•\n", "5.71k\n", "•\n", "116\n", "FreedomIntelligence/medical-o1-reasoning-SFT\n", "Updated\n", "Feb 22\n", "•\n", "27k\n", "•\n", "566\n", "facebook/collaborative_agent_bench\n", "Updated\n", "8 days ago\n", "•\n", "83\n", "•\n", "47\n", "a-m-team/AM-DeepSeek-R1-Distilled-1.4M\n", "Updated\n", "about 18 hours ago\n", "•\n", "2.26k\n", "•\n", "68\n", "Browse 250k+ datasets\n", "The Home of Machine Learning\n", "Create, discover and collaborate on ML better.\n", "The collaboration platform\n", "Host and collaborate on unlimited public models, datasets and applications.\n", "Move faster\n", "With the HF Open source stack.\n", "Explore all modalities\n", "Text, image, video, audio or even 3D.\n", "Build your portfolio\n", "Share your work with the world and build your ML profile.\n", "Sign Up\n", "Accelerate your ML\n", "We provide paid Compute and Enterprise solutions.\n", "Compute\n", "Deploy on optimized\n", "Inference Endpoints\n", "or update your\n", "Spaces applications\n", "to a GPU in a few clicks.\n", "View pricing\n", "Starting at $0.60/hour for GPU\n", "Enterprise\n", "Give your team the most advanced platform to build AI with enterprise-grade security, access controls and\n", "\t\t\tdedicated support.\n", "Getting started\n", "Starting at $20/user/month\n", "Single Sign-On\n", "Regions\n", "Priority Support\n", "<PERSON><PERSON>\n", "Resource Groups\n", "Private Datasets Viewer\n", "More than 50,000 organizations are using Hugging Face\n", "Ai2\n", "Enterprise\n", "non-profit\n", "•\n", "396 models\n", "•\n", "2.97k followers\n", "AI at Meta\n", "Enterprise\n", "company\n", "•\n", "2.07k models\n", "•\n", "5.27k followers\n", "Amazon\n", "company\n", "•\n", "10 models\n", "•\n", "2.91k followers\n", "Google\n", "company\n", "•\n", "974 models\n", "•\n", "10.6k followers\n", "Intel\n", "company\n", "•\n", "219 models\n", "•\n", "2.37k followers\n", "Microsoft\n", "company\n", "•\n", "365 models\n", "•\n", "10.7k followers\n", "Grammarly\n", "Enterprise\n", "company\n", "•\n", "10 models\n", "•\n", "145 followers\n", "Writer\n", "Enterprise\n", "company\n", "•\n", "21 models\n", "•\n", "253 followers\n", "Our Open Source\n", "We are building the foundation of ML tooling with the community.\n", "Transformers\n", "142,056\n", "State-of-the-art ML for PyTorch, TensorFlow, JAX\n", "Diffusers\n", "28,292\n", "State-of-the-art Diffusion models in PyTorch\n", "Safetensors\n", "3,189\n", "Safe way to store/distribute neural network weights\n", "Hub Python Library\n", "2,469\n", "Python client to interact with the Hugging Face Hub\n", "Tokenizers\n", "9,538\n", "Fast tokenizers optimized for research & production\n", "TRL\n", "12,887\n", "Train transformers LMs with reinforcement learning\n", "Transformers.js\n", "13,301\n", "State-of-the-art ML running directly in your browser\n", "smolagents\n", "15,893\n", "Smol library to build great agents in Python\n", "PEFT\n", "17,927\n", "Parameter-efficient finetuning for large language models\n", "Datasets\n", "19,888\n", "Access & share datasets for any ML tasks\n", "Text Generation Inference\n", "9,937\n", "Serve language models with TGI optimized toolkit\n", "Accelerate\n", "8,542\n", "Train PyTorch models with multi-GPU, TPU, mixed precision\n", "System theme\n", "Website\n", "Models\n", "Datasets\n", "Spaces\n", "Tasks\n", "Inference Endpoints\n", "HuggingChat\n", "Company\n", "About\n", "Brand assets\n", "Terms of service\n", "Privacy\n", "Jobs\n", "Press\n", "Resources\n", "Learn\n", "Documentation\n", "Blog\n", "Forum\n", "Service Status\n", "Social\n", "GitHub\n", "Twitter\n", "LinkedIn\n", "Discord\n", "\n", "\n", "\n", "About page\n", "Webpage Title:\n", "Hugging Face – The AI community building the future.\n", "Webpage Contents:\n", "Hugging Face\n", "Models\n", "Datasets\n", "Spaces\n", "Posts\n", "Docs\n", "Enterprise\n", "Pricing\n", "Log In\n", "Sign Up\n", "The AI community building the future.\n", "The platform where the machine learning community collaborates on models, datasets, and applications.\n", "Explore AI Apps\n", "or\n", "Browse 1M+ models\n", "Trending on\n", "this week\n", "Models\n", "deepseek-ai/DeepSeek-V3-0324\n", "Updated\n", "1 day ago\n", "•\n", "32.5k\n", "•\n", "1.88k\n", "Qwen/Qwen2.5-Omni-7B\n", "Updated\n", "1 day ago\n", "•\n", "3.03k\n", "•\n", "662\n", "manycore-research/SpatialLM-Llama-1B\n", "Updated\n", "7 days ago\n", "•\n", "6.14k\n", "•\n", "754\n", "ByteDance/InfiniteYou\n", "Updated\n", "3 days ago\n", "•\n", "436\n", "ds4sd/SmolDocling-256M-preview\n", "Updated\n", "5 days ago\n", "•\n", "40.8k\n", "•\n", "994\n", "Browse 1M+ models\n", "Spaces\n", "Running\n", "on\n", "Zero\n", "473\n", "473\n", "InfiniteYou-FLUX\n", "📸\n", "Flexible Photo Recrafting While Preserving Your Identity\n", "Running\n", "323\n", "323\n", "Gemini Co-Drawing\n", "✏\n", "Gemini 2.0 native image generation co-doodling\n", "Running\n", "on\n", "Zero\n", "204\n", "204\n", "LHM\n", "⚡\n", "Large Animatable Human Model\n", "Running\n", "on\n", "L40S\n", "325\n", "325\n", "Stable Virtual Camera\n", "⚡\n", "Generate virtual camera views from input images\n", "Running\n", "163\n", "163\n", "Hunyuan T1\n", "💬\n", "Hunyuan T1模型体验\n", "Browse 400k+ applications\n", "Datasets\n", "nvidia/Llama-Nemotron-Post-Training-Dataset-v1\n", "Updated\n", "10 days ago\n", "•\n", "6.96k\n", "•\n", "256\n", "glaiveai/reasoning-v1-20m\n", "Updated\n", "9 days ago\n", "•\n", "5.71k\n", "•\n", "116\n", "FreedomIntelligence/medical-o1-reasoning-SFT\n", "Updated\n", "Feb 22\n", "•\n", "27k\n", "•\n", "566\n", "facebook/collaborative_agent_bench\n", "Updated\n", "8 days ago\n", "•\n", "83\n", "•\n", "47\n", "a-m-team/AM-DeepSeek-R1-Distilled-1.4M\n", "Updated\n", "about 18 hours ago\n", "•\n", "2.26k\n", "•\n", "68\n", "Browse 250k+ datasets\n", "The Home of Machine Learning\n", "Create, discover and collaborate on ML better.\n", "The collaboration platform\n", "Host and collaborate on unlimited public models, datasets and applications.\n", "Move faster\n", "With the HF Open source stack.\n", "Explore all modalities\n", "Text, image, video, audio or even 3D.\n", "Build your portfolio\n", "Share your work with the world and build your ML profile.\n", "Sign Up\n", "Accelerate your ML\n", "We provide paid Compute and Enterprise solutions.\n", "Compute\n", "Deploy on optimized\n", "Inference Endpoints\n", "or update your\n", "Spaces applications\n", "to a GPU in a few clicks.\n", "View pricing\n", "Starting at $0.60/hour for GPU\n", "Enterprise\n", "Give your team the most advanced platform to build AI with enterprise-grade security, access controls and\n", "\t\t\tdedicated support.\n", "Getting started\n", "Starting at $20/user/month\n", "Single Sign-On\n", "Regions\n", "Priority Support\n", "<PERSON><PERSON>\n", "Resource Groups\n", "Private Datasets Viewer\n", "More than 50,000 organizations are using Hugging Face\n", "Ai2\n", "Enterprise\n", "non-profit\n", "•\n", "396 models\n", "•\n", "2.97k followers\n", "AI at Meta\n", "Enterprise\n", "company\n", "•\n", "2.07k models\n", "•\n", "5.27k followers\n", "Amazon\n", "company\n", "•\n", "10 models\n", "•\n", "2.91k followers\n", "Google\n", "company\n", "•\n", "974 models\n", "•\n", "10.6k followers\n", "Intel\n", "company\n", "•\n", "219 models\n", "•\n", "2.37k followers\n", "Microsoft\n", "company\n", "•\n", "365 models\n", "•\n", "10.7k followers\n", "Grammarly\n", "Enterprise\n", "company\n", "•\n", "10 models\n", "•\n", "145 followers\n", "Writer\n", "Enterprise\n", "company\n", "•\n", "21 models\n", "•\n", "253 followers\n", "Our Open Source\n", "We are building the foundation of ML tooling with the community.\n", "Transformers\n", "142,056\n", "State-of-the-art ML for PyTorch, TensorFlow, JAX\n", "Diffusers\n", "28,292\n", "State-of-the-art Diffusion models in PyTorch\n", "Safetensors\n", "3,189\n", "Safe way to store/distribute neural network weights\n", "Hub Python Library\n", "2,469\n", "Python client to interact with the Hugging Face Hub\n", "Tokenizers\n", "9,538\n", "Fast tokenizers optimized for research & production\n", "TRL\n", "12,887\n", "Train transformers LMs with reinforcement learning\n", "Transformers.js\n", "13,301\n", "State-of-the-art ML running directly in your browser\n", "smolagents\n", "15,893\n", "Smol library to build great agents in Python\n", "PEFT\n", "17,927\n", "Parameter-efficient finetuning for large language models\n", "Datasets\n", "19,888\n", "Access & share datasets for any ML tasks\n", "Text Generation Inference\n", "9,937\n", "Serve language models with TGI optimized toolkit\n", "Accelerate\n", "8,542\n", "Train PyTorch models with multi-GPU, TPU, mixed precision\n", "System theme\n", "Website\n", "Models\n", "Datasets\n", "Spaces\n", "Tasks\n", "Inference Endpoints\n", "HuggingChat\n", "Company\n", "About\n", "Brand assets\n", "Terms of service\n", "Privacy\n", "Jobs\n", "Press\n", "Resources\n", "Learn\n", "Documentation\n", "Blog\n", "Forum\n", "Service Status\n", "Social\n", "GitHub\n", "Twitter\n", "LinkedIn\n", "Discord\n", "\n", "\n", "\n", "Company page\n", "Webpage Title:\n", "Brand assets - Hugging Face\n", "Webpage Contents:\n", "Hugging Face\n", "Models\n", "Datasets\n", "Spaces\n", "Posts\n", "Docs\n", "Enterprise\n", "Pricing\n", "Log In\n", "Sign Up\n", "Hugging Face · Brand assets\n", "HF Logos\n", ".svg\n", ".png\n", ".ai\n", ".svg\n", ".png\n", ".ai\n", ".svg\n", ".png\n", ".ai\n", "HF Colors\n", "#FFD21E\n", "#FF9D00\n", "#6B7280\n", "HF Bio\n", "Hugging Face is the collaboration platform for the machine learning community.\n", "\n", "The Hugging Face Hub works as a central place where anyone can share, explore, discover, and experiment with open-source ML. HF empowers the next generation of machine learning engineers, scientists, and end users to learn, collaborate and share their work to build an open and ethical AI future together.\n", "\n", "With the fast-growing community, some of the most used open-source ML libraries and tools, and a talented science team exploring the edge of tech, Hugging Face is at the heart of the AI revolution.\n", "Copy to clipboard\n", "HF Universe\n", "Find other assets available for use from the Hugging Face brand universe\n", "here\n", ".\n", "System theme\n", "Website\n", "Models\n", "Datasets\n", "Spaces\n", "Tasks\n", "Inference Endpoints\n", "HuggingChat\n", "Company\n", "About\n", "Brand assets\n", "Terms of service\n", "Privacy\n", "Jobs\n", "Press\n", "Resources\n", "Learn\n", "Documentation\n", "Blog\n", "Forum\n", "Service Status\n", "Social\n", "GitHub\n", "Twitter\n", "LinkedIn\n", "Discord\n", "\n", "\n", "\n", "Allenai team member (likely CEO)\n", "Webpage Title:\n", "Hugging Face · GitHub\n", "Webpage Contents:\n", "Skip to content\n", "Navigation Menu\n", "Toggle navigation\n", "Sign in\n", "huggingface\n", "Product\n", "GitHub Copilot\n", "Write better code with AI\n", "Security\n", "Find and fix vulnerabilities\n", "Actions\n", "Automate any workflow\n", "Codespaces\n", "Instant dev environments\n", "Issues\n", "Plan and track work\n", "Code Review\n", "Manage code changes\n", "Discussions\n", "Collaborate outside of code\n", "Code Search\n", "Find more, search less\n", "Explore\n", "All features\n", "Documentation\n", "GitHub Skills\n", "Blog\n", "Solutions\n", "By company size\n", "Enterprises\n", "Small and medium teams\n", "Startups\n", "Nonprofits\n", "By use case\n", "DevSecOps\n", "DevOps\n", "CI/CD\n", "View all use cases\n", "By industry\n", "Healthcare\n", "Financial services\n", "Manufacturing\n", "Government\n", "View all industries\n", "View all solutions\n", "Resources\n", "Topics\n", "AI\n", "DevOps\n", "Security\n", "Software Development\n", "View all\n", "Explore\n", "Learning Pathways\n", "Events & Webinars\n", "Ebooks & Whitepapers\n", "Customer Stories\n", "Partners\n", "Executive Insights\n", "Open Source\n", "GitHub Sponsors\n", "Fund open source developers\n", "The ReadME Project\n", "GitHub community articles\n", "Repositories\n", "Topics\n", "Trending\n", "Collections\n", "Enterprise\n", "Enterprise platform\n", "AI-powered developer platform\n", "Available add-ons\n", "Advanced Security\n", "Enterprise-grade security features\n", "Copilot for business\n", "Enterprise-grade AI features\n", "Premium Support\n", "Enterprise-grade 24/7 support\n", "Pricing\n", "Search or jump to...\n", "Search code, repositories, users, issues, pull requests...\n", "Search\n", "Clear\n", "Search syntax tips\n", "Provide feedback\n", "We read every piece of feedback, and take your input very seriously.\n", "Include my email address so I can be contacted\n", "Cancel\n", "Submit feedback\n", "Saved searches\n", "Use saved searches to filter your results more quickly\n", "Cancel\n", "Create saved search\n", "Sign in\n", "Sign up\n", "Reseting focus\n", "You signed in with another tab or window.\n", "Reload\n", "to refresh your session.\n", "You signed out in another tab or window.\n", "Reload\n", "to refresh your session.\n", "You switched accounts on another tab or window.\n", "Reload\n", "to refresh your session.\n", "Dismiss alert\n", "Hugging Face\n", "The AI community building the future.\n", "Verified\n", "We've verified that the organization\n", "huggingface\n", "controls the domain:\n", "huggingface.co\n", "Learn more about verified organizations\n", "46.8k\n", "followers\n", "NYC + Paris\n", "https://huggingface.co/\n", "X\n", "@huggingface\n", "Overview\n", "Repositories\n", "Projects\n", "Packages\n", "People\n", "Sponsoring\n", "0\n", "More\n", "Overview\n", "Repositories\n", "Projects\n", "Packages\n", "People\n", "Sponsoring\n", "Pinned\n", "Loading\n", "transformers\n", "transformers\n", "Public\n", "🤗 Transformers: State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX.\n", "Python\n", "142k\n", "28.4k\n", "diffusers\n", "diffusers\n", "Public\n", "🤗 Diffusers: State-of-the-art diffusion models for image, video, and audio generation in PyTorch and FLAX.\n", "Python\n", "28.3k\n", "5.8k\n", "datasets\n", "datasets\n", "Public\n", "🤗 The largest hub of ready-to-use datasets for ML models with fast, easy-to-use and efficient data manipulation tools\n", "Python\n", "19.9k\n", "2.8k\n", "peft\n", "peft\n", "Public\n", "🤗 PEFT: State-of-the-art Parameter-Efficient Fine-Tuning.\n", "Python\n", "17.9k\n", "1.8k\n", "accelerate\n", "accelerate\n", "Public\n", "🚀 A simple way to launch, train, and use PyTorch models on almost any device and distributed configuration, automatic mixed precision (including fp8), and easy-to-configure FSDP and DeepSpeed support\n", "Python\n", "8.5k\n", "1.1k\n", "optimum\n", "optimum\n", "Public\n", "🚀 Accelerate inference and training of 🤗 Transformers, Diffusers, TIMM and Sentence Transformers with easy to use hardware optimization tools\n", "Python\n", "2.8k\n", "516\n", "Repositories\n", "Loading\n", "Type\n", "Select type\n", "Forks\n", "Archived\n", "Mirrors\n", "Templates\n", "Language\n", "Select language\n", "All\n", "C\n", "C#\n", "C++\n", "<PERSON><PERSON>\n", "Dockerfile\n", "Go\n", "Handlebars\n", "HTML\n", "Java\n", "JavaScript\n", "Jupyter Notebook\n", "<PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "MDX\n", "Mustache\n", "<PERSON>\n", "Python\n", "Rust\n", "Shell\n", "Smarty\n", "Swift\n", "TypeScript\n", "Sort\n", "Select order\n", "Last updated\n", "Name\n", "Stars\n", "Showing 10 of 301 repositories\n", "kernel-builder\n", "Public\n", "👷 Build compute kernels\n", "huggingface/kernel-builder’s past year of commit activity\n", "<PERSON>\n", "22\n", "6\n", "7\n", "4\n", "Updated\n", "Mar 28, 2025\n", "<PERSON><PERSON><PERSON>\n", "Public\n", "🤗 LeRobot: Making AI for Robotics more accessible with end-to-end learning\n", "huggingface/lerobot’s past year of commit activity\n", "Python\n", "11,165\n", "Apache-2.0\n", "1,225\n", "153\n", "118\n", "Updated\n", "Mar 28, 2025\n", "smolagents\n", "Public\n", "🤗 smolagents: a barebones library for agents that think in python code.\n", "huggingface/smolagents’s past year of commit activity\n", "Python\n", "15,893\n", "Apache-2.0\n", "1,404\n", "105\n", "99\n", "Updated\n", "Mar 28, 2025\n", "notebooks\n", "Public\n", "Notebooks using the Hugging Face libraries 🤗\n", "huggingface/notebooks’s past year of commit activity\n", "Jupyter Notebook\n", "3,973\n", "Apache-2.0\n", "1,630\n", "131\n", "69\n", "Updated\n", "Mar 28, 2025\n", "meshgen\n", "Public\n", "A blender addon for generating meshes with AI\n", "huggingface/meshgen’s past year of commit activity\n", "Python\n", "520\n", "MIT\n", "29\n", "9\n", "0\n", "Updated\n", "Mar 28, 2025\n", "huggingface_hub\n", "Public\n", "The official Python client for the Huggingface Hub.\n", "huggingface/huggingface_hub’s past year of commit activity\n", "Python\n", "2,469\n", "Apache-2.0\n", "658\n", "146\n", "(4 issues need help)\n", "19\n", "Updated\n", "Mar 28, 2025\n", "lighteval\n", "Public\n", "Lighteval is your all-in-one toolkit for evaluating LLMs across multiple backends\n", "huggingface/lighteval’s past year of commit activity\n", "Python\n", "1,349\n", "MIT\n", "211\n", "90\n", "(1 issue needs help)\n", "33\n", "Updated\n", "Mar 28, 2025\n", "trl\n", "Public\n", "Train transformer language models with reinforcement learning.\n", "huggingface/trl’s past year of commit activity\n", "Python\n", "12,887\n", "Apache-2.0\n", "1,734\n", "329\n", "73\n", "Updated\n", "Mar 27, 2025\n", "optimum-neuron\n", "Public\n", "Easy, fast and very cheap training and inference on AWS Trainium and Inferentia chips.\n", "huggingface/optimum-neuron’s past year of commit activity\n", "Jupyter Notebook\n", "222\n", "Apache-2.0\n", "71\n", "19\n", "8\n", "Updated\n", "Mar 27, 2025\n", "hub-docs\n", "Public\n", "Docs of the Hugging Face Hub\n", "huggingface/hub-docs’s past year of commit activity\n", "Handlebars\n", "363\n", "Apache-2.0\n", "286\n", "100\n", "35\n", "Updated\n", "Mar 27, 2025\n", "View all repositories\n", "People\n", "View all\n", "Top languages\n", "Loading…\n", "Most used topics\n", "Loading…\n", "Footer\n", "© 2025 GitHub, Inc.\n", "Footer navigation\n", "Terms\n", "Privacy\n", "Security\n", "Status\n", "Docs\n", "Contact\n", "Manage cookies\n", "Do not share my personal information\n", "You can’t perform that action at this time.\n", "\n", "\n", "\n", "Microsoft partnership\n", "Webpage Title:\n", "Hugging Face | LinkedIn\n", "Webpage Contents:\n", "Skip to main content\n", "LinkedIn\n", "Articles\n", "People\n", "Learning\n", "Jobs\n", "Games\n", "Get the app\n", "Join now\n", "Sign in\n", "Hugging Face\n", "Software Development\n", "The AI community building the future.\n", "See jobs\n", "Follow\n", "View all 513 employees\n", "Report this company\n", "About us\n", "The AI community building the future.\n", "Website\n", "https://huggingface.co\n", "External link for Hugging Face\n", "Industry\n", "Software Development\n", "Company size\n", "51-200 employees\n", "Type\n", "Privately Held\n", "Founded\n", "2016\n", "Specialties\n", "machine learning, natural language processing, and deep learning\n", "Products\n", "Hugging Face\n", "Hugging Face\n", "Natural Language Processing (NLP) Software\n", "We‚Äôre on a journey to solve and democratize artificial intelligence through natural language.\n", "Locations\n", "Primary\n", "Get directions\n", "Paris, FR\n", "Get directions\n", "Employees at Hugging Face\n", "<PERSON><PERSON><PERSON>\n", "Bassem ASSEH\n", "<PERSON><PERSON>\n", "Tech Lead & Software Engineer @ HF | prev: co-founder XetHub, Apple, Turi, AWS, Microsoft\n", "<PERSON>\n", "Product + Growth at Hugging Face\n", "See all employees\n", "Updates\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "Software Engineer @ ü§ó\n", "14h\n", "Report this post\n", "Generate lifelike audio in real-time without a GPU! üöÄ\n", "\n", "Check out orpheus-cpp: a\n", "llama.cpp\n", "port of orpheus 3b text-to-speech model with built-in support for sync and async streaming.\n", "\n", "ùöôùöíùöô ùöíùöóùöúùöùùöäùöïùöï ùöòùöõùöôùöëùöéùöûùöú-ùöåùöôùöô\n", "ùöôùö¢ùöùùöëùöòùöó -<PERSON><PERSON><PERSON> ùöòùöõùöôùöëùöéùöûùöú_ùöåùöôùöô\n", "\n", "Project code:\n", "https://lnkd.in/ekPpN9mc\n", "‚<PERSON>¶more\n", "227\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Report this post\n", "We just turned the humble dataframe into a superweapon‚ö°Ô∏è\n", "dashboarding will never be the same!! üìä\n", "\n", "new Gradio Dataframe has:\n", "- multi-cell selection\n", "- column pinning\n", "- search + filtering\n", "- fullscreen mode\n", "- accessibility upgrades, and more\n", "‚<PERSON>¶more\n", "88\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON>\n", "open-sourceress at ü§ó | Google Developer Expert in Machine Learning, MSc Candidate in Data Science\n", "1d\n", "Report this post\n", "is your vision LM in prod even safe? üëÄ\n", "\n", "ShieldGemma 2 is the first ever safety model for multimodal vision LMs in production by\n", "Google DeepMind\n", ", came with <PERSON> 3 üî•\n", "\n", "I saw confusion around how to use it, so I put together a notebook and a demo, find it in the comments üí¨\n", "364\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON>\n", "open-sourceress at ü§ó | Google Developer Expert in Machine Learning, MSc Candidate in Data Science\n", "1d\n", "Report this post\n", "is your vision LM in prod even safe? üëÄ\n", "\n", "ShieldGemma 2 is the first ever safety model for multimodal vision LMs in production by\n", "Google DeepMind\n", ", came with <PERSON> 3 üî•\n", "\n", "I saw confusion around how to use it, so I put together a notebook and a demo, find it in the comments üí¨\n", "364\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "ML Engineer @ Hugging Face ü§ó | AI PhD | Google Summer of Code '18-'24\n", "2d\n", "Report this post\n", "The Bonus Unit 2, \"AI Agent Observability & Evaluation,\" is now live on our\n", "Hugging Face\n", "agents course! üéì\n", "\n", "You'll learn to:\n", "üîß Instrument agents with OpenTelemetry\n", "üìä Track token usage, latency & errors\n", "<PERSON><PERSON><PERSON>ate with LLM-as-a-judge\n", "üìö Benchmark with GSM8K\n", "\n", "üëâ Check out the course here:\n", "https://lnkd.in/d2jiTx6j\n", "199\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Edited\n", "Report this post\n", "Create Infinite Photographs of You with InfiniteYou-Flux!\n", "\n", "Flexible photo recreation that better preserves identity compared to current solutions like Pulid, IP Adapter, etc. üî• üí™ \n", "\n", "Current full-performance bf16 model inference requires a peak VRAM of around 43 GB.\n", "\n", "You can build InfU on your own hardware:\n", "https://lnkd.in/g9dc_vVh\n", "Or Play for free on\n", "Hugging Face\n", ":\n", "https://lnkd.in/gzF7rikZ\n", "147\n", "5 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Edited\n", "Report this post\n", "ü§Ø Generate high-quality podcasts with the voices you want!\n", "\n", "MoonCast is an open sourced,  multi-lingual, and zeroshot model.\n", "\n", "You just need to upload two sample voices, create a script, and that's it, run the model--You get a üî• notebooklm-like podcast.\n", "\n", "Model and App are released on\n", "Hugging Face\n", ":\n", "https://lnkd.in/gUk2EssP\n", "119\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "Building data tools @ Hugging Face ü§ó\n", "2d\n", "Edited\n", "Report this post\n", "üî• Big news for GPU poors: thanks to\n", "Hyperbolic\n", "and\n", "Fireworks AI\n", ", you can run¬†\n", "DeepSeek AI\n", "'s¬†new model using Hugging Face Inference Providers. What has changed since V3? Here's my quick home experiment üëá \n", "\n", "DeepSeek silently dropped an update to V3 yesterday. Benchmark results are available, showing significant improvements over V3. \n", "\n", "Still, it is always a good idea to run new models on data you care about and see more detailed, fine-grained results.\n", "\n", "Now that we can all run these new models from Day 0 with no GPUs required, I wanted to share my approach with an example I created this morning:\n", "\n", "1. I got a sample from the LIMA dataset (containing high-quality general instructions).\n", "2. Run the instructions with V3 and the new version V3-0324.\n", "3. Define and run a simple judge with Llama3.3-70B to compare the model responses.\n", "4. Push the dataset and pipeline so you can check and run similar experiments! (see first comment)\n", "5. Extracted the results with\n", "Hugging Face\n", "Data Studio.\n", "\n", "Results summary\n", "- LIMA is not very challenging, but it is still interesting to see the differences between the two models.\n", "- A majority of Ties indicate that both models are close for this domain and task.\n", "- But still, V3-0324 consistently wins over V3 (33 times vs 6 times).\n", "\n", "As usual, the dataset, prompts, and pipeline are open-source (see first comment).\n", "\n", "What other experiments you'd like to see?\n", "191\n", "4 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Report this post\n", "StarVector is a multimodal vision-language model for generating SVG (Scalable Vector Graphics). üëá \n", "\n", "It can be used to perform image2SVG and text2SVG generation. Live demo shows how the image generation is treated similar to a code generation task, using the power of StarVector multimodal VLM! ü§© \n", "\n", "üöÄ Play with the app on Huggingface:\n", "https://lnkd.in/gCzdEbvj\n", "ü•≥ If you want to build the model locally with a gradio app:\n", "https://lnkd.in/gDzCpdDN\n", "‚<PERSON>¶more\n", "1,365\n", "39 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON><PERSON>\n", "Principal Ethicist at Hugging Face | PhD in Philosophy at Sorbonne Universit√©\n", "3d\n", "Report this post\n", "Excited to share our latest piece on the double-edged sword of AI agents published in\n", "MIT Technology Review\n", "! ü§ñ It builds on our research paper that's been making waves lately -- pretty cool to see all the attention it's getting!\n", "\n", "As these systems move beyond chat windows to navigate applications and execute complex tasks independently, we need to ask ourselves: how much control are we willing to surrender, and at what cost?\n", "\n", "In our recent op-ed,\n", "<PERSON>\n", ",\n", "<PERSON><PERSON><PERSON>, PhD\n", ",\n", "Dr. <PERSON>\n", ", and I explore why the very feature being sold (reduced human oversight) is actually the primary vulnerability. When AI systems can control multiple information sources simultaneously, the potential for harm explodes exponentially. \n", "\n", "We imagine that \"It wasn't me‚<PERSON><PERSON><PERSON> was my agent!!\" will soon be a common refrain to excuse bad outcomes.\n", "\n", "The benefits of AI agents are undeniable, from assisting people with mobility challenges to coordinating emergency responses. But these benefits don't require surrendering complete human control.\n", "\n", "At\n", "Hugging Face\n", ", we're developing frameworks like smolagents that prioritize transparency and appropriate human oversight. Because human judgment, with all its imperfections, remains the fundamental component in ensuring these systems serve rather than subvert our interests.\n", "123\n", "15 Comments\n", "Like\n", "Comment\n", "Share\n", "Join now to see what you are missing\n", "Find people you know at Hugging Face\n", "<PERSON><PERSON><PERSON> recommended jobs for you\n", "View all updates, news, and articles\n", "Join now\n", "Similar pages\n", "Anthropic\n", "Research Services\n", "Mistral AI\n", "Technology, Information and Internet\n", "Paris, France\n", "Perplexity\n", "Software Development\n", "San Francisco, California\n", "OpenAI\n", "Research Services\n", "San Francisco, CA\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Technology, Information and Internet\n", "Generative AI\n", "Technology, Information and Internet\n", "DeepLearning.AI\n", "Software Development\n", "Palo Alto, California\n", "Google DeepMind\n", "Research Services\n", "London, London\n", "LlamaIndex\n", "Technology, Information and Internet\n", "San Francisco, California\n", "Cohere\n", "Software Development\n", "Toronto, Ontario\n", "Show more similar pages\n", "Show fewer similar pages\n", "Browse jobs\n", "Engineer jobs\n", "555,845 open jobs\n", "Machine Learning Engineer jobs\n", "148,937 open jobs\n", "Scientist jobs\n", "48,969 open jobs\n", "Software Engineer jobs\n", "300,699 open jobs\n", "Analyst jobs\n", "694,057 open jobs\n", "Intern jobs\n", "71,196 open jobs\n", "Developer jobs\n", "258,935 open jobs\n", "Manager jobs\n", "1,880,925 open jobs\n", "Product Manager jobs\n", "199,941 open jobs\n", "Director jobs\n", "1,220,357 open jobs\n", "Python Developer jobs\n", "46,642 open jobs\n", "Data Scientist jobs\n", "264,158 open jobs\n", "Data Analyst jobs\n", "329,009 open jobs\n", "Senior Software Engineer jobs\n", "78,145 open jobs\n", "Project Manager jobs\n", "253,048 open jobs\n", "Researcher jobs\n", "195,654 open jobs\n", "Associate jobs\n", "1,091,945 open jobs\n", "Data Engineer jobs\n", "192,126 open jobs\n", "Vice President jobs\n", "235,270 open jobs\n", "Specialist jobs\n", "768,666 open jobs\n", "Show more jobs like this\n", "Show fewer jobs like this\n", "Funding\n", "Hugging Face\n", "8 total rounds\n", "Last Round\n", "Series unknown\n", "Sep 1, 2024\n", "External Crunchbase Link for last round of funding\n", "See more info on\n", "crunchbase\n", "More searches\n", "More searches\n", "Engineer jobs\n", "Scientist jobs\n", "Machine Learning Engineer jobs\n", "Software Engineer jobs\n", "Intern jobs\n", "Developer jobs\n", "Analyst jobs\n", "Manager jobs\n", "Senior Software Engineer jobs\n", "Data Scientist jobs\n", "Researcher jobs\n", "Product Manager jobs\n", "Director jobs\n", "Associate jobs\n", "Intelligence Specialist jobs\n", "Data Analyst jobs\n", "Data Science Specialist jobs\n", "Python Developer jobs\n", "Quantitative Analyst jobs\n", "Project Manager jobs\n", "Account Executive jobs\n", "Specialist jobs\n", "Data Engineer jobs\n", "Designer jobs\n", "Quantitative Researcher jobs\n", "Consultant jobs\n", "Solutions Architect jobs\n", "Vice President jobs\n", "User Experience Designer jobs\n", "Head jobs\n", "Full Stack Engineer jobs\n", "Engineering Manager jobs\n", "Software Engineer Inter<PERSON> jobs\n", "Junior Software Engineer jobs\n", "Software Intern jobs\n", "Product Designer jobs\n", "Solutions Engineer jobs\n", "Staff Software Engineer jobs\n", "Program Manager jobs\n", "Senior Scientist jobs\n", "Writer jobs\n", "Research Intern jobs\n", "Senior Product Manager jobs\n", "Summer Intern jobs\n", "Account Manager jobs\n", "Recruiter jobs\n", "Lead jobs\n", "Research Engineer jobs\n", "Computer Science Intern jobs\n", "Platform Engineer jobs\n", "Junior Developer jobs\n", "Android Developer jobs\n", "User Experience Researcher jobs\n", "Java Software Engineer jobs\n", "Site Reliability Engineer jobs\n", "Graduate jobs\n", "Software Engineering Manager jobs\n", "Representative jobs\n", "Business Development Specialist jobs\n", "Computer Engineer jobs\n", "LinkedIn\n", "© 2025\n", "About\n", "Accessibility\n", "User Agreement\n", "Privacy Policy\n", "<PERSON><PERSON>\n", "Copyright Policy\n", "Brand Policy\n", "Guest Controls\n", "Community Guidelines\n", "ÿßŸÑÿπÿ±ÿ®Ÿäÿ© (Arabic)\n", "‡¶¨‡¶æ‡¶Ç‡¶≤‡¶æ (Bangla)\n", "<PERSON><PERSON><PERSON> (Czech)\n", "<PERSON><PERSON> (Danish)\n", "<PERSON><PERSON><PERSON> (German)\n", "ŒïŒªŒªŒ∑ŒΩŒπŒ∫Œ¨ (Greek)\n", "English (English)\n", "Espa√±ol (Spanish)\n", "ŸÅÿßÿ±ÿ≥€å (Persian)\n", "<PERSON><PERSON> (Finnish)\n", "<PERSON><PERSON><PERSON><PERSON> (French)\n", "‡§π‡§ø‡§Ç‡§¶‡•Ä (Hindi)\n", "<PERSON><PERSON><PERSON> (Hungarian)\n", "Bahasa Indonesia (Indonesian)\n", "<PERSON><PERSON> (Italian)\n", "◊¢◊ë◊®◊ô◊™ (Hebrew)\n", "Êó•Êú¨Ë™û (Japanese)\n", "ÌïúÍµ≠Ïñ¥ (Korean)\n", "‡§Æ‡§∞‡§æ‡§†‡•Ä (Marathi)\n", "Bahasa <PERSON> (Malay)\n", "<PERSON><PERSON><PERSON> (Dutch)\n", "<PERSON><PERSON> (Norwegian)\n", "‡®™‡©∞‡®ú‡®æ‡®¨‡©Ä (Punjabi)\n", "<PERSON><PERSON> (Polish)\n", "Portugu√™s (Portuguese)\n", "Rom√¢nƒÉ (Romanian)\n", "–†—<PERSON>—Å—Å–∫–∏–π (Russian)\n", "<PERSON><PERSON> (Swedish)\n", "‡∞§‡±Ü‡∞≤‡±Å‡∞ó‡±Å (Telugu)\n", "‡∏†‡∏≤‡∏©‡∏≤‡πÑ‡∏ó‡∏¢ (Thai)\n", "Tagalog (Tagalog)\n", "T√ºrk√ße (Turkish)\n", "–£–∫—Ä–∞—ó–Ω—Å—å–∫–∞ (Ukrainian)\n", "Ti·∫øng Vi·<PERSON><PERSON><PERSON> (Vietnamese)\n", "ÁÆÄ‰Ωì‰∏≠Êñá (Chinese (Simplified))\n", "Ê≠£È´î‰∏≠Êñá (Chinese (Traditional))\n", "Language\n", "Agree & Join <PERSON>\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "Sign in to see who you already know at Hugging Face\n", "Sign in\n", "Welcome back\n", "Email or phone\n", "Password\n", "Show\n", "Forgot password?\n", "Sign in\n", "or\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "New to LinkedIn?\n", "Join now\n", "or\n", "New to LinkedIn?\n", "Join now\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "LinkedIn\n", "LinkedIn is better on the app\n", "<PERSON><PERSON>Äôt have the app? Get it in the Microsoft Store.\n", "Open the app\n", "\n", "\n", "\n", "Intel partnership\n", "Webpage Title:\n", "Hugging Face | LinkedIn\n", "Webpage Contents:\n", "Skip to main content\n", "LinkedIn\n", "Articles\n", "People\n", "Learning\n", "Jobs\n", "Games\n", "Get the app\n", "Join now\n", "Sign in\n", "Hugging Face\n", "Software Development\n", "The AI community building the future.\n", "See jobs\n", "Follow\n", "Discover all 513 employees\n", "Report this company\n", "About us\n", "The AI community building the future.\n", "Website\n", "https://huggingface.co\n", "External link for Hugging Face\n", "Industry\n", "Software Development\n", "Company size\n", "51-200 employees\n", "Type\n", "Privately Held\n", "Founded\n", "2016\n", "Specialties\n", "machine learning, natural language processing, and deep learning\n", "Products\n", "Hugging Face\n", "Hugging Face\n", "Natural Language Processing (NLP) Software\n", "We‚Äôre on a journey to solve and democratize artificial intelligence through natural language.\n", "Locations\n", "Primary\n", "Get directions\n", "Paris, FR\n", "Get directions\n", "Employees at Hugging Face\n", "<PERSON><PERSON><PERSON>\n", "Bassem ASSEH\n", "<PERSON><PERSON>\n", "Tech Lead & Software Engineer @ HF | prev: co-founder XetHub, Apple, Turi, AWS, Microsoft\n", "<PERSON>\n", "Product + Growth at Hugging Face\n", "See all employees\n", "Updates\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "Software Engineer @ ü§ó\n", "14h\n", "Report this post\n", "Generate lifelike audio in real-time without a GPU! üöÄ\n", "\n", "Check out orpheus-cpp: a\n", "llama.cpp\n", "port of orpheus 3b text-to-speech model with built-in support for sync and async streaming.\n", "\n", "ùöôùöíùöô ùöíùöóùöúùöùùöäùöïùöï ùöòùöõùöôùöëùöéùöûùöú-ùöåùöôùöô\n", "ùöôùö¢ùöùùöëùöòùöó -<PERSON><PERSON><PERSON> ùöòùöõùöôùöëùöéùöûùöú_ùöåùöôùöô\n", "\n", "Project code:\n", "https://lnkd.in/ekPpN9mc\n", "‚<PERSON>¶more\n", "227\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Report this post\n", "We just turned the humble dataframe into a superweapon‚ö°Ô∏è\n", "dashboarding will never be the same!! üìä\n", "\n", "new Gradio Dataframe has:\n", "- multi-cell selection\n", "- column pinning\n", "- search + filtering\n", "- fullscreen mode\n", "- accessibility upgrades, and more\n", "‚<PERSON>¶more\n", "88\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON>\n", "open-sourceress at ü§ó | Google Developer Expert in Machine Learning, MSc Candidate in Data Science\n", "1d\n", "Report this post\n", "is your vision LM in prod even safe? üëÄ\n", "\n", "ShieldGemma 2 is the first ever safety model for multimodal vision LMs in production by\n", "Google DeepMind\n", ", came with <PERSON> 3 üî•\n", "\n", "I saw confusion around how to use it, so I put together a notebook and a demo, find it in the comments üí¨\n", "364\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON>\n", "open-sourceress at ü§ó | Google Developer Expert in Machine Learning, MSc Candidate in Data Science\n", "1d\n", "Report this post\n", "is your vision LM in prod even safe? üëÄ\n", "\n", "ShieldGemma 2 is the first ever safety model for multimodal vision LMs in production by\n", "Google DeepMind\n", ", came with <PERSON> 3 üî•\n", "\n", "I saw confusion around how to use it, so I put together a notebook and a demo, find it in the comments üí¨\n", "364\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "ML Engineer @ Hugging Face ü§ó | AI PhD | Google Summer of Code '18-'24\n", "2d\n", "Report this post\n", "The Bonus Unit 2, \"AI Agent Observability & Evaluation,\" is now live on our\n", "Hugging Face\n", "agents course! üéì\n", "\n", "You'll learn to:\n", "üîß Instrument agents with OpenTelemetry\n", "üìä Track token usage, latency & errors\n", "<PERSON><PERSON><PERSON>ate with LLM-as-a-judge\n", "üìö Benchmark with GSM8K\n", "\n", "üëâ Check out the course here:\n", "https://lnkd.in/d2jiTx6j\n", "199\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Edited\n", "Report this post\n", "Create Infinite Photographs of You with InfiniteYou-Flux!\n", "\n", "Flexible photo recreation that better preserves identity compared to current solutions like Pulid, IP Adapter, etc. üî• üí™ \n", "\n", "Current full-performance bf16 model inference requires a peak VRAM of around 43 GB.\n", "\n", "You can build InfU on your own hardware:\n", "https://lnkd.in/g9dc_vVh\n", "Or Play for free on\n", "Hugging Face\n", ":\n", "https://lnkd.in/gzF7rikZ\n", "147\n", "5 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Edited\n", "Report this post\n", "ü§Ø Generate high-quality podcasts with the voices you want!\n", "\n", "MoonCast is an open sourced,  multi-lingual, and zeroshot model.\n", "\n", "You just need to upload two sample voices, create a script, and that's it, run the model--You get a üî• notebooklm-like podcast.\n", "\n", "Model and App are released on\n", "Hugging Face\n", ":\n", "https://lnkd.in/gUk2EssP\n", "119\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "Building data tools @ Hugging Face ü§ó\n", "2d\n", "Edited\n", "Report this post\n", "üî• Big news for GPU poors: thanks to\n", "Hyperbolic\n", "and\n", "Fireworks AI\n", ", you can run¬†\n", "DeepSeek AI\n", "'s¬†new model using Hugging Face Inference Providers. What has changed since V3? Here's my quick home experiment üëá \n", "\n", "DeepSeek silently dropped an update to V3 yesterday. Benchmark results are available, showing significant improvements over V3. \n", "\n", "Still, it is always a good idea to run new models on data you care about and see more detailed, fine-grained results.\n", "\n", "Now that we can all run these new models from Day 0 with no GPUs required, I wanted to share my approach with an example I created this morning:\n", "\n", "1. I got a sample from the LIMA dataset (containing high-quality general instructions).\n", "2. Run the instructions with V3 and the new version V3-0324.\n", "3. Define and run a simple judge with Llama3.3-70B to compare the model responses.\n", "4. Push the dataset and pipeline so you can check and run similar experiments! (see first comment)\n", "5. Extracted the results with\n", "Hugging Face\n", "Data Studio.\n", "\n", "Results summary\n", "- LIMA is not very challenging, but it is still interesting to see the differences between the two models.\n", "- A majority of Ties indicate that both models are close for this domain and task.\n", "- But still, V3-0324 consistently wins over V3 (33 times vs 6 times).\n", "\n", "As usual, the dataset, prompts, and pipeline are open-source (see first comment).\n", "\n", "What other experiments you'd like to see?\n", "191\n", "4 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Report this post\n", "StarVector is a multimodal vision-language model for generating SVG (Scalable Vector Graphics). üëá \n", "\n", "It can be used to perform image2SVG and text2SVG generation. Live demo shows how the image generation is treated similar to a code generation task, using the power of StarVector multimodal VLM! ü§© \n", "\n", "üöÄ Play with the app on Huggingface:\n", "https://lnkd.in/gCzdEbvj\n", "ü•≥ If you want to build the model locally with a gradio app:\n", "https://lnkd.in/gDzCpdDN\n", "‚<PERSON>¶more\n", "1,365\n", "39 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON><PERSON>\n", "Principal Ethicist at Hugging Face | PhD in Philosophy at Sorbonne Universit√©\n", "3d\n", "Report this post\n", "Excited to share our latest piece on the double-edged sword of AI agents published in\n", "MIT Technology Review\n", "! ü§ñ It builds on our research paper that's been making waves lately -- pretty cool to see all the attention it's getting!\n", "\n", "As these systems move beyond chat windows to navigate applications and execute complex tasks independently, we need to ask ourselves: how much control are we willing to surrender, and at what cost?\n", "\n", "In our recent op-ed,\n", "<PERSON>\n", ",\n", "<PERSON><PERSON><PERSON>, PhD\n", ",\n", "Dr. <PERSON>\n", ", and I explore why the very feature being sold (reduced human oversight) is actually the primary vulnerability. When AI systems can control multiple information sources simultaneously, the potential for harm explodes exponentially. \n", "\n", "We imagine that \"It wasn't me‚<PERSON><PERSON><PERSON> was my agent!!\" will soon be a common refrain to excuse bad outcomes.\n", "\n", "The benefits of AI agents are undeniable, from assisting people with mobility challenges to coordinating emergency responses. But these benefits don't require surrendering complete human control.\n", "\n", "At\n", "Hugging Face\n", ", we're developing frameworks like smolagents that prioritize transparency and appropriate human oversight. Because human judgment, with all its imperfections, remains the fundamental component in ensuring these systems serve rather than subvert our interests.\n", "123\n", "15 Comments\n", "Like\n", "Comment\n", "Share\n", "Join now to see what you are missing\n", "Find people you know at Hugging Face\n", "<PERSON><PERSON><PERSON> recommended jobs for you\n", "View all updates, news, and articles\n", "Join now\n", "Similar pages\n", "Anthropic\n", "Research Services\n", "Mistral AI\n", "Technology, Information and Internet\n", "Paris, France\n", "Perplexity\n", "Software Development\n", "San Francisco, California\n", "OpenAI\n", "Research Services\n", "San Francisco, CA\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Technology, Information and Internet\n", "Generative AI\n", "Technology, Information and Internet\n", "DeepLearning.AI\n", "Software Development\n", "Palo Alto, California\n", "Google DeepMind\n", "Research Services\n", "London, London\n", "LlamaIndex\n", "Technology, Information and Internet\n", "San Francisco, California\n", "Cohere\n", "Software Development\n", "Toronto, Ontario\n", "Show more similar pages\n", "Show fewer similar pages\n", "Browse jobs\n", "Engineer jobs\n", "555,845 open jobs\n", "Machine Learning Engineer jobs\n", "148,937 open jobs\n", "Scientist jobs\n", "48,969 open jobs\n", "Software Engineer jobs\n", "300,699 open jobs\n", "Analyst jobs\n", "694,057 open jobs\n", "Intern jobs\n", "71,196 open jobs\n", "Developer jobs\n", "258,935 open jobs\n", "Manager jobs\n", "1,880,925 open jobs\n", "Product Manager jobs\n", "199,941 open jobs\n", "Director jobs\n", "1,220,357 open jobs\n", "Python Developer jobs\n", "46,642 open jobs\n", "Data Scientist jobs\n", "264,158 open jobs\n", "Data Analyst jobs\n", "329,009 open jobs\n", "Senior Software Engineer jobs\n", "78,145 open jobs\n", "Project Manager jobs\n", "253,048 open jobs\n", "Researcher jobs\n", "195,654 open jobs\n", "Associate jobs\n", "1,091,945 open jobs\n", "Data Engineer jobs\n", "192,126 open jobs\n", "Vice President jobs\n", "235,270 open jobs\n", "Specialist jobs\n", "768,666 open jobs\n", "Show more jobs like this\n", "Show fewer jobs like this\n", "Funding\n", "Hugging Face\n", "8 total rounds\n", "Last Round\n", "Series unknown\n", "Sep 1, 2024\n", "External Crunchbase Link for last round of funding\n", "See more info on\n", "crunchbase\n", "More searches\n", "More searches\n", "Engineer jobs\n", "Scientist jobs\n", "Machine Learning Engineer jobs\n", "Software Engineer jobs\n", "Intern jobs\n", "Developer jobs\n", "Analyst jobs\n", "Manager jobs\n", "Senior Software Engineer jobs\n", "Data Scientist jobs\n", "Researcher jobs\n", "Product Manager jobs\n", "Director jobs\n", "Associate jobs\n", "Intelligence Specialist jobs\n", "Data Analyst jobs\n", "Data Science Specialist jobs\n", "Python Developer jobs\n", "Quantitative Analyst jobs\n", "Project Manager jobs\n", "Account Executive jobs\n", "Specialist jobs\n", "Data Engineer jobs\n", "Designer jobs\n", "Quantitative Researcher jobs\n", "Consultant jobs\n", "Solutions Architect jobs\n", "Vice President jobs\n", "User Experience Designer jobs\n", "Head jobs\n", "Full Stack Engineer jobs\n", "Engineering Manager jobs\n", "Software Engineer Inter<PERSON> jobs\n", "Junior Software Engineer jobs\n", "Software Intern jobs\n", "Product Designer jobs\n", "Solutions Engineer jobs\n", "Staff Software Engineer jobs\n", "Program Manager jobs\n", "Senior Scientist jobs\n", "Writer jobs\n", "Research Intern jobs\n", "Senior Product Manager jobs\n", "Summer Intern jobs\n", "Account Manager jobs\n", "Recruiter jobs\n", "Lead jobs\n", "Research Engineer jobs\n", "Computer Science Intern jobs\n", "Platform Engineer jobs\n", "Junior Developer jobs\n", "Android Developer jobs\n", "User Experience Researcher jobs\n", "Java Software Engineer jobs\n", "Site Reliability Engineer jobs\n", "Graduate jobs\n", "Software Engineering Manager jobs\n", "Representative jobs\n", "Business Development Specialist jobs\n", "Computer Engineer jobs\n", "LinkedIn\n", "© 2025\n", "About\n", "Accessibility\n", "User Agreement\n", "Privacy Policy\n", "<PERSON><PERSON>\n", "Copyright Policy\n", "Brand Policy\n", "Guest Controls\n", "Community Guidelines\n", "ÿßŸÑÿπÿ±ÿ®Ÿäÿ© (Arabic)\n", "‡¶¨‡¶æ‡¶Ç‡¶≤‡¶æ (Bangla)\n", "<PERSON><PERSON><PERSON> (Czech)\n", "<PERSON><PERSON> (Danish)\n", "<PERSON><PERSON><PERSON> (German)\n", "ŒïŒªŒªŒ∑ŒΩŒπŒ∫Œ¨ (Greek)\n", "English (English)\n", "Espa√±ol (Spanish)\n", "ŸÅÿßÿ±ÿ≥€å (Persian)\n", "<PERSON><PERSON> (Finnish)\n", "<PERSON><PERSON><PERSON><PERSON> (French)\n", "‡§π‡§ø‡§Ç‡§¶‡•Ä (Hindi)\n", "<PERSON><PERSON><PERSON> (Hungarian)\n", "Bahasa Indonesia (Indonesian)\n", "<PERSON><PERSON> (Italian)\n", "◊¢◊ë◊®◊ô◊™ (Hebrew)\n", "Êó•Êú¨Ë™û (Japanese)\n", "ÌïúÍµ≠Ïñ¥ (Korean)\n", "‡§Æ‡§∞‡§æ‡§†‡•Ä (Marathi)\n", "Bahasa <PERSON> (Malay)\n", "<PERSON><PERSON><PERSON> (Dutch)\n", "<PERSON><PERSON> (Norwegian)\n", "‡®™‡©∞‡®ú‡®æ‡®¨‡©Ä (Punjabi)\n", "<PERSON><PERSON> (Polish)\n", "Portugu√™s (Portuguese)\n", "Rom√¢nƒÉ (Romanian)\n", "–†—<PERSON>—Å—Å–∫–∏–π (Russian)\n", "<PERSON><PERSON> (Swedish)\n", "‡∞§‡±Ü‡∞≤‡±Å‡∞ó‡±Å (Telugu)\n", "‡∏†‡∏≤‡∏©‡∏≤‡πÑ‡∏ó‡∏¢ (Thai)\n", "Tagalog (Tagalog)\n", "T√ºrk√ße (Turkish)\n", "–£–∫—Ä–∞—ó–Ω—Å—å–∫–∞ (Ukrainian)\n", "Ti·∫øng Vi·<PERSON><PERSON><PERSON> (Vietnamese)\n", "ÁÆÄ‰Ωì‰∏≠Êñá (Chinese (Simplified))\n", "Ê≠£È´î‰∏≠Êñá (Chinese (Traditional))\n", "Language\n", "Agree & Join <PERSON>\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "Sign in to see who you already know at Hugging Face\n", "Sign in\n", "Welcome back\n", "Email or phone\n", "Password\n", "Show\n", "Forgot password?\n", "Sign in\n", "or\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "New to LinkedIn?\n", "Join now\n", "or\n", "New to LinkedIn?\n", "Join now\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "LinkedIn\n", "LinkedIn is better on the app\n", "<PERSON><PERSON>Äôt have the app? Get it in the Microsoft Store.\n", "Open the app\n", "\n", "\n", "\n", "Amazon partnership\n", "Webpage Title:\n", "Hugging Face | LinkedIn\n", "Webpage Contents:\n", "Skip to main content\n", "LinkedIn\n", "Articles\n", "People\n", "Learning\n", "Jobs\n", "Games\n", "Get the app\n", "Join now\n", "Sign in\n", "Hugging Face\n", "Software Development\n", "The AI community building the future.\n", "See jobs\n", "Follow\n", "View all 513 employees\n", "Report this company\n", "About us\n", "The AI community building the future.\n", "Website\n", "https://huggingface.co\n", "External link for Hugging Face\n", "Industry\n", "Software Development\n", "Company size\n", "51-200 employees\n", "Type\n", "Privately Held\n", "Founded\n", "2016\n", "Specialties\n", "machine learning, natural language processing, and deep learning\n", "Products\n", "Hugging Face\n", "Hugging Face\n", "Natural Language Processing (NLP) Software\n", "We‚Äôre on a journey to solve and democratize artificial intelligence through natural language.\n", "Locations\n", "Primary\n", "Get directions\n", "Paris, FR\n", "Get directions\n", "Employees at Hugging Face\n", "<PERSON><PERSON><PERSON>\n", "Bassem ASSEH\n", "<PERSON><PERSON>\n", "Tech Lead & Software Engineer @ HF | prev: co-founder XetHub, Apple, Turi, AWS, Microsoft\n", "<PERSON>\n", "Product + Growth at Hugging Face\n", "See all employees\n", "Updates\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "Software Engineer @ ü§ó\n", "14h\n", "Report this post\n", "Generate lifelike audio in real-time without a GPU! üöÄ\n", "\n", "Check out orpheus-cpp: a\n", "llama.cpp\n", "port of orpheus 3b text-to-speech model with built-in support for sync and async streaming.\n", "\n", "ùöôùöíùöô ùöíùöóùöúùöùùöäùöïùöï ùöòùöõùöôùöëùöéùöûùöú-ùöåùöôùöô\n", "ùöôùö¢ùöùùöëùöòùöó -<PERSON><PERSON><PERSON> ùöòùöõùöôùöëùöéùöûùöú_ùöåùöôùöô\n", "\n", "Project code:\n", "https://lnkd.in/ekPpN9mc\n", "‚<PERSON>¶more\n", "227\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Report this post\n", "We just turned the humble dataframe into a superweapon‚ö°Ô∏è\n", "dashboarding will never be the same!! üìä\n", "\n", "new Gradio Dataframe has:\n", "- multi-cell selection\n", "- column pinning\n", "- search + filtering\n", "- fullscreen mode\n", "- accessibility upgrades, and more\n", "‚<PERSON>¶more\n", "88\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON>\n", "open-sourceress at ü§ó | Google Developer Expert in Machine Learning, MSc Candidate in Data Science\n", "1d\n", "Report this post\n", "is your vision LM in prod even safe? üëÄ\n", "\n", "ShieldGemma 2 is the first ever safety model for multimodal vision LMs in production by\n", "Google DeepMind\n", ", came with <PERSON> 3 üî•\n", "\n", "I saw confusion around how to use it, so I put together a notebook and a demo, find it in the comments üí¨\n", "364\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON>\n", "open-sourceress at ü§ó | Google Developer Expert in Machine Learning, MSc Candidate in Data Science\n", "1d\n", "Report this post\n", "is your vision LM in prod even safe? üëÄ\n", "\n", "ShieldGemma 2 is the first ever safety model for multimodal vision LMs in production by\n", "Google DeepMind\n", ", came with <PERSON> 3 üî•\n", "\n", "I saw confusion around how to use it, so I put together a notebook and a demo, find it in the comments üí¨\n", "364\n", "10 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "ML Engineer @ Hugging Face ü§ó | AI PhD | Google Summer of Code '18-'24\n", "2d\n", "Report this post\n", "The Bonus Unit 2, \"AI Agent Observability & Evaluation,\" is now live on our\n", "Hugging Face\n", "agents course! üéì\n", "\n", "You'll learn to:\n", "üîß Instrument agents with OpenTelemetry\n", "üìä Track token usage, latency & errors\n", "<PERSON><PERSON><PERSON>ate with LLM-as-a-judge\n", "üìö Benchmark with GSM8K\n", "\n", "üëâ Check out the course here:\n", "https://lnkd.in/d2jiTx6j\n", "199\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Edited\n", "Report this post\n", "Create Infinite Photographs of You with InfiniteYou-Flux!\n", "\n", "Flexible photo recreation that better preserves identity compared to current solutions like Pulid, IP Adapter, etc. üî• üí™ \n", "\n", "Current full-performance bf16 model inference requires a peak VRAM of around 43 GB.\n", "\n", "You can build InfU on your own hardware:\n", "https://lnkd.in/g9dc_vVh\n", "Or Play for free on\n", "Hugging Face\n", ":\n", "https://lnkd.in/gzF7rikZ\n", "147\n", "5 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Edited\n", "Report this post\n", "ü§Ø Generate high-quality podcasts with the voices you want!\n", "\n", "MoonCast is an open sourced,  multi-lingual, and zeroshot model.\n", "\n", "You just need to upload two sample voices, create a script, and that's it, run the model--You get a üî• notebooklm-like podcast.\n", "\n", "Model and App are released on\n", "Hugging Face\n", ":\n", "https://lnkd.in/gUk2EssP\n", "119\n", "7 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON>\n", "Building data tools @ Hugging Face ü§ó\n", "2d\n", "Edited\n", "Report this post\n", "üî• Big news for GPU poors: thanks to\n", "Hyperbolic\n", "and\n", "Fireworks AI\n", ", you can run¬†\n", "DeepSeek AI\n", "'s¬†new model using Hugging Face Inference Providers. What has changed since V3? Here's my quick home experiment üëá \n", "\n", "DeepSeek silently dropped an update to V3 yesterday. Benchmark results are available, showing significant improvements over V3. \n", "\n", "Still, it is always a good idea to run new models on data you care about and see more detailed, fine-grained results.\n", "\n", "Now that we can all run these new models from Day 0 with no GPUs required, I wanted to share my approach with an example I created this morning:\n", "\n", "1. I got a sample from the LIMA dataset (containing high-quality general instructions).\n", "2. Run the instructions with V3 and the new version V3-0324.\n", "3. Define and run a simple judge with Llama3.3-70B to compare the model responses.\n", "4. Push the dataset and pipeline so you can check and run similar experiments! (see first comment)\n", "5. Extracted the results with\n", "Hugging Face\n", "Data Studio.\n", "\n", "Results summary\n", "- LIMA is not very challenging, but it is still interesting to see the differences between the two models.\n", "- A majority of Ties indicate that both models are close for this domain and task.\n", "- But still, V3-0324 consistently wins over V3 (33 times vs 6 times).\n", "\n", "As usual, the dataset, prompts, and pipeline are open-source (see first comment).\n", "\n", "What other experiments you'd like to see?\n", "191\n", "4 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "Gradio\n", "60,588 followers\n", "2d\n", "Report this post\n", "StarVector is a multimodal vision-language model for generating SVG (Scalable Vector Graphics). üëá \n", "\n", "It can be used to perform image2SVG and text2SVG generation. Live demo shows how the image generation is treated similar to a code generation task, using the power of StarVector multimodal VLM! ü§© \n", "\n", "üöÄ Play with the app on Huggingface:\n", "https://lnkd.in/gCzdEbvj\n", "ü•≥ If you want to build the model locally with a gradio app:\n", "https://lnkd.in/gDzCpdDN\n", "‚<PERSON>¶more\n", "1,365\n", "39 Comments\n", "Like\n", "Comment\n", "Share\n", "Hugging Face\n", "reposted this\n", "<PERSON><PERSON><PERSON>\n", "Principal Ethicist at Hugging Face | PhD in Philosophy at Sorbonne Universit√©\n", "3d\n", "Report this post\n", "Excited to share our latest piece on the double-edged sword of AI agents published in\n", "MIT Technology Review\n", "! ü§ñ It builds on our research paper that's been making waves lately -- pretty cool to see all the attention it's getting!\n", "\n", "As these systems move beyond chat windows to navigate applications and execute complex tasks independently, we need to ask ourselves: how much control are we willing to surrender, and at what cost?\n", "\n", "In our recent op-ed,\n", "<PERSON>\n", ",\n", "<PERSON><PERSON><PERSON>, PhD\n", ",\n", "Dr. <PERSON>\n", ", and I explore why the very feature being sold (reduced human oversight) is actually the primary vulnerability. When AI systems can control multiple information sources simultaneously, the potential for harm explodes exponentially. \n", "\n", "We imagine that \"It wasn't me‚<PERSON><PERSON><PERSON> was my agent!!\" will soon be a common refrain to excuse bad outcomes.\n", "\n", "The benefits of AI agents are undeniable, from assisting people with mobility challenges to coordinating emergency responses. But these benefits don't require surrendering complete human control.\n", "\n", "At\n", "Hugging Face\n", ", we're developing frameworks like smolagents that prioritize transparency and appropriate human oversight. Because human judgment, with all its imperfections, remains the fundamental component in ensuring these systems serve rather than subvert our interests.\n", "123\n", "15 Comments\n", "Like\n", "Comment\n", "Share\n", "Join now to see what you are missing\n", "Find people you know at Hugging Face\n", "<PERSON><PERSON><PERSON> recommended jobs for you\n", "View all updates, news, and articles\n", "Join now\n", "Similar pages\n", "Anthropic\n", "Research Services\n", "Mistral AI\n", "Technology, Information and Internet\n", "Paris, France\n", "Perplexity\n", "Software Development\n", "San Francisco, California\n", "OpenAI\n", "Research Services\n", "San Francisco, CA\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Technology, Information and Internet\n", "Generative AI\n", "Technology, Information and Internet\n", "DeepLearning.AI\n", "Software Development\n", "Palo Alto, California\n", "Google DeepMind\n", "Research Services\n", "London, London\n", "LlamaIndex\n", "Technology, Information and Internet\n", "San Francisco, California\n", "Cohere\n", "Software Development\n", "Toronto, Ontario\n", "Show more similar pages\n", "Show fewer similar pages\n", "Browse jobs\n", "Engineer jobs\n", "555,845 open jobs\n", "Machine Learning Engineer jobs\n", "148,937 open jobs\n", "Scientist jobs\n", "48,969 open jobs\n", "Software Engineer jobs\n", "300,699 open jobs\n", "Analyst jobs\n", "694,057 open jobs\n", "Intern jobs\n", "71,196 open jobs\n", "Developer jobs\n", "258,935 open jobs\n", "Manager jobs\n", "1,880,925 open jobs\n", "Product Manager jobs\n", "199,941 open jobs\n", "Director jobs\n", "1,220,357 open jobs\n", "Python Developer jobs\n", "46,642 open jobs\n", "Data Scientist jobs\n", "264,158 open jobs\n", "Data Analyst jobs\n", "329,009 open jobs\n", "Senior Software Engineer jobs\n", "78,145 open jobs\n", "Project Manager jobs\n", "253,048 open jobs\n", "Researcher jobs\n", "195,654 open jobs\n", "Associate jobs\n", "1,091,945 open jobs\n", "Data Engineer jobs\n", "192,126 open jobs\n", "Vice President jobs\n", "235,270 open jobs\n", "Specialist jobs\n", "768,666 open jobs\n", "Show more jobs like this\n", "Show fewer jobs like this\n", "Funding\n", "Hugging Face\n", "8 total rounds\n", "Last Round\n", "Series unknown\n", "Sep 1, 2024\n", "External Crunchbase Link for last round of funding\n", "See more info on\n", "crunchbase\n", "More searches\n", "More searches\n", "Engineer jobs\n", "Scientist jobs\n", "Machine Learning Engineer jobs\n", "Software Engineer jobs\n", "Intern jobs\n", "Developer jobs\n", "Analyst jobs\n", "Manager jobs\n", "Senior Software Engineer jobs\n", "Data Scientist jobs\n", "Researcher jobs\n", "Product Manager jobs\n", "Director jobs\n", "Associate jobs\n", "Intelligence Specialist jobs\n", "Data Analyst jobs\n", "Data Science Specialist jobs\n", "Python Developer jobs\n", "Quantitative Analyst jobs\n", "Project Manager jobs\n", "Account Executive jobs\n", "Specialist jobs\n", "Data Engineer jobs\n", "Designer jobs\n", "Quantitative Researcher jobs\n", "Consultant jobs\n", "Solutions Architect jobs\n", "Vice President jobs\n", "User Experience Designer jobs\n", "Head jobs\n", "Full Stack Engineer jobs\n", "Engineering Manager jobs\n", "Software Engineer Inter<PERSON> jobs\n", "Junior Software Engineer jobs\n", "Software Intern jobs\n", "Product Designer jobs\n", "Solutions Engineer jobs\n", "Staff Software Engineer jobs\n", "Program Manager jobs\n", "Senior Scientist jobs\n", "Writer jobs\n", "Research Intern jobs\n", "Senior Product Manager jobs\n", "Summer Intern jobs\n", "Account Manager jobs\n", "Recruiter jobs\n", "Lead jobs\n", "Research Engineer jobs\n", "Computer Science Intern jobs\n", "Platform Engineer jobs\n", "Junior Developer jobs\n", "Android Developer jobs\n", "User Experience Researcher jobs\n", "Java Software Engineer jobs\n", "Site Reliability Engineer jobs\n", "Graduate jobs\n", "Software Engineering Manager jobs\n", "Representative jobs\n", "Business Development Specialist jobs\n", "Computer Engineer jobs\n", "LinkedIn\n", "© 2025\n", "About\n", "Accessibility\n", "User Agreement\n", "Privacy Policy\n", "<PERSON><PERSON>\n", "Copyright Policy\n", "Brand Policy\n", "Guest Controls\n", "Community Guidelines\n", "ÿßŸÑÿπÿ±ÿ®Ÿäÿ© (Arabic)\n", "‡¶¨‡¶æ‡¶Ç‡¶≤‡¶æ (Bangla)\n", "<PERSON><PERSON><PERSON> (Czech)\n", "<PERSON><PERSON> (Danish)\n", "<PERSON><PERSON><PERSON> (German)\n", "ŒïŒªŒªŒ∑ŒΩŒπŒ∫Œ¨ (Greek)\n", "English (English)\n", "Espa√±ol (Spanish)\n", "ŸÅÿßÿ±ÿ≥€å (Persian)\n", "<PERSON><PERSON> (Finnish)\n", "<PERSON><PERSON><PERSON><PERSON> (French)\n", "‡§π‡§ø‡§Ç‡§¶‡•Ä (Hindi)\n", "<PERSON><PERSON><PERSON> (Hungarian)\n", "Bahasa Indonesia (Indonesian)\n", "<PERSON><PERSON> (Italian)\n", "◊¢◊ë◊®◊ô◊™ (Hebrew)\n", "Êó•Êú¨Ë™û (Japanese)\n", "ÌïúÍµ≠Ïñ¥ (Korean)\n", "‡§Æ‡§∞‡§æ‡§†‡•Ä (Marathi)\n", "Bahasa <PERSON> (Malay)\n", "<PERSON><PERSON><PERSON> (Dutch)\n", "<PERSON><PERSON> (Norwegian)\n", "‡®™‡©∞‡®ú‡®æ‡®¨‡©Ä (Punjabi)\n", "<PERSON><PERSON> (Polish)\n", "Portugu√™s (Portuguese)\n", "Rom√¢nƒÉ (Romanian)\n", "–†—<PERSON>—Å—Å–∫–∏–π (Russian)\n", "<PERSON><PERSON> (Swedish)\n", "‡∞§‡±Ü‡∞≤‡±Å‡∞ó‡±Å (Telugu)\n", "‡∏†‡∏≤‡∏©‡∏≤‡πÑ‡∏ó‡∏¢ (Thai)\n", "Tagalog (Tagalog)\n", "T√ºrk√ße (Turkish)\n", "–£–∫—Ä–∞—ó–Ω—Å—å–∫–∞ (Ukrainian)\n", "Ti·∫øng Vi·<PERSON><PERSON><PERSON> (Vietnamese)\n", "ÁÆÄ‰Ωì‰∏≠Êñá (Chinese (Simplified))\n", "Ê≠£È´î‰∏≠Êñá (Chinese (Traditional))\n", "Language\n", "Agree & Join <PERSON>\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "Sign in to see who you already know at Hugging Face\n", "Sign in\n", "Welcome back\n", "Email or phone\n", "Password\n", "Show\n", "Forgot password?\n", "Sign in\n", "or\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "New to LinkedIn?\n", "Join now\n", "or\n", "By clicking Continue to join or sign in, you agree to LinkedIn‚Äôs\n", "User Agreement\n", ",\n", "Privacy Policy\n", ", and\n", "<PERSON><PERSON>\n", ".\n", "New to LinkedIn?\n", "Join now\n", "LinkedIn\n", "LinkedIn is better on the app\n", "<PERSON><PERSON>Äôt have the app? Get it in the Microsoft Store.\n", "Open the app\n", "\n", "\n", "\n", "Grammarly integration\n", "Webpage Title:\n", "Site not found · GitHub Pages\n", "Webpage Contents:\n", "404\n", "There isn't a GitHub Pages site here.\n", "If you're trying to publish one,\n", "read the full documentation\n", "to learn how to set up\n", "GitHub Pages\n", "for your repository, organization, or user account.\n", "GitHub Status\n", "—\n", "@githubstatus\n", "\n", "\n", "\n", "Writer product page\n", "\n", "\n", "GitHub repository\n", "Webpage Title:\n", "Hugging Face · GitHub\n", "Webpage Contents:\n", "Skip to content\n", "Navigation Menu\n", "Toggle navigation\n", "Sign in\n", "huggingface\n", "Product\n", "GitHub Copilot\n", "Write better code with AI\n", "Security\n", "Find and fix vulnerabilities\n", "Actions\n", "Automate any workflow\n", "Codespaces\n", "Instant dev environments\n", "Issues\n", "Plan and track work\n", "Code Review\n", "Manage code changes\n", "Discussions\n", "Collaborate outside of code\n", "Code Search\n", "Find more, search less\n", "Explore\n", "All features\n", "Documentation\n", "GitHub Skills\n", "Blog\n", "Solutions\n", "By company size\n", "Enterprises\n", "Small and medium teams\n", "Startups\n", "Nonprofits\n", "By use case\n", "DevSecOps\n", "DevOps\n", "CI/CD\n", "View all use cases\n", "By industry\n", "Healthcare\n", "Financial services\n", "Manufacturing\n", "Government\n", "View all industries\n", "View all solutions\n", "Resources\n", "Topics\n", "AI\n", "DevOps\n", "Security\n", "Software Development\n", "View all\n", "Explore\n", "Learning Pathways\n", "Events & Webinars\n", "Ebooks & Whitepapers\n", "Customer Stories\n", "Partners\n", "Executive Insights\n", "Open Source\n", "GitHub Sponsors\n", "Fund open source developers\n", "The ReadME Project\n", "GitHub community articles\n", "Repositories\n", "Topics\n", "Trending\n", "Collections\n", "Enterprise\n", "Enterprise platform\n", "AI-powered developer platform\n", "Available add-ons\n", "Advanced Security\n", "Enterprise-grade security features\n", "Copilot for business\n", "Enterprise-grade AI features\n", "Premium Support\n", "Enterprise-grade 24/7 support\n", "Pricing\n", "Search or jump to...\n", "Search code, repositories, users, issues, pull requests...\n", "Search\n", "Clear\n", "Search syntax tips\n", "Provide feedback\n", "We read every piece of feedback, and take your input very seriously.\n", "Include my email address so I can be contacted\n", "Cancel\n", "Submit feedback\n", "Saved searches\n", "Use saved searches to filter your results more quickly\n", "Cancel\n", "Create saved search\n", "Sign in\n", "Sign up\n", "Reseting focus\n", "You signed in with another tab or window.\n", "Reload\n", "to refresh your session.\n", "You signed out in another tab or window.\n", "Reload\n", "to refresh your session.\n", "You switched accounts on another tab or window.\n", "Reload\n", "to refresh your session.\n", "Dismiss alert\n", "Hugging Face\n", "The AI community building the future.\n", "Verified\n", "We've verified that the organization\n", "huggingface\n", "controls the domain:\n", "huggingface.co\n", "Learn more about verified organizations\n", "46.8k\n", "followers\n", "NYC + Paris\n", "https://huggingface.co/\n", "X\n", "@huggingface\n", "Overview\n", "Repositories\n", "Projects\n", "Packages\n", "People\n", "Sponsoring\n", "0\n", "More\n", "Overview\n", "Repositories\n", "Projects\n", "Packages\n", "People\n", "Sponsoring\n", "Pinned\n", "Loading\n", "transformers\n", "transformers\n", "Public\n", "🤗 Transformers: State-of-the-art Machine Learning for Pytorch, TensorFlow, and JAX.\n", "Python\n", "142k\n", "28.4k\n", "diffusers\n", "diffusers\n", "Public\n", "🤗 Diffusers: State-of-the-art diffusion models for image, video, and audio generation in PyTorch and FLAX.\n", "Python\n", "28.3k\n", "5.8k\n", "datasets\n", "datasets\n", "Public\n", "🤗 The largest hub of ready-to-use datasets for ML models with fast, easy-to-use and efficient data manipulation tools\n", "Python\n", "19.9k\n", "2.8k\n", "peft\n", "peft\n", "Public\n", "🤗 PEFT: State-of-the-art Parameter-Efficient Fine-Tuning.\n", "Python\n", "17.9k\n", "1.8k\n", "accelerate\n", "accelerate\n", "Public\n", "🚀 A simple way to launch, train, and use PyTorch models on almost any device and distributed configuration, automatic mixed precision (including fp8), and easy-to-configure FSDP and DeepSpeed support\n", "Python\n", "8.5k\n", "1.1k\n", "optimum\n", "optimum\n", "Public\n", "🚀 Accelerate inference and training of 🤗 Transformers, Diffusers, TIMM and Sentence Transformers with easy to use hardware optimization tools\n", "Python\n", "2.8k\n", "516\n", "Repositories\n", "Loading\n", "Type\n", "Select type\n", "Forks\n", "Archived\n", "Mirrors\n", "Templates\n", "Language\n", "Select language\n", "All\n", "C\n", "C#\n", "C++\n", "<PERSON><PERSON>\n", "Dockerfile\n", "Go\n", "Handlebars\n", "HTML\n", "Java\n", "JavaScript\n", "Jupyter Notebook\n", "<PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "MDX\n", "Mustache\n", "<PERSON>\n", "Python\n", "Rust\n", "Shell\n", "Smarty\n", "Swift\n", "TypeScript\n", "Sort\n", "Select order\n", "Last updated\n", "Name\n", "Stars\n", "Showing 10 of 301 repositories\n", "kernel-builder\n", "Public\n", "👷 Build compute kernels\n", "huggingface/kernel-builder’s past year of commit activity\n", "<PERSON>\n", "22\n", "6\n", "7\n", "4\n", "Updated\n", "Mar 28, 2025\n", "<PERSON><PERSON><PERSON>\n", "Public\n", "🤗 LeRobot: Making AI for Robotics more accessible with end-to-end learning\n", "huggingface/lerobot’s past year of commit activity\n", "Python\n", "11,165\n", "Apache-2.0\n", "1,225\n", "153\n", "118\n", "Updated\n", "Mar 28, 2025\n", "smolagents\n", "Public\n", "🤗 smolagents: a barebones library for agents that think in python code.\n", "huggingface/smolagents’s past year of commit activity\n", "Python\n", "15,893\n", "Apache-2.0\n", "1,404\n", "105\n", "99\n", "Updated\n", "Mar 28, 2025\n", "notebooks\n", "Public\n", "Notebooks using the Hugging Face libraries 🤗\n", "huggingface/notebooks’s past year of commit activity\n", "Jupyter Notebook\n", "3,973\n", "Apache-2.0\n", "1,630\n", "131\n", "69\n", "Updated\n", "Mar 28, 2025\n", "meshgen\n", "Public\n", "A blender addon for generating meshes with AI\n", "huggingface/meshgen’s past year of commit activity\n", "Python\n", "520\n", "MIT\n", "29\n", "9\n", "0\n", "Updated\n", "Mar 28, 2025\n", "huggingface_hub\n", "Public\n", "The official Python client for the Huggingface Hub.\n", "huggingface/huggingface_hub’s past year of commit activity\n", "Python\n", "2,469\n", "Apache-2.0\n", "658\n", "146\n", "(4 issues need help)\n", "19\n", "Updated\n", "Mar 28, 2025\n", "lighteval\n", "Public\n", "Lighteval is your all-in-one toolkit for evaluating LLMs across multiple backends\n", "huggingface/lighteval’s past year of commit activity\n", "Python\n", "1,349\n", "MIT\n", "211\n", "90\n", "(1 issue needs help)\n", "33\n", "Updated\n", "Mar 28, 2025\n", "trl\n", "Public\n", "Train transformer language models with reinforcement learning.\n", "huggingface/trl’s past year of commit activity\n", "Python\n", "12,887\n", "Apache-2.0\n", "1,734\n", "329\n", "73\n", "Updated\n", "Mar 27, 2025\n", "optimum-neuron\n", "Public\n", "Easy, fast and very cheap training and inference on AWS Trainium and Inferentia chips.\n", "huggingface/optimum-neuron’s past year of commit activity\n", "Jupyter Notebook\n", "222\n", "Apache-2.0\n", "71\n", "19\n", "8\n", "Updated\n", "Mar 27, 2025\n", "hub-docs\n", "Public\n", "Docs of the Hugging Face Hub\n", "huggingface/hub-docs’s past year of commit activity\n", "Handlebars\n", "363\n", "Apache-2.0\n", "286\n", "100\n", "35\n", "Updated\n", "Mar 27, 2025\n", "View all repositories\n", "People\n", "View all\n", "Top languages\n", "Loading…\n", "Most used topics\n", "Loading…\n", "Footer\n", "© 2025 GitHub, Inc.\n", "Footer navigation\n", "Terms\n", "Privacy\n", "Security\n", "Status\n", "Docs\n", "Contact\n", "Manage cookies\n", "Do not share my personal information\n", "You can’t perform that action at this time.\n", "\n", "\n", "\n", "Discord server for community discussion\n", "\n"]}], "source": ["print(get_all_details(\"https://huggingface.co\"))"]}, {"cell_type": "code", "execution_count": 91, "id": "78ed1710-303f-45e4-9ce7-42d00247dc8a", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "and creates a short brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "Include details of company culture, customers and careers/jobs if you have the information.\"\n", "\n", "# Or uncomment the lines below for a more humorous brochure - this demonstrates how easy it is to incorporate 'tone':\n", "\n", "# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short humorous, entertaining, jokey brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\"\n"]}, {"cell_type": "code", "execution_count": 93, "id": "ad673f38-dd5c-40a5-9c79-768689c74488", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure of the company in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:5_000] # Truncate if more than 5,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": 99, "id": "7d2278ff-3cd4-460c-af4d-eed6a5d4c78b", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found links: {'links': [{'type': 'Company Page', 'url': 'https://huggingface.co'}, {'type': 'About page', 'url': 'https://docs.huggingface.co/'}, {'type': 'GitHub', 'url': 'https://github.com/huggingface'}, {'type': 'LinkedIn Company Page', 'url': 'https://www.linkedin.com/company/huggingface/'}, {'type': 'Twitter Company Handle', 'url': 'https://twitter.com/huggingface'}, {'type': 'Blog', 'url': 'https://blog.huggingface.co'}, {'type': 'Discord Community', 'url': 'https://join.huggingface.codiscord'}, {'type': 'Join Careers', 'url': 'https://apply.workable.com/huggingface/'}]}\n", "website error\n", "website error\n", "website error\n"]}, {"data": {"text/plain": ["'You are looking at a company called: HuggingFace\\nHere are the contents of its landing page and other relevant pages; use this information to build a short brochure of the company in markdown.\\nLanding page:\\nWebpage Title:\\nHugging Face – The AI community building the future.\\nWebpage Contents:\\nHugging Face\\nModels\\nDatasets\\nSpaces\\nPosts\\nDocs\\nEnterprise\\nPricing\\nLog In\\nSign Up\\nThe AI community building the future.\\nThe platform where the machine learning community collaborates on models, datasets, and applications.\\nExplore AI Apps\\nor\\nBrowse 1M+ models\\nTrending on\\nthis week\\nModels\\ndeepseek-ai/DeepSeek-V3-0324\\nUpdated\\n1 day ago\\n•\\n32.5k\\n•\\n1.88k\\nQwen/Qwen2.5-Omni-7B\\nUpdated\\n1 day ago\\n•\\n3.03k\\n•\\n662\\nmanycore-research/SpatialLM-Llama-1B\\nUpdated\\n7 days ago\\n•\\n6.14k\\n•\\n754\\nByteDance/InfiniteYou\\nUpdated\\n3 days ago\\n•\\n436\\nds4sd/SmolDocling-256M-preview\\nUpdated\\n5 days ago\\n•\\n40.8k\\n•\\n994\\nBrowse 1M+ models\\nSpaces\\nRunning\\non\\nZero\\n473\\n473\\nInfiniteYou-FLUX\\n📸\\nFlexible Photo Recrafting While Preserving Your Identity\\nRunning\\n323\\n323\\nGemini Co-Drawing\\n✏\\nGemini 2.0 native image generation co-doodling\\nRunning\\non\\nZero\\n204\\n204\\nLHM\\n⚡\\nLarge Animatable Human Model\\nRunning\\non\\nL40S\\n325\\n325\\nStable Virtual Camera\\n⚡\\nGenerate virtual camera views from input images\\nRunning\\n163\\n163\\nHunyuan T1\\n💬\\nHunyuan T1模型体验\\nBrowse 400k+ applications\\nDatasets\\nnvidia/Llama-Nemotron-Post-Training-Dataset-v1\\nUpdated\\n10 days ago\\n•\\n6.96k\\n•\\n256\\nglaiveai/reasoning-v1-20m\\nUpdated\\n9 days ago\\n•\\n5.71k\\n•\\n116\\nFreedomIntelligence/medical-o1-reasoning-SFT\\nUpdated\\nFeb 22\\n•\\n27k\\n•\\n566\\nfacebook/collaborative_agent_bench\\nUpdated\\n8 days ago\\n•\\n83\\n•\\n47\\na-m-team/AM-DeepSeek-R1-Distilled-1.4M\\nUpdated\\nabout 18 hours ago\\n•\\n2.26k\\n•\\n68\\nBrowse 250k+ datasets\\nThe Home of Machine Learning\\nCreate, discover and collaborate on ML better.\\nThe collaboration platform\\nHost and collaborate on unlimited public models, datasets and applications.\\nMove faster\\nWith the HF Open source stack.\\nExplore all modalities\\nText, image, video, audio or even 3D.\\nBuild your portfolio\\nShare your work with the world and build your ML profile.\\nSign Up\\nAccelerate your ML\\nWe provide paid Compute and Enterprise solutions.\\nCompute\\nDeploy on optimized\\nInference Endpoints\\nor update your\\nSpaces applications\\nto a GPU in a few clicks.\\nView pricing\\nStarting at $0.60/hour for GPU\\nEnterprise\\nGive your team the most advanced platform to build AI with enterprise-grade security, access controls and\\n\\t\\t\\tdedicated support.\\nGetting started\\nStarting at $20/user/month\\nSingle Sign-On\\nRegions\\nPriority Support\\nAudit Logs\\nResource Groups\\nPrivate Datasets Viewer\\nMore than 50,000 organizations are using Hugging Face\\nAi2\\nEnterprise\\nnon-profit\\n•\\n396 models\\n•\\n2.97k followers\\nAI at Meta\\nEnterprise\\ncompany\\n•\\n2.07k models\\n•\\n5.27k followers\\nAmazon\\ncompany\\n•\\n10 models\\n•\\n2.91k followers\\nGoogle\\ncompany\\n•\\n974 models\\n•\\n10.6k followers\\nIntel\\ncompany\\n•\\n219 models\\n•\\n2.37k followers\\nMicrosoft\\ncompany\\n•\\n365 models\\n•\\n10.7k followers\\nGrammarly\\nEnterprise\\ncompany\\n•\\n10 models\\n•\\n145 followers\\nWriter\\nEnterprise\\ncompany\\n•\\n21 models\\n•\\n253 followers\\nOur Open Source\\nWe are building the foundation of ML tooling with the community.\\nTransformers\\n142,056\\nState-of-the-art ML for PyTorch, TensorFlow, JAX\\nDiffusers\\n28,292\\nState-of-the-art Diffusion models in PyTorch\\nSafetensors\\n3,189\\nSafe way to store/distribute neural network weights\\nHub Python Library\\n2,469\\nPython client to interact with the Hugging Face Hub\\nTokenizers\\n9,538\\nFast tokenizers optimized for research & production\\nTRL\\n12,887\\nTrain transformers LMs with reinforcement learning\\nTransformers.js\\n13,301\\nState-of-the-art ML running directly in your browser\\nsmolagents\\n15,893\\nSmol library to build great agents in Python\\nPEFT\\n17,927\\nParameter-efficient finetuning for large language models\\nDatasets\\n19,888\\nAccess & share datasets for any ML tasks\\nText Generation Inference\\n9,937\\nServe language models with TGI optimized toolkit\\nAccelerate\\n8,542\\nTrain PyTorch models with multi-GPU, TPU, mixed precision\\nSystem theme\\nWebsite\\nModels\\nDatasets\\nSpaces\\nTasks\\nInference Endpoints\\nHuggingChat\\nCompany\\nAbout\\nBrand assets\\nTerms of service\\nPrivacy\\nJobs\\nPress\\nResources\\nLearn\\nDocumentation\\nBlog\\nForum\\nService Status\\nSocial\\nGitHub\\nTwitter\\nLinkedIn\\nDiscord\\n\\n\\n\\nCompany Page\\nWebpage Title:\\nHugging Face – The AI community building the future.\\nWebpage Contents:\\nHugging Face\\nModels\\nDatasets\\nSpaces\\nPosts\\nDocs\\nEnterprise\\nPricing\\nLog In\\nSign Up\\nThe AI community building the future.\\nThe platform where the machine learning community collaborates on models, datasets, and applications.\\nExplore AI Apps\\nor\\nBrowse 1M+ models\\nTrending on\\nthis week\\nModels\\ndeepseek-ai/DeepSeek-V3-0324\\nUpdated\\n1 day ago\\n•\\n32.5k\\n•\\n1.88k\\nQwen/Qwen2.5-Omni-7B\\nUpdated\\n1 day ago\\n•\\n3.03k\\n•\\n662\\nmanycore-research/SpatialLM-Llama-1B\\nUpdated\\n7 days ago\\n•\\n6.14k\\n•\\n754\\nByteDance/InfiniteYou\\nUpdated\\n3 days ago\\n•\\n436\\nds4sd/SmolDocling-256M-preview\\nUpdated\\n5 days ago\\n•\\n40.8k\\n•\\n994\\nBrowse 1M+ models\\nSpaces\\nRunning\\non\\nZero\\n473\\n473\\nInfiniteYou-FLUX\\n📸\\nFlexible Photo Recrafting While Preserving Your Identity\\nRunning'"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["get_brochure_user_prompt(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": 101, "id": "0dcf1180-75ef-4367-a764-2b442b91bc97", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "    )\n", "    result = response.choices[0].message.content\n", "    display(Markdown(result))"]}, {"cell_type": "code", "execution_count": 103, "id": "684e56b5-1799-469c-8772-1d467b666921", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found links: {'links': [{'type': 'About page', 'url': 'https://huggingface.co/'}, {'type': 'Company page', 'url': 'https://huggingface.co/'}, {'type': 'Careers/Jobs page', 'url': 'https://apply.workable.com/huggingface/'}, {'type': 'Pricing page', 'url': 'https://ui.endpoints.huggingface.co/pricing#endpoints'}, {'type': 'Enterprise page', 'url': 'https://huggingface.co/enterprise'}, {'type': 'Blog page', 'url': 'https://blog.huggingface.co/'}, {'type': 'Documentation pages', 'url': 'https://docs.huggingface.co/'}]}\n", "website error\n", "website error\n"]}, {"data": {"text/markdown": ["# Hugging Face <PERSON>\n", "## About Us\n", "\n", "Hugging Face is the AI community building the future. We're a platform where the machine learning community collaborates on models, datasets, and applications. Our mission is to make it easy for anyone to build, discover, and connect with other ML practitioners on our open-source ecosystem.\n", "\n", "### Company Culture\n", "\n", "At Hugging Face, we value collaboration, innovation, and openness. We believe that AI should be accessible to everyone, and we strive to create an inclusive environment where ML researchers and practitioners can share ideas, learn from each other, and accelerate their progress.\n", "\n", "### Our Community\n", "\n", "Our community is growing rapidly, with over 50,000 organizations using our platform. We're proud of our collaborations with leading companies like Meta, Google, Amazon, Intel, Microsoft, Grammarly, Writer, and Hunyuan T1, who are leveraging our technology to build cutting-edge AI applications.\n", "\n", "### Products & Services\n", "\n", "We offer a wide range of products and services, including:\n", "\n", "* **Hugging Face Hub**: A vast catalog of pre-trained models, datasets, and applications developed by the community.\n", "* **Spaces**: A platform for hosting, collaborating on, and deploying public models, datasets, and applications.\n", "* **Computing**: Optimized inference endpoints for deploying models on-premise or in the cloud.\n", "\n", "### Leadership\n", "\n", "Our leadership team is comprised of experienced professionals with a deep understanding of AI and machine learning.\n", "\n", "### Career Opportunities\n", "\n", "Join our team! We're always looking for talented ML practitioners to help us build the future of AI. Check out our career page for more information.\n", "\n", "## Model Gallery\n", "Browse 1M+ pre-trained models and discover new applications.\n", "\n", "*DeepSeek-V3-0324*\n", "\n", "Qwen/Omni-7B\n", "\n", "SpatialLM-Llama-1B\n", "\n", "InfiniteYou FLUX\n", "\n", "# Datasets\n", " Access & share datasets for any ML tasks.\n", "\n", "*   NVidia/Llama-Nemotron-Post-Training-Dataset-v1*\n", "*   glaive.ai/reasoning-v1-20M*\n", "\n", "## Get Started\n", "Accelerate your ML with our paid Compute and Enterprise solutions. Sign up today!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["create_brochure(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": 107, "id": "a51a4434-6e75-4caa-a857-d162205431e5", "metadata": {}, "outputs": [], "source": ["def stream_brochure(company_name, url):\n", "    stream = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "        stream=True\n", "    )\n", "    \n", "    response = \"\"\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        response = response.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "        update_display(Markdown(response), display_id=display_handle.display_id)"]}, {"cell_type": "code", "execution_count": 109, "id": "258beed4-5cfb-4de2-9855-20cc1b4c51ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found links: {'links': [{'type': 'About page', 'url': 'https://huggingface.co'}, {'type': 'Brand page', 'url': 'https://huggingface.com/brand/'}, {'type': 'Home (company) page', 'url': 'https://huggingface.co/company'}, {'type': 'Careers/Jobs page', 'url': 'https://apply.workable.com/huggingface/', 'alt': 'Join our team'}, {'type': 'Learn page', 'url': 'https://learn.huggingface.co'}, {'type': 'Blog page', 'url': 'https://blog.huggingface.co'}, {'type': 'News/Updates page', 'url': 'https://status.huggingface.co/'}, {'type': 'Forum/Discussion page', 'url': 'https://discuss.huggingface.co'}, {'type': 'Twitter account', 'url': 'https://twitter.com/huggingface'}, {'type': 'LinkedIn company page', 'url': 'https://www.linkedin.com/company/huggingface/'}, {'type': 'GitHub repository', 'url': 'https://github.com/huggingface'}, {'type': 'Documentation/Transformers page', 'url': 'https://huggingface.co/docs/transformers'}, {'type': 'Documentation/Hub page', 'url': 'https://huggingface.co/docs/huggingface_hub'}, {'type': 'Documentation/Safetensors page', 'url': 'https://huggingface.co/docs/safetensors'}]}\n", "website error\n", "website error\n"]}, {"data": {"text/markdown": ["# Hugging Face: The AI Community Building the Future\n", "\n", "[Image of a futuristic cityscape with artificial intelligence elements, such as robots and screens displaying code]\n", "\n", "Welcome to Hugging Face, the leading platform for the machine learning community. We are on a mission to build the future of artificial intelligence through collaboration, innovation, and open-source technology.\n", "\n", "## About Us\n", "\n", "Hugging Face was founded by <PERSON> who aims to bring together researchers, developers, and organizations from around the world to collaborate on building AI models. Our platform provides a suite of tools and resources for text, image, video, audio, and 3D models, making it easier for individuals and teams to accelerate their machine learning journey.\n", "\n", "## Models\n", "\n", "### Browse 1M+ Models\n", "\n", "From deep-seek to transformers, our model repository is home to over 1 million AI models. Explore popular models like:\n", "\n", "*   **deepseek-ai/DeepSeek-V3-0324**: An optimized version of the Deep Seek model for image generation\n", "*   **Qwen/Qwen2.5-Omni-7B**: A novel framework for multi-modal language understanding\n", "*   **manycore-research/SpatialLM-Llama-1B**: A spatial transformer model for natural language processing\n", "\n", "**Select Your Model**\n", "\n", "| Model | Description |\n", "| --- | --- |\n", "| InfiniteYou-FLUX  | Flexible photo recrafting while preserving your identity  |\n", "| Gemini Co-Drawing  | Native image generation co-doodling with AI  |\n", "| LHM   | Large animatable human model for virtual camera views |\n", "\n", "## Datasets\n", "\n", "With access to over 250,000 datasets, our platform enables you to train and validate your models on a diverse range of data sources.\n", "\n", "*   **nvidia/Llama-Nemotron-Post-Training-Dataset-v1**: A dataset for learning from few-shot text classification\n", "*   **glaiveai/reasoning-v1-20m**: An extensive benchmark for common sense reasoning\n", "\n", "## Spaces\n", "\n", "Our spaces feature allow users to build, share, and deploy custom models directly onto our infrastructure.\n", "\n", "### 400k+ Applications\n", "\n", "From chatbots to image generators, our platform hosts a vast range of applications built on top of our ecosystem.\n", "\n", "## Community Support\n", "\n", "Join us at [Twitter](https://twitter.com/HuggingFace) for the latest news, research opportunities, and open-source releases."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["stream_brochure(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "code", "execution_count": null, "id": "1e327058-3cfa-4299-872e-9ffd62cbee63", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}