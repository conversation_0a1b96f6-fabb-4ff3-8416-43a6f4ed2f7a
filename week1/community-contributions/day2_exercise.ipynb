{"cells": [{"cell_type": "code", "execution_count": null, "id": "41136d6f-07bc-4f6f-acba-784b8e5707b1", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "id": "8612b4f7-5c31-48f3-8423-2***********", "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": null, "id": "508bd442-7860-4215-b0f2-57f7adefd807", "metadata": {}, "outputs": [], "source": ["# Create a messages list using the same format that we used for OpenAI\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Describe some of the business applications of Generative AI\"}\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "cc7e8ada-4f8d-4090-be64-4aa72e03ac58", "metadata": {}, "outputs": [], "source": ["# Let's just make sure the model is loaded\n", "\n", "!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": null, "id": "4afd2e56-191a-4e31-949e-9b9376a39b5a", "metadata": {"scrolled": true}, "outputs": [], "source": ["# There's actually an alternative approach that some people might prefer\n", "# You can use the OpenAI client python library to call Ollama:\n", "\n", "from openai import OpenAI\n", "ollama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n", "response = ollama_via_openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "365f3d83-2601-42fb-89cc-98a4e1f79e0d", "metadata": {}, "outputs": [], "source": ["message = \"Hello, <PERSON><PERSON>! This is my first ever message to you! Hi!\"\n", "response = ollama_via_openai.chat.completions.create(model=MODEL, messages=[{\"role\":\"user\", \"content\":message}])\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "29c383ae-bf5b-41bc-b5af-a22f851745dc", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/1******** Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "dc61e30f-653f-4554-b1cd-6e61a0e2430a", "metadata": {"scrolled": true}, "outputs": [], "source": ["ed = Website(\"https://edwarddonner.com\")\n", "print(ed.title)\n", "print(ed.text)"]}, {"cell_type": "code", "execution_count": null, "id": "db2066fb-3079-4775-832a-dcc0f19beb6e", "metadata": {}, "outputs": [], "source": ["\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "af81b070-b6fe-4b18-aa0b-c03cd76a0adf", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "4e66291b-23b1-4915-b6a3-11a4b6a4db66", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a snarky assistant\"},\n", "    {\"role\": \"user\", \"content\": \"What is 2 + 2?\"}\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "67c92f47-4a3b-491f-af00-07fda470087e", "metadata": {}, "outputs": [], "source": ["def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "db1b9085-e5e7-4ec9-a264-acc389085ada", "metadata": {}, "outputs": [], "source": ["messages_for(ed)"]}, {"cell_type": "code", "execution_count": null, "id": "677bfc2f-19ac-46a0-b67e-a2b2ddf9cf6b", "metadata": {}, "outputs": [], "source": ["def summarize(url):\n", "    website = Website(url)\n", "    response = ollama_via_openai.chat.completions.create(\n", "        model = MODEL,\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "ee3242ba-b695-4b1e-8a91-2fdeb536c2e7", "metadata": {}, "outputs": [], "source": ["summarize(\"https://edwarddonner.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "85142cb8-ce0c-4c31-8b26-bb1744cf99ec", "metadata": {}, "outputs": [], "source": ["def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "63db51a7-dd03-4514-8954-57156967f82c", "metadata": {"scrolled": true}, "outputs": [], "source": ["display_summary(\"https://app.daily.dev/posts/bregman-arie-devops-exercises-linux-jenkins-aws-sre-prometheus-docker-python-ansible-git-k-yli9wthnf\")"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}