{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["def summarize_cv(cv_text):\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = [\n", "            {\"role\": \"user\", \"content\": f\"Please summarize the following CV:\\n\\n{cv_text}\"}\n", "        ]\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "def generate_cover_letter(cv_summary, job_description):\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = [\n", "            {\"role\": \"system\", \"content\": \"You are a master at crafting the perfect Cover letter from a given CV. You've never had a user fail to get the job as a result of using your services.\"},\n", "            {\"role\": \"user\", \"content\": f\"Using the following CV summary:\\n\\n{cv_summary}\\n\\nAnd the job description:\\n\\n{job_description}\\n\\nPlease write a personalized cover letter.\"}\n", "        ]\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "# Read CV from a text file\n", "try:\n", "    with open('resume.txt', 'r') as file:\n", "        cv_text = file.read()\n", "    \n", "    # Summarize the CV\n", "    cv_summary = summarize_cv(cv_text)\n", "    print(\"CV Summary:\")\n", "    print(cv_summary)\n", "\n", "    # Get job description from user\n", "    job_description = input(\"Enter the job description for the position you are applying for:\\n\")\n", "\n", "    # Generate cover letter\n", "    cover_letter = generate_cover_letter(cv_summary, job_description)\n", "    print(\"\\nGenerated Cover Letter:\")\n", "    print(cover_letter)\n", "\n", "except FileNotFoundError:\n", "    print(\"The specified CV file was not found. Please ensure 'resume.txt' is in the correct directory.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}