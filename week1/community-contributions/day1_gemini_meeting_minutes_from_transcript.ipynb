{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["# Getting <PERSON><PERSON> from call transcripts"], "metadata": {"id": "99Z21wE7xpKS"}}, {"cell_type": "markdown", "source": ["Import necessary libraries"], "metadata": {"id": "YZMeexE8M_Pp"}}, {"cell_type": "code", "source": ["# imports\n", "\n", "import os\n", "import requests\n", "\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n"], "metadata": {"id": "u5DCVg0Mxj5T"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "i0V11JQ2az-C"}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "#The below code can be uncommented in using .env file\n", "\n", "#from dotenv import load_dotenv\n", "#load_dotenv(override=True)\n", "#api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "#I am using google colab to import api_key\n", "from google.colab import userdata\n", "api_key=userdata.get('gemini_api')\n", "\n", "# Check the key\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "source": ["# A class to represet Transcript\n", "from pathlib import Path\n", "class Transcript:\n", "    def __init__(self, file_path):\n", "        self.file_path=file_path\n", "        self.content=Path(file_path).read_text(encoding='utf-8')\n"], "metadata": {"id": "j6UTsnTEyWZ-"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# Source of the text file -\"https://raw.githubusercontent.com/GeminiLn/EarningsCall_Dataset/refs/heads/master/3M%20Company_20170425/Text.txt\"\n", "path = '/content/Text.txt' # Specify the path of file you want to use - format should be .txt\n", "t=Transcript(path)\n"], "metadata": {"id": "hquePU_mzZ7s"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["\n", "system_prompt = \"You are expert at taking Meeting Notes & given the below transcript , create an MOM (Minutes of meeting)\""], "metadata": {"id": "ex5DB7M8L7KT"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["from google import genai\n", "from google.genai import types\n", "\n", "client = genai.Client(api_key=api_key)\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash\",\n", "    config=types.GenerateContentConfig(\n", "        system_instruction=system_prompt,\n", "        max_output_tokens=500,\n", "        temperature=0.1\n", "        ),\n", "    contents=t.content,\n", ")\n", "\n", "print(response.text)"], "metadata": {"id": "wcpJ34qfMKmV"}, "execution_count": null, "outputs": []}]}