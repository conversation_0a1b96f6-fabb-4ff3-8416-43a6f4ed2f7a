{"cells": [{"cell_type": "code", "execution_count": 1, "id": "1b809d22-d170-4db3-a298-1740ce06b534", "metadata": {}, "outputs": [], "source": ["#Udemy Course >> LLM Engineering: Master AI and LLMs\n", "#Student: <PERSON>\n", "#Date: Apr 20, 2025\n", "#Home work: Day1 - Summmarize website using local LLama\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 10, "id": "01e91579-7e32-4c4d-9cc9-c06d13c16209", "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": 2, "id": "8d780fba-868c-4216-88f5-1e3ca5ad43ed", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": 3, "id": "839b645f-90ee-434d-b0bd-1cb4e574a8de", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": 4, "id": "ef2453e8-3eca-4f6d-8ccf-9e5274b589a7", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": 5, "id": "6ec397d5-e9b0-411d-8bdb-66605273cb11", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a snarky assistant\"},\n", "    {\"role\": \"user\", \"content\": \"What is 2 + 2?\"}\n", "]"]}, {"cell_type": "code", "execution_count": 6, "id": "76aed9eb-a085-4687-859d-817c771156fa", "metadata": {}, "outputs": [], "source": ["def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "code", "execution_count": 7, "id": "26de4682-cf4f-4b7e-8cb2-049f7f46b758", "metadata": {}, "outputs": [], "source": ["def summarize(url):\n", "    website = Website(url)\n", "    ollama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n", "    response = ollama_via_openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=messages_for(website)   \n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 8, "id": "16b2532a-d44c-4903-83ec-0b828a2d1b92", "metadata": {}, "outputs": [], "source": ["def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": 11, "id": "86af4905-5d5c-47c9-b9b2-27257452ff94", "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Anthropic Website Summary**\n", "=====================================\n", "\n", "### Mission and Values\n", "\n", "Anthropic's mission is to build AI that serves humanity's long-term well-being. They focus on designing powerful technologies with human benefit at their foundation, aiming to demonstrate responsible AI development in practice.\n", "\n", "### Notable Releases\n", "\n", "#### 2025\n", "\n", "* **Claude 3.7 Sonnet**: Anthropic's most intelligent AI model, now available.\n", "* Recent news articles:\n", "\t+ \"Tracing the thoughts of a large language model: Interpretability\"\n", "\t+ \"Anthropic Economic Index: Societal Impacts\"\n", "\n", "### Products and Solutions\n", "\n", "* **Claude**: A suite of AI tools for building applications and custom experiences with human benefit in mind.\n", "* **Claude <PERSON>**, **API Platform**, and various other products, including:\n", "\t+ **Claude 3.5 <PERSON><PERSON>**\n", "\t+ **Claude 3 Opus**\n", "\n", "### Research and Commitments\n", "\n", "* The Anthropic Academy: A learning platform for developers to build AI solutions with <PERSON>.\n", "* Responsible scaling policy and alignment science initiatives.\n", "\n", "### News Section (Selection)**\n", "\n", "<PERSON><PERSON><PERSON>'s recent news articles:\n", "* \"Claude extended thinking\"\n", "* \"Alignment faking in large language models\"\n", "\n", "### Company Information\n", "\n", "For more information on Anthropic, including company, careers, and help resources, follow the provided links."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary(\"https://anthropic.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "a5151062-614e-44ff-b341-d3f64e28aa93", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}