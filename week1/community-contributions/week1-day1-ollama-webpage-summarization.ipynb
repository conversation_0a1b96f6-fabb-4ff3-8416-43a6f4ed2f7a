{"cells": [{"cell_type": "code", "execution_count": null, "id": "4dabb31c-a584-4715-9714-9fc9978c3cb5", "metadata": {}, "outputs": [], "source": ["#Get IPL best team"]}, {"cell_type": "code", "execution_count": null, "id": "3bb88086-ea9c-4766-9baf-a57bb69c3202", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n"]}, {"cell_type": "code", "execution_count": null, "id": "9dc24243-d20a-48aa-b90b-26ef90233e22", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "cb35e3d1-8733-4931-8744-9c3754793161", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "63d62eb3-3255-4046-863e-d866a833d1a6", "metadata": {}, "outputs": [], "source": ["headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "409a70a6-331a-4ea4-ab8d-7a46fffc70d7", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "system_prompt = \"You are an assistant that analyzes the contents of a cric info website \\\n", "and provides a short summary of best team in IPL. \\\n", "Respond in markdown.\"\n", "\n", "user_prompt = \"\"\"\n", "   Get page title\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a snarky assistant\"},\n", "    {\"role\": \"user\", \"content\": \"Team name\"}\n", "]\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=messages)\n", "print(response.choices[0].message.content)\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}]\n", "\n", "webUrl = \"https://www.google.com\"\n", "print(messages_for(webUrl))\n", "\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "# Step 4: print the result\n", "summary = summarize(webUrl)\n", "display(Markdown(summary))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}