{"cells": [{"cell_type": "markdown", "id": "2c80b652-eadd-4d48-a512-d5945c0365d3", "metadata": {}, "source": ["# Compare websites"]}, {"cell_type": "code", "execution_count": null, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": null, "id": "7b87cadb-d513-4303-baee-a37b6f938e4d", "metadata": {}, "outputs": [], "source": ["# Load environment variables \n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')"]}, {"cell_type": "code", "execution_count": null, "id": "019974d9-f3ad-4a8a-b5f9-0a3719aea2d3", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "markdown", "id": "2aa190e5-cb31-456a-96cc-db109919cd78", "metadata": {}, "source": ["## Website class"]}, {"cell_type": "code", "execution_count": null, "id": "c5e793b2-6775-426a-a139-4848291d0463", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/1******** Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "abdb8417-c5dc-44bc-9bee-2e059d162699", "metadata": {}, "outputs": [], "source": ["# Define our system prompt - you can experiment with this later, changing the last sentence to '<PERSON><PERSON><PERSON> in markdown in Spanish.\"\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "f0275b1b-7cfe-4f9d-abfa-7650d378da0c", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "markdown", "id": "d06e8d78-ce4c-4b05-aa8e-17050c82bb47", "metadata": {}, "source": ["## Website messages function"]}, {"cell_type": "code", "execution_count": null, "id": "0134dfa4-8299-48b5-b444-f2a8c3403c88", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "markdown", "id": "16f49d46-bf55-4c3e-928f-68fc0bf715b0", "metadata": {}, "source": ["## Website summary"]}, {"cell_type": "code", "execution_count": null, "id": "905b9919-aba7-45b5-ae65-81b3d1d78e34", "metadata": {}, "outputs": [], "source": ["# And now: call the OpenAI API. You will get very familiar with this!\n", "\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "# A function to display this nicely in the Ju<PERSON><PERSON> output, using markdown\n", "\n", "def display_summary(summary):    \n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "3d926d59-450e-4609-92ba-2d6f244f1342", "metadata": {}, "outputs": [], "source": ["w1 = \"https://cnn.com\"\n", "summary1 = summarize(w1)\n", "display_summary(summary1)"]}, {"cell_type": "code", "execution_count": null, "id": "45d83403-a24c-44b5-84ac-961449b4008f", "metadata": {}, "outputs": [], "source": ["w2 = \"https://www.foxnews.com\"\n", "summary2 = summarize(w2)\n", "display_summary(summary2)"]}, {"cell_type": "markdown", "id": "0a51b45c-f3a6-4b0b-acfe-52957c04fd94", "metadata": {}, "source": ["## Comparison between two websites"]}, {"cell_type": "code", "execution_count": null, "id": "4b30d5a5-bbe5-499c-9392-0896440f80c7", "metadata": {}, "outputs": [], "source": ["system_prompt_compare = \"\"\"You are a weblsite analyst that compares the summaries of two websites\n", "and provides a compare and contrast bewtween the two. \n", "Respond in markdown.\"\"\"\n", "\n", "def user_prompt_for_compare(summary1, summary2):\n", "    user_prompt = f\"You are asked to compare this summary of a website {summary1}\\n\\n\"\n", "    user_prompt += f\"\\nWith the summary of this second website {summary2}\\n\\n\"\n", "    user_prompt += \"please provide a short comparison of the two websites. \\\n", "List the similarities and differences in bullet point format.\\n\\n\"    \n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "c5c9c955-840f-4c31-a1a7-b4872f77f3b4", "metadata": {}, "outputs": [], "source": ["def messages_for_compare():\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt_compare},\n", "        {\"role\": \"user\", \"content\": user_prompt_for_compare(summary1, summary2)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "56307d77-f207-48f1-b59a-e97f6a2a37dd", "metadata": {}, "outputs": [], "source": ["def compare():    \n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for_compare()\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "ae3140bb-ddad-43e2-b697-6d05ae541544", "metadata": {}, "outputs": [], "source": ["display_summary(compare())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}