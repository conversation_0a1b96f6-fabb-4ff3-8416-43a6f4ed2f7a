{"cells": [{"cell_type": "markdown", "id": "e14248ff-07be-4ba8-a13c-d8c7f40ffb5f", "metadata": {}, "source": ["# A full business solution\n", "## Now we will take our project from Day 1 to the next level\n", "## BUSINESS CHALLENGE:\n", "Create a product that builds a Brochure for a company to be used for prospective clients, investors and potential recruits.\n", "\n", "We will be provided a company name and their primary website.\n", "\n", "See the end of this notebook for examples of real-world business applications.\n", "\n", "And remember: I'm always available if you have problems or ideas! Please do reach out."]}, {"cell_type": "code", "execution_count": null, "id": "6c8dc88a-85d9-493b-965c-68895cdd93f2", "metadata": {}, "outputs": [], "source": ["#imports \n", "\n", "import os\n", "import requests\n", "import json\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "131c483b-dd58-4faa-baf5-469ab6b00fbb", "metadata": {}, "outputs": [], "source": ["# Initialize and constants\n", "\n", "load_dotenv()\n", "api_key=os.getenv('OPENAI_API_KEY')\n", "\n", "if api_key and api_key[:8]=='sk-proj-':\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? \")\n", "\n", "MODEL='gpt-4o-mini'\n", "openai=OpenAI()\n"]}, {"cell_type": "code", "execution_count": null, "id": "196c0dee-7236-4f88-b7c2-f2a885190b19", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "f1329717-3727-4987-ada7-75df87a10459", "metadata": {}, "outputs": [], "source": ["ed=Website(\"https://www.anthropic.com/\")\n", "print(ed.get_contents)\n", "ed.links"]}, {"cell_type": "markdown", "id": "912d4f83-c8f1-437c-a01b-e21988af477c", "metadata": {}, "source": ["## First step: Have GPT-4o-mini figure out which links are relevant\n", "\n", "### Use a call to gpt-4o-mini to read the links on a webpage, and respond in structured JSON.  \n", "It should decide which links are relevant, and replace relative links such as \"/about\" with \"https://company.com/about\".  \n", "We will use \"one shot prompting\" in which we provide an example of how it should respond in the prompt.\n", "\n", "This is an excellent use case for an LLM, because it requires nuanced understanding. Imagine trying to code this without LLMs by parsing and analyzing the webpage - it would be very hard!\n", "\n", "Sidenote: there is a more advanced technique called \"Structured Outputs\" in which we require the model to respond according to a spec. We cover this technique in Week 8 during our autonomous Agentic AI project."]}, {"cell_type": "code", "execution_count": null, "id": "ed206771-df05-429d-8743-310bc86358ce", "metadata": {}, "outputs": [], "source": ["link_system_prompt=\"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the company, \\\n", "such as links to an About page, or a Company page, or Careers/Jobs pages.\\n\"\n", "link_system_prompt+=\"You should respond in JSON as in this example:\"\n", "link_system_prompt+=\"\"\"\n", "{\n", "    \"links\":[\n", "        {\"type\": \"about page\", \"url\": \"https://full.url/goes/here/about\"},\n", "        {\"type\": \"careers page\": \"url\": \"https://another.full.url/careers\"}\n", "    ]\n", "}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "ef835a85-9a48-42bd-979e-ca5f51bb1586", "metadata": {}, "outputs": [], "source": ["print(link_system_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "f2885e89-6455-4239-a98d-5599ea6e5947", "metadata": {}, "outputs": [], "source": ["def get_links_user_prompt(website):\n", "    user_prompt = f\"Here is the list of links on the website of {website.url} - \"\n", "    user_prompt += \"please decide which of these are relevant web links for a brochure about the company, respond with the full https URL in JSON format. \\\n", "Do not include Terms of Service, Privacy, email links.\\n\"\n", "    user_prompt += \"Links (some might be relative links):\\n\"\n", "    user_prompt += \"\\n\".join(website.links)\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "da7e4468-a225-4263-a212-94b1c69d38da", "metadata": {}, "outputs": [], "source": ["print(get_links_user_prompt(ed))"]}, {"cell_type": "code", "execution_count": null, "id": "53c59051-eed0-4292-8204-abbbd1d78df4", "metadata": {}, "outputs": [], "source": ["def get_links(url):\n", "    website=Website(url)\n", "    response=openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": link_system_prompt},\n", "            {\"role\": \"user\", \"content\": get_links_user_prompt(website)}\n", "      ],\n", "        response_format={\"type\":\"json_object\"}\n", "    )\n", "    result=response.choices[0].message.content\n", "    return json.loads(result)"]}, {"cell_type": "code", "execution_count": null, "id": "76d3d68d-6534-4b04-8a26-a07a9e532665", "metadata": {}, "outputs": [], "source": ["anthropic=Website(\"https://www.anthropic.com/\")\n", "anthropic.links"]}, {"cell_type": "code", "execution_count": null, "id": "12ca6438-bc99-4b45-9603-54bee5d8bce2", "metadata": {}, "outputs": [], "source": ["get_links(\"https://www.anthropic.com/\")"]}, {"cell_type": "markdown", "id": "4304d6e8-900e-4702-b84c-f202d6265459", "metadata": {}, "source": ["## Second step: make the brochure!\n", "\n", "Assemble all the details into another prompt to GPT4-o"]}, {"cell_type": "code", "execution_count": null, "id": "91ac10e6-8a7a-4367-939b-ac537c1c6c67", "metadata": {}, "outputs": [], "source": ["def get_all_details(url):\n", "    result=\"Landing page:\\n\"\n", "    result+=Website(url).get_contents()\n", "    links=get_links(url)\n", "    print(\"Found links:\",links)\n", "    for link in links[\"links\"]:\n", "        result+=f\"\\n\\n{link['type']}\\n\"\n", "        result+=Website(link[\"url\"]).get_contents()\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "765e9c71-2bbc-4222-bce1-0f553d8d2b10", "metadata": {}, "outputs": [], "source": ["print(get_all_details(\"https://anthropic.com\"))"]}, {"cell_type": "code", "execution_count": null, "id": "7116adc1-6f5e-445f-9869-ffcf5fa6a9b8", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "and creates a short brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "Include details of company culture, customers and careers/jobs if you have the information.\"\n", "\n", "# Or uncomment the lines below for a more humorous brochure - this demonstrates how easy it is to incorporate 'tone':\n", "\n", "# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short humorous, entertaining, jokey brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "02edb903-6352-417f-8c0f-85c2eee269b6", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure of the company in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:20_000] # Truncate if more than 20,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "2f760069-910e-4209-b357-b97e710f560d", "metadata": {}, "outputs": [], "source": ["get_brochure_user_prompt(\"Anthropic\", \"https://anthropic.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "faf9d9cc-fe30-4441-9adc-aee5b4dc80ca", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "    )\n", "    result = response.choices[0].message.content\n", "    display(Markdown(result))"]}, {"cell_type": "code", "execution_count": null, "id": "c8a672f4-ee87-4e2a-a6b1-dfb46f344ef3", "metadata": {}, "outputs": [], "source": ["create_brochure(\"Anthropic\", \"https://anthropic.com\")"]}, {"cell_type": "markdown", "id": "781fa1db-7acc-41fc-b26c-0d64964eb161", "metadata": {}, "source": ["## Finally - a minor improvement\n", "\n", "With a small adjustment, we can change this so that the results stream back from OpenAI,\n", "with the familiar typewriter animation"]}, {"cell_type": "code", "execution_count": null, "id": "b8359501-9f05-42bc-916c-7990ac910866", "metadata": {}, "outputs": [], "source": ["def stream_brochure(company_name, url):\n", "    stream= openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "        stream=True\n", "    )\n", "\n", "    response=\"\"\n", "    display_handle=display(Markdown(\"\"),display_id=True)\n", "    for chunk in stream:\n", "        response +=chunk.choices[0].delta.content or ''\n", "        response = response.replace(\"```\",\"\").replace(\"markdown\",\"\")\n", "        update_display(Markdown(response),display_id=display_handle.display_id)"]}, {"cell_type": "code", "execution_count": null, "id": "cd834aa7-deda-40cd-97ab-5fa5117fc6e0", "metadata": {}, "outputs": [], "source": ["stream_brochure(\"HuggingFace\", \"http://huggingface.co\")"]}, {"cell_type": "markdown", "id": "207068f8-d768-46b2-8b92-0ec78a9f71ae", "metadata": {}, "source": ["# Convert the brochure to a specified language\n"]}, {"cell_type": "code", "execution_count": null, "id": "e75be9e6-040d-4178-a5b3-1b7ae4460bc8", "metadata": {}, "outputs": [], "source": ["def create_brochure_language(company_name, url, language):\n", "    language_prompt = f\"You are a professional translator and writer specializing in creating and translating brochures. Convert the brochure to {language} while maintaining its original tone, format, and purpose.\"\n", "    user_language_prompt = f\"Generate a brochure for the company '{company_name}' available at the URL: {url}, and translate it into {language}.\"\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": language_prompt},\n", "            {\"role\": \"user\", \"content\": user_language_prompt}\n", "        ],\n", "    )\n", "    result = response.choices[0].message.content\n", "    display(Markdown(result))\n"]}, {"cell_type": "code", "execution_count": null, "id": "0748ec58-335b-4796-ae15-300dee7b24b0", "metadata": {}, "outputs": [], "source": ["create_brochure_language(\"HuggingFace\", \"http://huggingface.co\",\"Hindi\")"]}, {"cell_type": "code", "execution_count": null, "id": "ba54f80b-b2cd-4a50-b460-e0d042499c49", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "182f35da-d7b1-40f8-b1a7-74e0cd7fd6fe", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}