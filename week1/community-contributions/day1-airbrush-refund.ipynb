{"cells": [{"cell_type": "code", "execution_count": 2, "id": "f3c6d883-58a2-47de-823f-3c7430cffcc9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\"Airbrush or Air Bust? Let's Find Out!\"\n"]}], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "\n", "openai = OpenAI()\n", "\n", "# Step 1: Create your prompts\n", "\n", "system_prompt = \"You will take the body of an email and evaluate it to suggest a brief snarky subject\"\n", "user_prompt = \"\"\"\n", "Dear Air Brush Customer Service Team,\n", "\n", "I hope this message finds you well. I am writing to formally lodge a complaint regarding the airbrush product I purchased from your store. Unfortunately, the product I received is defective and does not meet the quality standards as advertised.\n", "\n", "Below are the details of my issue:\n", "\n", "Order Number: #12345\n", "\n", "Product Name: Air Brush model 123\n", "\n", "Date of Purchase: 18/1/2025\n", "\n", "Issue Description:\n", "Defective Nozzle: The nozzle of the airbrush is clogged and does not allow proper airflow, making it impossible to use.\n", "\n", "Inconsistent Spray <PERSON>: Even after multiple attempts to clean and adjust the settings, the spray pattern is uneven and inconsistent.\n", "\n", "Leakage: The airbrush leaks air and paint from the joints, which is a significant safety hazard.\n", "\n", "Build Quality: The overall build quality of the product feels subpar, with loose fittings and a flimsy trigger mechanism.\n", "\n", "Steps Taken:\n", "I followed the user manual and cleaning instructions provided, but the issues persist.\n", "\n", "I also reached out to your technical support team on [Date] but have not received a resolution.\n", "\n", "Expectation:\n", "Given the defective nature of the product, I would like to request a full refund for the item. Alternatively, if a refund is not possible, I would appreciate a replacement with a fully functional unit.\n", "\n", "Attachments:\n", "I have attached photos and a video demonstrating the issues for your reference.\n", "\n", "Copies of the invoice and order confirmation are also attached for your convenience.\n", "\n", "Request for Resolution:\n", "Kindly let me know the next steps to process the refund or replacement. I would appreciate a prompt response within [X business days, e.g., 3-5 business days] to resolve this matter.\n", "\n", "Thank you for your attention to this issue. I trust that you will handle this matter professionally and ensure customer satisfaction.\n", "\n", "Looking forward to your swift response.\n", "\n", "Best regards,\n", "Oya YILDIZ\n", "İstanbul\n", "Turkey\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt}\n", "] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=messages)\n", "\n", "# Step 4: print the result\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "d9b655de-e8c3-4136-b6a6-2fb0ce01c364", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}