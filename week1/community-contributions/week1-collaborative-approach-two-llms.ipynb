{"cells": [{"cell_type": "markdown", "id": "fe12c203-e6a6-452c-a655-afb8a03a4ff5", "metadata": {}, "source": ["# **End of week 1 exercise**\n", "\n", "To demonstrate your familiarity with OpenAI API, and also Ollama, build a tool that takes a technical question,  \n", "and responds with an explanation. This is a tool that you will be able to use yourself during the course!"]}, {"cell_type": "markdown", "id": "c70e5ab1", "metadata": {}, "source": ["## **1. Get a response from your favorite AI Tutor** "]}, {"cell_type": "code", "execution_count": 1, "id": "c1070317-3ed9-4659-abe3-828943230e03", "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "import json\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": null, "id": "65dace69", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if api_key and api_key.startswith('sk-proj-') and len(api_key) > 10:\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? Please visit the troubleshooting notebook!\")"]}, {"cell_type": "code", "execution_count": 3, "id": "4a456906-915a-4bfd-bb9d-57e505c5093f", "metadata": {}, "outputs": [], "source": ["# constants\n", "\n", "MODEL_GPT = 'gpt-4o-mini'\n", "MODEL_LLAMA = 'llama3.2'\n", "\n", "openai = OpenAI()\n", "\n", "ollama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')"]}, {"cell_type": "code", "execution_count": 38, "id": "3673d863", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"You are the software engnieer, phd in mathematics, machine learning engnieer, and other topics\"\"\"\n", "system_prompt += \"\"\"\n", "When responding, always use Markdown for formatting. For any code, use well-structured code blocks with syntax highlighting,\n", "For instance:\n", "```python\n", "\n", "sample_list = [for i in range(10)]\n", "```\n", "Another example\n", "```javascript\n", "        function displayMessage() {\n", "            alert(\"Hello, welcome to JavaScript!\");\n", "        }\n", "\n", "```\n", "\n", "Break down explanations into clear, numbered steps for better understanding. \n", "Highlight important terms using inline code formatting (e.g., `function_name`, `variable`).\n", "Provide examples for any concepts and ensure all examples are concise, clear, and relevant.\n", "Your goal is to create visually appealing, easy-to-read, and informative responses.\n", "\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 39, "id": "1df78d41", "metadata": {}, "outputs": [], "source": ["def tutor_user_prompt(question):\n", "    # Ensure the question is properly appended to the user prompt.\n", "    user_prompt = (\n", "        \"Please carefully explain the following question in a step-by-step manner for clarity:\\n\\n\"\n", "    )\n", "    user_prompt += question\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": 43, "id": "6dccbccb", "metadata": {}, "outputs": [], "source": ["\n", "\n", "def askTutor(question, MODEL):\n", "    # Generate the user prompt dynamically.\n", "    user_prompt = tutor_user_prompt(question)\n", "    \n", "    # OpenAI API call to generate response.\n", "    if MODEL == 'gpt-4o-mini':\n", "        print(f'You are getting response from {MODEL}')\n", "        stream = openai.chat.completions.create(\n", "            model=MODEL,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            stream=True\n", "        )\n", "    else:\n", "        MODEL == 'llama3.2'\n", "        print(f'You are getting response from {MODEL}')\n", "        stream = ollama_via_openai.chat.completions.create(\n", "            model=MODEL,\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": system_prompt},\n", "                {\"role\": \"user\", \"content\": user_prompt}\n", "            ],\n", "            stream=True\n", "        )\n", "\n", "    # Initialize variables for response processing.\n", "    response = \"\"\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    \n", "    # Process the response stream and update display dynamically.\n", "    for chunk in stream:\n", "        # Safely access the content attribute.\n", "        response_chunk = getattr(chunk.choices[0].delta, \"content\", \"\")\n", "        if response_chunk:  # Check if response_chunk is not None or empty\n", "            response += response_chunk\n", "            # No replacement of Markdown formatting here!\n", "            update_display(Markdown(response), display_id=display_handle.display_id)\n"]}, {"cell_type": "code", "execution_count": 44, "id": "a8d7923c-5f28-4c30-8556-342d7c8497c1", "metadata": {}, "outputs": [], "source": ["# here is the question; type over this to ask something new\n", "\n", "question = \"\"\"\n", "Please explain what this code does and why:\n", "yield from {book.get(\"author\") for book in books if book.get(\"author\")}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "3f0d0137-52b0-47a8-81a8-11a90a010798", "metadata": {}, "outputs": [], "source": ["askTutor(question=question, MODEL=MODEL_GPT)"]}, {"cell_type": "markdown", "id": "b79f9479", "metadata": {}, "source": ["## **2. Using both LLMs collaboratively approach**"]}, {"cell_type": "markdown", "id": "80e3c8f5", "metadata": {}, "source": ["- I thought about like similar the idea of a RAG (Retrieval-Augmented Generation) approach, is an excellent idea to improve responses by refining the user query and producing a polished, detailed final answer. Two LLM talking each other its cool!!! Here's how we can implement this:\n", "\n", "**Updated Concept:**\n", "1. Refine Query with Ollama:\n", "    - Use Ollama to refine the raw user query into a well-structured prompt.\n", "    - This is especially helpful when users input vague or poorly structured queries.\n", "2. Generate Final Response with GPT:\n", "    - Pass the refined prompt from Ollama to GPT to generate the final, detailed, and polished response.\n", "3. Return the Combined Output:\n", "    - Combine the input, refined query, and the final response into a single display to ensure clarity."]}, {"cell_type": "code", "execution_count": 59, "id": "60f5ac2d", "metadata": {}, "outputs": [], "source": ["def refine_with_ollama(raw_question):\n", "    \"\"\"\n", "    Use Ollama to refine the user's raw question into a well-structured prompt.\n", "    \"\"\"\n", "    print(\"Refining the query using Ollama...\")\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"You are a helpful assistant. Refine and structure the following user input.\"},\n", "\n", "        {\"role\": \"user\", \"content\": raw_question},\n", "    ]\n", "    response = ollama_via_openai.chat.completions.create(\n", "        model=MODEL_LLAMA,\n", "        messages=messages,\n", "        stream=False  # Non-streamed refinement\n", "    )\n", "    refined_query = response.choices[0].message.content\n", "    return refined_query"]}, {"cell_type": "code", "execution_count": 60, "id": "2aa4c9f6", "metadata": {}, "outputs": [], "source": ["def ask_with_ollama_and_gpt(raw_question):\n", "    \"\"\"\n", "    Use Ollama to refine the user query and GPT to generate the final response.\n", "    \"\"\"\n", "    # Step 1: Refine the query using Ollama\n", "    refined_query = refine_with_ollama(raw_question)\n", "    \n", "    # Step 2: Generate final response with GPT\n", "    print(\"Generating the final response using GPT...\")\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": refined_query},\n", "    ]\n", "    stream = openai.chat.completions.create(\n", "        model=MODEL_GPT,\n", "        messages=messages,\n", "        stream=True  # Stream response for dynamic display\n", "    )\n", "\n", "    # Step 3: Combine responses\n", "    response = \"\"\n", "    display_handle = display(Markdown(f\"### Refined Query:\\n\\n{refined_query}\\n\\n---\\n\\n### Final Response:\"), display_id=True)\n", "    for chunk in stream:\n", "        response_chunk = getattr(chunk.choices[0].delta, \"content\", \"\")\n", "        if response_chunk:\n", "            response += response_chunk\n", "            update_display(Markdown(f\"### Refined Query:\\n\\n{refined_query}\\n\\n---\\n\\n### Final Response:\\n\\n{response}\"), display_id=display_handle.display_id)"]}, {"cell_type": "code", "execution_count": 61, "id": "4150e857", "metadata": {}, "outputs": [], "source": ["# Example Usage\n", "question = \"\"\"\n", "Please explain what this code does:\n", "yield from {book.get(\"author\") for book in books if book.get(\"author\")}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f2b8935f", "metadata": {}, "outputs": [], "source": ["ask_with_ollama_and_gpt(raw_question=question)"]}, {"cell_type": "code", "execution_count": null, "id": "086a5294", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}