{"cells": [{"cell_type": "code", "execution_count": null, "id": "6418dce8-3ad0-4da9-81de-b3bf57956086", "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "id": "75b7849a-841b-4525-90b9-b9fd003516fb", "metadata": {}, "outputs": [], "source": ["headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "45c07164-3276-47f3-8620-a5d0ca6a8d24", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "b334629a-cf2a-49fa-b198-edd73493720f", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt\n"]}, {"cell_type": "code", "execution_count": null, "id": "e4dd0855-302d-4423-9b8b-80c4bbb9ab31", "metadata": {}, "outputs": [], "source": ["website = Website(\"https://cnn.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "65c6cc43-a16a-4337-8c3d-4ab10ee0377a", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt_for(website)}]"]}, {"cell_type": "code", "execution_count": null, "id": "59799f7b-a244-4572-9296-34e4b87ba026", "metadata": {}, "outputs": [], "source": ["import ollama\n", "\n", "MODEL = \"llama3.2\"\n", "response = ollama.chat(model=MODEL, messages=messages)\n", "print(response['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "a0c03050-60d2-4165-9d8a-27eb57455704", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}