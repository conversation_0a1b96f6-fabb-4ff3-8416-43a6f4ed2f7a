{"cells": [{"cell_type": "markdown", "id": "44aba2a0-c6eb-4fc1-a5cc-0a8f8679dbb8", "metadata": {}, "source": ["## Far Far Away..."]}, {"cell_type": "code", "execution_count": null, "id": "d4d58124-5e9a-4f5a-9e0a-ff74f43896a8", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "33179b68-7ed5-46ab-b583-d67ed57cd39d", "metadata": {}, "outputs": [], "source": ["def add_user_greeting(greeting):\n", "    user_prompt = \"\"\"\n", "    The following is the greeting from the user. Please respond in character as a barman in the Mos Eisley Cantina.\\n\\n\n", "    \"\"\"\n", "    user_prompt += greeting\n", "\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "67dc3099-2ccc-4ee8-8ff2-0dbbe4ae2fcb", "metadata": {}, "outputs": [], "source": ["def approach_the_bar(greeting):\n", "\n", "    system_prompt = \"You are a barman in the Mos Eisley Cantina from the Star Wars universe.\\\n", "It is a Tuesday evening, the year is 3BBY, and the Cantina is quiet except for a few lonely regulars.\\\n", "The barman (you) is slightly skeptical but eager to share some interesting news regarding some nearby imperial activity.\\\n", "You will recieve a greeting from the user, you must respond and provide them with some gossip detailing \\\n", "some local shady dealings occuring in Mos Eisley. Please format your response using markdown to provide a sense of the conversation.\"\n", "\n", "    user_prompt = add_user_greeting(greeting)\n", "    \n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt},\n", "    ]\n", "        \n", "    response = openai.chat.completions.create(\n", "            model = \"gpt-4o-mini\",\n", "            messages = messages\n", "        )\n", "    \n", "    # Step 4: print the result in markdown format\n", "    pretty_response = Markdown(response.choices[0].message.content)\n", "    display(pretty_response)"]}, {"cell_type": "code", "execution_count": null, "id": "fb47e2b7-5509-4d1a-8e71-ff103fc8a885", "metadata": {}, "outputs": [], "source": ["approach_the_bar(\"\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}