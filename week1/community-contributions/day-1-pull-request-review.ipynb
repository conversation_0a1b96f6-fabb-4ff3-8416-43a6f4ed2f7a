{"cells": [{"cell_type": "code", "execution_count": null, "id": "6af348cb", "metadata": {}, "outputs": [], "source": ["# imports\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "8254a11a", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env and load openai\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "# Use a personal access token (PAT) for authentication. This allows access to private repositories and avoids low request limits.\n", "# You can generate a token at: https://github.com/settings/tokens\n", "github_token = os.getenv('GITHUB_TOKEN')\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "ac552db9", "metadata": {}, "outputs": [], "source": ["def extract_diff_from_pr(pr_url: str) -> str:\n", "    parts = pr_url.rstrip(\"/\").split(\"/\")\n", "    owner, repo, pr_number = parts[3], parts[4], parts[6]\n", "    \n", "    api_url = f\"https://github.com/{owner}/{repo}/pull/{pr_number}.diff\"\n", "    headers = {\n", "        \"Accept\": \"application/vnd.github.v3.diff\",\n", "        \"Authorization\": f\"token {github_token}\"\n", "    }\n", "\n", "    response = requests.get(api_url, headers=headers)\n", "    response.raise_for_status()\n", "    \n", "    return response.text\n"]}, {"cell_type": "code", "execution_count": null, "id": "45d4012b", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"You are an assistant that reviews code and provides concise, constructive feedback based on best practices. \n", "Focus on readability, architecture, performance, security, testability, and adherence to style guides.\n", "Highlight issues and suggest improvements clearly. Respond in English and in markdown.\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "5ed584ec", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(code_diffs):\n", "    user_prompt = \"You are reviewing the following code diffs\"\n", "    user_prompt += \". Please provide a concise code review focused on best practices: readability, architecture, performance, security, testability, and style guide adherence.\\n\"\n", "    user_prompt += \"Use a numbered list and be constructive. Suggest improvements where necessary, and highlight what was done well.\\n\\n\"\n", "    user_prompt += code_diffs\n", "    return user_prompt\n"]}, {"cell_type": "code", "execution_count": null, "id": "dc403124", "metadata": {}, "outputs": [], "source": ["def code_review_for(code_diffs):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(code_diffs)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "5208abd3", "metadata": {}, "outputs": [], "source": ["def reviewer(pr_link):\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = code_review_for(extract_diff_from_pr(pr_link))\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "525d92bf", "metadata": {}, "outputs": [], "source": ["def display_code_review(pr_link):\n", "    code_review = reviewer(pr_link)\n", "    display(Markdown(code_review))"]}, {"cell_type": "code", "execution_count": null, "id": "03517335", "metadata": {}, "outputs": [], "source": ["display_code_review(\"GITHUB PR LINK HERE\")"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}