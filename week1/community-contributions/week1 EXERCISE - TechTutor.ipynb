{"cells": [{"cell_type": "markdown", "id": "fe12c203-e6a6-452c-a655-afb8a03a4ff5", "metadata": {}, "source": ["# End of week 1 exercise\n", "\n", "To demonstrate your familiarity with OpenAI API, and also Ollama, build a tool that takes a technical question,  \n", "and responds with an explanation. This is a tool that you will be able to use yourself during the course!"]}, {"cell_type": "code", "execution_count": null, "id": "c1070317-3ed9-4659-abe3-828943230e03", "metadata": {}, "outputs": [], "source": ["# imports\n", "import os\n", "from dotenv import load_dotenv\n", "\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "4a456906-915a-4bfd-bb9d-57e505c5093f", "metadata": {}, "outputs": [], "source": ["# constants\n", "\n", "MODEL_GPT = 'gpt-4o-mini'\n", "MODEL_LLAMA = 'llama3.2'"]}, {"cell_type": "code", "execution_count": null, "id": "a8d7923c-5f28-4c30-8556-342d7c8497c1", "metadata": {}, "outputs": [], "source": ["# set up environment\n", "load_dotenv(override=True)\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "# set up clients\n", "openai = OpenAI()\n", "ollama = OpenAI(base_url=\"http://localhost:11434/v1\" , api_key=\"ollama\")\n", "\n", "# set up system prompt\n", "system_prompt = \"You are a coding tutor. If the user asks you a question, answer it to the point. If you are asked to create a code snippet, generate the code in Python and then explain it shortly.\""]}, {"cell_type": "code", "execution_count": 53, "id": "58f098cb-4b4e-4394-b0b5-29db88e9101c", "metadata": {}, "outputs": [], "source": ["def send_request(user_prompt, model=MODEL_LLAMA, stream=False):\n", "    message = [{\"role\": \"system\", \"content\": system_prompt}, {\"role\": \"user\", \"content\": user_prompt}]\n", "    if model.startswith(\"gpt\"):\n", "        model_client = openai\n", "    else:\n", "        model_client = ollama\n", "\n", "    \n", "    response = model_client.chat.completions.create(\n", "        model=model,\n", "        messages=message,\n", "        stream=stream\n", "    )\n", "\n", "    if stream:\n", "        streaming = \"\"\n", "        display_handle = display(Markdown(\"\"), display_id=True)\n", "        for chunk in response:\n", "            streaming += chunk.choices[0].delta.content or ''\n", "            streaming = streaming.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "            update_display(Markdown(streaming), display_id=display_handle.display_id)\n", "\n", "    else:\n", "        return display(Markdown(response.choices[0].message.content))\n", "\n"]}, {"cell_type": "code", "execution_count": 49, "id": "3f0d0137-52b0-47a8-81a8-11a90a010798", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": [" How can I display python code properly while streaming the answer from openai? Create a code snippet for this. The streaming should happen in the code canvas.\n"]}], "source": ["# here is the question; type over this to ask something new\n", "question = input()"]}, {"cell_type": "code", "execution_count": 48, "id": "2bc093fa-b2ff-47e9-8ea8-e41499385116", "metadata": {}, "outputs": [], "source": ["# question = \"\"\"How can I display python code properly while streaming the answer from openai? Create a code snippet for this. The streaming should happen in the code canvas.\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "60ce7000-a4a5-4cce-a261-e75ef45063b4", "metadata": {}, "outputs": [], "source": ["# Get gpt-4o-mini to answer, with streaming\n", "send_request(model=MODEL_GPT, user_prompt=question, stream=True)"]}, {"cell_type": "code", "execution_count": 54, "id": "8f7c8ea8-4082-4ad0-8751-3301adcf6538", "metadata": {}, "outputs": [{"data": {"text/markdown": ["To display Python code properly with OpenAI's chat interface, you'll need to use the `code` formatting in the response format provided by the API endpoint. \n", "\n", "Here's an example of how you can modify the API request URL to include the formatted code:\n", "\n", "```python\n", "import requests\n", "import json\n", "\n", "query = {\n", "    \"text\": \"{\\n}  # Python code here\\n}\"\n", "\n", "headers = {\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "response = requests.post('https://api.openai.com/v1/answers', data=json.dumps(query), headers=headers)\n", "\n", "answer = response.json()\n", "```\n", "\n", "However, the most convenient way to display the code is by using the `code` directive directly in your chat prompt. OpenAI will automatically format and highlight your code."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Get Llama 3.2 to answer\n", "send_request(user_prompt=question)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}