{"cells": [{"cell_type": "code", "execution_count": 208, "id": "f61139a1-40e1-4273-b9a6-5a0a9d63a9bd", "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "from reportlab.lib.pagesizes import letter\n", "from reportlab.pdfgen import canvas\n", "from IPython.display import display, FileLink\n", "from IPython.display import display, HTML, FileLink\n", "from reportlab.lib.pagesizes import A4"]}, {"cell_type": "code", "execution_count": 80, "id": "e0858b96-fd41-4911-a333-814e4ed23279", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting reportlab\n", "  Downloading reportlab-4.2.5-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: pillow>=9.0.0 in c:\\users\\<USER>\\anaconda3\\envs\\to_do_list\\lib\\site-packages (from reportlab) (11.0.0)\n", "Collecting chardet (from reportlab)\n", "  Downloading chardet-5.2.0-py3-none-any.whl.metadata (3.4 kB)\n", "Downloading reportlab-4.2.5-py3-none-any.whl (1.9 MB)\n", "   ---------------------------------------- 0.0/1.9 MB ? eta -:--:--\n", "   ---------------- ----------------------- 0.8/1.9 MB 6.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 1.9/1.9 MB 11.9 MB/s eta 0:00:00\n", "Downloading chardet-5.2.0-py3-none-any.whl (199 kB)\n", "Installing collected packages: chardet, reportlab\n", "Successfully installed chardet-5.2.0 reportlab-4.2.5\n"]}], "source": ["!pip install reportlab"]}, {"cell_type": "code", "execution_count": 220, "id": "62cc9d37-c801-4e8a-ad2c-7b1450725a10", "metadata": {}, "outputs": [], "source": ["OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\":\"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": 249, "id": "525a81e7-30f8-4db7-bc8d-29948195bd4f", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"You are a to-do list generator. Based on the user's input, you will create a clear and descriptive to-do\n", "list using bullet points. Only generate the to-do list as bullet points with some explaination and time fraame only if asked for and nothing else. \n", "Be a little descriptive.\"\"\""]}, {"cell_type": "code", "execution_count": 315, "id": "7fca3303-3add-468a-a6bd-be7a4d72c811", "metadata": {}, "outputs": [], "source": ["def generate_to_do_list(task_description):\n", "    payload = {\n", "        \"model\": MODEL,\n", "        \"messages\": [\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": task_description}\n", "        ],\n", "        \"stream\": False\n", "    }\n", "\n", "    response = requests.post(OLLAMA_API, json=payload, headers=HEADERS)\n", "\n", "    if response.status_code == 200:\n", "        try:\n", "            json_response = response.json()\n", "            to_do_list = json_response.get(\"message\", {}).get(\"content\", \"No to-do list found.\")\n", "            \n", "            formatted_output = \"Your To-Do List:\\n\\n\" + to_do_list\n", "            file_name = \"to_do_list.txt\"\n", "            \n", "            with open(file_name, \"w\", encoding=\"utf-8\") as file:\n", "                file.write(formatted_output)\n", "\n", "            return file_name\n", "        \n", "        except Exception as e:\n", "            return f\"Error parsing JSON: {e}\"\n", "    else:\n", "        return f\"Error: {response.status_code} - {response.text}\""]}, {"cell_type": "code", "execution_count": 316, "id": "d45d6c7e-0e89-413e-8f30-e4975ea6d043", "metadata": {}, "outputs": [{"name": "stdin", "output_type": "stream", "text": ["Enter the task description of the to-do list: Give me a 4-week to-do list plan for a wedding reception party.\n"]}], "source": ["task_description = input(\"Enter the task description of the to-do list:\")"]}, {"cell_type": "code", "execution_count": 317, "id": "5493da44-e254-4d06-b973-a8069c2fc625", "metadata": {"scrolled": true}, "outputs": [], "source": ["result = generate_to_do_list(task_description)"]}, {"cell_type": "code", "execution_count": 318, "id": "5e95c722-ce1a-4630-b21a-1e00e7ba6ab9", "metadata": {}, "outputs": [{"data": {"text/html": ["<p>You can download your to-do list by clicking the link below:</p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<a href='to_do_list.txt' target='_blank'>to_do_list.txt</a><br>"], "text/plain": ["C:\\Users\\<USER>\\to-do list using ollama\\to_do_list.txt"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(HTML(\"<p>You can download your to-do list by clicking the link below:</p>\"))\n", "display(FileLink(result))"]}, {"cell_type": "code", "execution_count": null, "id": "f3d0a44e-bca4-4944-8593-1761c2f73a70", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}