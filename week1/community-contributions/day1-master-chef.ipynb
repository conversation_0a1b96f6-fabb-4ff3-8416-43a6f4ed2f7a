{"cells": [{"cell_type": "markdown", "id": "d15d8294-3328-4e07-ad16-8a03e9bbfdb9", "metadata": {}, "source": ["# YOUR FIRST LAB\n", "### Please read this section. This is valuable to get you prepared, even if it's a long read -- it's important stuff.\n", "\n", "## Your first Frontier LLM Project\n", "\n", "Let's build a useful LLM solution - in a matter of minutes.\n", "\n", "By the end of this course, you will have built an autonomous Agentic AI solution with 7 agents that collaborate to solve a business problem. All in good time! We will start with something smaller...\n", "\n", "Our goal is to code a new kind of Web Browser. Give it a URL, and it will respond with a summary. The Reader's Digest of the internet!!\n", "\n", "Before starting, you should have completed the setup for [PC](../SETUP-PC.md) or [Mac](../SETUP-mac.md) and you hopefully launched this jupyter lab from within the project root directory, with your environment activated.\n", "\n", "## If you're new to Jupyter Lab\n", "\n", "Welcome to the wonderful world of Data Science experimentation! Once you've used Jupyter Lab, you'll wonder how you ever lived without it. Simply click in each \"cell\" with code in it, such as the cell immediately below this text, and hit Shift+Return to execute that cell. As you wish, you can add a cell with the + button in the toolbar, and print values of variables, or try out variations.  \n", "\n", "I've written a notebook called [Guide to <PERSON><PERSON><PERSON>](Guide%20to%20Jupyter.ipynb) to help you get more familiar with Jupyter Labs, including adding Markdown comments, using `!` to run shell commands, and `tqdm` to show progress.\n", "\n", "## If you're new to the Command Line\n", "\n", "Please see these excellent guides: [Command line on PC](https://chatgpt.com/share/67b0acea-ba38-8012-9c34-7a2541052665) and [Command line on Mac](https://chatgpt.com/canvas/shared/67b0b10c93a081918210723867525d2b).  \n", "\n", "## If you'd prefer to work in IDEs\n", "\n", "If you're more comfortable in IDEs like VSCode or Pycharm, they both work great with these lab notebooks too.  \n", "If you'd prefer to work in VSCode, [here](https://chatgpt.com/share/676f2e19-c228-8012-9911-6ca42f8ed766) are instructions from an AI friend on how to configure it for the course.\n", "\n", "## If you'd like to brush up your Python\n", "\n", "I've added a notebook called [Intermediate Python](Intermediate%20Python.ipynb) to get you up to speed. But you should give it a miss if you already have a good idea what this code does:    \n", "`yield from {book.get(\"author\") for book in books if book.get(\"author\")}`\n", "\n", "## I am here to help\n", "\n", "If you have any problems at all, please do reach out.  \n", "I'm available through the platform, <NAME_EMAIL>, or at https://www.linkedin.com/in/eddonner/ if you'd like to connect (and I love connecting!)  \n", "And this is new to me, but I'm also trying out X/Twitter at [@edwarddonner](https://x.com/edwarddonner) - if you're on X, please show me how it's done 😂  \n", "\n", "## More troubleshooting\n", "\n", "Please see the [troubleshooting](troubleshooting.ipynb) notebook in this folder to diagnose and fix common problems. At the very end of it is a diagnostics script with some useful debug info.\n", "\n", "## If this is old hat!\n", "\n", "If you're already comfortable with today's material, please hang in there; you can move swiftly through the first few labs - we will get much more in depth as the weeks progress.\n", "\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Please read - important note</h2>\n", "            <span style=\"color:#900;\">The way I collaborate with you may be different to other courses you've taken. I prefer not to type code while you watch. Rather, I execute Jupyter Labs, like this, and give you an intuition for what's going on. My suggestion is that you carefully execute this yourself, <b>after</b> watching the lecture. Add print statements to understand what's going on, and then come up with your own variations. If you have a Github account, use this to showcase your variations. Not only is this essential practice, but it demonstrates your skills to others, including perhaps future clients or employers...</span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">Treat these labs as a resource</h2>\n", "            <span style=\"color:#f71;\">I push updates to the code regularly. When people ask questions or have problems, I incorporate it in the code, adding more examples or improved commentary. As a result, you'll notice that the code below isn't identical to the videos. Everything from the videos is here; but in addition, I've added more steps and better explanations, and occasionally added new models like DeepSeek. Consider this like an interactive book that accompanies the lectures.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business value of these exercises</h2>\n", "            <span style=\"color:#181;\">A final thought. While I've designed these notebooks to be educational, I've also tried to make them enjoyable. We'll do fun things like have LLMs tell jokes and argue with each other. But fundamentally, my goal is to teach skills you can apply in business. I'll explain business implications as we go, and it's worth keeping this in mind: as you build experience with models and techniques, think of ways you could put this into action at work today. Please do contact me if you'd like to discuss more or if you have ideas to bounce off me.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "markdown", "id": "6900b2a8-6384-4316-8aaa-5e519fca4254", "metadata": {}, "source": ["# Connecting to OpenAI\n", "\n", "The next cell is where we load in the environment variables in your `.env` file and connect to OpenAI.\n", "\n", "## Troubleshooting if you have problems:\n", "\n", "Head over to the [troubleshooting](troubleshooting.ipynb) notebook in this folder for step by step code to identify the root cause and fix it!\n", "\n", "If you make a change, try restarting the \"Kernel\" (the python process sitting behind this notebook) by Kernel menu >> Restart Kernel and Clear Outputs of All Cells. Then try this notebook again, starting at the top.\n", "\n", "Or, contact me! Message me <NAME_EMAIL> and we will get this to work.\n", "\n", "Any concerns about API costs? See my notes in the README - costs should be minimal, and you can control it at every point. You can also use Ollama as a free alternative, which we discuss during Day 2."]}, {"cell_type": "code", "execution_count": null, "id": "7b87cadb-d513-4303-baee-a37b6f938e4d", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "019974d9-f3ad-4a8a-b5f9-0a3719aea2d3", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "markdown", "id": "442fc84b-0815-4f40-99ab-d9a5da6bda91", "metadata": {}, "source": ["# Let's make a quick call to a Frontier model to get started, as a preview!"]}, {"cell_type": "code", "execution_count": null, "id": "a58394bf-1e45-46af-9bfd-01e24da6f49a", "metadata": {}, "outputs": [], "source": ["# To give you a preview -- calling OpenAI with these messages is this easy. Any problems, head over to the Troubleshooting notebook.\n", "\n", "message = \"Hello, <PERSON><PERSON>! This is my first ever message to you! Hi!\"\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=[{\"role\":\"user\", \"content\":message}])\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "2aa190e5-cb31-456a-96cc-db109919cd78", "metadata": {}, "source": ["## OK onwards with our first project"]}, {"cell_type": "code", "execution_count": null, "id": "c5e793b2-6775-426a-a139-4848291d0463", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/1******** Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "2ef960cf-6dc2-4cda-afb3-b38be12f4c97", "metadata": {}, "outputs": [], "source": ["# Let's try one out. Change the website and add print statements to follow along.\n", "\n", "ed = Website(\"https://edwarddonner.com\")\n", "print(ed.title)\n", "print(ed.text)"]}, {"cell_type": "markdown", "id": "6a478a0c-2c53-48ff-869c-4d08199931e1", "metadata": {}, "source": ["## Types of prompts\n", "\n", "You may know this already - but if not, you will get very familiar with it!\n", "\n", "Models like GPT4o have been trained to receive instructions in a particular way.\n", "\n", "They expect to receive:\n", "\n", "**A system prompt** that tells them what task they are performing and what tone they should use\n", "\n", "**A user prompt** -- the conversation starter that they should reply to"]}, {"cell_type": "code", "execution_count": null, "id": "abdb8417-c5dc-44bc-9bee-2e059d162699", "metadata": {}, "outputs": [], "source": ["# Define our system prompt - you can experiment with this later, changing the last sentence to '<PERSON><PERSON><PERSON> in markdown in Spanish.\"\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "f0275b1b-7cfe-4f9d-abfa-7650d378da0c", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "26448ec4-5c00-4204-baec-7df91d11ff2e", "metadata": {}, "outputs": [], "source": ["print(user_prompt_for(ed))"]}, {"cell_type": "markdown", "id": "ea211b5f-28e1-4a86-8e52-c0b7677cadcc", "metadata": {}, "source": ["## Messages\n", "\n", "The API from OpenAI expects to receive messages in a particular structure.\n", "Many of the other APIs share this structure:\n", "\n", "```\n", "[\n", "    {\"role\": \"system\", \"content\": \"system message goes here\"},\n", "    {\"role\": \"user\", \"content\": \"user message goes here\"}\n", "]\n", "\n", "To give you a preview, the next 2 cells make a rather simple call - we won't stretch the mighty GPT (yet!)"]}, {"cell_type": "code", "execution_count": null, "id": "f25dcd35-0cd0-4235-9f64-ac37ed9eaaa5", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a snarky assistant\"},\n", "    {\"role\": \"user\", \"content\": \"What is 2 + 2?\"}\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "21ed95c5-7001-47de-a36d-1d6673b403ce", "metadata": {}, "outputs": [], "source": ["# To give you a preview -- calling OpenAI with system and user messages:\n", "\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=messages)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "d06e8d78-ce4c-4b05-aa8e-17050c82bb47", "metadata": {}, "source": ["## And now let's build useful messages for GPT-4o-mini, using a function"]}, {"cell_type": "code", "execution_count": null, "id": "0134dfa4-8299-48b5-b444-f2a8c3403c88", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "36478464-39ee-485c-9f3f-6a4e458dbc9c", "metadata": {}, "outputs": [], "source": ["# Try this out, and then try for a few more websites\n", "\n", "messages_for(ed)"]}, {"cell_type": "markdown", "id": "16f49d46-bf55-4c3e-928f-68fc0bf715b0", "metadata": {}, "source": ["## Time to bring it together - the API for OpenAI is very simple!"]}, {"cell_type": "code", "execution_count": null, "id": "905b9919-aba7-45b5-ae65-81b3d1d78e34", "metadata": {}, "outputs": [], "source": ["# And now: call the OpenAI API. You will get very familiar with this!\n", "\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "05e38d41-dfa4-4b20-9c96-c46ea75d9fb5", "metadata": {}, "outputs": [], "source": ["summarize(\"https://edwarddonner.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "3d926d59-450e-4609-92ba-2d6f244f1342", "metadata": {}, "outputs": [], "source": ["# A function to display this nicely in the Ju<PERSON><PERSON> output, using markdown\n", "\n", "def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "3018853a-445f-41ff-9560-d925d1774b2f", "metadata": {}, "outputs": [], "source": ["display_summary(\"https://edwarddonner.com\")"]}, {"cell_type": "markdown", "id": "b3bcf6f4-adce-45e9-97ad-d9a5d7a3a624", "metadata": {}, "source": ["# Let's try more websites\n", "\n", "Note that this will only work on websites that can be scraped using this simplistic approach.\n", "\n", "Websites that are rendered with Javascript, like React apps, won't show up. See the community-contributions folder for a Selenium implementation that gets around this. You'll need to read up on installing Selenium (ask ChatGPT!)\n", "\n", "Also Websites protected with CloudFront (and similar) may give 403 errors - many thanks <PERSON> for pointing this out.\n", "\n", "But many websites will work just fine!"]}, {"cell_type": "code", "execution_count": null, "id": "45d83403-a24c-44b5-84ac-961449b4008f", "metadata": {}, "outputs": [], "source": ["display_summary(\"https://cnn.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "75e9fd40-b354-4341-991e-863ef2e59db7", "metadata": {}, "outputs": [], "source": ["display_summary(\"https://anthropic.com\")"]}, {"cell_type": "markdown", "id": "c951be1a-7f1b-448f-af1f-845978e47e2c", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business applications</h2>\n", "            <span style=\"color:#181;\">In this exercise, you experienced calling the Cloud API of a Frontier Model (a leading model at the frontier of AI) for the first time. We will be using APIs like OpenAI at many stages in the course, in addition to building our own LLMs.\n", "\n", "More specifically, we've applied this to Summarization - a classic Gen AI use case to make a summary. This can be applied to any business vertical - summarizing the news, summarizing financial performance, summarizing a resume in a cover letter - the applications are limitless. Consider how you could apply Summarization in your business, and try prototyping a solution.</span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Before you continue - now try yourself</h2>\n", "            <span style=\"color:#900;\">Use the cell below to make your own simple commercial example. Stick with the summarization use case for now. Here's an idea: write something that will take the contents of an email, and will suggest an appropriate short subject line for the email. That's the kind of feature that might be built into a commercial email tool.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "00743dac-0e70-45b7-879a-d7293a6f68a6", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"You are an head chef of a michelin star restaurant who has a diverse skillset \\\n", "and loves to teach new and interesting recepies for homechefs. Given input of several ingredients \\\n", "provide step by step instruction of what could be cooked for any cuisine of your choice. Respond in markdown.\"\n", "\n", "user_prompt = \"\"\"\n", "You are a Michelin-starred head chef with a passion for teaching home chefs.  \n", "I have the following ingredients:  \n", "\n", "**[Chicken breast, Bell peppers, cherry tomatoes, spinach, Basmati rice,\n", "Garlic, basil, black pepper, smoked paprika]**  \n", "\n", "Can you provide a step-by-step recipe using these ingredients? You can choose any cuisine that best fits them.  \n", "Please include cooking times, techniques, and any chef tips for enhancing flavors. \n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "        ]\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages\n", "    )\n", "\n", "\n", "\n", "# Step 4: print the result\n", "def display_summary(summary):\n", "    display(Markdown(summary))\n", "display_summary(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "36ed9f14-b349-40e9-a42c-b367e77f8bda", "metadata": {}, "source": ["## An extra exercise for those who enjoy web scraping\n", "\n", "You may notice that if you try `display_summary(\"https://openai.com\")` - it doesn't work! That's because OpenAI has a fancy website that uses Javascript. There are many ways around this that some of you might be familiar with. For example, Selenium is a hugely popular framework that runs a browser behind the scenes, renders the page, and allows you to query it. If you have experience with Se<PERSON><PERSON>, <PERSON><PERSON> or similar, then feel free to improve the Website class to use them. In the community-contributions folder, you'll find an example Selenium solution from a student (thank you!)"]}, {"cell_type": "markdown", "id": "eeab24dc-5f90-4570-b542-b0585aca3eb6", "metadata": {}, "source": ["# Sharing your code\n", "\n", "I'd love it if you share your code afterwards so I can share it with others! You'll notice that some students have already made changes (including a Selenium implementation) which you will find in the community-contributions folder. If you'd like add your changes to that folder, submit a Pull Request with your new versions in that folder and I'll merge your changes.\n", "\n", "If you're not an expert with git (and I am not!) then GPT has given some nice instructions on how to submit a Pull Request. It's a bit of an involved process, but once you've done it once it's pretty clear. As a pro-tip: it's best if you clear the outputs of your Jupyter notebooks (Edit >> Clean outputs of all cells, and then Save) for clean notebooks.\n", "\n", "Here are good instructions courtesy of an AI friend:  \n", "https://chatgpt.com/share/677a9cb5-c64c-8012-99e0-e06e88afd293"]}, {"cell_type": "code", "execution_count": null, "id": "f4484fcf-8b39-4c3f-9674-37970ed71988", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}