{"cells": [{"cell_type": "code", "execution_count": 8, "id": "6ba7c60a-c338-49a1-b1ba-46b7c20e33cb", "metadata": {}, "outputs": [], "source": ["import openai\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": 2, "id": "4acb4062-17b2-43b1-8b74-aefaa9599463", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key found and looks good so far!\n"]}], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": 5, "id": "56f011b2-b759-4ad6-9d01-870fbcb8ade1", "metadata": {}, "outputs": [], "source": ["def generate_quiz(topic):\n", "    prompt = f\"Generate a multiple-choice quiz with 5 questions on the topic: {topic}. Include the correct answer for each question.\"\n", "    \n", "    messages = [\n", "        {\"role\": \"system\", \"content\": \"You are a quiz generator. Create a multiple-choice quiz with 5 questions and provide the correct answers.Respond in markdown.\"},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "    ]\n", "    \n", "    response = openai.chat.completions.create(\n", "        model=\"gpt-4\",\n", "        messages=messages,\n", "        max_tokens=300\n", "    )\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 10, "id": "1cf977e7-b04b-49e7-8b0a-d0ab2800c234", "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Question 1:** What is Python?\n", "\n", "**Choice A:** A type of snake\n", "**Choice B:** A medical term\n", "**Choice C:** A drilling tool\n", "**Choice D:** A high-level programming language\n", "\n", "Correct Answer: **Choice D:** A high-level programming language\n", "\n", "**Question 2:** In Python, what keyword is used to create a function?\n", "\n", "**Choice A:** func\n", "**Choice B:** def\n", "**Choice C:** function\n", "**Choice D:** create\n", "\n", "Correct Answer: **Choice B:** def\n", "\n", "**Question 3:** What is the correct syntax to output \"Hello World\" in Python?\n", "\n", "**Choice A:** printf(\"Hello World\")\n", "**Choice B:** println(\"Hello World\")\n", "**Choice C:** echo(\"Hello World\")\n", "**Choice D:** print(\"Hello World\")\n", "\n", "Correct Answer: **Choice D:** print(\"Hello World\")\n", "\n", "**Question 4:** How would you create a variable \"x\" that equals 5 in Python?\n", "\n", "**Choice A:** var x = 5\n", "**Choice B:** x := 5\n", "**Choice C:** x = 5\n", "**Choice D:** x : 5\n", "\n", "Correct Answer: **Choice C:** x = 5\n", "\n", "**Question 5:** How do you create a comment in Python?\n", "\n", "**Choice A:** // This is a comment\n", "**Choice B:** # This is a comment\n", "**Choice C:** <!-- This is a comment -->\n", "**Choice D:** /* This is a comment */\n", "\n", "Correct Answer"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Example usage\n", "topic = \"Python programming\"\n", "quiz = generate_quiz(topic)\n", "display(Mark<PERSON>(quiz))"]}, {"cell_type": "code", "execution_count": null, "id": "70990d7c-6061-43c6-b3c9-9146a3c51c3e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}