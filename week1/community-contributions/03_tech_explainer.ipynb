{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "6e907206-4c13-4698-91c6-9ca1c32be8e7", "metadata": {}, "source": ["# TechExplainAI\n", "---\n", "\n", "AI-driven tool that provides concise, structured explanations for technical questions and code snippets.\n", "\n", "- 🌍 Task: AI-powered technical explanation generator\n", "- 🧠 Model: OpenAI's `GPT-4o-mini`, Ollama's `llama3.2:3b`\n", "- 📌 Output Format: Markdown with real-time streaming\n", "- 🧑‍💻 Skill Level: Beginner\n", "- 🔄 Interaction Mode: User enters a technical question → AI generates a structured, concise explanation\n", "- 🎯 Purpose: Quickly explain technical concepts and Python code snippets\n", "- 🔧 Customization: Users can modify the models, prompts, and formatting as needed\n", "\n", "🛠️ Requirements\n", "- ⚙️ Hardware: ✅ CPU is sufficient — no GPU required\n", "- 🔑 OpenAI API Key\n", "- Install Ollama and pull llama3.2:3b or another lightweight model\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"]}, {"cell_type": "code", "execution_count": null, "id": "f743c87a-ed80-43d5-84ad-c78c8bdacb09", "metadata": {}, "outputs": [], "source": ["import os\n", "import openai\n", "import ollama\n", "from dotenv import load_dotenv\n", "from IPython.display import display, Markdown, update_display\n", "\n", "# Load environment variables\n", "load_dotenv(override=True)\n", "\n", "# Set up OpenAI API key\n", "OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')\n", "if not OPENAI_API_KEY:\n", "    raise ValueError(\"Please set your OpenAI API key in environment variables.\")\n", "\n", "# Constants\n", "MODEL_GPT = \"gpt-4o-mini\"\n", "MODEL_LLAMA = \"llama3.2:3b\"\n", "\n", "# Prompt user for question (until input is provided)\n", "while True:\n", "    question = input(\"Hello, I am your personal technical tutor. Enter your question: \").strip()\n", "    if question:\n", "        break  # Proceed only if a valid question is entered\n", "    print(\"Question cannot be empty. Please enter a question.\")\n", "\n", "# Common user prompt\n", "user_prompt = f\"\"\"\n", "Please give a detailed explanation to the following question: {question}.\n", "Be less verbose.\n", "Provide a clear and concise explanation without unnecessary elaboration.\n", "\"\"\"\n", "\n", "# Common system prompt\n", "system_prompt = \"\"\"\n", "You are a helpful AI assistant that explains Python code in a clear and concise manner. Provide structured explanations and examples when necessary.\n", "Be less verbose.\n", "\"\"\"\n", "\n", "def ask_openai():\n", "    \"\"\"Gets response from OpenAI's GPT model with streaming.\"\"\"\n", "    print(\"\\n\\n\\n🚀🤖🚀 Response from OpenAI GPT-4o-mini 🚀🤖🚀\")\n", "    client = openai.OpenAI(api_key=OPENAI_API_KEY)\n", "    response_stream = client.chat.completions.create(\n", "        model=MODEL_GPT,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        stream=True\n", "    )\n", "    response = \"\"\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    for chunk in response_stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        response = response.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "        update_display(Markdown(response), display_id=display_handle.display_id)\n", "\n", "def ask_ollama():\n", "    \"\"\"Gets response from Ollama's Llama 3.2 model with streaming.\"\"\"\n", "    print(\"\\n\\n\\n🔥✨🔥 Response from Llama 3.2 🔥✨🔥\\n\")\n", "    response = ollama.chat(\n", "        model=MODEL_LLAMA,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "        ],\n", "        stream=True\n", "    )\n", "\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    full_text = \"\"\n", "    for chunk in response:\n", "        if \"message\" in chunk:\n", "                content = chunk[\"message\"][\"content\"] or \"\"\n", "                full_text += content\n", "                update_display(Markdown(full_text), display_id=display_handle.display_id)\n", "\n", "# Call the functions\n", "ask_openai()\n", "ask_ollama()\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}