{"cells": [{"cell_type": "code", "execution_count": null, "id": "fc3a96d1-eedf-4e3a-b3ce-151485c574b5", "metadata": {}, "outputs": [], "source": ["import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "id": "385dc3d5-f6ce-46d8-958e-83dc1150c24e", "metadata": {}, "outputs": [], "source": ["OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\n", "    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": null, "id": "21f7dacc-1fa8-491c-8e94-39238dae52b3", "metadata": {}, "outputs": [], "source": ["class Website:\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=HEADERS)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ca431e32-9191-4940-b62d-f25e8cbac627", "metadata": {}, "outputs": [], "source": ["web = Website(\"https://silviayomdesign.com/\")\n", "print(web.title)\n", "print(web.text)"]}, {"cell_type": "code", "execution_count": null, "id": "76475815-0dbc-451b-ab65-f7e2ea3aaa8a", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "3cf03913-f595-4817-8580-19b182c599de", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a very artistic graphic designer's website titled name {website.title}\"\n", "    user_prompt += \"\\nHer creativity of her works are as follow;\\\n", "please provide a short summary of her works in markdown. \\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "6f130cfe-756b-4df8-b1f0-6918956a6162", "metadata": {}, "outputs": [], "source": ["print(user_prompt_for(web))"]}, {"cell_type": "code", "execution_count": null, "id": "85d85b64-1452-408f-bfae-d27b52d7dfa7", "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt_for(web)}\n", "]"]}, {"cell_type": "code", "execution_count": null, "id": "36d66055-66d6-4123-b092-eceab055829d", "metadata": {}, "outputs": [], "source": ["payload = {\n", "    \"model\": MODEL,\n", "    \"messages\": messages,\n", "    \"stream\": False\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "163db8a9-b0eb-49f3-a5f2-1e74cf51c245", "metadata": {}, "outputs": [], "source": ["response = requests.post(OLLAMA_API, json=payload, headers=HEADERS)\n", "print(response.json()[\"message\"][\"content\"])"]}, {"cell_type": "code", "execution_count": null, "id": "afabfff5-81e5-4b61-aca9-6c19d3584b86", "metadata": {}, "outputs": [], "source": ["def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(web)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "b2e83b58-16fc-4049-8116-24a0cbb3635a", "metadata": {}, "outputs": [], "source": ["messages_for(web)"]}, {"cell_type": "code", "execution_count": null, "id": "05ed519a-514f-4ed8-b323-4f4817e1e1c6", "metadata": {}, "outputs": [], "source": ["import ollama\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = ollama.chat(\n", "        model=MODEL, \n", "        messages=messages\n", "    )\n", "    return response[\"message\"][\"content\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b182f686-0a3e-4959-9bfd-0a59d2befd4c", "metadata": {}, "outputs": [], "source": ["summarize(\"https://silviayomdesign.com/\")"]}, {"cell_type": "code", "execution_count": null, "id": "f4f1f807-28d4-4b8b-9698-9b90dcbac59f", "metadata": {}, "outputs": [], "source": ["def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "a988d29b-ed36-4a40-bd77-0f7d60a29ac3", "metadata": {}, "outputs": [], "source": ["display_summary(\"https://silviayomdesign.com/\")"]}, {"cell_type": "code", "execution_count": null, "id": "15e72eeb-1c35-4bb2-9596-6ff2546aa046", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}