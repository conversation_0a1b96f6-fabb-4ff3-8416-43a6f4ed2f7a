{"cells": [{"cell_type": "markdown", "id": "d15d8294-3328-4e07-ad16-8a03e9bbfdb9", "metadata": {}, "source": ["# YOUR FIRST LAB\n", "### Please read this section. This is valuable to get you prepared, even if it's a long read -- it's important stuff.\n", "\n", "## Your first Frontier LLM Project\n", "\n", "Let's build a useful LLM solution - in a matter of minutes.\n", "\n", "By the end of this course, you will have built an autonomous Agentic AI solution with 7 agents that collaborate to solve a business problem. All in good time! We will start with something smaller...\n", "\n", "Our goal is to code a new kind of Web Browser. Give it a URL, and it will respond with a summary. The Reader's Digest of the internet!!\n", "\n", "Before starting, you should have completed the setup for [PC](../SETUP-PC.md) or [Mac](../SETUP-mac.md) and you hopefully launched this jupyter lab from within the project root directory, with your environment activated.\n", "\n", "## If you're new to Jupyter Lab\n", "\n", "Welcome to the wonderful world of Data Science experimentation! Once you've used Jupyter Lab, you'll wonder how you ever lived without it. Simply click in each \"cell\" with code in it, such as the cell immediately below this text, and hit Shift+Return to execute that cell. As you wish, you can add a cell with the + button in the toolbar, and print values of variables, or try out variations.  \n", "\n", "I've written a notebook called [Guide to <PERSON><PERSON><PERSON>](Guide%20to%20Jupyter.ipynb) to help you get more familiar with Jupyter Labs, including adding Markdown comments, using `!` to run shell commands, and `tqdm` to show progress.\n", "\n", "## If you're new to the Command Line\n", "\n", "Please see these excellent guides: [Command line on PC](https://chatgpt.com/share/67b0acea-ba38-8012-9c34-7a2541052665) and [Command line on Mac](https://chatgpt.com/canvas/shared/67b0b10c93a081918210723867525d2b).  \n", "\n", "## If you'd prefer to work in IDEs\n", "\n", "If you're more comfortable in IDEs like VSCode or Pycharm, they both work great with these lab notebooks too.  \n", "If you'd prefer to work in VSCode, [here](https://chatgpt.com/share/676f2e19-c228-8012-9911-6ca42f8ed766) are instructions from an AI friend on how to configure it for the course.\n", "\n", "## If you'd like to brush up your Python\n", "\n", "I've added a notebook called [Intermediate Python](Intermediate%20Python.ipynb) to get you up to speed. But you should give it a miss if you already have a good idea what this code does:    \n", "`yield from {book.get(\"author\") for book in books if book.get(\"author\")}`\n", "\n", "## I am here to help\n", "\n", "If you have any problems at all, please do reach out.  \n", "I'm available through the platform, <NAME_EMAIL>, or at https://www.linkedin.com/in/eddonner/ if you'd like to connect (and I love connecting!)  \n", "And this is new to me, but I'm also trying out X/Twitter at [@edwarddonner](https://x.com/edwarddonner) - if you're on X, please show me how it's done 😂  \n", "\n", "## More troubleshooting\n", "\n", "Please see the [troubleshooting](troubleshooting.ipynb) notebook in this folder to diagnose and fix common problems. At the very end of it is a diagnostics script with some useful debug info.\n", "\n", "## If this is old hat!\n", "\n", "If you're already comfortable with today's material, please hang in there; you can move swiftly through the first few labs - we will get much more in depth as the weeks progress.\n", "\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Please read - important note</h2>\n", "            <span style=\"color:#900;\">The way I collaborate with you may be different to other courses you've taken. I prefer not to type code while you watch. Rather, I execute Jupyter Labs, like this, and give you an intuition for what's going on. My suggestion is that you carefully execute this yourself, <b>after</b> watching the lecture. Add print statements to understand what's going on, and then come up with your own variations. If you have a Github account, use this to showcase your variations. Not only is this essential practice, but it demonstrates your skills to others, including perhaps future clients or employers...</span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">Treat these labs as a resource</h2>\n", "            <span style=\"color:#f71;\">I push updates to the code regularly. When people ask questions or have problems, I incorporate it in the code, adding more examples or improved commentary. As a result, you'll notice that the code below isn't identical to the videos. Everything from the videos is here; but in addition, I've added more steps and better explanations, and occasionally added new models like DeepSeek. Consider this like an interactive book that accompanies the lectures.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business value of these exercises</h2>\n", "            <span style=\"color:#181;\">A final thought. While I've designed these notebooks to be educational, I've also tried to make them enjoyable. We'll do fun things like have LLMs tell jokes and argue with each other. But fundamentally, my goal is to teach skills you can apply in business. I'll explain business implications as we go, and it's worth keeping this in mind: as you build experience with models and techniques, think of ways you could put this into action at work today. Please do contact me if you'd like to discuss more or if you have ideas to bounce off me.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "markdown", "id": "6900b2a8-6384-4316-8aaa-5e519fca4254", "metadata": {}, "source": ["# Connecting to OpenAI (or Ollama)\n", "\n", "The next cell is where we load in the environment variables in your `.env` file and connect to OpenAI.  \n", "\n", "If you'd like to use free Ollama instead, please see the README section \"Free Alternative to Paid APIs\", and if you're not sure how to do this, there's a full solution in the solutions folder (day1_with_ollama.ipynb).\n", "\n", "## Troubleshooting if you have problems:\n", "\n", "Head over to the [troubleshooting](troubleshooting.ipynb) notebook in this folder for step by step code to identify the root cause and fix it!\n", "\n", "If you make a change, try restarting the \"Kernel\" (the python process sitting behind this notebook) by Kernel menu >> Restart Kernel and Clear Outputs of All Cells. Then try this notebook again, starting at the top.\n", "\n", "Or, contact me! Message me <NAME_EMAIL> and we will get this to work.\n", "\n", "Any concerns about API costs? See my notes in the README - costs should be minimal, and you can control it at every point. You can also use Ollama as a free alternative, which we discuss during Day 2."]}, {"cell_type": "code", "execution_count": null, "id": "7b87cadb-d513-4303-baee-a37b6f938e4d", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "019974d9-f3ad-4a8a-b5f9-0a3719aea2d3", "metadata": {}, "outputs": [], "source": ["openai = OpenAI(base_url=\"http://localhost:11434/v1\", api_key=\"ollama\")\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "markdown", "id": "442fc84b-0815-4f40-99ab-d9a5da6bda91", "metadata": {}, "source": ["# Let's make a quick call to a Frontier model to get started, as a preview!"]}, {"cell_type": "markdown", "id": "c951be1a-7f1b-448f-af1f-845978e47e2c", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business applications</h2>\n", "            <span style=\"color:#181;\">In this exercise, you experienced calling the Cloud API of a Frontier Model (a leading model at the frontier of AI) for the first time. We will be using APIs like OpenAI at many stages in the course, in addition to building our own LLMs.\n", "\n", "More specifically, we've applied this to Summarization - a classic Gen AI use case to make a summary. This can be applied to any business vertical - summarizing the news, summarizing financial performance, summarizing a resume in a cover letter - the applications are limitless. Consider how you could apply Summarization in your business, and try prototyping a solution.</span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Before you continue - now try yourself</h2>\n", "            <span style=\"color:#900;\">Use the cell below to make your own simple commercial example. Stick with the summarization use case for now. Here's an idea: write something that will take the contents of an email, and will suggest an appropriate short subject line for the email. That's the kind of feature that might be built into a commercial email tool.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "00743dac-0e70-45b7-879a-d7293a6f68a6", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"You're an AI assistant who suggests subject line for the given email content \\\n", "    by ignoring greetings, sign-offs, and other irrelevant text. You suggest 5 best subject lines, starting with best fitting\" \\\n", "\"\"\n", "user_prompt = \"\"\"\n", "    Suggest 3 subject lines for the given email content in markdown. \\\n", "    Give the fit percentage of each subject line as well. \\\n", "    Give tone of the mail, action items, purpose of the mail.\\n\\n\"\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\"\"\"Dear Sir/<PERSON>am,\n", "\n", "I am <PERSON><PERSON><PERSON>, currently pursuing my Online MCA from Lovely Professional University. I am writing this email to express my concern regarding the scheduling of the online classes for the current semester.\n", "\n", "During the time of admission, it was conveyed to us that the online classes for the program would be conducted on weekends to ensure that working professionals like me can easily manage their work and studies. However, to my surprise, the classes for this semester have been scheduled on weekdays, which is not convenient for students who are working or have businesses.\n", "\n", "As a working professional, I find it difficult to balance my job responsibilities and attend the classes regularly on weekdays. Similarly, there are many students who are facing a similar issue. Therefore, I would like to request you to kindly reschedule the classes and conduct them on weekends as was initially promised during the admission process.\n", "\n", "I believe that conducting the classes on weekends would help students like me to balance their work and studies in a better way, and would also result in better attendance and improved learning outcomes.\n", "\n", "I hope that my request would be taken into consideration, and appropriate steps would be taken to ensure that the classes are conducted on weekends as promised during the admission process.\n", "\n", "Thank you for your understanding.\n", "\n", "<PERSON><PERSON><PERSON>,\n", "\n", "<PERSON><PERSON><PERSON> \"\"\",\n", "\"\"\"Hi team,\n", "It is to inform you that i've studied computer science in my graduation i.e. bsc physical science eoth computer science, but still i'm seeing bridge courses i.e. ecap010 and acap011 in my timetable.\n", "Therefore, I knidly request you to look into this matter.\n", "\n", "Best Regards\n", "<PERSON><PERSON><PERSON>\n", "\"\"\",] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "responses = [openai.chat.completions.create(\n", "    model=\"llama3.2\",\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt+message},\n", "    ]\n", ") for message in messages\n", "]\n", "# Step 4: print the result\n", "responses = [response.choices[0].message.content for response in responses]\n", "for response in responses:\n", "    display(Markdown(response))"]}, {"cell_type": "markdown", "id": "36ed9f14-b349-40e9-a42c-b367e77f8bda", "metadata": {}, "source": ["## An extra exercise for those who enjoy web scraping\n", "\n", "You may notice that if you try `display_summary(\"https://openai.com\")` - it doesn't work! That's because OpenAI has a fancy website that uses Javascript. There are many ways around this that some of you might be familiar with. For example, Selenium is a hugely popular framework that runs a browser behind the scenes, renders the page, and allows you to query it. If you have experience with Se<PERSON><PERSON>, <PERSON><PERSON> or similar, then feel free to improve the Website class to use them. In the community-contributions folder, you'll find an example Selenium solution from a student (thank you!)"]}, {"cell_type": "markdown", "id": "eeab24dc-5f90-4570-b542-b0585aca3eb6", "metadata": {}, "source": ["# Sharing your code\n", "\n", "I'd love it if you share your code afterwards so I can share it with others! You'll notice that some students have already made changes (including a Selenium implementation) which you will find in the community-contributions folder. If you'd like add your changes to that folder, submit a Pull Request with your new versions in that folder and I'll merge your changes.\n", "\n", "If you're not an expert with git (and I am not!) then GPT has given some nice instructions on how to submit a Pull Request. It's a bit of an involved process, but once you've done it once it's pretty clear. As a pro-tip: it's best if you clear the outputs of your Jupyter notebooks (Edit >> Clean outputs of all cells, and then Save) for clean notebooks.\n", "\n", "Here are good instructions courtesy of an AI friend:  \n", "https://chatgpt.com/share/677a9cb5-c64c-8012-99e0-e06e88afd293"]}, {"cell_type": "code", "execution_count": null, "id": "175ca116", "metadata": {}, "outputs": [], "source": ["from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "from selenium.webdriver.chrome.service import Service\n", "from selenium.webdriver.support.ui import WebDriverWait\n", "from selenium.webdriver.support import expected_conditions as EC\n", "from selenium.webdriver.common.by import By\n", "from bs4 import BeautifulSoup\n", "from openai import OpenAI\n", "from IPython.display import Markdown, display\n", "import platform\n", "\n", "class JSWebsite:\n", "    def __init__(self, url, model=\"llama3.2\", headless=True, wait_time=5):\n", "        \"\"\"\n", "        @Param url: The URL of the website to scrape\n", "        @Param model: The model to use for summarization. Valid values are \"gpt-4o-mini\" and \"llama3.2\"\n", "        @Param headless: Whether to run the browser in headless mode\n", "        @Param wait_time: Additional seconds to wait for JavaScript content to load\n", "        \"\"\"\n", "        self.url = url\n", "        self.model = model\n", "        self.wait_time = wait_time\n", "        \n", "        # Validate model choice\n", "        assert model in [\"gpt-4o-mini\", \"llama3.2\"], f\"Invalid model: {model}. Valid models are 'gpt-4o-mini' and 'llama3.2'.\"\n", "        \n", "        # Initialize appropriate API client\n", "        if \"gpt\" in model:\n", "            self.openai = OpenAI()\n", "        <PERSON><PERSON> \"llama\" in model:\n", "            self.openai = OpenAI(base_url=\"http://localhost:11434/v1\", api_key=\"ollama\")\n", "        \n", "        # Set up Chrome options with platform-specific settings\n", "        options = Options()\n", "        \n", "        if headless:\n", "            # Use appropriate headless setting based on platform\n", "            if platform.system() == \"Darwin\":  # macOS\n", "                options.add_argument(\"--headless=chrome\")  # macOS-friendly headless mode\n", "            else:\n", "                options.add_argument(\"--headless=new\")  # Modern headless for other platforms\n", "                \n", "            # These settings help with headless JavaScript rendering\n", "            options.add_argument(\"--disable-web-security\")\n", "            options.add_argument(\"--allow-running-insecure-content\")\n", "            options.add_argument(\"--disable-setuid-sandbox\")\n", "            \n", "            # Add a user agent to look more like a real browser\n", "            # options.add_argument(\"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\")\n", "            # options.add_argument(\"--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.7103.49 Safari/537.36\")\n", "            options.add_argument(\"--user-agent=Mozilla/5.0 (Macintosh; Apple Silicon Mac OS X 14_3_1) AppleWebKit/605.1.15 (KHTML, like Gecko)  Chrome/136.0.7103.49 Safari/537.36\")\n", "\n", "\n", "        \n", "        options.add_argument(\"--disable-gpu\")\n", "        options.add_argument(\"--window-size=1920,1080\")\n", "        options.add_argument(\"--disable-blink-features=AutomationControlled\")\n", "        options.add_argument(\"--disable-infobars\")\n", "        options.add_argument(\"--disable-extensions\")\n", "        options.add_argument(\"--start-maximized\")\n", "        options.add_argument(\"--no-sandbox\")\n", "        options.add_argument(\"--disable-dev-shm-usage\")\n", "        \n", "        try:\n", "            # Initialize Chrome driver\n", "            driver = webdriver.Chrome(options=options)\n", "            driver.get(url)\n", "            \n", "            # Wait for the page to load\n", "            WebDriverWait(driver, 10).until(\n", "                EC.presence_of_element_located((By.TAG_NAME, \"body\"))\n", "            )\n", "            \n", "            # Get the page source and close the browser\n", "            html = driver.page_source\n", "            driver.quit()\n", "            \n", "            # Parse HTML with BeautifulSoup\n", "            soup = BeautifulSoup(html, 'html.parser')\n", "            self.title = soup.title.string if soup.title else \"No title found\"\n", "            \n", "            # Remove irrelevant elements\n", "            if soup.body:\n", "                for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                    irrelevant.decompose()\n", "                self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "                # Check if content is too short, which might indicate loading issues\n", "                if len(self.text.strip()) < 100:\n", "                    self.has_content_error = True\n", "                    print(\"Warning: Page content seems too short or empty\")\n", "                else:\n", "                    self.has_content_error = False\n", "            else:\n", "                self.text = \"No body content found\"\n", "                self.has_content_error = True\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing page: {e}\")\n", "            self.title = \"Error loading page\"\n", "            self.text = f\"Failed to process page: {str(e)}\"\n", "            self.has_content_error = True\n", "\n", "    def summarize(self):\n", "        \"\"\"Generate a summary of the website content using the specified AI model.\"\"\"\n", "        # Check if page was loaded with errors\n", "        if hasattr(self, 'has_content_error') and self.has_content_error:\n", "            self.summary = \"Cannot summarize due to page loading or content errors.\"\n", "            return self.summary\n", "            \n", "        try:\n", "            response = self.openai.chat.completions.create(\n", "                model=self.model,\n", "                messages=self.messages_for()\n", "            )\n", "            self.summary = response.choices[0].message.content\n", "            return self.summary\n", "        except Exception as e:\n", "            self.summary = f\"Error generating summary: {str(e)}\"\n", "            return self.summary\n", "\n", "    def messages_for(self):\n", "        \"\"\"Create the message structure for the AI model.\"\"\"\n", "        self.system_prompt = (\n", "            \"You are an assistant that analyzes the contents of a website \"\n", "            \"and provides a short summary, ignoring text that might be navigation related. \"\n", "            \"Respond in markdown.\"\n", "        )\n", "        return [\n", "            {\"role\": \"system\", \"content\": self.system_prompt},\n", "            {\"role\": \"user\", \"content\": self.user_prompt_for()}\n", "        ]\n", "\n", "    def display_summary(self):\n", "        \"\"\"Display the summary in markdown format.\"\"\"\n", "        if hasattr(self, 'summary'):\n", "            display(Markdown(self.summary))\n", "        else:\n", "            print(\"Please run the summarize() method first.\")\n", "\n", "    def user_prompt_for(self):\n", "        \"\"\"Create the user prompt for the AI model.\"\"\"\n", "        user_prompt = f\"You are looking at a website titled {self.title}\\n\"\n", "        user_prompt += (\n", "            \"The contents of this website is as follows; \"\n", "            \"please provide a short summary of this website in markdown. \"\n", "            \"If it includes news or announcements, then summarize these too.\\n\\n\"\n", "        )\n", "        user_prompt += self.text\n", "        return user_prompt\n", "\n", "\n", "# Example usage\n", "if __name__ == \"__main__\":\n", "    # Site to test\n", "    site = JSWebsite(\"https://openai.com\", model=\"llama3.2\", headless=True, wait_time=15)\n", "    \n", "    # Only attempt to summarize if there were no content errors\n", "    summary = site.summarize()\n", "    \n", "    # Display results\n", "    if hasattr(site, 'has_content_error') and site.has_content_error:\n", "        print(\"Skipped summarization due to page loading or content errors.\")\n", "        print(\"Try with headless=False to see what's happening in the browser.\")\n", "    else:\n", "        site.display_summary()"]}, {"cell_type": "code", "execution_count": null, "id": "102d19b6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 5}