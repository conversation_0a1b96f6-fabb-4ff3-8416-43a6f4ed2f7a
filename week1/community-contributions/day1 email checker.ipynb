{"cells": [{"cell_type": "code", "execution_count": null, "id": "82b3f7d7-a628-4824-b0b5-26c78b833b7f", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": null, "id": "7bb45eea-2ae0-4550-a9c8-fb42ff6a5f55", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key looks good!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a10c24ce-c334-4424-8a2d-ae79ad3eb393", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# working on assumption that this is OK"]}, {"cell_type": "code", "execution_count": null, "id": "d9dff1ca-4e0a-44ca-acd6-0bc4004ffc3c", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "# As you can probably tell I am a University lecturer who deals with some dreadful assessment submissions and have to email students telling them why they got the marks they did.\n", "# This AI Assisstemt would help immensely as we could write what we want to say and let the reviewer fix it for us !\n", "# It is based on the day1_email_reviewer notebook\n", "# MV\n", "\n", "system_prompt = \"You are an AI email reviewer that checks the content of emails sent out to higher education under graduate students. You must identify the meaning of the context in the text given, run a spell check in UK English and provide the subject line and email and rewrite to make the email more professional. and in the end of text, please provide the tone info.\"\n", "user_prompt = \"\"\"\n", "    Dear <PERSON>,\n", "You asked for the reasons why you received the marks that you did in your recently submitted assessment. I have looked over your submission again, bear in mind the fact that you are only 1 student out of a cohort of over 350 and have nagged me for a quick response, and your work was awful.\n", "You submitted work of an appalling standard and obvously did not actually put much work into your submission, you were givben the chance to have feedback on what you were going to submit but you could not be bothered to get this feedback.\n", "You did not bother to turn up to many of the lessons then submitted work with the most basic errors that anyone who had put the right level of effort into their studies would have been able to identify easily and not had such a low mark when they submitted. I think I put more work into marking this rubbish than you did in writing it.\n", "\n", "Best regards,\n", "<PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\":\"system\", \"content\": system_prompt},\n", "    {\"role\":\"user\", \"content\": user_prompt}\n", "    \n", "] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=messages\n", ")\n", "\n", "# Step 4: print the result\n", "\n", "display(Markdown(response.choices[0].message.content))"]}, {"cell_type": "code", "execution_count": null, "id": "5a65b65f-4b3f-41f5-894a-0f8e81f0ba27", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}