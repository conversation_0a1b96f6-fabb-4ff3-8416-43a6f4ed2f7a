{"cells": [{"cell_type": "code", "execution_count": null, "id": "fa4447be-7825-45d9-a6a5-ed41f2500533", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "MODEL = \"llama3.2\"\n", "\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\"\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]       \n", "\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = MODEL,\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))\n", "\n", "\n", "display_summary(\"https://esarijal.my.id\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}