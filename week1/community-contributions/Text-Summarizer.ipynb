{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1) Import Required Libraries \n", "\n", "import requests\n", "import gradio as gr\n", "\n", "# Deepseek only uses abstract summarization\n", "# This tool use DeepSeek API Endpoint\n", "\n", "# 2) Define the DeepSeek API Endpoint\n", "\n", "OLLAMA_URL = \"http://localhost:11434/api/generate\"\n", "\n", "# 3) Define the Summarization Function which can retrieve Information\n", "\n", "def summarize_text(text):\n", "    payload = {\n", "        \"model\": \"deepseek-r1\", #Here you can load whatever the model you have in your ollama(ex:deepseek-r1:1.5b,7b,8b,14b) I used 7b model here \n", "        \"prompt\": f\"Summarize the following text in **5 bullet points**:\\n\\n{text}\", #The prompt is here for tell commands for the llm to act \n", "        \"stream\": False  # Ensures the response is returned as a whole, not streamed\n", "    }\n", "\n", "    response = requests.post(OLLAMA_URL, json=payload) #Send Requests to deepseekAPI\n", "\n", "    if response.status_code == 200: #if server run correctly it return the result or it will give error\n", "        return response.json().get(\"response\", \"No summary generated.\")\n", "    else:\n", "        return f\"Error: {response.text}\"\n", "\n", "# 4) Create Gradio interface to design \n", "interface = gr.Interface(\n", "    fn=summarize_text,\n", "    inputs=gr.Textbox(lines=10, placeholder=\"Enter text to summarize\"),\n", "    outputs=gr.Textbox(label=\"Summarized Text\"),\n", "    #theme='NoCrypt/miku', #Theme for the Interface I used <PERSON><PERSON><PERSON> from HF \n", "    title=\"AI-Powered Text Summarizer\",\n", "    description=\"Enter a long text and DeepSeek AI will generate a concise summary.\"\n", ")\n", "\n", "# Launch the web app\n", "if __name__ == \"__main__\":\n", "    interface.launch()\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}