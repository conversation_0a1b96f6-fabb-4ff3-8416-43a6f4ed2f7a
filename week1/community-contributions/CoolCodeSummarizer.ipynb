{"cells": [{"cell_type": "markdown", "id": "0b15b939-593a-4ccc-89bd-0cee09fe2f12", "metadata": {}, "source": ["# Python Code Summarizer\n", "\n", "The Below code will summarize the python code and example it in details which can help codes better understand a forigen code."]}, {"cell_type": "code", "execution_count": null, "id": "8dcf353c-e4f2-4ce7-a3b5-71b29700a148", "metadata": {}, "outputs": [], "source": ["# Imports\n", "from IPython.display import Markdown, display\n", "import os\n", "import openai\n", "from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": null, "id": "111cf632-08e8-4246-a5bb-b56942789242", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')"]}, {"cell_type": "code", "execution_count": null, "id": "e4f5376f-5e6f-4d75-81bf-222e34bfe828", "metadata": {}, "outputs": [], "source": ["def read_code(**kwargs):\n", "    \"\"\"\n", "    You can pass two types of key word arguments to this function.\n", "    code_path= Path to your complex python code.\n", "    code= Passing raw python code.\n", "    \"\"\"\n", "    code_path = kwargs.get('code_path',None)\n", "    code_raw = kwargs.get('code',None)\n", "    \n", "    if code_path:\n", "        with open(code_path, 'r') as code_file:\n", "            code = code_file.read()\n", "        return (True, code)\n", "\n", "    if code_raw:\n", "        return (True, code_raw)\n", "\n", "    return (<PERSON><PERSON><PERSON>, <PERSON>)"]}, {"cell_type": "code", "execution_count": null, "id": "00743dac-0e70-45b7-879a-d7293a6f68a6", "metadata": {}, "outputs": [], "source": ["# Model Prompt\n", "system_prompt = (\n", "                \"You are a helpful assistant. The following input will be a Python code snippet. \"\n", "                \"Your task is to:\\n\\n\"\n", "                \"1. Summarize the overall purpose of the code.\\n\"\n", "                \"2. Explain the code line by line, describing what each line does and why it's written that way.\\n\"\n", "                \"3. Provide reasoning behind the code structure and logic to help novice Python developers understand the concepts better.\\n\\n\"\n", "                \"Use Markdown format in your response. Make the explanation beginner-friendly, using code blocks, bullet points, and headings where helpful.\"\n", "            ) \n", "# In a plot twist worthy of sci-fi, this prompt was written by ChatGPT...\n", "# to tell ChatGPT how to respond. We’ve officially entered the Matrix. 🤖🌀"]}, {"cell_type": "code", "execution_count": null, "id": "ed7d2447-32a9-4761-8b0a-b31814bee7e5", "metadata": {}, "outputs": [], "source": ["\n", "# Guess where I got this code from :)\n", "code_line = \"\"\"yeild from set(book.get(\"author)) for book  in books if book.get(\"author\"))\"\"\"\n", "is_code, raw_code = read_code(code=code_line)\n", "\n", "if is_code:\n", "    user_prompt = raw_code\n", "else:\n", "    print(\"Invalid Arguments\")"]}, {"cell_type": "code", "execution_count": null, "id": "d74a1a39-1c24-4d4b-bd49-0ca416377a93", "metadata": {}, "outputs": [], "source": ["def messages_for():\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "df6c2726-d0fb-4ab6-b13b-d047e8807558", "metadata": {}, "outputs": [], "source": ["def summarize():\n", "    \n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for()\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "8425144c-595e-4ad6-9801-3e8778d285c4", "metadata": {}, "outputs": [], "source": ["def display_summary():\n", "    summary = summarize()\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "744bffdd-ec3c-4b27-b126-81bf3e8c8295", "metadata": {}, "outputs": [], "source": ["display_summary()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}