{"cells": [{"cell_type": "raw", "id": "f64407a0-fda5-48f3-a2d3-82e80d320931", "metadata": {}, "source": ["### \"Career Well-Being Companion\" ###\n", "This project will gather feelings at the end of day from employee.\n", "Based on employee feelings provided as input, model will analyze feelings and provide suggestions and acknowledge with feelings employtee is going thru.\n", "Model even will ask employee \"Do you want more detailed resposne to cope up with your feelings?\".\n", "If employee agrees, model even replies with online courses, tools, meetups and other ideas for the well being of the employee.\n", "\n", "Immediate Impact: Professionals can quickly see value through insights or actionable suggestions.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2b30a8fa-1067-4369-82fc-edb197551e43", "metadata": {}, "outputs": [], "source": ["### Step 1: Emotional Check-in:\n", "\n", "# Input: User describes their feelings or workday.\n", "# LLM Task: Analyze the input for emotional tone and identify keywords (e.g., \"stress,\" \"boredom\").\n", "# Output: A summary of emotional trends.\n"]}, {"cell_type": "code", "execution_count": null, "id": "2b52469e-da81-42ec-9e6c-0c121ad349a7", "metadata": {}, "outputs": [], "source": ["print(\"I am your well being companion and end goal is to help you in your career.\\nI want to start by asking about your feelings, how was your day today.\\n\")\n", "print(\"I will do my best as well being companion to analyze your day and come up with the suggestions that might help you in your career and life. \\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "a6df2e2c-785d-4323-90f4-b49592ab33fc", "metadata": {}, "outputs": [], "source": ["how_was_day = \"\""]}, {"cell_type": "code", "execution_count": null, "id": "247e4a80-f634-4a7a-9f40-315f042be59c", "metadata": {}, "outputs": [], "source": ["how_was_day = input(\"How was your day today,can you describe about your day, what went well, what did not go well, what you did not like :\\n\")"]}, {"cell_type": "code", "execution_count": null, "id": "0faac2dd-0d53-431a-87a7-d57a6881e043", "metadata": {}, "outputs": [], "source": ["what_went_well = input(\"What went well for you , today?\")"]}, {"cell_type": "code", "execution_count": null, "id": "2c11628b-d14b-47eb-a97e-70d08ddf3364", "metadata": {}, "outputs": [], "source": ["what_went_bad = input(\"What did not go well, today?\")"]}, {"cell_type": "code", "execution_count": null, "id": "f64e34b4-f83a-4ae4-86bb-5bd164121412", "metadata": {}, "outputs": [], "source": ["how_was_day = how_was_day + what_went_well + what_went_bad\n", "print(how_was_day)"]}, {"cell_type": "code", "execution_count": null, "id": "c5fe08c4-4d21-4917-a556-89648eb543c7", "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import OpenAI\n", "from dotenv import load_dotenv\n", "import json\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": null, "id": "d6875d51-f33b-462e-85cb-a5d6a7cfb86e", "metadata": {}, "outputs": [], "source": ["#Initialize environment and constants:\n", "load_dotenv(override=True)\n", "\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "if api_key and api_key.startswith('sk-proj-') and len(api_key)>10:\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? Please visit the troubleshooting notebook!\")\n", "    \n", "MODEL = 'gpt-4o-mini'\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": 1, "id": "c12cf934-4bd4-4849-9e8f-5bb89eece996", "metadata": {}, "outputs": [], "source": ["### Step 2: From day spent and what went good, what went bad  => LLM will extract feelings, emotions from those unspoken words :)"]}, {"cell_type": "code", "execution_count": null, "id": "237d14b3-571e-4598-a57b-d3ebeaf81afc", "metadata": {}, "outputs": [], "source": ["system_prompt_for_emotion_check_in = \"You are a career well-being assistant. Your task is to analyze the user's emotional state based on their text input.\"\\\n", "\"Look for signs of stress, burnout, dissatisfaction, boredom, motivation, or any other emotional indicators related to work.\"\\\n", "\"Based on the input, provide a summary of the user's feelings and categorize them under relevant emotional states (e.g., ‘Burnout,’ ‘Boredom,’ ‘Stress,’ ‘Satisfaction,’ etc.).\"\\\n", "\"Your response should be empathetic and non-judgmental. Please summarize the list of feelings, emotions , those unspoken but unheard feelings you get it.\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "a205a6d3-b0d7-4fcb-9eed-f3a86576cd9f", "metadata": {}, "outputs": [], "source": ["def get_feelings(how_was_day):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages = [\n", "            {'role':'system','content': system_prompt_for_emotion_check_in},\n", "            {'role':'user', 'content': how_was_day}\n", "        ]\n", "    )\n", "    result = response.choices[0].message.content\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "45e152c8-37c4-4818-a8a0-49f1ea3c1b65", "metadata": {}, "outputs": [], "source": ["## <PERSON><PERSON> will give the feelings you have based on \"the day you had today\".\n", "print(get_feelings(how_was_day))\n"]}, {"cell_type": "code", "execution_count": null, "id": "4a62a385-4c51-42b1-ad73-73949e740e66", "metadata": {}, "outputs": [], "source": ["### Step 3: From those feelings, emotions ==> Get suggestions from LLM."]}, {"cell_type": "code", "execution_count": null, "id": "d856ca4f-ade9-4e6f-b540-2d07a70867c7", "metadata": {}, "outputs": [], "source": ["## Lets construct system prompt for LLM to get suggestions (from these feelings above).\n", "\n", "system_prompt_for_suggestion =\"You are a career well-being assistant.Provide a list of practical,actionable suggestions to help them improve their emotional state.\"\n", "\n", "system_prompt_for_suggestion+=\"The suggestions should be personalized based on their current feelings, and they should be simple, effective actions the user can take immediately.\"\\\n", "\"Include activities, tasks, habits, or approaches that will either alleviate stress, boost motivation, or help them reconnect with their work in a positive way.\"\\\n", "\"Be empathetic, non-judgmental, and encouraging in your tone.\\n\"\n", "system_prompt_for_suggestion += \"Request you to respond in JSON format. Below is example:\\n\"\n", "system_prompt_for_suggestion += '''\n", "{\n", "  \"suggestions\": [\n", "    {\n", "      \"action\": \"Take a short break\",\n", "      \"description\": \"Step away from your workspace for 5-10 minutes. Use this time to take deep breaths, stretch, or grab a drink. This mini-break can help clear your mind and reduce feelings of overwhelm.\"\n", "    },\n", "    {\n", "      \"action\": \"Write a quick journal entry\",\n", "      \"description\": \"Spend 5-10 minutes writing down your thoughts and feelings. Specify what's distracting you and what you appreciate about your personal life. This can help you process emotions and refocus on tasks.\"\n", "    },\n", "    {\n", "      \"action\": \"Set a small task goal\",\n", "      \"description\": \"Choose one manageable task to complete today. Break it down into smaller steps to make it less daunting. Completing even a small task can give you a sense of achievement and boost motivation.\"\n", "    }\n", "    ]\n", "}\n", "'''\n"]}, {"cell_type": "code", "execution_count": null, "id": "e9eee380-7fa5-4d21-9357-f4fc34d3368d", "metadata": {}, "outputs": [], "source": ["##  Lets build user prompt to ask LLM for the suggestions based on the feelings above.\n", "## Note: Here while building user_prompt, we are making another LLM call (via function get_feelings() to get feelings analyzed from \"day spent\".\n", "## Because first step is to get feelings from day spent then we move to offer suggestions to ease discomfort feelings.\n", "\n", "def get_user_prompt_for_suggestion(how_was_day):\n", "    user_prompt_for_suggestion = \"You are a career well-being assistant.Please see below user’s emotional input on 'day user had spent' and this user input might have feeling burnt out, bored, uninspired, or stressed or sometime opposite \"\\\n", "    \"of these feelings.\"\n", "    user_prompt_for_suggestion += f\"{get_feelings(how_was_day)}\"\n", "    return user_prompt_for_suggestion\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "3576e451-b29c-44e1-bcdb-addc8d61afa7", "metadata": {}, "outputs": [], "source": ["print(get_user_prompt_for_suggestion(how_was_day))"]}, {"cell_type": "code", "execution_count": null, "id": "4a41ee40-1f49-4474-809f-a0d5e44e4aa4", "metadata": {}, "outputs": [], "source": ["def get_suggestions(how_was_day):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages = [\n", "            {'role': 'system', 'content':system_prompt_for_suggestion},\n", "            {'role': 'user', 'content': get_user_prompt_for_suggestion(how_was_day)}\n", "        ],\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", "    result = response.choices[0].message.content\n", "    return json.loads(result)\n", "    #display(Markdown(result))"]}, {"cell_type": "code", "execution_count": null, "id": "33e3a14e-0e2c-43cb-b50b-d6df52b4d300", "metadata": {}, "outputs": [], "source": ["suggestions = get_suggestions(how_was_day)\n", "print(suggestions)"]}, {"cell_type": "code", "execution_count": null, "id": "31c75e04-2800-4ba2-845b-bc38f8965622", "metadata": {}, "outputs": [], "source": ["### Step 4: From those suggestions from companion ==> Enhance with support you need to follow sugestions like action plan for your self."]}, {"cell_type": "code", "execution_count": null, "id": "d07f9d3f-5acf-4a86-9160-4c6de8df4eb0", "metadata": {}, "outputs": [], "source": ["system_prompt_for_enhanced_suggestions = \"You are a helpful assistant that enhances actionable suggestions for users. For each suggestion provided, enhance it by adding:\\n\"\\\n", "\"1. A step-by-step guide for implementation.\"\\\n", "\"2. Tools, resources, or apps that can help.\"\\\n", "\"3. Examples or additional context to make the suggestion practical.\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "6ab449f1-7a6c-4982-99e0-83d99c45ad2d", "metadata": {}, "outputs": [], "source": ["def get_user_prompt_for_enhanced_suggestions(suggestions):\n", "     prompt = \"You are able to check below suggestions and can enhance to help end user. Below is the list of suggestions.\\n\"\n", "     prompt += f\"{suggestions}\"\n", "     return prompt"]}, {"cell_type": "code", "execution_count": null, "id": "d5187b7a-d8cd-4377-b011-7805bd50443d", "metadata": {}, "outputs": [], "source": ["def enhance_suggestions(suggestions):\n", "    stream = openai.chat.completions.create(\n", "        model = MODEL,\n", "        messages=[\n", "            {'role':'system', 'content':system_prompt_for_enhanced_suggestions},\n", "            {'role':'user', 'content':get_user_prompt_for_enhanced_suggestions(suggestions)}\n", "        ],\n", "        stream = True\n", "    )\n", "    \n", "    #result = response.choices[0].message.content\n", "    #for chunk in stream:\n", "    #    print(chunk.choices[0].delta.content or '', end='')\n", "\n", "    response = \"\"\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        response = response.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "        update_display(Markdown(response), display_id=display_handle.display_id)\n", "    \n", "    #display(Markdown(result))\n"]}, {"cell_type": "code", "execution_count": null, "id": "429cd6f8-3215-4140-9a6d-82d14a9b9798", "metadata": {}, "outputs": [], "source": ["detailed = input(\"\\nWould you like a DETAILED PLAN for implementing this suggestion?(Yes/ No)\")"]}, {"cell_type": "code", "execution_count": null, "id": "5efda045-5bde-4c51-bec6-95b5914102dd", "metadata": {}, "outputs": [], "source": ["if detailed.lower() == 'yes':\n", "    enhance_suggestions(suggestions)\n", "else:\n", "    print(suggestions)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "1969b2ec-c850-4dfc-b790-8ae8e3fa36e9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}