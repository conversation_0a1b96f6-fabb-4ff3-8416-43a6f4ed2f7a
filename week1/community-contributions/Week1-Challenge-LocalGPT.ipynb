{"cells": [{"cell_type": "markdown", "id": "87c2da09-bd0c-4683-828b-4f7643018795", "metadata": {}, "source": ["# Community contribution\n", "\n", "Implementing simple ChatGPT interface to maintain conversation and context with sleected model"]}, {"cell_type": "code", "execution_count": 26, "id": "77a850ed-61f8-4a0d-9c41-45781eb60bc9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key looks good so far\n"]}], "source": ["import os\n", "from dotenv import load_dotenv\n", "import ipywidgets as widgets\n", "from IPython.display import Markdown, display, update_display, clear_output\n", "from openai import OpenAI\n", "\n", "load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if api_key and api_key.startswith('sk-proj-') and len(api_key)>10:\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? Please visit the troubleshooting notebook!\")\n", "    \n", "MODEL = 'gpt-4o-mini'\n"]}, {"cell_type": "code", "execution_count": null, "id": "1f7f16f0-6fec-4190-882a-3fe1f0e9704a", "metadata": {}, "outputs": [], "source": ["class ChatGPTInterface:\n", "    def __init__(self, api_key, model, system_message=\"You are a helpful assistant. You can format your responses using Markdown.\"):\n", "        self.openai = OpenAI(api_key=api_key)\n", "        self.model = model\n", "        self.conversation_history = [{\"role\": \"system\", \"content\": system_message}]\n", "\n", "        self.chat_area = widgets.Output()\n", "        self.input_box = widgets.Text(placeholder=\"Enter your message here...\")\n", "        self.send_button = widgets.Button(description=\"Send\")\n", "        self.clear_button = widgets.Button(description=\"Clear\")\n", "\n", "        self.send_button.on_click(self.send_message)\n", "        self.clear_button.on_click(self.clear_chat)\n", "\n", "        self.layout = widgets.VBox([\n", "            self.chat_area,\n", "            widgets.HBox([self.input_box, self.send_button, self.clear_button])\n", "        ])\n", "\n", "    def display(self):\n", "        display(self.layout)\n", "\n", "    def send_message(self, _):\n", "        user_message = self.input_box.value.strip()\n", "        if user_message:\n", "            self.conversation_history.append({\"role\": \"user\", \"content\": user_message})\n", "            self.display_message(\"You\", user_message)\n", "            self.input_box.value = \"\"\n", "\n", "            try:\n", "                response = self.openai.chat.completions.create(\n", "                    model=self.model,\n", "                    messages=self.conversation_history\n", "                )\n", "                assistant_message = response.choices[0].message.content.strip()\n", "                self.conversation_history.append({\"role\": \"assistant\", \"content\": assistant_message})\n", "                self.display_message(\"ChatGPT\", assistant_message)\n", "            except Exception as e:\n", "                self.display_message(\"Error\", str(e))\n", "\n", "    def clear_chat(self, _):\n", "        self.conversation_history = [{\"role\": \"system\", \"content\": self.conversation_history[0][\"content\"]}]\n", "        self.chat_area.clear_output(wait=True)\n", "\n", "    def display_message(self, sender, message):\n", "        self.chat_area.append_display_data(Markdown(f\"**{sender}:**\\n{message}\"))\n"]}, {"cell_type": "code", "execution_count": 28, "id": "78287e42-8964-4da6-bd48-a7dffd0ce7dd", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "54956535cb32419bbe38d2bee125992d", "version_major": 2, "version_minor": 0}, "text/plain": ["VBox(children=(Output(), HBox(children=(Text(value='', placeholder='Enter your message here...'), <PERSON><PERSON>(descr…"]}, "metadata": {}, "output_type": "display_data"}], "source": ["chat_interface = ChatGPTInterface(api_key,MODEL)\n", "chat_interface.display()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}