{"cells": [{"cell_type": "code", "execution_count": null, "id": "0ee39d65-f27d-416d-8b46-43d15aebe752", "metadata": {}, "outputs": [], "source": ["# Below is a sample for email reviewer using Bahasa Indonesia. "]}, {"cell_type": "code", "execution_count": null, "id": "f9fd62af-9b14-490b-8d0b-990da96101bf", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"<PERSON><PERSON> ad<PERSON> se<PERSON>g Asisten untuk menganalisa email berdasarkan user prompt yang nanti akan diberikan. Summarize the email and give me a tone about that email\"\n", "user_prompt = \"\"\"\n", "    Subject: <PERSON><PERSON><PERSON><PERSON>\n", "\n", "<PERSON> ter<PERSON><PERSON>,\n", "\n", "<PERSON>a ingin meminta waktu Anda untuk membahas Generative AI untuk bisnis. Apakah Anda tersedia pada besok pukul 19:00? \n", "<PERSON><PERSON> t<PERSON>, mohon beri tahu waktu yang lebih sesuai bagi <PERSON>a.\n", "\n", "<PERSON><PERSON> kasih atas per<PERSON>ian <PERSON>.\n", "\n", "Salam,\n", "\n", "<PERSON><PERSON><PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages\n", "    )\n", "\n", "# Step 4: print the result\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "d10208fa-02d8-41a0-b9bb-0bf30f237f25", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"<PERSON><PERSON> ad<PERSON> se<PERSON>g Asisten untuk menganalisa email berdasarkan user prompt yang nanti akan diberikan. Summarize the email and give me a tone about that email\"\n", "user_prompt = \"\"\"\n", "    Subject: Feedback terkait Bapak\n", "\n", "<PERSON> ter<PERSON><PERSON>,\n", "\n", "<PERSON>a ingin member<PERSON>n sedikit feedback untuk BBapak.\n", "\n", "Kemampuan Anda dalam memimpin tim ini mampu membawa saya dan rekan lainnya untuk mengerahkan semua kemampuan saya agar jadi lebih baik.\n", "<PERSON><PERSON>a ini saya cukup senang bekerja dengan Anda karena memberikan saya peluang untuk mencoba banyak hal baru. Tapi ada beberapa kekhawatiran yang mau saya samp<PERSON>kan, terutama terkait target yang perlu dicapai oleh tim. <PERSON>a pikir melihat performa ke belakang, target yang ditentukan harus lebih realistis lagi.\n", "<PERSON>a beruntung bisa berkesempatan bekerja dengan Anda sehingga banyak ilmu yang saya dapat. Kira-kira untuk ke depannya, hal apa lagi yang bisa tim ini tingkatkan agar kita bisa mencapai target yang lebih baik?\n", "<PERSON><PERSON><PERSON> ini, banyak terjadi miskomunikasi dalam pekerjaan. <PERSON> menurut saya salah satunya karena arahan yang Anda berikan kurang jelas dan kurang ditangkap sepenuhnya oleh anggota yang lain. Saya dan tim berharap ke depan bisa mendapatkan arahan yang lebih jelas dan satu arah.\n", "\n", "<PERSON><PERSON> kasih atas per<PERSON>ian <PERSON>.\n", "\n", "Salam,\n", "\n", "<PERSON><PERSON><PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt}\n", "    ] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages\n", "    )\n", "\n", "# Step 4: print the result\n", "\n", "print(response.choices[0].message.content)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}