{"cells": [{"cell_type": "markdown", "id": "bfa3abd0-4e66-4117-96f9-7a71fbb6d0cb", "metadata": {}, "source": ["# Powerpoint Slides Summarizer\n", "\n", "This converts a Power Point presentation into notes that a student can easily skim through.\n", "\n", "Concepts Used:\n", "- Converting Contents of PPT to text via python-pptx\n", "- User and System Prompts\n", "- Use of Open AI GPT-4o-mini via API key\n"]}, {"cell_type": "code", "execution_count": null, "id": "ab95eb49-6a2d-4c7d-9057-78a2cd9364cc", "metadata": {}, "outputs": [], "source": ["!pip install python-pptx"]}, {"cell_type": "code", "execution_count": null, "id": "62715f16-7125-455e-98e7-5705871c0e4a", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "ff42eab7-789d-44f8-a5cc-64baeebf3224", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": null, "id": "bce425c2-6d19-4c03-93ce-8930dabc61ee", "metadata": {}, "outputs": [], "source": ["# creating an instance\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "c0c75e30-3b38-4a89-b7d3-a41a6f5dc650", "metadata": {}, "outputs": [], "source": ["from pptx import Presentation\n", "\n", "class PowerPoint():\n", "    def __init__(self,ppt):\n", "        \"\"\"\n", "        Creates a PowerPoint object, with name and text.\n", "        \"\"\"\n", "        self.ppt = ppt\n", "        self.title = os.path.basename(ppt)\n", "        self.text = self.extract_text()\n", "\n", "    def extract_text(self):\n", "        \"\"\"\n", "        Extracts text from powerpoint.\n", "        \"\"\"\n", "        prs = Presentation(self.ppt)\n", "        text_content = []\n", "    \n", "        for slide in prs.slides:\n", "            for shape in slide.shapes:\n", "                if hasattr(shape, \"text\"):\n", "                    text_content.append(shape.text)\n", "    \n", "        return \"\\n\".join(text_content)\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "1963a055-87f4-4e47-8456-cac4d4ac57fc", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents \\\n", "of a PowerPoint presentation, and provides a summary in the style of \\\n", "a cheat-sheet, for students to easily learn key concepts from.\\\n", "You are to ignore text that might be navigation-related\\\n", "and respond in Markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "ca600e90-7d3f-4fc7-a698-1b8f2925f81e", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of PowerPoints:\n", "\n", "def user_prompt_for(powerpoint):\n", "    user_prompt = f\"You are looking at a website titled {powerpoint.title}\"\n", "    user_prompt += \"\\nThe contents of this powerpoint are as follows; \\\n", "please provide a summary of the content in markdown. \\\n", "If it includes a question bank, add that along with short answers too.\\n\\n\"\n", "    user_prompt += powerpoint.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "4fe19c56-9940-4528-b43a-c86798b215d2", "metadata": {}, "outputs": [], "source": ["def messages_for(powerpoint):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(powerpoint)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "f7704da5-90b0-40af-bbb4-7d589309f180", "metadata": {}, "outputs": [], "source": ["# And now: call the OpenAI API. \n", "\n", "def summarize(powerpoint_path):\n", "    powerpoint = PowerPoint(powerpoint_path)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(powerpoint)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "49d1d0cf-fa4b-4bea-bd68-a834145070ef", "metadata": {}, "outputs": [], "source": ["def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "348078d1-e86f-4eb3-909d-33ab4ede984e", "metadata": {}, "outputs": [], "source": ["ppt_file = \"Theoretical Perspectives on Media and Technology.pptx\" \n", "display_summary(ppt_file)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}