{"cells": [{"cell_type": "markdown", "id": "227e221d-cb4c-4b52-9c4f-2bcff51b00a5", "metadata": {}, "source": ["# This exercise is to test and try generating images using gpt. Note: This API is more expensive."]}, {"cell_type": "code", "execution_count": null, "id": "dddabc12-ce06-45c1-875c-ab7e32b94e10", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": null, "id": "ef28b0bd-f11f-4b2a-88b4-112f932c9132", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "d8f1af3b-c748-41f0-95f3-e21f512e7539", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "markdown", "id": "2319710e-10a4-4964-acec-276ad43442c0", "metadata": {}, "source": ["# Setup done. Below code is for image generation"]}, {"cell_type": "code", "execution_count": null, "id": "77d07d13-b2d0-4402-94a6-02a46632ac8e", "metadata": {}, "outputs": [], "source": ["pip show openai"]}, {"cell_type": "code", "execution_count": null, "id": "1b28e163-7518-4b18-b1a7-c6a6f5b7f62c", "metadata": {}, "outputs": [], "source": ["# client = openai.OpenAI()\n", "\n", "response = openai.images.generate(\n", "    model=\"gpt-image-1\",  # or \"dall-e-2\"\n", "    prompt=\"realistic peaceful sunset\",\n", "    size=\"1024x1024\",\n", "    quality=\"high\",  # or \"hd\" (for DALL·E 3 only, costs more)\n", "    n=1\n", ")\n", "\n", "# image_url = response.data[0].url\n", "# print(image_url)"]}, {"cell_type": "code", "execution_count": null, "id": "b121b843-680f-4abd-9aaa-1b3eb9393541", "metadata": {}, "outputs": [], "source": ["import base64\n", "\n", "image_base64 = response.data[0].b64_json\n", "image_bytes = base64.b64decode(image_base64)\n", "\n", "# Save the image to a file\n", "with open(\"genimage.png\", \"wb\") as f:\n", "    f.write(image_bytes)"]}, {"cell_type": "code", "execution_count": null, "id": "75b24ef1-c779-490a-a763-5bb8ede8903b", "metadata": {}, "outputs": [], "source": ["from IPython.display import Image\n", "Image(filename='genimage.png') "]}, {"cell_type": "code", "execution_count": null, "id": "b4b6a4a4-88ff-40ea-9434-6a667939d800", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}