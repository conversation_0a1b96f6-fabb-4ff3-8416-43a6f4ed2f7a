{"cells": [{"cell_type": "markdown", "id": "2112166e-3629-4167-a4cb-0a1a6e549e97", "metadata": {}, "source": ["# Hello everyone, \n", "The community contributions folder is super motivating. Thanks to <PERSON> for democratising learning with this great idea of sharing. The below small piece is my novice attempt in summarizing content from wikipedia page. It is pretty straightforward, but a good learning exercise for me nevertheless. "]}, {"cell_type": "code", "execution_count": null, "id": "947028c8-30c6-456a-8e0c-25e0de1ecbb6", "metadata": {}, "outputs": [], "source": ["!pip install wikipedia"]}, {"cell_type": "code", "execution_count": null, "id": "aa18a060-6dbe-42c9-bc11-c8b079397d6b", "metadata": {}, "outputs": [], "source": ["# Import statements\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "import wikipedia\n", "import warnings"]}, {"cell_type": "code", "execution_count": null, "id": "8d9c128d-ed7d-4e58-8cd1-1468242c7967", "metadata": {}, "outputs": [], "source": ["#To supress a warning from wikipedia module when there are multiple options.\n", "warnings.filterwarnings(\"ignore\", category=UserWarning, module=\"wikipedia\")"]}, {"cell_type": "code", "execution_count": null, "id": "5371f405-e628-4b6a-a5ab-5774c1431749", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": null, "id": "e6610504-bd7b-459f-9722-0044b3101e05", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the troubleshooting notebook, or try the below line instead:\n", "# openai = OpenAI(api_key=\"your-key-here-starting-sk-proj-\")"]}, {"cell_type": "code", "execution_count": null, "id": "ac37741a-2608-4760-8ba8-163fb9155f0f", "metadata": {}, "outputs": [], "source": ["class Wikipedia:\n", "    def __init__(self, searchText):\n", "        \"\"\"\n", "        Create this object to extract the summary of wikipedia page for a text entered by user\n", "        \"\"\"\n", "        self.searchText = searchText\n", "        self.summary_text = None\n", "        self.user_prompt = None\n", "        \n", "        self._fetch_summary()\n", "\n", "    def _fetch_summary(self):\n", "        \"\"\"\n", "        Fetches the summary from wikipedia page based on user entered search text and sets user prompt accordingly\n", "        \"\"\"\n", "        try:\n", "            # Try to get the summary of the text from Wikipedia based on user entered text. Using starightforward summary module in wikipedia.\n", "            self.summary_text = wikipedia.summary(self.searchText)\n", "            self.user_prompt = f\"You are looking a summary extract from a wikipedia page. The content is as follows\\n {self.summary_text}.\\nProvide \\\n", "            a summary taking key points from each sections listed on the page\"\n", "        except wikipedia.DisambiguationError as e:\n", "            #Modify user and system prompts if there are multiple options for a user search text\n", "            self.user_prompt = f\"You have received quite a few options {e.options} for the keyword {self.searchText}. Please request user to choose one of them\"\n", "        except wikipedia.PageError:\n", "            #To handle when there is no page\n", "            self.user_prompt = f\"There is no wiki page for {self.searchText}. Apparently it is not your fault!\"\n", "        except Exception as e:\n", "            # To handle any other exceptions\n", "            self.user_prompt = f\"Sorry, something seems to be wrong on my end. Please try again later\""]}, {"cell_type": "code", "execution_count": null, "id": "143c203e-bb99-49c6-89a2-2a32ea429719", "metadata": {}, "outputs": [], "source": ["# Our by-now familiar sumamrize function\n", "def summarize(searchText):\n", "    wiki = Wikipedia(searchText)\n", "    system_prompt = f\"You are an assitant trying to summarize content from Wikipedia. You will have three scenarios to handle your responses \\\n", "        1. You will have the summary text content and you will just show that to user\\\n", "        2. You will have multiple options for the user entered keyword, and you will respond by asking user to choose from that and request again \\\n", "        3. You will not have the content due to a page not found error. Respond accordingly.\\\n", "        Respond all of these in Markdown format.\"\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": wiki.user_prompt}\n", "    ]\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages\n", "    )\n", "    return response.choices[0].message.content\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b61532fc-189c-4cd8-9402-93d8d8fa8c59", "metadata": {}, "outputs": [], "source": ["summary = summarize(\"mukhari\")\n", "display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "5c3f05f6-acb5-41e4-a521-8d8b8ace0192", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}