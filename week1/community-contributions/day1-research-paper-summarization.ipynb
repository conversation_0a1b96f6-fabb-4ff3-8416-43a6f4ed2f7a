{"cells": [{"cell_type": "markdown", "id": "1b8f7ac7-7089-427a-8f63-57211da7e691", "metadata": {}, "source": ["## Summarizing Research Papers"]}, {"cell_type": "code", "execution_count": null, "id": "641d5c00-ff09-4697-9c87-5de5df1469f8", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": null, "id": "1a6a2864-fd9d-43e2-b0ca-1476c0153077", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "340e3166-5aa7-4bcf-9cf0-e2fc776dc322", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "73198fb7-581f-42ac-99a6-76c56c86248d", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Paper:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3b39c3ad-d238-418e-9e6a-55a4fd717ebc", "metadata": {}, "outputs": [], "source": ["#Insert Paper URL\n", "res = Paper(\" \")"]}, {"cell_type": "code", "execution_count": null, "id": "83bc1eec-4187-4c6c-b188-3f72564351f1", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"You are a research paper summarizer. You take the url of the research paper and extract the following:\n", "1) Title and Author of the research paper.\n", "2) Year it was published it\n", "3) Objective or aim of the research to specify why the research was conducted\n", "4) Background or Introduction to explain the need to conduct this research or any topics the readers must have knowledge about\n", "5) Type of research/study/experiment to explain what kind of research it is.\n", "6) Methods or methodology to explain what the researchers did to conduct the research\n", "7) Results and key findings to explain what the researchers found\n", "8) Conclusion tells about the conclusions that can be drawn from this research including limitations and future direction\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "4aba1b51-9a72-4325-8c86-3968b9d3172e", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(paper):\n", "    user_prompt = f\"You are looking at a website titled {paper.title}\"\n", "    user_prompt += \"\\nThe contents of this paper is as follows; \\\n", "please provide a short summary of this paper in markdown. \\\n", "If it includes additional headings, then summarize these too.\\n\\n\"\n", "    user_prompt += paper.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "659cb3c4-8a02-493d-abe7-20da9219e358", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "def messages_for(paper):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(paper)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "08ea1193-1bbb-40de-ba64-d02ffe109372", "metadata": {}, "outputs": [], "source": ["messages_for(res)"]}, {"cell_type": "code", "execution_count": null, "id": "e07d00e7-1b87-4ca8-a69d-4a206e34a2b2", "metadata": {}, "outputs": [], "source": ["# And now: call the OpenAI API. You will get very familiar with this!\n", "\n", "def summarize(url):\n", "    paper = Paper(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(paper)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "5c12df95-1700-47ee-891b-96b0a7227bdd", "metadata": {}, "outputs": [], "source": ["# A function to display this nicely in the Ju<PERSON><PERSON> output, using markdown\n", "\n", "def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "05cff05f-2b74-44a4-9dbd-57c08f8f56cb", "metadata": {"scrolled": true}, "outputs": [], "source": ["# Insert Paper URL in the quotes below\n", "display_summary(\" \")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}