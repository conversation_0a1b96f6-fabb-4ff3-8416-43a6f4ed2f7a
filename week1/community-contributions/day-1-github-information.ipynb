{"cells": [{"cell_type": "code", "execution_count": null, "id": "4d011f3d-c10c-4a75-bd36-576e383a8d1d", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": null, "id": "c51302e0-c848-4ec4-a0ab-03deeb9e7987", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if not api_key:\n", "    print('No Api Key was found')\n", "elif not api_key.startswith('sk-proj-'):\n", "    print(\"An api key was found, but it doesnt start with sk-proj\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An api key was found, but it might have space in the first or end\")\n", "else:\n", "    print(\"Api key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": null, "id": "d1df04f3-bd4d-4b14-87cc-1e91eaf7c0ab", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "340b018a-6e97-491c-aa26-66c683ece8a0", "metadata": {}, "outputs": [], "source": ["message = \"Hello GP<PERSON>, this is my first message\"\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=[{\"role\": \"user\", \"content\":message}])\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "4a06c291-2fe6-4669-a8b6-3b67769eb3fa", "metadata": {}, "outputs": [], "source": ["headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "dd36b141-a252-44a8-8fa4-d4c2c33d3db9", "metadata": {}, "outputs": [], "source": ["github = Website(\"https://github.com/Fikriraihan\")\n", "print(github.title)\n", "print(github.text)"]}, {"cell_type": "code", "execution_count": null, "id": "ea402ba2-6c7f-4f96-95c0-d68a0e96e644", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are a skilled GitHub profile analyzer. \" \\\n", "\"Your job is to take the provided GitHub profile or repository URL and generate a clear, structured summary covering these points: \" \\\n", "\"1️⃣ **Profile Summary** \" \\\n", "\"- <PERSON><PERSON><PERSON> \" \\\n", "\"- Bio (if available) \" \\\n", "\"- Total public repositories \" \\\n", "\"- Total followers \" \\\n", "\"- Total stars received (sum across repos) \" \\\n", "\"- Top programming languages (by repo count) \" \\\n", "\"2️⃣ **Repository Highlights** (top 3 by stars or activity) \" \\\n", "\"For each: \" \\\n", "\"- Repository name \" \\\n", "\"- Description \" \\\n", "\"- Primary language \" \\\n", "\"- Star count \" \\\n", "\"- Last updated date \" \\\n", "\"- Notable technologies or frameworks used \" \\\n", "\"3️⃣ **Overall Assessment** \" \\\n", "\"- What does this user specialize in? \" \\\n", "\"- Are they more focused on personal projects or collaborations? \" \\\n", "\"- Any standout strengths or skills you notice? \" \\\n", "\"4️⃣ **Recommendations** \" \\\n", "\"- Suggest one area or technology they could explore next to grow. \" \\\n", "\"- Suggest one improvement to make their GitHub profile more appealing. \" \\\n", "\"Be concise, insightful, and encourage the user’s growth. \" \\\n", "\"If some data is missing, state it clearly instead of guessing.\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "a964e8f2-40f4-457b-9c81-7e6e2768f450", "metadata": {}, "outputs": [], "source": ["def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a github named {website.title}\"\n", "    user_prompt += \"\\nThe contents of this github is as follows; \\\n", "please provide a summary of this website in markdown.\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "026d8ae4-1aea-45b9-b694-db0809527780", "metadata": {}, "outputs": [], "source": ["system_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "2e040916-8d7e-421b-b1a7-56e710940eaa", "metadata": {}, "outputs": [], "source": ["print(user_prompt_for(github))"]}, {"cell_type": "code", "execution_count": null, "id": "11bc74b0-7ca7-40da-81cc-84b2dd04780b", "metadata": {}, "outputs": [], "source": ["messages_for(github)"]}, {"cell_type": "code", "execution_count": null, "id": "e64f497f-3742-4d70-9e15-29d1974b3361", "metadata": {}, "outputs": [], "source": ["def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "95d0938d-0b26-4253-94a6-ac9240e7a8c9", "metadata": {}, "outputs": [], "source": ["summarize(\"https://github.com/Fikriraihan\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd863db3-731a-46d8-ac14-f74f8ae39bd4", "metadata": {}, "outputs": [], "source": ["def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "70c5c3aa-2c06-460b-9c4f-6465d2c8611c", "metadata": {}, "outputs": [], "source": ["display_summary(\"https://github.com/Fikriraihan\")"]}, {"cell_type": "code", "execution_count": null, "id": "f3dfe6e3-dfd2-4acd-a2e4-681873c650c8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}