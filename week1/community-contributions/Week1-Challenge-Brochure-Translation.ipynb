{"cells": [{"cell_type": "markdown", "id": "5787d599-798e-4161-a473-970e8d948db3", "metadata": {}, "source": ["# Community contribution\n", "\n", "Generating a sports brochure - and then in Spanish! Many thanks for the contribution."]}, {"cell_type": "code", "execution_count": null, "id": "4ed9b1c0-50dc-48ea-b1b6-6be3264255b6", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "import json\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "23cc579a-43eb-44b5-8105-8ec3d46ec029", "metadata": {}, "outputs": [], "source": ["# Initialize and constants\n", "\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "MODEL = 'gpt-4o-mini'\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "0206cf1d-00c5-401d-8aa0-88a83aeb8c83", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "class Website:\n", "    url: str\n", "    title: str\n", "    body: str\n", "    links: List[str]\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "e1ab939b-0a81-4301-8820-abdc1b740a86", "metadata": {}, "outputs": [], "source": ["caps = Website(\"https://www.nhl.com/capitals/\")\n", "print(caps.get_contents())"]}, {"cell_type": "code", "execution_count": null, "id": "6bc9763c-e8cb-47f4-a5f0-fccbdb34f5f9", "metadata": {}, "outputs": [], "source": ["print(caps.links)"]}, {"cell_type": "code", "execution_count": null, "id": "11a407e0-6d23-4199-bb58-344352178978", "metadata": {}, "outputs": [], "source": ["link_system_prompt = \"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the team, \\\n", "such as links to an About page, Team, News, Schedule, History, Stats pages.\\n\"\n", "link_system_prompt += \"You should respond in JSON as in this example:\"\n", "link_system_prompt += \"\"\"\n", "{\n", "    \"links\": [\n", "        {\"type\": \"about page\", \"url\": \"https://full.url/goes/here/about\"},\n", "        {\"type\": \"team page\", \"url\": \"https://full.url/goes/here/team\"},\n", "        {\"type\": \"news page\": \"url\": \"https://another.full.url/news\"},\n", "        {\"type\": \"schedule page\": \"url\": \"https://another.full.url/schedule\"},\n", "        {\"type\": \"history page\": \"url\": \"https://another.full.url/history\"},\n", "        {\"type\": \"stats page\": \"url\": \"https://another.full.url/stats\"},\n", "        {\"type\": \"standings page\": \"url\": \"https://another.full.url/standings\"},\n", "    ]\n", "}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b45c01f5-e27d-47d6-b9e7-7500940da3c0", "metadata": {}, "outputs": [], "source": ["def get_links_user_prompt(website):\n", "    user_prompt = f\"Here is the list of links on the website of {website.url} - \"\n", "    user_prompt += \"please decide which of these are relevant web links for a brochure about the team, respond with the full https URL in JSON format. \\\n", "Do not include Terms of Service, Privacy, Tickets, Video, Listen, Community, Fans, Youth Hockey, Shop, League, email links.\\n\"\n", "    user_prompt += \"Links (some might be relative links):\\n\"\n", "    user_prompt += \"\\n\".join(website.links)\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "9dbfc365-b3c7-45d2-bd6d-79efb2372f43", "metadata": {}, "outputs": [], "source": ["print(get_links_user_prompt(caps))"]}, {"cell_type": "code", "execution_count": null, "id": "410c113b-a5b2-4639-aace-2203a8631dbc", "metadata": {}, "outputs": [], "source": ["def get_links(url):\n", "    website = Website(url)\n", "    completion = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": link_system_prompt},\n", "            {\"role\": \"user\", \"content\": get_links_user_prompt(website)}\n", "      ],\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", "    result = completion.choices[0].message.content\n", "    return json.loads(result)"]}, {"cell_type": "code", "execution_count": null, "id": "b8a5d809-c85e-4a97-9a81-61114b635027", "metadata": {}, "outputs": [], "source": ["get_links(\"https://www.nhl.com/capitals/\")"]}, {"cell_type": "code", "execution_count": null, "id": "ffb4dbdf-758c-4180-aa4f-3c18899493eb", "metadata": {}, "outputs": [], "source": ["def get_all_details(url):\n", "    result = \"Landing page:\\n\"\n", "    result += Website(url).get_contents()\n", "    links = get_links(url)\n", "    print(\"Found links:\", links)\n", "    for link in links[\"links\"]:\n", "        result += f\"\\n\\n{link['type']}\\n\"\n", "        result += Website(link[\"url\"]).get_contents()\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "b7546e12-c819-4092-a783-7be23d4d7b8c", "metadata": {}, "outputs": [], "source": ["print(get_all_details(\"https://www.nhl.com/capitals/\"))"]}, {"cell_type": "code", "execution_count": null, "id": "3d9aa466-e68d-4119-95e4-10893d774ebf", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are a sports marketing analyst that analyzes the contents of several relevant pages from a sports team website \\\n", "and creates a short brochure about the team for prospective fans and players to recruit. Respond in markdown.\\\n", "Include details of team history, team culture, team news, and team stats if you have the information.\"\n", "\n", "# Or uncomment the lines below for a more humorous brochure - this demonstrates how easy it is to incorporate 'tone':\n", "\n", "# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short humorous, entertaining, jokey brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\""]}, {"cell_type": "code", "execution_count": null, "id": "07097603-5196-47cc-9e61-b412c14b62c6", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure of the team in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:40_000] # Truncate if more than 40,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "36d42b59-0a96-41b7-b9fc-02ae37659f7a", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "    )\n", "    result = response.choices[0].message.content\n", "    display(Markdown(result))"]}, {"cell_type": "code", "execution_count": null, "id": "6e642228-092a-4bf5-a3c2-1ab78822453a", "metadata": {}, "outputs": [], "source": ["create_brochure(\"Washington Capitals\", \"https://www.nhl.com/capitals\")"]}, {"cell_type": "code", "execution_count": null, "id": "1ccee810-3a11-48ec-8a69-d12efcbeb1cd", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "d392b8a2-9d1a-40bb-a8c1-5b88ad7e463d", "metadata": {}, "source": ["# Translate to Spanish"]}, {"cell_type": "code", "execution_count": null, "id": "7bc08bb5-c02e-4685-bbd1-5afa084b7f18", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure in spanish of the team in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:40_000] # Truncate if more than 40,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "a40a2437-499a-4665-8d45-8241705477d6", "metadata": {}, "outputs": [], "source": ["create_brochure(\"Washington Capitals\", \"https://www.nhl.com/capitals\")"]}, {"cell_type": "code", "execution_count": null, "id": "36f5d6d7-952c-4d50-894c-9b01e29cebc8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}