{"cells": [{"cell_type": "code", "execution_count": null, "id": "39e3e763-9b00-49eb-aead-034a2d0517a7", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "# If you get an error running this cell, then please head over to the troubleshooting notebook!"]}, {"cell_type": "code", "execution_count": null, "id": "f3bb5e2a-b70f-42ba-9f22-030a9c6bc9d1", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "994f51fb-eab3-45a2-847f-87aebb92b17a", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "\n", "# If this doesn't work, try Kernel menu >> Restart Kernel and Clear Outputs Of All Cells, then run the cells from the top of this notebook down.\n", "# If it STILL doesn't work (horrors!) then please see the Troubleshooting notebook in this folder for full instructions"]}, {"cell_type": "code", "execution_count": null, "id": "a8125c6d-c884-4f65-b477-cab155e29ce3", "metadata": {}, "outputs": [], "source": ["# Step 1: Create your prompts\n", "\n", "system_prompt = \"You are an AI that suggests short and relevant subject lines for emails based on their content.\"\n", "user_prompt = \"\"\"\n", "Here is the content of an email:\n", "\n", "Dear Team,\n", "\n", "I hope you're all doing well. I wanted to remind you that our next project meeting is scheduled for this Friday at 3 PM. We will be discussing our progress and any blockers. Please make sure to review the latest updates before the meeting.\n", "\n", "Best,  \n", "<PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt}] # fill this in\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages=messages\n", ")\n", "\n", "# Step 4: print the result\n", "\n", "print(\"Suggested Subject Line:\", response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": null, "id": "1010ac80-1ee8-432f-aa3f-12af419dc23a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}