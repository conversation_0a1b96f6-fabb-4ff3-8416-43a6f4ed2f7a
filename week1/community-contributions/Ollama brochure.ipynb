{"cells": [{"cell_type": "code", "execution_count": 52, "id": "b56a950c-db41-4575-bef9-0fa651dea363", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "import json\n", "import ollama\n", "from typing import List\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display,clear_output\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "0ec875db-0f6a-4eec-a3b6-eae4b71a4b89", "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": null, "id": "227cd07c-98a4-463b-94ad-94e33d04944b", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "4d5c5e40-c010-4102-8359-899f988185fb", "metadata": {}, "outputs": [], "source": ["ed = Website(\"https://edwarddonner.com\")\n", "ed.links"]}, {"cell_type": "code", "execution_count": null, "id": "5f0b5d71-487c-47a5-ace6-8e02465ed452", "metadata": {}, "outputs": [], "source": ["link_system_prompt = \"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the company, \\\n", "such as links to an About page, or a Company page, or Careers/Jobs pages.\\n\"\n", "link_system_prompt += \"You should respond in JSON as in this example:\"\n", "link_system_prompt += \"\"\"\n", "{\n", "    \"links\": [\n", "        {\"type\": \"about page\", \"url\": \"https://full.url/goes/here/about\"},\n", "        {\"type\": \"careers page\": \"url\": \"https://another.full.url/careers\"}\n", "    ]\n", "}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "c6550325-5160-42c9-b7e7-980b504cd096", "metadata": {}, "outputs": [], "source": ["print(link_system_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "2db4ccc6-5c35-4775-a5b2-4b86e4c73808", "metadata": {}, "outputs": [], "source": ["def get_links_user_prompt(website):\n", "    user_prompt = f\"Here is the list of links on the website of {website.url} - \"\n", "    user_prompt += \"please decide which of these are relevant web links for a brochure about the company, respond with the full https URL in JSON format. \\\n", "Do not include Terms of Service, Privacy, email links.\\n\"\n", "    user_prompt += \"Links (some might be relative links):\\n\"\n", "    user_prompt += \"\\n\".join(website.links)\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "8af511c7-5a74-4d1a-b763-b31370e70cff", "metadata": {}, "outputs": [], "source": ["print(get_links_user_prompt(ed))"]}, {"cell_type": "code", "execution_count": null, "id": "a3b7fb61-ca15-4eab-b017-b0fe5cce46fd", "metadata": {}, "outputs": [], "source": ["def get_links(url):\n", "    website = Website(url)\n", "    response = ollama.chat(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": link_system_prompt},\n", "            {\"role\": \"user\", \"content\": get_links_user_prompt(website)}\n", "        ], format = \"json\"  #Define format as json!\n", "    )\n", "    result = response['message']['content']\n", "\n", "    return json.loads(result)\n"]}, {"cell_type": "code", "execution_count": null, "id": "7816d393-620d-4c53-913e-4ec130b2baba", "metadata": {}, "outputs": [], "source": ["# Anthropic has made their site harder to scrape, so I'm using HuggingFace..\n", "\n", "anthropic = Website(\"https://anthropic.com\")\n", "anthropic.links"]}, {"cell_type": "code", "execution_count": null, "id": "f32ceccb-1d45-41a3-a5c1-fb2e6cd76afe", "metadata": {}, "outputs": [], "source": ["get_links(\"https://anthropic.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "a7ec4727-e897-473c-a657-e74f6999c974", "metadata": {}, "outputs": [], "source": ["def get_all_details(url):\n", "    result = \"Landing page:\\n\"\n", "    result += Website(url).get_contents()\n", "    links = get_links(url)\n", "    print(\"Found links:\", links)\n", "    for link in links[\"links\"]:\n", "        result += f\"\\n\\n{link['type']}\\n\"\n", "        result += Website(link[\"url\"]).get_contents()\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "7acde0c5-1af2-4e8e-9303-e2a98ec9cdbb", "metadata": {"scrolled": true}, "outputs": [], "source": ["print(get_all_details(\"https://anthropic.com\"))"]}, {"cell_type": "code", "execution_count": null, "id": "5a2e2b1d-eb55-4bfb-bf55-5e8c87db0d96", "metadata": {}, "outputs": [], "source": ["system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "and creates a short brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "Include details of company culture, customers and careers/jobs if you have the information.\"\n", "\n", "# Or uncomment the lines below for a more humorous brochure - this demonstrates how easy it is to incorporate 'tone':\n", "\n", "# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short humorous, entertaining, jokey brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\""]}, {"cell_type": "code", "execution_count": null, "id": "8eac1719-7f94-4460-bc4a-0c9c93bb17a5", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure of the company in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:5_000] # Truncate if more than 5,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "e2e312f6-01c5-4e57-9134-fb4aa447d155", "metadata": {}, "outputs": [], "source": ["get_brochure_user_prompt(\"Anthropic\", \"https://anthropic.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "8b05cbab-f0d2-4a9e-8b8c-c868a036e9cd", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    response = ollama.chat(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "        ]\n", "    )\n", "    result = response[\"message\"][\"content\"]\n", "    display(Markdown(result))\n"]}, {"cell_type": "code", "execution_count": null, "id": "91ede0c0-daf2-42ef-9d31-749afb9d5352", "metadata": {}, "outputs": [], "source": ["create_brochure(\"Anthropic\", \"https://anthropic.com\")"]}, {"cell_type": "markdown", "id": "afb4aeee-5108-42a7-a1c1-5bad254b7e8b", "metadata": {}, "source": ["# Final omprovement\n", "\n", "getting a typewriter animation"]}, {"cell_type": "code", "execution_count": 50, "id": "177de611-1cb1-49e2-b7ea-8d01191af3ee", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "    ]\n", "\n", "    display_markdown = display(Markdown(\"\"), display_id=True)  # Initialize Markdown display\n", "    response_text = \"\"\n", "\n", "    for chunk in ollama.chat(model=MODEL, messages=messages, stream=True):  # Ensure stream=True (not a string)\n", "        response_text += chunk['message']['content']\n", "        clear_output(wait=True)  # Clear previous output to create a streaming effect\n", "        display_markdown.update(Markdown(response_text))  # Update Markdown dynamically\n"]}, {"cell_type": "code", "execution_count": 53, "id": "a1971d81-fc7f-4ed1-97a0-7ef5e8ed332a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found links: {'links': [{'type': 'About page', 'url': 'https://www.anthropic.com/company'}, {'type': 'Careers page', 'url': 'https://www.anthropic.com/careers'}, {'type': 'Company page', 'url': 'https://www.anthropic.com/'}, {'type': 'Research page', 'url': 'https://www.anthropic.com/research'}, {'type': 'Twitter profile', 'url': 'https://twitter.com/AnthropicAI'}, {'type': 'LinkedIn company page', 'url': 'https://www.linkedin.com/company/anthropicresearch'}, {'type': 'YouTube channel', 'url': 'https://www.youtube.com/@anthropic-ai'}]}\n"]}, {"data": {"text/markdown": ["**Anthropic <PERSON>**\n", "======================\n", "\n", "**Mission Statement**\n", "-------------------\n", "\n", "Anthropic is an AI safety and research company dedicated to building reliable, interpretable, and steerable AI systems that benefit humanity in the long run.\n", "\n", "**Company Overview**\n", "--------------------\n", "\n", "Anthropic is headquartered in San Francisco and brings together a diverse team of researchers, engineers, policy experts, and business leaders with experience spanning various disciplines. Our mission is to conduct frontier AI research, develop and apply safety techniques, and deploy the resulting systems via partnerships and products.\n", "\n", "**Research Focus**\n", "-----------------\n", "\n", "Anthropic conducts cutting-edge AI research across various modalities, exploring novel and emerging safety research areas such as interpretability, RL from human feedback, policy, and societal impacts analysis. Our research aims to advance the field of AI safety and inform our product development.\n", "\n", "**Product Portfolio**\n", "---------------------\n", "\n", "Our flagship product is Claude, a highly intelligent AI model that enables customers to build custom applications and experiences using our API. We also offer various enterprise solutions, including Claude for Enterprise, designed to meet the needs of large organizations.\n", "\n", "**Customer Base**\n", "-----------------\n", "\n", "Anthropic serves a diverse range of customers, including businesses, nonprofits, civil society groups, and their clients around the globe. Our commitment to safety and reliability has earned us a reputation as a trusted partner in the AI industry.\n", "\n", "**Values and Culture**\n", "----------------------\n", "\n", "At Anthropic, we value:\n", "\n", "*   **Acting for the global good**: We strive to make decisions that maximize positive outcomes for humanity in the long run.\n", "*   **Holding light and shade**: We acknowledge the potential risks of AI and approach our work with caution and transparency.\n", "\n", "**Join Our Team**\n", "-----------------\n", "\n", "We're a collaborative team of researchers, engineers, policy experts, and business leaders passionate about building safer AI systems. Join us to be part of this exciting journey and contribute your skills and expertise to shaping the future of AI.\n", "\n", "**Careers**\n", "------------\n", "\n", "Check our website for open roles and learn more about our company culture, benefits, and career opportunities.\n", "\n", "[Learn <PERSON>](link)\n", "\n", "**Get in Touch**\n", "-----------------\n", "\n", "Stay up-to-date with the latest news and announcements from Anthropic. Follow us on Twitter, LinkedIn, or YouTube to join the conversation and stay informed.\n", "\n", "[Twitter](link)\n", "[LinkedIn](link)\n", "[YouTube](link)"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["create_brochure(\"Anthropic\", \"https://anthropic.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "c33277a4-84f1-447c-a66e-eb7e2af42d2a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}