{"cells": [{"cell_type": "markdown", "id": "9ae10427-6ca2-4ac0-b6a0-e9206dd3cb52", "metadata": {}, "source": ["### Using OpenAI gpt-4o-mini model to generate social media posts for events"]}, {"cell_type": "code", "execution_count": 10, "id": "477fe060-a11f-424f-bac4-34c5121cf437", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 11, "id": "61f012e5-cdba-48cb-ae74-df9659c23d90", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key found and looks good so far!\n"]}], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv()\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "# Check the key\n", "\n", "if not api_key:\n", "    print(\"No API key was found - please head over to the troubleshooting notebook in this folder to identify & fix!\")\n", "elif not api_key.startswith(\"sk-proj-\"):\n", "    print(\"An API key was found, but it doesn't start sk-proj-; please check you're using the right key - see troubleshooting notebook\")\n", "elif api_key.strip() != api_key:\n", "    print(\"An API key was found, but it looks like it might have space or tab characters at the start or end - please remove them - see troubleshooting notebook\")\n", "else:\n", "    print(\"API key found and looks good so far!\")"]}, {"cell_type": "code", "execution_count": 12, "id": "19c79615-57aa-40e0-a83b-891f43df4f65", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": 13, "id": "68ad05f8-dfcc-47b1-ba16-b35bedeff48b", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": 14, "id": "acff6c95-77a5-40f0-bf9f-7d47cec987fc", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\"},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]\n", "\n", "# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt\n", "    \n", "# Generate a summary of content fetched by scraping the website\n", "\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model = \"gpt-4o-mini\",\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 15, "id": "b43f8cda-8a61-4773-83b2-bb8fe55a0cb2", "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Twitter Post:**  \n", "🚀 Join us online for #StartupMastery on Jan 7, 6-9 PM GMT! Explore Lean Startup, Agile, & Design Thinking methodologies. Gain practical skills, access resources, and earn a certificate! Tickets from €74.98. Don't miss out! 🎟️🌟\n", "\n", "**Instagram Post:**  \n", "🌟 Ready to boost your startup skills? Join us for **Startup Mastery**! 💡 On January 7 from 6 PM to 9 PM GMT, dive into Lean Startup, Agile, and Design Thinking with top-notch experts. Access recorded sessions, worksheets, and get certified! 🎟️ Tickets from €74.98. See you online! 🚀✨ #StartupMastery #LeanStartup #Agile #DesignThinking\n", "\n", "**Facebook Post:**  \n", "🗓️ Exciting opportunity for entrepreneurs and startup enthusiasts! Attend our **Startup Mastery** online workshop on January 7, from 6 PM to 9 PM GMT. Learn about Lean Startup, Agile, and Design Thinking methodologies to enhance your startup journey. Enjoy a transformative experience with insights on MVP development, rapid prototyping, and feedback loops. Plus, you'll get access to recorded sessions and can earn a certificate! Limited tickets available from €74.98. Organizers: Lean Agile Zone. Don’t miss out! 🚀 #StartupMastery #Entrepreneurship"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Step 1: Create your prompts\n", "WEBSITE_LINK = \"https://www.eventbrite.ie/e/startup-mastery-leveraging-lean-startup-agile-and-design-thinking-tickets-920474252267?aff=ebdssbcategorybrowse&keep_tld=1\"\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of an event \\\n", "and provides short summaries for a Twitter post, an instagram post and a facebook post.\\\n", "Ensure the summaries abide by the platform rules for each of the platforms.\"\n", "\n", "website_summary = summarize(WEBSITE_LINK)\n", "user_prompt = f\"The events details are as follows: {website_summary}. Please summarize the above. Capture details like time and location, please capture them as well.\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt},\n", "]\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=messages)\n", "\n", "# Step 4: print the result\n", "\n", "display(Markdown(response.choices[0].message.content))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}