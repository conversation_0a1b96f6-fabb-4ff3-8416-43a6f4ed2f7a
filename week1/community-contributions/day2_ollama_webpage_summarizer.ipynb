{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": 2, "id": "29ddd15d-a3c5-4f4e-a678-873f56162724", "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": 3, "id": "dac0a679-599c-441f-9bf2-ddc73d35b940", "metadata": {}, "outputs": [], "source": ["# Create a messages list using the same format that we used for OpenAI\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Describe some of the business applications of Generative AI\"}\n", "]"]}, {"cell_type": "code", "execution_count": 4, "id": "7bb9c624-14f0-4945-a719-8ddb64f66f47", "metadata": {}, "outputs": [], "source": ["payload = {\n", "        \"model\": MODEL,\n", "        \"messages\": messages,\n", "        \"stream\": False\n", "    }"]}, {"cell_type": "code", "execution_count": 5, "id": "479ff514-e8bd-4985-a572-2ea28bb4fa40", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠋ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠙ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠹ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠸ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠼ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠴ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠦ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠧ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠇ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠏ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠋ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠙ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠹ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠸ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest ⠼ \u001b[K\u001b[?25h\u001b[?2026l\u001b[?2026h\u001b[?25l\u001b[1Gpulling manifest \u001b[K\n", "pulling dde5aa3fc5ff: 100% ▕██████████████████▏ 2.0 GB                         \u001b[K\n", "pulling 966de95ca8a6: 100% ▕██████████████████▏ 1.4 KB                         \u001b[K\n", "pulling fcc5a6bec9da: 100% ▕██████████████████▏ 7.7 KB                         \u001b[K\n", "pulling a70ff7e570d9: 100% ▕██████████████████▏ 6.0 KB                         \u001b[K\n", "pulling 56bb8bd477a5: 100% ▕██████████████████▏   96 B                         \u001b[K\n", "pulling 34bb5ab01051: 100% ▕██████████████████▏  561 B                         \u001b[K\n", "verifying sha256 digest \u001b[K\n", "writing manifest \u001b[K\n", "success \u001b[K\u001b[?25h\u001b[?2026l\n"]}], "source": ["# Let's just make sure the model is loaded\n", "\n", "!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": 6, "id": "42b9f644-522d-4e05-a691-56e7658c0ea9", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generative AI has numerous business applications across various industries, including:\n", "\n", "1. **Content Creation**: Generative AI can be used to create high-quality content such as articles, social media posts, product descriptions, and more. This helps businesses save time and resources on content creation while maintaining consistency and quality.\n", "2. **Marketing Automation**: Generative AI can generate personalized marketing materials, such as email templates, ad copy, and product descriptions, based on customer data and behavior.\n", "3. **Customer Service Chatbots**: Generative AI-powered chatbots can provide 24/7 support to customers, answering common questions and routing complex issues to human representatives.\n", "4. **Product Design**: Generative AI can help designers create new products, such as 3D models, prototypes, and even entire product lines, using machine learning algorithms to optimize design parameters.\n", "5. **Virtual Assistants**: Generative AI-powered virtual assistants can be integrated into businesses' IT systems to automate tasks, provide personalized recommendations, and offer customer support.\n", "6. **Data Analysis**: Generative AI can help analyze large datasets, identify patterns, and make predictions about future trends and outcomes.\n", "7. **Supply Chain Optimization**: Generative AI can optimize supply chain operations by predicting demand, managing inventory, and optimizing logistics.\n", "8. **Sales Forecasting**: Generative AI can analyze historical sales data, market trends, and external factors to predict future sales performance and identify areas for improvement.\n", "9. **Creative Writing**: Generative AI can be used to generate creative content such as poetry, music, or even entire scripts for films and TV shows.\n", "10. **Music Generation**: Generative AI can create original music tracks, beats, or melodies based on user input or style preferences.\n", "11. **Image and Video Generation**: Generative AI can create realistic images and videos that can be used in various applications such as advertising, product photography, or even entertainment.\n", "12. **Language Translation**: Generative AI-powered language translation tools can help businesses communicate with customers and clients who speak different languages.\n", "\n", "Some notable companies that are leveraging Generative AI for business applications include:\n", "\n", "* Google (Google DeepMind)\n", "* Amazon (Amazon SageMaker)\n", "* Microsoft (Microsoft Azure Machine Learning)\n", "* IBM (IBM Watson Studio)\n", "* Salesforce (Salesforce Einstein)\n", "\n", "These applications of Generative AI can help businesses gain a competitive edge, improve efficiency, and enhance customer experiences.\n"]}], "source": ["# If this doesn't work for any reason, try the 2 versions in the following cells\n", "# And double check the instructions in the 'Recap on installation of Ollama' at the top of this lab\n", "# And if none of that works - contact me!\n", "\n", "response = requests.post(OLLAMA_API, json=payload, headers=HEADERS)\n", "print(response.json()['message']['content'])"]}, {"cell_type": "markdown", "id": "6a021f13-d6a1-4b96-8e18-4eae49d876fe", "metadata": {}, "source": ["# Introducing the ollama package\n", "\n", "And now we'll do the same thing, but using the elegant ollama python package instead of a direct HTTP call.\n", "\n", "Under the hood, it's making the same call as above to the ollama server running at localhost:11434"]}, {"cell_type": "code", "execution_count": 7, "id": "7745b9c4-57dc-4867-9180-61fa5db55eb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generative AI has numerous business applications across various industries. Here are some examples:\n", "\n", "1. **Content Creation**: Generative AI can be used to generate high-quality content such as articles, social media posts, product descriptions, and even entire books. This can help businesses reduce the time and cost associated with content creation.\n", "2. **Product Design**: Generative AI can be used to design new products, such as jewelry, fashion items, or household goods. This can help businesses quickly prototype and test new designs without the need for extensive human involvement.\n", "3. **Marketing and Advertising**: Generative AI can be used to generate personalized ads, product recommendations, and even entire marketing campaigns. This can help businesses tailor their marketing efforts to specific customer segments.\n", "4. **Customer Service Chatbots**: Generative AI can be used to create chatbots that can understand and respond to customer inquiries in a more human-like way. This can help businesses provide better customer service without the need for human agents.\n", "5. **Data Analysis and Visualization**: Generative AI can be used to analyze large datasets and generate visualizations, such as charts and graphs, that can help businesses gain insights into their data.\n", "6. **Predictive Maintenance**: Generative AI can be used to predict when equipment is likely to fail, allowing businesses to schedule maintenance and reduce downtime.\n", "7. **Personalized Recommendations**: Generative AI can be used to generate personalized product recommendations based on customer behavior and preferences.\n", "8. **Music Composition**: Generative AI can be used to compose music for various applications, such as film scores, advertisements, or even entire albums.\n", "9. **Image and Video Generation**: Generative AI can be used to generate high-quality images and videos that can be used in various business contexts, such as product photography or marketing materials.\n", "10. **Supply Chain Optimization**: Generative AI can be used to optimize supply chain operations, such as predicting demand, managing inventory, and identifying bottlenecks.\n", "\n", "Some specific industries where generative AI is being applied include:\n", "\n", "* **Finance**: Generative AI can be used to analyze financial data, generate investment recommendations, and even create personalized financial plans.\n", "* **Healthcare**: Generative AI can be used to analyze medical images, generate diagnostic reports, and even develop personalized treatment plans.\n", "* **Education**: Generative AI can be used to create personalized learning plans, generate educational content, and even grade student assignments.\n", "\n", "These are just a few examples of the many business applications of generative AI. As the technology continues to evolve, we can expect to see even more innovative uses across various industries.\n"]}], "source": ["import ollama\n", "\n", "response = ollama.chat(model=MODEL, messages=messages)\n", "print(response['message']['content'])"]}, {"cell_type": "markdown", "id": "a4704e10-f5fb-4c15-a935-f046c06fb13d", "metadata": {}, "source": ["## Alternative approach - using OpenAI python library to connect to Ollama"]}, {"cell_type": "code", "execution_count": 8, "id": "23057e00-b6fc-4678-93a9-6b31cb704bff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generative AI has numerous business applications across various industries. Here are some examples:\n", "\n", "1. **Content Creation**: Generative AI can be used to automate content creation, such as generating news articles, product descriptions, and social media posts. This can help businesses save time and resources while maintaining consistency in their content.\n", "2. **Digital Marketing**: Generative AI can be used to optimize online ads, generate ad copy, and create personalized email campaigns. It can also help analyze customer data and predict their behavior, enabling more effective marketing strategies.\n", "3. **Design and Prototyping**: Generative AI can be used to generate designs for products, such as product labels, packaging, and branding materials. It can also create prototypes and simulations, reducing the need for physical prototyping and iterative design processes.\n", "4. **Creative Writing and Storytelling**: Generative AI can be used to co-create stories, articles, and blog posts with human writers, helping to generate ideas, outlines, and even entire pieces of content.\n", "5. **Music Composition and Generation**: Generative AI can be used to compose music, generate sound effects, and create personalized playlists. This can help businesses like music streaming services and content creators generate original content without having to rely on human composers.\n", "6. **Image and Video Generation**: Generative AI can be used to create high-quality images and videos for various applications, including advertising, media production, and film and television studios.\n", "7. **Predictive Analytics and Risk Analysis**: Generative AI can be used to analyze large datasets, identify patterns, and predict outcomes, helping businesses make informed decisions about investments, customers, products, and resource allocation.\n", "8. **Chat<PERSON>s and Virtual Assistants**: Generative AI can be used to create conversational interfaces that simulate human-like interactions, making it easier for businesses to engage with their customers, provide customer support, and automate routine tasks.\n", "9. **Materials Science and Product Development**: Generative AI can be used to design new materials, predict material behavior, and optimize product performance, enabling faster and more accurate product development cycles.\n", "10. **Supply Chain Management and Logistics**: Generative AI can be used to analyze supply chain data, predict demand, and optimize logistics operations, helping businesses reduce costs, improve efficiency, and increase delivery times.\n", "\n", "These are just a few examples of the business applications of Generative AI. As the technology continues to evolve, we can expect to see even more innovative uses across various industries and sectors.\n"]}], "source": ["# There's actually an alternative approach that some people might prefer\n", "# You can use the OpenAI client python library to call Ollama:\n", "\n", "from openai import OpenAI\n", "ollama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n", "response = ollama_via_openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "1622d9bb-5c68-4d4e-9ca4-b492c751f898", "metadata": {}, "source": ["# NOW the exercise for you\n", "\n", "Take the code from day1 and incorporate it here, to build a website summarizer that uses Llama 3.2 running locally instead of OpenAI; use either of the above approaches."]}, {"cell_type": "code", "execution_count": 9, "id": "6de38216-6d1c-48c4-877b-86d403f4e0f8", "metadata": {}, "outputs": [], "source": ["# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "# A class to represent a Webpage\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "\n", "# Define our system prompt - you can experiment with this later, changing the last sentence to '<PERSON><PERSON><PERSON> in markdown in Spanish.\"\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\"\n", "\n", "# A function that writes a User Prompt that asks for summaries of websites:\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt\n", "\n", "# See how this function creates exactly the format above\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]\n", "\n", "# Call the Ollama local API.\n", "def summarize(url):\n", "    website = Website(url)\n", "    response = ollama_via_openai.chat.completions.create(\n", "        model = MODEL,\n", "        messages = messages_for(website)\n", "    )\n", "    return response.choices[0].message.content\n", "\n", "# A function to display this nicely in the Ju<PERSON><PERSON> output, using markdown\n", "def display_summary(url):\n", "    summary = summarize(url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": 10, "id": "16277650-7925-47dc-9194-02bbb520d691", "metadata": {}, "outputs": [{"data": {"text/markdown": ["This appears to be a sample issue of the CNN website, showcasing various news articles and features from around the world. I'll summarize some of the top headlines:\n", "\n", "**World News**\n", "\n", "* **<PERSON> Francis**: The <PERSON> has passed away at the age of 96, leaving behind a legacy of service and compassion.\n", "* **Israel-Hamas War**: The conflict between Israel and Hamas has intensified, with both sides suffering losses and a human cost.\n", "* **Ukraine-Russia War**: Russia has returned the body of a Ukrainian journalist who died in Russian detention, sparking concerns about Russian treatment of prisoners.\n", "\n", "**US Politics**\n", "\n", "* **<PERSON> Administration**: Former President <PERSON> is rumored to be planning a comeback, with several high-profile officials announcing their resignation or departures.\n", "* **TSAFIR ABAVOV**: Two Israeli officials have been accused of attempting to purchase the remains of two dead Palestinian men for thousands of dollars.\n", "\n", "**Business and Technology**\n", "\n", "* **Apple Tariffs**: The US government has imposed tariffs on Chinese tech giant Apple, with CEO <PERSON> stating that the tariffs could cost the company up to $900 million this quarter.\n", "* **Meta's AI Assistant App**: Facebook parent Meta has launched an AI assistant app, further competing with OpenAI and Google in the emerging field of digital assistants.\n", "\n", "**Health**\n", "\n", "* **Whooping Cough Outbreak**: Cases of whooping cough are rising globally, with experts warning of the need for increased vaccination efforts.\n", "* **Forever Chemicals Research**: Researchers have made gains in understanding how to build homes using fungi as a sustainable alternative material solution.\n", "\n", "This is just a snapshot of some of the top news headlines from the CNN website. If you'd like to know more about any specific topic, feel free to ask!"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary(\"https://cnn.com\")"]}, {"cell_type": "code", "execution_count": 11, "id": "86fd552d-d95c-4636-878c-86d3f6338a0c", "metadata": {}, "outputs": [{"data": {"text/markdown": ["**Anthropic Website Summary**\n", "==========================\n", "\n", "Anthropic is a company that builds AI to serve humanity's long-term well-being. They aim to create tools with human benefit at their foundation, focusing on responsible AI development.\n", "\n", "### News and Announcements\n", "\n", "* **Claude 3.7 Sonnet**: Anthropic's most intelligent AI model is now available.\n", "\t+ Released in February 2025\n", "* **Anthropic Economic Index**: New publication released on March 27, 2025, discussing societal impacts of large language models.\n", "* **Alignment faking in large language models**: Blog post from December 18, 2024, exploring alignment science.\n", "* **Introducing the Model Context Protocol**: Product update for November 25, 2024.\n", "\n", "### AI Research and Products\n", "\n", "Anthropic focuses on building powerful technologies with human benefit at their foundation. They provide various resources, including:\n", "\n", "* Claude, an open-source AI platform\n", "* Anthropic Academy, a learning platform\n", "* Research overview, featuring the Anthropic Economic Index and more\n", "\n", "The company's mission is to create tools that put safety at the frontier of AI development.\n", "\n", "### Products and Pricing\n", "\n", "Anthropic offers various products and pricing plans for customers, including:\n", "\n", "* <PERSON>\n", "* Claude team plan\n", "* Claude enterprise plan\n", "* Claude education plan\n", "* Claude apps\n", "* Pricing plans for Claude.ai"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display_summary(\"https://anthropic.com\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}