{"cells": [{"cell_type": "code", "execution_count": null, "id": "ce3db287-8661-4649-a599-cba0edd115b1", "metadata": {}, "outputs": [], "source": ["\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "from openai import OpenAI\n", "\n", "openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "system_prompt = \"You are an assistant that analyzes the contents of an email\"\n", "user_prompt = \"Provide a short subject line which aligns with the body of the email\"\n", "email_content = \"\"\"\n", "From: <EMAIL>\n", "To: <EMAIL>\n", "Subject: \n", "\n", "Hi Team,\n", "\n", "This email provides an update on Project Alpha. We've successfully completed Phase 1, which involved, and would like to thank all the \n", "team for the great work, and looking forward to the same energy and rigor for Phase 2 too. \n", "\n", "Keep up the good work!! \n", "\n", "Thanks,\n", "<PERSON>\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt+email_content}\n", "]\n", "\n", "# Step 3: Call OpenAI\n", "\n", "#response =\n", "response = openai.chat.completions.create(model=\"llama3.2\", messages=messages)\n", "\n", "# Step 4: print the result\n", "print(response.choices[0].message.content)\n"]}, {"cell_type": "code", "execution_count": null, "id": "526b87f1-aa38-44d4-9609-292a4f4d72a9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}