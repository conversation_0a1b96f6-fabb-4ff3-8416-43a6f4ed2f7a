{"cells": [{"cell_type": "markdown", "id": "d15d8294-3328-4e07-ad16-8a03e9bbfdb9", "metadata": {}, "source": ["# Welcome to your first assignment!\n", "\n", "Instructions are below. Please give this a try, and look in the solutions folder if you get stuck (or feel free to ask me!)"]}, {"cell_type": "markdown", "id": "ada885d9-4d42-4d9b-97f0-74fbbbfe93a9", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">Just before we get to the assignment --</h2>\n", "            <span style=\"color:#f71;\">I thought I'd take a second to point you at this page of useful resources for the course. This includes links to all the slides.<br/>\n", "            <a href=\"https://edwarddonner.com/2024/11/13/llm-engineering-resources/\">https://edwarddonner.com/2024/11/13/llm-engineering-resources/</a><br/>\n", "            Please keep this bookmarked, and I'll continue to add more useful links there over time.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "6e9fa1fc-eac5-4d1d-9be4-541b3f2b3458", "metadata": {}, "source": ["# HOMEW<PERSON><PERSON> EXERCISE ASSIGNMENT\n", "\n", "Upgrade the day 1 project to summarize a webpage to use an Open Source model running locally via Ollama rather than OpenAI\n", "\n", "You'll be able to use this technique for all subsequent projects if you'd prefer not to use paid APIs.\n", "\n", "**Benefits:**\n", "1. No API charges - open-source\n", "2. Data doesn't leave your box\n", "\n", "**Disadvantages:**\n", "1. Significantly less power than Frontier Model\n", "\n", "## Recap on installation of Ollama\n", "\n", "Simply visit [ollama.com](https://ollama.com) and install!\n", "\n", "Once complete, the ollama server should already be running locally.  \n", "If you visit:  \n", "[http://localhost:11434/](http://localhost:11434/)\n", "\n", "You should see the message `<PERSON><PERSON><PERSON> is running`.  \n", "\n", "If not, bring up a new Terminal (Mac) or Powershell (Windows) and enter `ollama serve`  \n", "And in another Terminal (Mac) or Powershell (Windows), enter `ollama pull llama3.2`  \n", "Then try [http://localhost:11434/](http://localhost:11434/) again.\n", "\n", "If Ollama is slow on your machine, try using `llama3.2:1b` as an alternative. Run `ollama pull llama3.2:1b` from a Terminal or Powershell, and change the code below from `MODEL = \"llama3.2\"` to `MODEL = \"llama3.2:1b\"`"]}, {"cell_type": "code", "execution_count": 2, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "raw", "id": "07e106bd-10c5-4365-b85b-397b5f059656", "metadata": {}, "source": ["# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": 5, "id": "dac0a679-599c-441f-9bf2-ddc73d35b940", "metadata": {}, "outputs": [], "source": ["# Create a messages list using the same format that we used for OpenAI\n", "\n", "messages = [\n", "    {\"role\": \"user\", \"content\": \"Describe some of the business applications of Generative AI\"}\n", "]"]}, {"cell_type": "code", "execution_count": 6, "id": "7bb9c624-14f0-4945-a719-8ddb64f66f47", "metadata": {}, "outputs": [], "source": ["payload = {\n", "        \"model\": MODEL,\n", "        \"messages\": messages,\n", "        \"stream\": False\n", "    }"]}, {"cell_type": "code", "execution_count": 7, "id": "42b9f644-522d-4e05-a691-56e7658c0ea9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generative AI (Artificial Intelligence) has numerous business applications across various industries. Here are some examples:\n", "\n", "1. **Content Generation**: Generative AI can create high-quality content such as articles, social media posts, product descriptions, and more. This can help businesses save time and resources on content creation.\n", "2. **Product Design**: Generative AI can be used to design new products, such as fashion items, jewelry, or electronics. It can also generate 3D models and prototypes, reducing the need for manual design and prototyping.\n", "3. **Image and Video Generation**: Generative AI can create realistic images and videos that can be used in marketing campaigns, advertising, and social media. This can help businesses create engaging visual content without requiring extensive photography or videography skills.\n", "4. **Chatbots and Virtual Assistants**: Generative AI can power chatbots and virtual assistants that provide customer support, answer frequently asked questions, and even engage in basic conversations.\n", "5. **Predictive Maintenance**: Generative AI can analyze sensor data from machines and predict when maintenance is needed, reducing downtime and increasing efficiency.\n", "6. **Personalized Recommendations**: Generative AI can analyze customer behavior and preferences to generate personalized product recommendations, improving the overall shopping experience.\n", "7. **Customer Segmentation**: Generative AI can help businesses segment their customers based on their behavior, demographics, and preferences, enabling targeted marketing campaigns.\n", "8. **Automated Writing Assistance**: Generative AI can assist writers with ideas, suggestions, and even full-text writing, helping to boost productivity and creativity.\n", "9. **Data Analysis and Visualization**: Generative AI can analyze large datasets and generate insights, visualizations, and predictions that can inform business decisions.\n", "10. **Creative Collaboration**: Generative AI can collaborate with human creatives, such as artists, designers, and writers, to generate new ideas, concepts, and content.\n", "\n", "Some specific industries where Generative AI is being applied include:\n", "\n", "1. **Marketing and Advertising**: generating personalized ads, content, and messaging.\n", "2. **Finance and Banking**: automating financial analysis, risk assessment, and customer service.\n", "3. **Healthcare**: generating medical images, analyzing patient data, and predicting disease outcomes.\n", "4. **Manufacturing and Supply Chain**: optimizing production workflows, predicting demand, and identifying potential bottlenecks.\n", "5. **Education**: creating personalized learning experiences, grading assignments, and developing educational content.\n", "\n", "These are just a few examples of the many business applications of Generative AI. As the technology continues to evolve, we can expect to see even more innovative uses across various industries.\n"]}], "source": ["response = requests.post(OLLAMA_API, json=payload, headers=HEADERS)\n", "print(response.json()['message']['content'])"]}, {"cell_type": "markdown", "id": "6a021f13-d6a1-4b96-8e18-4eae49d876fe", "metadata": {}, "source": ["# Introducing the ollama package\n", "\n", "And now we'll do the same thing, but using the elegant ollama python package instead of a direct HTTP call.\n", "\n", "Under the hood, it's making the same call as above to the ollama server running at localhost:11434"]}, {"cell_type": "code", "execution_count": 8, "id": "7745b9c4-57dc-4867-9180-61fa5db55eb8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generative AI has numerous business applications across various industries. Here are some examples:\n", "\n", "1. **Content Generation**: Generative AI can be used to generate high-quality content such as articles, social media posts, product descriptions, and more. This can save time and resources for businesses that need to produce a large volume of content.\n", "2. **Product Design**: Generative AI can be used to design new products, such as furniture, electronics, and other consumer goods. It can also help optimize product designs by generating multiple versions and selecting the most suitable one based on various criteria.\n", "3. **Marketing Automation**: Generative AI can be used to create personalized marketing campaigns, such as email marketing automation, social media ads, and more. This can help businesses tailor their marketing efforts to specific customer segments and improve engagement rates.\n", "4. **Image and Video Editing**: Generative AI can be used to edit images and videos, such as removing background noise, correcting color casts, and enhancing video quality. This can save time and resources for businesses that need to create high-quality visual content.\n", "5. **Chatbots and Virtual Assistants**: Generative AI can be used to create chatbots and virtual assistants that can understand natural language and respond accordingly. This can help businesses provide better customer service and improve user experience.\n", "6. **Predictive Analytics**: Generative AI can be used to analyze large datasets and generate predictive models that can forecast future trends and behaviors. This can help businesses make data-driven decisions and stay ahead of the competition.\n", "7. **Customer Segmentation**: Generative AI can be used to segment customers based on their behavior, demographics, and preferences. This can help businesses tailor their marketing efforts and improve customer engagement.\n", "8. **Language Translation**: Generative AI can be used to translate languages in real-time, which can help businesses communicate with international clients and customers more effectively.\n", "9. **Music Composition**: Generative AI can be used to compose music for various applications such as advertising, film scoring, and video game soundtracks.\n", "10. **Financial Modeling**: Generative AI can be used to create financial models that can predict future revenue streams, costs, and other financial metrics. This can help businesses make more accurate predictions and inform better investment decisions.\n", "\n", "Some of the industries that are already leveraging generative AI include:\n", "\n", "* E-commerce\n", "* Healthcare\n", "* Finance\n", "* Marketing\n", "* Education\n", "* Entertainment\n", "* Manufacturing\n", "\n", "These applications have the potential to transform various business processes, improve customer experiences, and drive innovation in various sectors.\n"]}], "source": ["import ollama\n", "\n", "response = ollama.chat(model=MODEL, messages=messages)\n", "print(response['message']['content'])"]}, {"cell_type": "markdown", "id": "a4704e10-f5fb-4c15-a935-f046c06fb13d", "metadata": {}, "source": ["## Alternative approach - using OpenAI python library to connect to Ollama"]}, {"cell_type": "code", "execution_count": 9, "id": "23057e00-b6fc-4678-93a9-6b31cb704bff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generative AI has numerous business applications across various industries, transforming the way companies operate, create products, and interact with customers. Some key applications include:\n", "\n", "1. **Content Generation**: Automate content creation for marketing materials, such as blog posts, product descriptions, social media posts, and more, using Generative AI-powered tools.\n", "2. **Product Design and Prototyping**: Use Generative AI to design new products, furniture, or other innovative solutions, reducing design time and costs while increasing creativity.\n", "3. **Customer Experience (CX) Tools**: Leverage Generative AI to create personalized customer experiences, such as chatbots that can respond to customer queries and provide tailored recommendations.\n", "4. **Predictive Maintenance**: Use Generative AI to analyze sensor data, identify potential issues, and predict maintenance needs for equipment, reducing downtime and increasing overall efficiency.\n", "5. **Personalized Marketing**: Use Generative AI to create targeted marketing campaigns based on individual customer preferences, behaviors, and demographics.\n", "6. **Content Optimization**: Utilize Generative AI to optimize content for better performance in search engine results pages (SERPs), ensuring improved visibility and traffic.\n", "7. **Brand Storytelling**: Automate the creation of brand stories, taglines, and overall brand narrative using Generative AI-powered tools.\n", "8. **Financial Modeling and Forecasting**: Use Generative AI to create financial models, forecasts, and predictions for businesses, helping them make data-driven decisions.\n", "9. **Supply Chain Optimization**: Leverage Generative AI to optimize supply chain operations, predicting demand, reducing inventory levels, and streamlining logistics.\n", "10. **Automated Transcription and Translation**: Use Generative AI to automate the transcription of audio and video files into written text, as well as translate materials across languages.\n", "11. **Digital Asset Management**: Utilize Generative AI to manage digital assets, such as images, videos, and documents, and automatically generate metadata for easy search and retrieval.\n", "12. **Chatbots and Virtual Assistants**: Create more advanced chatbots using Generative AI that can understand context, emotions, and intent, providing better customer service experiences.\n", "\n", "In healthcare, Generative AI is being applied to:\n", "\n", "1. Medical Imaging Analysis\n", "2. Personalized Medicine\n", "3. Patient Data Analysis\n", "\n", "In education, Generative AI is used in:\n", "\n", "1. Adaptive Learning Systems\n", "2. Automated Grading and Feedback\n", "\n", "Generative AI has numerous applications across various industries, from creative content generation to predictive maintenance and supply chain optimization.\n", "\n", "Keep in mind that these are just a few examples of the many business applications of Generative AI as this technology continues to evolve at a rapid pace.\n"]}], "source": ["# There's actually an alternative approach that some people might prefer\n", "# You can use the OpenAI client python library to call Ollama:\n", "\n", "from openai import OpenAI\n", "ollama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n", "response = ollama_via_openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "1622d9bb-5c68-4d4e-9ca4-b492c751f898", "metadata": {}, "source": ["# NOW the exercise for you\n", "\n", "Take the code from day1 and incorporate it here, to build a website summarizer that uses Llama 3.2 running locally instead of OpenAI; use either of the above approaches."]}, {"cell_type": "code", "execution_count": 28, "id": "de923314-a427-4199-b1f9-0e60f85114c3", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display\n", "\n", "# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": 31, "id": "0cedada6-adc6-40dc-bdf3-bc8a3b6b3826", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Home\n", "Outsmart\n", "An arena that pits LLMs against each other in a battle of diplomacy and deviousness\n", "About\n", "Posts\n", "Well, hi there.\n", "I’m <PERSON>. I like writing code and experimenting with LLMs, and hopefully you’re here because you do too. I also enjoy DJing (but I’m badly out of practice), amateur electronic music production (\n", "very\n", "amateur) and losing myself in\n", "Hacker News\n", ", nodding my head sagely to things I only half understand.\n", "I’m the co-founder and CTO of\n", "Nebula.io\n", ". We’re applying AI to a field where it can make a massive, positive impact: helping people discover their potential and pursue their reason for being. Recruiters use our product today to source, understand, engage and manage talent. I’m previously the founder and CEO of AI startup untapt,\n", "acquired in 2021\n", ".\n", "We work with groundbreaking, proprietary LLMs verticalized for talent, we’ve\n", "patented\n", "our matching model, and our award-winning platform has happy customers and tons of press coverage.\n", "Connect\n", "with me for more!\n", "November 13, 2024\n", "Mastering AI and LLM Engineering – Resources\n", "October 16, 2024\n", "From Software Engineer to AI Data Scientist – resources\n", "August 6, 2024\n", "Outsmart LLM Arena – a battle of diplomacy and deviousness\n", "June 26, 2024\n", "Choosing the Right LLM: Toolkit and Resources\n", "Navigation\n", "Home\n", "Outsmart\n", "An arena that pits LLMs against each other in a battle of diplomacy and deviousness\n", "About\n", "Posts\n", "Get in touch\n", "ed [at] edwarddonner [dot] com\n", "www.edwarddonner.com\n", "Follow me\n", "LinkedIn\n", "Twitter\n", "Facebook\n", "Subscribe to newsletter\n", "Type your email…\n", "Subscribe\n"]}], "source": ["# Let's try one out. Change the website and add print statements to follow along.\n", "\n", "web_res = Website(\"https://edwarddonner.com\")\n", "print(web_res.text)"]}, {"cell_type": "code", "execution_count": 11, "id": "64d26055-756b-4095-a1d1-298fdf4fd8f1", "metadata": {}, "outputs": [], "source": ["\n", "# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\"\n"]}, {"cell_type": "code", "execution_count": 52, "id": "65b08550-7506-415f-8612-e2395d6e145d", "metadata": {}, "outputs": [], "source": ["\n", "# Define our system prompt - you can experiment with this later, changing the last sentence to '<PERSON><PERSON><PERSON> in markdown in Spanish.\"\n", "\n", "system_prompt = \"You are an helper that assist user to provide crisp summary\\\n", "of the website they pass in, respond with key points\"\n", "\n", "# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too with start bulletin.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt\n"]}, {"cell_type": "code", "execution_count": 33, "id": "36a0a2d0-f07a-40ac-a065-b713cdd5c028", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]\n"]}, {"cell_type": "code", "execution_count": 50, "id": "8c2b20ea-6a8e-41c9-be3b-f24a5b29e8de", "metadata": {}, "outputs": [], "source": ["#website search\n", "\n", "web_msg=Website(\"https://www.cricbuzz.com/cricket-match-squads/91796/aus-vs-ind-3rd-test-india-tour-of-australia-2024-25\")\n", "messages=messages_for(web_msg)\n", "\n", "payload = {\n", "        \"model\": MODEL,\n", "        \"messages\": messages,\n", "        \"stream\": False\n", "    }"]}, {"cell_type": "code", "execution_count": 54, "id": "e5636b3b-7763-4f9c-ab18-88aa25b50de6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**Summary of the Website**\n", "=========================\n", "\n", "* The website provides live updates and information about the 3rd Test match between Australia and India as part of India's tour of Australia in the 2024-25 season.\n", "* It includes news, scores, stats, and analysis from the match.\n", "* The website is affiliated with Cricbuzz.com, a popular online cricket platform.\n", "\n", "**News and Announcements**\n", "==========================\n", "\n", "* **<PERSON> to miss the rest of the series**: Australian all-rounder <PERSON>'s teammate <PERSON> has been ruled out of the remaining Tests due to a knee injury.\n", "* **<PERSON><PERSON><PERSON> to feature in the third Test**: Indian fast bowler <PERSON><PERSON><PERSON><PERSON> is expected to return for the third Test, which starts on January 5 at the Sydney Cricket Ground.\n"]}], "source": ["#Using Ollama to run it in the local\n", "response = requests.post(OLLAMA_API, json=payload, headers=HEADERS)\n", "print(response.json()['message']['content'])"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}