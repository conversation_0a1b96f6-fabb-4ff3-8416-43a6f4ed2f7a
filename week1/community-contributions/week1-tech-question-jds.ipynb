{"cells": [{"cell_type": "markdown", "id": "fe12c203-e6a6-452c-a655-afb8a03a4ff5", "metadata": {}, "source": ["# End of week 1 exercise\n", "\n", "To demonstrate your familiarity with OpenAI API, and also Ollama, build a tool that takes a technical question,  \n", "and responds with an explanation. This is a tool that you will be able to use yourself during the course!"]}, {"cell_type": "code", "execution_count": 1, "id": "c1070317-3ed9-4659-abe3-828943230e03", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 2, "id": "4a456906-915a-4bfd-bb9d-57e505c5093f", "metadata": {}, "outputs": [], "source": ["# constants\n", "\n", "MODEL_GPT = 'gpt-4o-mini'\n", "MODEL_LLAMA = 'llama3.2'\n", "\n", "OLLAMA_API = \"http://localhost:11434/v1\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}"]}, {"cell_type": "code", "execution_count": 3, "id": "a8d7923c-5f28-4c30-8556-342d7c8497c1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["API key looks good so far\n"]}], "source": ["# set up environment\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if api_key and api_key.startswith('sk-proj-') and len(api_key)>10:\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? Please visit the troubleshooting notebook!\")\n", "    "]}, {"cell_type": "code", "execution_count": 4, "id": "3f0d0137-52b0-47a8-81a8-11a90a010798", "metadata": {}, "outputs": [], "source": ["# here is the system prompt and payloads;\n", "\n", "system_prompt = \"\"\"\n", "You are an expert on LLMs and writing python code. You are able to answer complex questions with\n", "detailed answers and explain what every line of code does. You can refactor the code when asked.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 7, "id": "60ce7000-a4a5-4cce-a261-e75ef45063b4", "metadata": {}, "outputs": [], "source": ["# Function to get answer, with streaming\n", "\n", "def llm_copilot(question, model):\n", "    if 'llama' in model.lower():\n", "        openai = OpenAI(base_url=OLLAMA_API, api_key='ollama')\n", "    else:\n", "        openai = OpenAI()\n", "        \n", "    stream = openai.chat.completions.create(\n", "        model=model,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": question}\n", "          ],\n", "        stream=True\n", "    )\n", "    response = \"\"\n", "    display_handle = display(Markdown(\"\"), display_id=True)\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        response = response.replace(\"```\",\"\").replace(\"markdown\", \"\")\n", "        update_display(Markdown(response), display_id=display_handle.display_id)"]}, {"cell_type": "code", "execution_count": 8, "id": "8f7c8ea8-4082-4ad0-8751-3301adcf6538", "metadata": {}, "outputs": [{"data": {"text/markdown": ["Here's a revised version of your code:\n", "\n", "python\n", "if 'llama' in model.lower():\n", "\n", "\n", "OR if you want to keep the original style, you can modify it as follows:\n", "\n", "python\n", "if model.split('.')[-1] == 'llama3.2':\n", "\n", "\n", "In this second example, we use string indexing (`-1`) to get the last part of the `model` string after splitting at the dot (`.`) character.\n", "\n", "The first revised version uses Python's built-in string method `lower()` to convert `model` to lowercase and then checks if 'llama' is present in it. It returns True if the text contains \"llama\", otherwise, it will return False. \n", "\n", "However, both of these codes are using lazy evaluation, which means if you do this check inside a loop:\n", "\n", "python\n", "for i in range(100):\n", "    print('llama')\n", "\n", "\n", "Python will use 'a' instead of 'llame' most of the time until `i == 98` because it has to wait for the condition to be met (and also does some lookup and look-around). If you want a case-insensitive search without this slowness, consider using a regular expression as shown below\n", "\n", "python\n", "import re\n", "\n", "if re.search(r' llama.', model):\n", "\n", "\n", "And if you still want that specific code structure, then use `replace` function as follows:\n", "\n", "python\n", "model = model.replace('llama', '')\n", "if model == '3.2':\n"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["None\n"]}], "source": ["# Ask question\n", "question = \"\"\"\n", "Change this code to check for just the 'llama' portion of text instead of the entire string:\n", "if model == 'llama3.2':\n", "\"\"\"\n", "\n", "print(llm_copilot(question, MODEL_LLAMA))"]}, {"cell_type": "code", "execution_count": null, "id": "1a4026cd-8967-4961-b26b-e3997307c4ba", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}