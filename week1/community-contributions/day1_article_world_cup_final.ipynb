{"cells": [{"cell_type": "code", "execution_count": null, "id": "2c4ce468", "metadata": {}, "outputs": [], "source": ["# imports\n", "from openai import OpenAI\n", "\n", "openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n", "# Step 1: Create your prompts\n", "\n", "system_prompt = \"You are a sports journalist.\"\n", "user_prompt = \"\"\"\n", "    Write a sports article in less than 500 words describing the FIFA World Cup Final 2022.\n", "\"\"\"\n", "\n", "# Step 2: Make the messages list\n", "\n", "messages = [\n", "    {\"role\": \"system\", \"content\": system_prompt},\n", "    {\"role\": \"user\", \"content\": user_prompt}\n", "]\n", "\n", "# Step 3: Call OpenAI\n", "\n", "response = openai.chat.completions.create(model=\"llama3.2\", messages=messages)\n", "\n", "# Step 4: print the result\n", "\n", "print(response.choices[0].message.content)"]}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}