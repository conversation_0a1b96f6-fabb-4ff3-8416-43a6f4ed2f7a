{"cells": [{"cell_type": "markdown", "id": "e3ce0a59-fbfb-4377-85db-f62f95039200", "metadata": {}, "source": ["# Day2 EXERCISE - Summarization using Ollama"]}, {"cell_type": "code", "execution_count": null, "id": "4e2a9393-7767-488e-a8bf-27c12dca35bd", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "id": "29ddd15d-a3c5-4f4e-a678-873f56162724", "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": null, "id": "cb5c0f84-4e4d-4f87-b492-e09d0333a638", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# If you're not familiar with Classes, check out the \"Intermediate Python\" notebook\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "23457b52-c85b-4dc1-b946-6f1461dc0675", "metadata": {}, "outputs": [], "source": ["\n", "ed = Website(\"https://edwarddonner.com\")"]}, {"cell_type": "code", "execution_count": null, "id": "bed206ed-43c1-4f68-ad01-a738b3b4648d", "metadata": {}, "outputs": [], "source": ["# Define our system prompt - you can experiment with this later, changing the last sentence to '<PERSON><PERSON><PERSON> in markdown in Spanish.\"\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "e558f381-614a-461f-83bc-e5bdc99460df", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "e5ba638d-aeb9-441e-a62a-8e8027ad8439", "metadata": {}, "outputs": [], "source": ["# See how this function creates exactly the format above\n", "\n", "def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "e85ca2ec-3e46-4b8f-9c2f-66e7d20138fa", "metadata": {}, "outputs": [], "source": ["#website search\n", "\n", "ed = Website(\"https://edwarddonner.com\")\n", "messages=messages_for(ed)\n", "\n", "payload = {\n", "        \"model\": MODEL,\n", "        \"messages\": messages,\n", "        \"stream\": False\n", "    }"]}, {"cell_type": "code", "execution_count": null, "id": "7745b9c4-57dc-4867-9180-61fa5db55eb8", "metadata": {}, "outputs": [], "source": ["import ollama\n", "\n", "response = ollama.chat(model=MODEL, messages=messages)\n", "print(response['message']['content'])"]}, {"cell_type": "code", "execution_count": null, "id": "402d5686-4e76-4110-b65a-b3906c35c0a4", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}