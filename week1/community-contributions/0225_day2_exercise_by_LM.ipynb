{"cells": [{"cell_type": "markdown", "id": "fef36918-109d-41e3-8603-75ff81b42379", "metadata": {}, "source": ["# Solution for exercise day 2 - slight modification: model is a parameter also - display_summary(\"deepseek-r1:1.5b\",\"https://yoururl\")\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "b50349ac-93ea-496b-ae20-bd72a93bb138", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "id": "edd073c7-8444-4a0d-b84e-4b2ed0ee7f35", "metadata": {}, "outputs": [], "source": ["# Constants\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}\n", "#MODEL = \"llama3.2\""]}, {"cell_type": "code", "execution_count": null, "id": "2e3a6e1a-e4c7-4448-9852-1b6ba2bd8d66", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "\n", "    def __init__(self, url):\n", "        \"\"\"\n", "        Create this Website object from the given url using the BeautifulSoup library\n", "        \"\"\"\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        soup = BeautifulSoup(response.content, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ae3752ca-3a97-4d6a-ac84-5b75ebfb50ed", "metadata": {}, "outputs": [], "source": ["# Define the system prompt \n", "system_prompt = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "48b5240f-7617-4e51-a320-cba9650bec84", "metadata": {}, "outputs": [], "source": ["# A function that writes a User Prompt that asks for summaries of websites:\n", "\n", "def user_prompt_for(website):\n", "    user_prompt = f\"You are looking at a website titled {website.title}\"\n", "    user_prompt += \"\\nThe contents of this website is as follows; \\\n", "please provide a short summary of this website in markdown. \\\n", "If it includes news or announcements, then summarize these too.\\n\\n\"\n", "    user_prompt += website.text\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "6f7d84f0-60f2-4cbf-b4d1-173a79fe3380", "metadata": {}, "outputs": [], "source": ["def messages_for(website):\n", "    return [\n", "        {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": user_prompt_for(website)}\n", "    ]"]}, {"cell_type": "code", "execution_count": null, "id": "25520a31-c857-4ed5-86da-50dfe5fab7bb", "metadata": {}, "outputs": [], "source": ["def summarize(model,url):\n", "    website = Website(url)\n", "    payload = {\n", "        \"model\": model,\n", "        \"messages\": messages_for(website),\n", "        \"stream\": False\n", "    }\n", "    response = requests.post(OLLAMA_API, json=payload, headers=HEADERS)\n", "    return response.json()['message']['content']"]}, {"cell_type": "code", "execution_count": null, "id": "430776ed-8516-43a9-8a22-618d9080f2e1", "metadata": {}, "outputs": [], "source": ["# A function to display this nicely in the Ju<PERSON><PERSON> output, using markdown\n", "def display_summary(model,url):\n", "    summary = summarize(model,url)\n", "    display(Markdown(summary))"]}, {"cell_type": "code", "execution_count": null, "id": "b2b05c1f-e4a2-4f65-bd6d-634d72e38b6e", "metadata": {}, "outputs": [], "source": ["#!o<PERSON>ma pull deepseek-r1:1.5b"]}, {"cell_type": "code", "execution_count": null, "id": "01513f8a-15b7-4053-bfe4-44b36e5494d1", "metadata": {}, "outputs": [], "source": ["display_summary(\"deepseek-r1:1.5b\",\"https://www.ipma.pt\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}