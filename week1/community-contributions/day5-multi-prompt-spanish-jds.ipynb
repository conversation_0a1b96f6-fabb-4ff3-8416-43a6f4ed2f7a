{"cells": [{"cell_type": "markdown", "id": "a98030af-fcd1-4d63-a36e-38ba053498fa", "metadata": {}, "source": ["# Snarky brochure"]}, {"cell_type": "code", "execution_count": null, "id": "d5b08506-dc8b-4443-9201-5f1848161363", "metadata": {}, "outputs": [], "source": ["# imports\n", "# If these fail, please check you're running from an 'activated' environment with (llms) in the command prompt\n", "\n", "import os\n", "import requests\n", "import json\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "fc5d8880-f2ee-4c06-af16-ecbc0262af61", "metadata": {}, "outputs": [], "source": ["# Initialize and constants\n", "\n", "load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if api_key and api_key.startswith('sk-proj-') and len(api_key)>10:\n", "    print(\"API key looks good so far\")\n", "else:\n", "    print(\"There might be a problem with your API key? Please visit the troubleshooting notebook!\")\n", "    \n", "MODEL = 'gpt-4o-mini'\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "106dd65e-90af-4ca8-86b6-23a41840645b", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "markdown", "id": "1771af9c-717a-4fca-bbbe-8a95893312c3", "metadata": {}, "source": ["## Link prompts\n", "### Multi-shot system prompt"]}, {"cell_type": "code", "execution_count": null, "id": "6957b079-0d96-45f7-a26a-3487510e9b35", "metadata": {}, "outputs": [], "source": ["link_system_prompt = \"You are provided with a list of links found on a webpage. \\\n", "You are able to decide which of the links would be most relevant to include in a brochure about the company, \\\n", "such as links to an About page, or a Company page, or Careers/Jobs pages.\\n\"\n", "link_system_prompt += \"You should respond in JSON as in these examples:\"\n", "link_system_prompt += \"\"\"\n", "Example 1\n", "['https://my-company.com', 'https://my-company.com/about-me', 'https://www.linkedin.com/in/my-company/', 'mailto:<EMAIL>', 'https://my-company.com/news', '/case-studies', 'https://patents.google.com/patent/US20210049536A1/', 'https://my-company.com/workshop-ai']\n", "\n", "    Links:\n", "{\n", "    \"links\": [\n", "        {\"type\": \"landing page\", \"url\": \"https://great-comps.com/about-me\"},\n", "        {\"type\": \"about page\", \"url\": \"https://great-comps.com/about-me\"},\n", "        {\"type\": \"news page\": \"url\": \"https://great-comps.com/news\"},\n", "        {\"type\": \"case studies page\": \"url\": \"https://great-comps.com/case-studies\"},\n", "        {\"type\": \"workshop page\": \"url\": \"https://great-comps.com/workshop-ai\"},\n", "    ]\n", "}\n", "Example 2\n", "['https://www.acmeinc.com', '/#about', '/#projects', '/#experience', '/#skills', 'https://github.com/acmeinc']\n", "\n", "    Links:\n", "{\n", "    \"links\": [\n", "        {\"type\": \"landing page\", \"url\": \"https://www.acmeinc.com\"},\n", "        {\"type\": \"GitHub projects\": \"url\": \"https://github.com/acmeinc\"},\n", "    ]\n", "}\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "b97e4068-97ed-4120-beae-c42105e4d59a", "metadata": {}, "outputs": [], "source": ["print(link_system_prompt)"]}, {"cell_type": "markdown", "id": "baf384bb-4577-4885-a445-dc8da232b1d9", "metadata": {}, "source": ["### User prompt"]}, {"cell_type": "markdown", "id": "51174859-666a-43ad-9c34-5f082298d398", "metadata": {}, "source": ["## Get links"]}, {"cell_type": "code", "execution_count": null, "id": "8e1f601b-2eaf-499d-b6b8-c99050c9d6b3", "metadata": {}, "outputs": [], "source": ["def get_links_user_prompt(website):\n", "    user_prompt = f\"Here is the list of links on the website of {website.url} - \"\n", "    user_prompt += \"please decide which of these are relevant web links for a brochure about the company, respond with the full https URL in JSON format. \\\n", "Do not include Terms of Service, Privacy, email links.\\n\"\n", "    user_prompt += \"Links (some might be relative links):\\n\"\n", "    user_prompt += \"\\n\".join(website.links)\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "a29aca19-ca13-471c-a4b4-5abbfa813f69", "metadata": {}, "outputs": [], "source": ["def get_links(url):\n", "    website = Website(url)\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": link_system_prompt},\n", "            {\"role\": \"user\", \"content\": get_links_user_prompt(website)}\n", "      ],\n", "        response_format={\"type\": \"json_object\"}\n", "    )\n", "    result = response.choices[0].message.content\n", "    return json.loads(result)"]}, {"cell_type": "markdown", "id": "0d74128e-dfb6-47ec-9549-288b621c838c", "metadata": {}, "source": ["## Create brochure"]}, {"cell_type": "code", "execution_count": null, "id": "85a5b6e2-e7ef-44a9-bc7f-59ede71037b5", "metadata": {}, "outputs": [], "source": ["def get_all_details(url):\n", "    result = \"Landing page:\\n\"\n", "    result += Website(url).get_contents()\n", "    links = get_links(url)\n", "    print(\"Found links:\", links)\n", "    for link in links[\"links\"]:\n", "        result += f\"\\n\\n{link['type']}\\n\"\n", "        result += Website(link[\"url\"]).get_contents()\n", "    return result"]}, {"cell_type": "markdown", "id": "4b4d8ec1-4855-4c0e-afc0-33055e6b0a6d", "metadata": {}, "source": ["### Snarky system prompt"]}, {"cell_type": "code", "execution_count": null, "id": "9b863a55-f86c-4e3f-8a79-94e24c1a8cf2", "metadata": {}, "outputs": [], "source": ["# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\"\n", "\n", "# Or uncomment the lines below for a more humorous brochure - this demonstrates how easy it is to incorporate 'tone':\n", "\n", "# system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "# and creates a short humorous, entertaining, jokey brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "# Include details of company culture, customers and careers/jobs if you have the information.\"\n", "\n", "system_prompt = \"You are an assistant that analyzes the contents of several relevant pages from a company website \\\n", "and creates a short snarky, entertaining, pun loaded brochure about the company for prospective customers, investors and recruits. Respond in markdown.\\\n", "Include details of company culture, customers and careers/jobs if you have the information.\"\n"]}, {"cell_type": "markdown", "id": "c5766318-97cc-4442-bb9f-fa8c6998777e", "metadata": {}, "source": ["### User prompt"]}, {"cell_type": "markdown", "id": "d6e224b2-8ab0-476e-96c3-42763ad21f25", "metadata": {}, "source": ["### Generate brochure in English"]}, {"cell_type": "code", "execution_count": null, "id": "6ab83d92-d36b-4ce0-8bcc-5bb4c2f8ff23", "metadata": {}, "outputs": [], "source": ["def get_brochure_user_prompt(company_name, url):\n", "    user_prompt = f\"You are looking at a company called: {company_name}\\n\"\n", "    user_prompt += f\"Here are the contents of its landing page and other relevant pages; use this information to build a short brochure of the company in markdown.\\n\"\n", "    user_prompt += get_all_details(url)\n", "    user_prompt = user_prompt[:5_000] # Truncate if more than 5,000 characters\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": null, "id": "e44de579-4a1a-4e6a-a510-20ea3e4b8d46", "metadata": {}, "outputs": [], "source": ["def create_brochure(company_name, url):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": get_brochure_user_prompt(company_name, url)}\n", "          ],\n", "    )\n", "    result = response.choices[0].message.content\n", "    display(Markdown(result))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "e093444a-9407-42ae-924a-145730591a39", "metadata": {}, "outputs": [], "source": ["brochure_text = create_brochure(\"HuggingFace\", \"https://huggingface.co\")"]}, {"cell_type": "markdown", "id": "30415c72-d26a-454e-8900-f584977aca96", "metadata": {}, "source": ["### Translate brochure to another language"]}, {"cell_type": "code", "execution_count": null, "id": "2331eb34-12bf-4e88-83f9-a48d97cc83ec", "metadata": {}, "outputs": [], "source": ["translation_sys_prompt = \"You are a language translator who is very good at translating business documents from \\\n", "English to any language. You preserve the formatting, tone and facts contained in the document.\"\n", "\n", "def translate_brochure(brochure, language):\n", "    response = openai.chat.completions.create(\n", "        model=MODEL,\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": translation_sys_prompt},\n", "            {\"role\": \"user\", \"content\": f\"Translate the following document into {language}: {brochure}\"}\n", "          ],\n", "    )\n", "    result = response.choices[0].message.content\n", "    display(Markdown(result))"]}, {"cell_type": "code", "execution_count": null, "id": "112beb4d-984b-4162-8d36-8cef79c351cc", "metadata": {}, "outputs": [], "source": ["translate_brochure(brochure_text, \"Spanish\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}