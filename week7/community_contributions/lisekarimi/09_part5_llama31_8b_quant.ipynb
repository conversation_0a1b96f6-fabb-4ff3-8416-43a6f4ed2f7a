{"cells": [{"cell_type": "markdown", "metadata": {"id": "4WDyBU0Vm0Zl"}, "source": ["# 🔍 Predicting Item Prices from Descriptions (Part 5)\n", "---\n", "-  Data Curation & Preprocessing\n", "- Model Benchmarking – Traditional ML vs LLMs\n", "- E5 Embeddings & RAG\n", "- Fine-Tuning GPT-4o Mini\n", "- ➡️ Evaluating LLaMA 3.1 8B Quantized\n", "- Fine-Tuning LLaMA 3.1 with QLoRA\n", "- Evaluating Fine-Tuned LLaMA\n", "- Summary & Leaderboard\n", "\n", "---\n", "\n", "# 🦙 Part 5: Evaluating LLaMA 3.1 8B Quantized\n", "\n", "- 🧑‍💻 Skill Level: Advanced\n", "- ⚙️ Hardware: ⚠️ GPU required - use Google Colab\n", "- 🛠️ Requirements: 🔑 HF Token\n", "- Tasks:\n", "    - Quantize LLaMA 3.1 8B to 4-bit\n", "    - Define prediction function\n", "    - Evaluate with <PERSON>er\n", "\n", "We know LLaMA 3.1 won’t beat frontier models — but how far behind is it without any tuning?\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "MDyR63OTNUJ6", "outputId": "7e9e5b6b-d11c-45df-d774-2da5f6455d51"}, "outputs": [], "source": ["# Install required packages in Google Colab\n", "%pip install -q datasets torch transformers bitsandbytes accelerate matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-yikV8pRBer9"}, "outputs": [], "source": ["#  imports\n", "\n", "import re\n", "import math\n", "import torch\n", "from huggingface_hub import login\n", "from datasets import load_dataset\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig, set_seed\n", "from google.colab import userdata\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "uuTX-xonNeOK"}, "outputs": [], "source": ["# Google Colab User Data\n", "# Ensure you have set the following in your Google Colab environment:\n", "hf_token = userdata.get('HF_TOKEN')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Constants\n", "\n", "BASE_MODEL = \"meta-llama/Meta-Llama-3.1-8B\"\n", "HF_USER = \"lisekarimi\"\n", "DATASET_NAME = f\"{HF_USER}/pricer-data\"\n", "\n", "login(hf_token, add_to_git_credential=True)"]}, {"cell_type": "markdown", "metadata": {"id": "DTMo_1msQb9X"}, "source": ["## 📥 Load Dataset"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# #If you face NotImplementedError: Loading a dataset cached in a LocalFileSystem is not supported run:\n", "# %pip install -U datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 177, "referenced_widgets": ["048d1b454cdc400ea5979230703770b8", "7dd26897141a4d87bc3893bb1b1bafb3", "c4f6e0b6237c46b98d393d27b35cabe7", "dd525aced49e4ebe8395514601c20b20", "69a35e3916ae488296a70687b5e890de", "e3442871bdb5445ba86e5aa0f0ee8de9", "8981816dd709488e9ace85e6b160892d", "6edd1bd723324c128fec4de5f1758330", "f77a98060e9d48dc8ac4919902ffc88d", "ed6dfe86de2e4957b4e24df3f564c5db", "e253ec71b5104dd291455753a160c7f1", "b802074124bc4d7d979c28ba9e84a432", "76abd56919414e2b8b2b4683d4cb2bd5", "2bb62653ed2d4e86b9eb0476a0333a3a", "58a799a559ff4f2681b586650c35b12e", "878d6cea9b2c40d3b3b58b1c1bff902f", "d00a41c676034c38881da90ae961e936", "da539e354ea540509a2ea7d13dc8dc45", "4f27fc91cbb14aa08d08b848c6689937", "abea7cdfa8624614aca8d8ab3c07a671", "b5ed6e3c852c49c1b904a19e05f5a90b", "ac7225138dfa48b086b30f154f9a1111", "70da4d47bd4c4b57a7f65d82d7a01829", "19f8ee6f626845beaa7154efe4802045", "13556136763b49bda041c92445ee2ad4", "a2f5735e3c314155be432484fcf72fe7", "81c15499cc8e4011b9bd392f660a3b6e", "96b6a830727d48539c181343efada938", "0ead4e0b3435492693636130d2782c25", "50a8e3f2c06c4595931788b18f5152e9", "cebc935dafae4d4eba105d3107c46ddc", "f566351d5c504181b00a53c3c654090f", "4238f42b624142fea3746fb2f03bcc2d", "be8416b487d04d769fd93973b7fe916a", "1bbada4a48444e60a360aba596af77b3", "75436fb8e4eb43e4b0a309871e4d3cc1", "e4ae815e69d3448296e4c3bcb713710e", "72ca180ed6604f148f2f2e61ac97259a", "c0b34963c7a446908fadfb38c958b612", "38fa12125f024935852122d434c2cbf6", "ff564729da354497b606bfb809ac4e33", "9ee352287f8b4e27af617e3427cb3012", "15522242cf72440ca8895496ad5144c4", "e7dc05ebb11a4b30b4806c2628ec6bde", "413fafe61f7c415a9c1c90dea56aa301", "ac2522256e73492d9b5d0e7976d92ff5", "53266635573042b4b94496f38915e6d4", "8da5f5529fef4f1bb884793e503e5fc5", "80b7529e0ad541749bf464a1d8927225", "2203154b7c464105b12f1ef8caf410cd", "87b8c46fa98a4940ab90422ab44d33de", "6dee11eaa4a849bbb58488a233d3719e", "be947f2a2b8a484daa61f45ae06c5232", "0b1de2365ad1497ab2ebfec1be33a720", "326453121cd84c1e95b3b5da0166c931"]}, "id": "5PVXACKHQhh4", "outputId": "80dc4772-ea31-4752-8f97-573efaa43917"}, "outputs": [], "source": ["dataset = load_dataset(DATASET_NAME)\n", "train = dataset['train']\n", "test = dataset['test']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pGJR24lbQlRS", "outputId": "a1bb5e66-1aa9-40b7-c361-562eafae5d8c"}, "outputs": [], "source": ["test[0]"]}, {"cell_type": "markdown", "metadata": {"id": "vrxH6h00P9qc"}, "source": ["## 🦙 Load Tokenizer and Quantized LLaMA Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 418, "referenced_widgets": ["8698049df4ce440d8a16bc34d69231e7", "b0f74f9ed33649ebb16952d0fb0aecab", "00241684d4f64383b032a1362d174d55", "b74ea8552d8145d28f43cf7ad8450d0b", "6838953f363945d79e079c12aeb2232b", "57c4a682571e401f9fec996603fa587d", "e2b93774cb4a4648a4210c7693864ccc", "766c8e2406ce407faf3489229dada75a", "a52ea6c6672b4ab2bae4f669dc45deb6", "381094cbfef64531a58df85e6d453847", "a6a14894a06a4d4b839f3b791cfadd34", "b43b118cae174c3c810f08c2249b80dc", "6502e59cbd344bf6be966923bb3e38fa", "f86fcf5f6bf24ce399c5d64dc83c3690", "46598b397fbc4461bb83ad45000e5569", "dd307f2b56824c6c91b8fe1c028a1704", "b87577323cb44a2f9f3cc0a8219eadf4", "0ed52815268a4d90a224029e9e23e09f", "fafd3011a3cb4e0099a0db77caf843aa", "89bb151875e349118677c9677bec4137", "b8722d98e81d4e3494c9c0b8b01319d4", "b8f661dd658e45a49b5bac2bdf7f3b78", "f031fadfbba14031a944c351fd99b032", "a6bf09a98289481abb0c9882702eb575", "09d54c271e55463495cb9b617d2ec41d", "32bdcc7f7efd47679258f398184dd0ab", "0f090959768e40aa8fabfacdf772d8df", "e40a9c85694c446d84d3493274138178", "ceb1bbb613f0438aa6996d5551f713f3", "59b59d4040e04b65a66f578160d13d43", "079ab8a1fae64a0782ea8035f494f2fd", "1e425099c1044c14986386e5a4ce0b48", "1e3e028ec53448c691abb2cfad4ffd8c", "d2142e9de5ea4dd2b8d00b56354352c3", "cf1d7a58189b4a5bbe6d005de998548c", "5b6e10b9e5a146be85e519c1bc476fcd", "6d9feded70b84654ab79f9d13b065c83", "ec5afa847d364fbc974b35d821ccd931", "fa392b34ae8647668e94aca22c1a5edb", "4d315c92d7c84191a165218d403c0f8d", "c4279579368841b99a46f529c55125e9", "572bb151402e4940a8d7c92156f9711b", "d4536337378b4146b054371b18f83fdc", "14ddae4a15d74c80b1712443853e3f96", "46e827e5d7a94a619536ff08127b6172", "ad7977cc642e4cae890e52d03d753788", "73ba16b54d314d94aaeebea2ba291a94", "1bc2120a87ab4ad99798b1706342bd89", "4c4772ace8c246b9a5c8f870ed27c11b", "d179e366c02f4bb2897cc9f531955e5e", "1134fd00384740d0a39b6de241ca17cd", "5a120718aa934959bd50cf4864b137b4", "46917a8997f942fea0aaf00a95459f93", "6076b184b66c4d50a91bc477c8eea53e", "2538bc7fc4594363934266f25bcd52bc", "f6144dfa2a20416b9e5c28615a5ff129", "35941d364234488da6fcc0997a5cccf7", "8c5e160cc4434ca99f694f5e195a2005", "fa82a4c6e8fc4591aa5652d7d95c6e40", "2a82034aebcb4e3fbaff825ca59817d0", "edcabc56841a4ba68ee53385fe2dc0f6", "e8bf66aa640e433d8d890ae541b21dcd", "eb7b76b25ce44dc1b8eba7cac8bc9671", "c9648aceda71470284f6ed7ce2add462", "7df6ddb46ae3419fbc3fac488eb8a6b6", "3b2aa7ca49e4451fbfc65560a2d3d43d", "6af2831aa4e641568d72df6d13fc074a", "732cb67ef916489298655df845773934", "cc96573e39e148dbac8b0bd299f0f0b0", "4e9cbcdc1cfb495a850be45cf752d3c4", "b811921bdbe84b0dbd9add0f69271ef0", "a1053fdea18348119949b326f3a12651", "90c10be928c54821aacf11705c0513ff", "a543366ec93c486bb2d28d1ff9567197", "97ff48d2660444a1a7503e735e2b2a55", "1fd84a85c98246adb2e18e41c8a9d88f", "fed4a63b10ea4788af8cd181d8d24863", "8bd5f65dda734db1a253897f85428d4c", "2744cec152a44fd483f5cdd8f4de8c70", "47780d4dab77454ab898f6707d8d4168", "6653b71e07bc488ebbb4ed5728564ccd", "59d44fcd08114cb4aaeea768b1438bcf", "18cf08eb051d48c9a5c0b6b827507b7f", "fee935f9fb354a67a37d42641ff0d81e", "22ec450031234856a304ccee34d452f4", "4944567015cc46be83a8524c0542722a", "b80d78f92da64255991b4fcfde98b1d4", "e239b0fe8301409f9dd7e5e801949ec2", "6e7533e6b43c4f1dbb1e0421b99fdc47", "4be15c8712e340b3b9d9a3bd1c7c7516", "337d98c0886948929a48411422a81ff8", "83d8d49cf93c4af7bb3e3cfa3234c6c6", "a1fb4ca7292e4cdc85b522248fdddaf6", "ce26e74cc006450ca4e44bee2d14d80c", "c01c7b35b1914ae681550421c0035a8c", "c7966f356f80422abb3dcb45dbc541db", "a86bb39581e1430a8314a616951af75e", "446c7ef56bcc437388d4a99859c1b9fb", "5c16ffe6a5504f2585aa6bc3132ff2ee", "9d466ef4939c43f2846f22a5a21e5cd0", "bec4a9e185074743848c04c4aff12037", "cff028485bae4d96b4f7a48b738f6b61", "c02af33357e64469aeb01a7af5a9ab37", "fd0ff0c9933d4238a373c286f8e1dd5d", "c517e6db93f04398b9a3ccc86e090499", "e75b68d16bc443e39974922342952de9", "0536d41437f54df38624a7d290e45325", "eec3717367b348a388bb76eb6482ce25", "e16f1ef5ee06493fac2d5871806a3b3a", "24f7575f0f47498480b2a2f79f0d4ce5", "c17fe53a4a2b4266a3cbd24c9f145cde", "b4d715f23ada4ee48fdfd9af463f7124", "7d9102b6a7b44e14809ecf8fa421ee70", "8d640aa311f34b33b0967e128c138130", "3680065e53494bad98e74fd7c81185dd", "11fb0bacbcc44352b3b25d9f0923c332", "7319ccdb3e3349328d6f9b4bb5445776", "35017cc6cb484eeaa12714532e872f99", "ae22d146f6f24981bde97896ad3d8b14", "3feeca46c382431c9868e4852ca04d49", "0e3b239635704ab391f1801b762b7f93", "90d35d2eda00413eba027093309f6c31", "d61446d3664a455baaada9761a1715be", "92b7d7f81ebc441d8e6d6e20477aa37c", "8b5b230489104f6bba63720fa9fad0ae", "0afc992c54ec4a10a7f9fd3e45fa7761", "4fbbb9ba6f4e44d6b2ccc5197dad5488", "18e5f93ef3b64301b7c1548d17843d64", "ee997f8eeccc4dd98aea71b930531cf5", "e11a6cfa4615457090d4c87815fdb716", "4d74f0ec93f54e09a22c3cb93a042570", "31cdf14402f34270bdc1b1efd2a0d011"]}, "id": "TAit9IzsQLcc", "outputId": "176a77ad-0245-4a3d-b9f3-e139de359da7"}, "outputs": [], "source": ["quant_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,\n", "    bnb_4bit_use_double_quant=True,\n", "    bnb_4bit_compute_dtype=torch.bfloat16,\n", "    bnb_4bit_quant_type=\"nf4\"\n", ")\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL, trust_remote_code=True)\n", "tokenizer.pad_token = tokenizer.eos_token\n", "tokenizer.padding_side = \"right\"\n", "\n", "base_model = AutoModelForCausalLM.from_pretrained(\n", "    BASE_MODEL,\n", "    quantization_config=quant_config,\n", "    device_map=\"auto\",\n", ")\n", "base_model.generation_config.pad_token_id = tokenizer.pad_token_id\n", "\n", "print(f\"Memory footprint: {base_model.get_memory_footprint() / 1e9:.1f} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🤖 Prediction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "1FfMJ2JbzEr3"}, "outputs": [], "source": ["def extract_price(s):\n", "    if \"Price is $\" in s:\n", "      contents = s.split(\"Price is $\")[1]\n", "      contents = contents.replace(',','').replace('$','')\n", "      match = re.search(r\"[-+]?\\d*\\.\\d+|\\d+\", contents)\n", "      return float(match.group()) if match else 0\n", "    return 0"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "CgN8eRttRAZx"}, "outputs": [], "source": ["def model_predict(prompt):\n", "    set_seed(42)\n", "    inputs = tokenizer.encode(prompt, return_tensors=\"pt\").to(\"cuda\")\n", "    attention_mask = torch.ones(inputs.shape, device=\"cuda\")\n", "    outputs = base_model.generate(inputs, max_new_tokens=4, attention_mask=attention_mask, num_return_sequences=1)\n", "    response = tokenizer.decode(outputs[0])\n", "    return extract_price(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "hpdEk2-FW6aT", "outputId": "f8913c56-1a8f-4a13-9084-21acfdb64ceb"}, "outputs": [], "source": ["model_predict(test[0]['text']), test[0]['price']"]}, {"cell_type": "markdown", "metadata": {"id": "auFzPUJKTLln"}, "source": ["## 🧪 Run Evaluation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "jnoI1EWGTUau"}, "outputs": [], "source": ["# Helper class for evaluating model predictions\n", "\n", "GREEN = \"\\033[92m\"\n", "YELLOW = \"\\033[93m\"\n", "RED = \"\\033[91m\"\n", "RESET = \"\\033[0m\"\n", "COLOR_MAP = {\"red\":RED, \"orange\": <PERSON><PERSON>L<PERSON>, \"green\": <PERSON>RE<PERSON>}\n", "\n", "class Tester:\n", "\n", "    def __init__(self, predictor, data, title=None, size=100):\n", "        self.predictor = predictor\n", "        self.data = data\n", "        self.title = title or predictor.__name__.replace(\"_\", \" \").title()\n", "        self.size = size\n", "        self.guesses = []\n", "        self.truths = []\n", "        self.errors = []\n", "        self.sles = []\n", "        self.colors = []\n", "\n", "    def color_for(self, error, truth):\n", "        if error<40 or error/truth < 0.2:\n", "            return \"green\"\n", "        elif error<80 or error/truth < 0.4:\n", "            return \"orange\"\n", "        else:\n", "            return \"red\"\n", "\n", "    def run_datapoint(self, i):\n", "        datapoint = self.data[i]\n", "        guess = self.predictor(datapoint[\"text\"])\n", "        truth = datapoint[\"price\"]\n", "        error = abs(guess - truth)\n", "        log_error = math.log(truth+1) - math.log(guess+1)\n", "        sle = log_error ** 2\n", "        color = self.color_for(error, truth)\n", "        # title = datapoint[\"text\"].split(\"\\n\\n\")[1][:20] + \"...\"\n", "        self.guesses.append(guess)\n", "        self.truths.append(truth)\n", "        self.errors.append(error)\n", "        self.sles.append(sle)\n", "        self.colors.append(color)\n", "        # print(f\"{COLOR_MAP[color]}{i+1}: Guess: ${guess:,.2f} Truth: ${truth:,.2f} Error: ${error:,.2f} SLE: {sle:,.2f} Item: {title}{RESET}\")\n", "\n", "    def chart(self, title):\n", "        # max_error = max(self.errors)\n", "        plt.figure(figsize=(12, 8))\n", "        max_val = max(max(self.truths), max(self.guesses))\n", "        plt.plot([0, max_val], [0, max_val], color='deepskyblue', lw=2, alpha=0.6)\n", "        plt.scatter(self.truths, self.guesses, s=3, c=self.colors)\n", "        plt.xlabel('Ground Truth')\n", "        plt.ylabel('Model Estimate')\n", "        plt.xlim(0, max_val)\n", "        plt.ylim(0, max_val)\n", "        plt.title(title)\n", "\n", "        # Add color legend\n", "        from matplotlib.lines import Line2D\n", "        legend_elements = [\n", "            Line2D([0], [0], marker='o', color='w', label='Accurate (green)', markerfacecolor='green', markersize=8),\n", "            Line2D([0], [0], marker='o', color='w', label='Medium error (orange)', markerfacecolor='orange', markersize=8),\n", "            Line2D([0], [0], marker='o', color='w', label='High error (red)', markerfacecolor='red', markersize=8)\n", "        ]\n", "        plt.legend(handles=legend_elements, loc='upper right')\n", "\n", "        plt.show()\n", "\n", "\n", "    def report(self):\n", "        average_error = sum(self.errors) / self.size\n", "        rmsle = math.sqrt(sum(self.sles) / self.size)\n", "        hits = sum(1 for color in self.colors if color==\"green\")\n", "        title = f\"{self.title} Error=${average_error:,.2f} RMSLE={rmsle:,.2f} Hits={hits/self.size*100:.1f}%\"\n", "        self.chart(title)\n", "\n", "    def run(self):\n", "        self.error = 0\n", "        for i in range(self.size):\n", "            self.run_datapoint(i)\n", "        self.report()\n", "\n", "    @classmethod\n", "    def test(cls, function, data):\n", "        cls(function, data).run()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 692}, "id": "1wA5uVgpTWLC", "outputId": "5a597437-50c8-419c-c1da-af0166dabe0f"}, "outputs": [], "source": ["Tester.test(model_predict, test)"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {"id": "lSfPbebIq2Ml"}, "source": ["![image.png](attachment:image.png)\n", "\n", "Alright — now that we know where things stand, it’s time to shake things up.\n", "\n", "Can QLoRA fine-tuning unlock the true power of LLaMA 3.1?\n", "\n", "👀 Let’s find out... in the [next notebook](https://github.com/lisekarimi/lexo/blob/main/09_part6_ft_llama_qlora.ipynb)"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}