{"cells": [{"cell_type": "markdown", "metadata": {"id": "GHsssBgWM_l0"}, "source": ["# 🔍 Predicting Item Prices from Descriptions (Part 7)\n", "---\n", "- Data Curation & Preprocessing\n", "- Model Benchmarking – Traditional ML vs LLMs\n", "- E5 Embeddings & RAG\n", "- Fine-Tuning GPT-4o Mini\n", "- Evaluating LLaMA 3.1 8B Quantized\n", "- Fine-Tuning LLaMA 3.1 with QLoRA\n", "- ➡️ Evaluating Fine-Tuned LLaMA\n", "- Summary & Leaderboard\n", "\n", "---\n", "\n", "# 🧪 Part 7: Evaluating the Fine-Tuned LLaMA 3.1 8B (Quantized)\n", "\n", "- 🧑‍💻 Skill Level: Advanced\n", "- ⚙️ Hardware: ⚠️ GPU required - use Google Colab\n", "- 🛠️ Requirements: 🔑 HF Token\n", "- Tasks:\n", "    - Load the tokenizer and fine-tuned base model\n", "    - Load the PEFT adapter for the fine-tuned weights\n", "    - Run evaluation — the moment of truth!\n", "\n", "🔔 **Reminder:**  \n", "As mentioned in Part 6, I fine-tuned the model on only 20K samples.  \n", "In this notebook, we’ll evaluate both this model and the full 400K-sample version fine-tuned by our instructor.\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "MDyR63OTNUJ6"}, "outputs": [], "source": ["# Install required packages in Google Colab\n", "%pip install -q datasets transformers torch peft bitsandbytes matplotlib"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "-yikV8pRBer9"}, "outputs": [], "source": ["# imports\n", "\n", "import math\n", "import torch\n", "from huggingface_hub import login\n", "import torch.nn.functional as F\n", "from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig, set_seed\n", "from datasets import load_dataset\n", "from peft import PeftModel\n", "import matplotlib.pyplot as plt\n", "from google.colab import userdata"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "WyFPZeMcM88v"}, "outputs": [], "source": ["# Google Colab User Data\n", "# Ensure you have set the following in your Google Colab environment:\n", "hf_token = userdata.get('HF_TOKEN')\n", "login(hf_token, add_to_git_credential=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "30lzJXBH7BcK"}, "outputs": [], "source": ["# Helper class for evaluating model predictions\n", "\n", "GREEN = \"\\033[92m\"\n", "YELLOW = \"\\033[93m\"\n", "RED = \"\\033[91m\"\n", "RESET = \"\\033[0m\"\n", "COLOR_MAP = {\"red\":RED, \"orange\": <PERSON><PERSON>L<PERSON>, \"green\": <PERSON>RE<PERSON>}\n", "\n", "class Tester:\n", "\n", "    def __init__(self, predictor, data, title=None, size=250):\n", "        self.predictor = predictor\n", "        self.data = data\n", "        self.title = title or predictor.__name__.replace(\"_\", \" \").title()\n", "        self.size = size\n", "        self.guesses = []\n", "        self.truths = []\n", "        self.errors = []\n", "        self.sles = []\n", "        self.colors = []\n", "\n", "    def color_for(self, error, truth):\n", "        if error<40 or error/truth < 0.2:\n", "            return \"green\"\n", "        elif error<80 or error/truth < 0.4:\n", "            return \"orange\"\n", "        else:\n", "            return \"red\"\n", "\n", "    def run_datapoint(self, i):\n", "        datapoint = self.data[i]\n", "        guess = self.predictor(datapoint[\"text\"])\n", "        truth = datapoint[\"price\"]\n", "        error = abs(guess - truth)\n", "        log_error = math.log(truth+1) - math.log(guess+1)\n", "        sle = log_error ** 2\n", "        color = self.color_for(error, truth)\n", "        # title = datapoint[\"text\"].split(\"\\n\\n\")[1][:20] + \"...\"\n", "        self.guesses.append(guess)\n", "        self.truths.append(truth)\n", "        self.errors.append(error)\n", "        self.sles.append(sle)\n", "        self.colors.append(color)\n", "        # print(f\"{COLOR_MAP[color]}{i+1}: Guess: ${guess:,.2f} Truth: ${truth:,.2f} Error: ${error:,.2f} SLE: {sle:,.2f} Item: {title}{RESET}\")\n", "\n", "    def chart(self, title):\n", "        # max_error = max(self.errors)\n", "        plt.figure(figsize=(12, 8))\n", "        max_val = max(max(self.truths), max(self.guesses))\n", "        plt.plot([0, max_val], [0, max_val], color='deepskyblue', lw=2, alpha=0.6)\n", "        plt.scatter(self.truths, self.guesses, s=3, c=self.colors)\n", "        plt.xlabel('Ground Truth')\n", "        plt.ylabel('Model Estimate')\n", "        plt.xlim(0, max_val)\n", "        plt.ylim(0, max_val)\n", "        plt.title(title)\n", "\n", "        # Add color legend\n", "        from matplotlib.lines import Line2D\n", "        legend_elements = [\n", "            Line2D([0], [0], marker='o', color='w', label='Accurate (green)', markerfacecolor='green', markersize=8),\n", "            Line2D([0], [0], marker='o', color='w', label='Medium error (orange)', markerfacecolor='orange', markersize=8),\n", "            Line2D([0], [0], marker='o', color='w', label='High error (red)', markerfacecolor='red', markersize=8)\n", "        ]\n", "        plt.legend(handles=legend_elements, loc='upper right')\n", "\n", "        plt.show()\n", "\n", "\n", "    def report(self):\n", "        average_error = sum(self.errors) / self.size\n", "        rmsle = math.sqrt(sum(self.sles) / self.size)\n", "        hits = sum(1 for color in self.colors if color==\"green\")\n", "        title = f\"{self.title} Error=${average_error:,.2f} RMSLE={rmsle:,.2f} Hits={hits/self.size*100:.1f}%\"\n", "        self.chart(title)\n", "\n", "    def run(self):\n", "        self.error = 0\n", "        for i in range(self.size):\n", "            self.run_datapoint(i)\n", "        self.report()\n", "\n", "    @classmethod\n", "    def test(cls, function, data):\n", "        cls(function, data).run()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 📥 Load Dataset"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# #If you face NotImplementedError: Loading a dataset cached in a LocalFileSystem is not supported run:\n", "# %pip install -U datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 177, "referenced_widgets": ["61f42f612e98467684716cc7421c7554", "a7e864c2ae21482e8bcdbc42a5a65309", "63405c5e47da4652b052ee6099ead31e", "0864a38b1c494308a07defced89f4fe3", "8f089946a97d4becb3ff06b7a65595a2", "42b865ac9e4f4ecaa475c4d69929e401", "3478290afe1d48268c7c07206c212eda", "f21c0db9205f4c40a2f9ea1ddd66b59e", "4604f38122454bc1b1826311a326eb12", "6e2b95e33cab4fe9b9f555195b634fac", "b8f0f357a61c4502962f385291c3bac8", "fa49b7e56b054faca67334e08bbf622c", "243d84401ba24360a42c2636d7984772", "bbcf01edcbcd425b9ca1e61e80f6df4f", "17b41698c33044c7942e66e63c5c2d2d", "14dfccde2f6a47679cea42ce965b6ef2", "6a1570c8980b4d5ebac78348f79c4f1b", "44f1922676f3417fb7baccd92bf53cea", "176b023546bc4053a4d484205d7ab200", "b02018254c4b4fb680e382974380c331", "766aba35ebf54996990e075e4f692f96", "24ceffd3b8c64e5f983e52d743ebef8d", "5b9076b6c05a4454a7233302114b9d8c", "4bfbd393271844de825a53c7d639fa60", "3313091548bf414fabf84f5aa2c85d14", "f98c7fe4ad6d4649a7a104f973992be0", "fd1eb06d0aa64ba59ae9bb214f2c94ed", "24237203b2c44709b20ca84b95387849", "7910e6a4881a43638c4e91dd0f024092", "f22dad57ee324ca8b927f9a3b8cc6edc", "20a702b1ccbe499eabf70af974561417", "48f72254ce6f408c94bf56a3919c032e", "6bf00cd26256489fb209b8b51ca9fb0e", "da3c453facaf41b6bc89d311d9f1ce74", "78487c1a13e84e7bb35a72a07ad9b681", "3866fe39fcc34120a0b4c4b36c8eaa6c", "54de8e445909429f9d7ca9ad02e8f190", "eeda8994cb8d46cc9d5c2212907ab869", "b670675ee9bc4689a34f997d0da13b82", "56727a21bb4648fe8ae46d3a61b39f4a", "da89c856fbf746b496d37cbef92305b9", "2f4ba348ef7246af8b1cd04352bcbd1d", "0d86b4a93411494eb8e725440e393cff", "203c4888674c46bba1033639ad4286a2", "005dac04aacb4955ae079d36bfc4cd19", "68ff796bdee44aa380324374ae38fd25", "411691dce3f1457cb3ee9e8ad652d61d", "f0fc209cb9e74d0ca3c0c9b14b1450e0", "6e2155c3ad3243508dff34919eecd0a2", "68891d88fe7e417abbd508d2089e7960", "8e1ab77817bc4ec2835b195a0beb1096", "c638e3a09f6b4caaa078e242b010744e", "ee9abd78adb54984868ebee19f638e25", "8280e432938b4e9794c95e47bb9c02fa", "abdd2ff8028b432091434805f81c455c"]}, "id": "cvXVoJH8LS6u", "outputId": "6308b124-a922-4e82-fb6a-5933d3c324e0"}, "outputs": [], "source": ["DATASET_NAME = \"lisekarimi/pricer-data\"\n", "dataset = load_dataset(DATASET_NAME)\n", "train = dataset['train']\n", "test = dataset['test']"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "xb86e__Wc7j_", "outputId": "8b699099-7414-4663-fab1-d069d3ec3d35"}, "outputs": [], "source": ["test[0]"]}, {"cell_type": "markdown", "metadata": {"id": "qJWQ0a3wZ0Bw"}, "source": ["## 📥 <PERSON><PERSON> and Model\n", "The fine-tuned model (PeftModel) only holds the LoRA adapters, so it requires the base model to apply them correctly."], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 401, "referenced_widgets": ["aee2cb6b13d64f1dab9f8190a274bbc0", "547a2807263e4295af11da5a43ccf5b7", "00b57ab6c0c44e39ad6fa27b7e5a085b", "d51c826dc6d749b38ce7e5fdfc730086", "f276602665c148999240ef916aa8a9c8", "9d83d7056aed43a59d82955bdb8f272d", "7a71aedc0f49430ba7c71040c5fa2529", "108880a9a7bb4a73837889ad2a25fd77", "0163275024a041e2bc9fba947c371269", "555a494cbcda41e79ee4584a8122774b", "5dfbe2af8afc43c691c34c52a47c9790", "25edb5ab02c3402998b75cffc13d0a55", "9a4f0361323540aa8428054a0d98ecb3", "cf149d1eceae43a9808e142fbfe5d4ff", "de86c9338690424fa0052e5b055cea88", "2acb3368945a48aeaf9fbc6d22e9238d", "4c4c4b1507814037bcea0519ec43ba26", "6d37385e79904b7ca267ad165774f962", "b14a5f0f71094aa98403edd429cb882e", "31b28b6183c644f9b5601208a1f72499", "d3cddc62e0fb4256bf4c74f6a59e686b", "82cb2192839e451292b27a186daaa7c1", "2e038c429eed4abdae8d27a7226d7298", "364c4658aba64512a1f50cdad9cc12f8", "fde7b1ab1e224fec8e9b761e703b53dd", "ad5db9c88ce64f73992d2e274ca1206a", "0e7ada829b22485ca7a628d2c464f3f1", "ec4f7d2076db4f6a856ab0d5e8edffbd", "3f00114026a4417db1b142e5bcb7a695", "e4e9cf32b99848baa6a587fb235ce6b5", "a109b5ee80574e40a14fa1e186f4f9f4", "af569da703694c27aa9ca2ddce6c4923", "886bb94abf2c437eb8505222c4336e85", "f668156d681e47f39e553f127a44261d", "9bb3d0deaac6439e9ad67c2bc0565ff4", "762b36fde5ac4a2982152f3babfa3ed9", "141911ee360d42ab8dd3b7fa3563bbf0", "340eae69eeaf4e458e6d8134018f4ad4", "3226e3a8c4564f6fbd6ffb3eeb7b45e7", "6ed52680f866470da1e8d4a48b6e42fb", "6d8a206edb824c5eb06c803e8cab14de", "86fd4472a7a84940a54f24104689a74d", "916c0e20af5e4b78a5e86532b0c9a3e8", "62dd475c101e4859a48ee57a272f71bc", "a8b7185a12c94adca0e63563d7df3ce4", "47d57186838d466fb91b6666df85d1b4", "9d37814d818c466c90892bf1f6e9a190", "b5fdba30791649a792d192a131890a4e", "789fe6f5489345c6a8b6a889d20e0ca0", "5ce12a0983bb49f1a871598a6b9a0a13", "d9eb89d218a44f21bb4447040e5c8925", "b04aaa7931e74297a55bca3ebf4ded1d", "837708f48ded4d78b7ad2e0dc6464e9c", "32236e0d0b3e46e4b2c26b7ccb63c89e", "499acde0cedf4ea1a90415f98660aaa5", "840d3e7824944889ac2091b35f0c17c0", "08f2fae4688b45729d8f5bf53837e56d", "133bb5607eb0457888b1fe4e8d3fab3e", "46bfe5feb9074050b556d804a544140d", "4c3b0c2d04d24ec6abe8acbadb420712", "eda1fcca6987495b87cf2206f93a0ecb", "00b803cf92754db1bbea8ca909e5ccef", "17e17b928555462abfbfa4caf7992427", "35f90fa89e8842cdaa487b59da45b3e8", "2887ef88074c4591b710688fa76329bf", "0a0c5f00b3cc477e8b7e06550fc6f1cb", "3b079fe81b7b44d796c531bec1754637", "e82f8ac6e8eb4ed6a6743e10b8b99904", "1f7de1e2970c4c8fbfe1ab400297e1a7", "7ea0d8782a1f4cca9a64b95fe47e8a2e", "689b49d52b8f4efb94f80d76a0fefab3", "2005939305c442f7bed3b83ea16e13b1", "1a6f2631e29444818fdbd9a0de265367", "6bfc89e091a5448d94d2ea559ce43a21", "bfc12d40caf4481280888506dfa01505", "a1fb82d5761843a49a0993ff937cb40d", "4c9c567918ee478a817b51e2a204d915", "305623f276ba45e5a57727d1829158e1", "b2722e271f78405b9151804ffc522530", "963435e51a7a4ce98510c0372cd05030", "d394cfc6af384a39b87c72ac6a3788d9", "2c621a7a90ed4bfd8b52cea9c79e11c1", "59ac0bb5c046448fbf16a27d2c3205f8", "7617f5670879416d9dbc2dabda76ef4d", "b32d6d6ff5dd4ac4adfb063205111707", "38f3a7159fc34d89bc18e4225473615d", "2a2c386e432f429f86c303d71472b480", "ece25eb325004ae48ec5ec00055dd845", "68e2b37bbd9a44f8a6032526acbf9ea6", "3af191957e3f453ba803a1c01d6969ae", "29dba394a6664e0f8984bcb966ccf19b", "d84373a3f97245ae94bfb666c7e93a17", "9f917250ccbf4078a90fda1eec71c6f4", "8171dd4382d24f0a83484fbf967fec03", "6f97606a500548e980c6481d756c72eb", "6d1054047d4645a69c272484fd9e0c04", "7fd14d942d2246bf8df28eca28e13fb2", "0dabd208524f426bb5c643791e736413", "368dea7bbf144cf0a667493cb23bddab", "d6b14f8e43754283ad96543c4c1ffee6", "f78562ef15524795bb9be326dcaab502", "b01c8091b96444f687a49c5c51b5faf7", "baab647e635a46ababa58993965a8159", "25d9a9b78d554f8fbe92d7e805640c3b", "95726f4b9bc34434b9d00fcdfe2ff87e", "a7b835a668ef40c986a6fd51e464d1f4", "188cac6192fc4b91be3ca5b01bab1d91", "3537ef715f3447388625ee606555bb85", "322ca0ccce644c48a2a0f4b44a38776d", "cc3726d026594cb6ac2d6bafb16562ac", "f48cc4a0a5d041cf9391a99353ff46af", "05134ca3a9954341951ff958ff30fe0a", "3a6aa623f1dd41b8940a41b509fa7500", "fd58111bb44347b8bdcb984a0e86f9b7", "c16cfb96177640a991c5509e652c85b9", "adc0ffacba0846fabd76ed7955397077", "e074da8f28d84ec891f22e30b86fb954", "0b53df078f4a4a259b677ccccbdf46cd", "954d5fa3b18a49589717cfc31fb58779", "af0beb46b198458794c85803fe5af47f", "c7322d41ae4c4068880521a136e923b4", "391d834aa8734d7b9a97c03cab5e1e7d", "5d779fc6bb1244449a68cf62dfd15698", "197ca7f2357a4a2c89f5f3da3844c606", "df4d22e6876b4c0082a7ace3281ff4e5", "28d44cfae7de4b62be11020d9015f92c", "3e8d7274ee3a4dfbbdd44ea0b2cd61b6", "fa768ce193b94a4882a1e796e69cffea", "c37a4882e4474f8690c4b479baf2d785", "68a033bbcb4d4774bdb115e09d78365b", "10b5e7970aa04bd6b3384aa645c48d92", "f838b073dd254bb091a7db7175cd2ce8"]}, "id": "lAUAAcEC6ido", "outputId": "b2983922-5036-4083-8cba-0cb3f51fbc51"}, "outputs": [], "source": ["BASE_MODEL = \"meta-llama/Meta-Llama-3.1-8B\"\n", "\n", "quant_config = BitsAndBytesConfig(\n", "    load_in_4bit=True,  # Reduce the precision to 4 bits\n", "    bnb_4bit_use_double_quant=True,\n", "    bnb_4bit_compute_dtype=torch.bfloat16,\n", "    bnb_4bit_quant_type=\"nf4\"\n", ")\n", "\n", "# <PERSON><PERSON> the Tokenizer and the Model\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(BASE_MODEL, trust_remote_code=True)\n", "tokenizer.pad_token = tokenizer.eos_token\n", "tokenizer.padding_side = \"right\"\n", "\n", "base_model = AutoModelForCausalLM.from_pretrained(\n", "    BASE_MODEL,\n", "    quantization_config=quant_config,\n", "    device_map=\"auto\",\n", ")\n", "base_model.generation_config.pad_token_id = tokenizer.pad_token_id\n"]}, {"cell_type": "markdown", "metadata": {"id": "2RJ0G-WRJGMK"}, "source": ["## 🧪 Load and Evaluate the Fine-Tuned Model with PEFT Adapters"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 20<PERSON>-Tuned Model"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["f0c0a20172294f77a0306801f8d76fb7", "f68ee0810c2a4ac087ac6ece5279fb09", "8aa12b380191454ebf55e8b42d0e0f2b", "63f6cfa30a274ee3835671d8e39a85ef", "0b980946a50d4248a4c63ef117fc2e8f", "18283c6dee9447ddaca34ad267773e48", "a7d10d9147df4adebf913e3023c2a3a4", "5886ca455d4d4aefa617478f4f69a3ca", "8c0e83bce4f74e7ba337fc9af5b977b8", "00dbc32bdb0440c0bc3ba2cc6677b04c", "243e6d8479ac4958a8d877e28f9b514a", "10b7df1ecfab4e5cb146932fc4fb2c17", "07c6fd1fe1ac442dbeb7037161841b78", "88adf6ab3f3e476fa66ad22e9ff49aa8", "fe522e9cee55448a9c13a5daaad5e7e7", "4b1b9e5a67e54a3b90f2c113355e735a", "5cdbdf93af9344ccabd7c3f236446541", "c4af3ca6696d4fcd9b831d825456c7fa", "525b1673c902412db32691056d49fd35", "42de37b9a74143b4a851a178c484a706", "f5f42d9201dc4fbaaa9c684fdb748d4a", "10a0e99256a149a0a94ff652a4fd259a"]}, "id": "R_O04fKxMMT-", "outputId": "06fc64f8-3407-460b-e093-0293e958915e"}, "outputs": [], "source": ["# Load lisekarimi model (trained on 20K datapoints)\n", "\n", "FINETUNED_MODEL = \"lisekarimi/llama3-pricer-2025-04-08_18.44.04-size20000\"\n", "fine_tuned_model = PeftModel.from_pretrained(base_model, FINETUNED_MODEL)\n", "print(f\"Memory footprint: {fine_tuned_model.get_memory_footprint() / 1e6:.1f} MB\")\n", "fine_tuned_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "Je5dR8QEAI1d"}, "outputs": [], "source": ["# Gets top 3 predicted tokens from the model\n", "# Filters valid numeric outputs (prices)\n", "# Returns a weighted average based on token probabilities\n", "\n", "# This code would be more complex if we couldn't take advantage of the fact\n", "# That <PERSON>lam<PERSON> generates 1 token for any 3 digit number\n", "\n", "top_K = 3\n", "\n", "def improved_model_predict(prompt, device=\"cuda\"):\n", "    set_seed(42) # Reproducibility : same prompt = same o/p every time\n", "    inputs = tokenizer.encode(prompt, return_tensors=\"pt\").to(device)\n", "    attention_mask = torch.ones(inputs.shape, device=device)\n", "\n", "    with torch.no_grad(): # Do not track gradients during inference\n", "        outputs = fine_tuned_model(inputs, attention_mask=attention_mask)\n", "        next_token_logits = outputs.logits[:, -1, :].to('cpu')\n", "\n", "    next_token_probs = <PERSON>.softmax(next_token_logits, dim=-1)\n", "    top_prob, top_token_id = next_token_probs.topk(top_K)\n", "\n", "    prices, weights = [], [] # weights = corresponding probabilities\n", "\n", "    for i in range(top_K):\n", "      predicted_token = tokenizer.decode(top_token_id[0][i])\n", "      probability = top_prob[0][i]\n", "\n", "      try:\n", "        result = float(predicted_token)\n", "      except ValueError as e:\n", "        result = 0.0\n", "\n", "      if result > 0:\n", "        prices.append(result)\n", "        weights.append(probability)\n", "\n", "    if not prices:\n", "      return 0.0, 0.0\n", "\n", "    total = sum(weights)\n", "\n", "    weighted_prices = [price * weight / total for price, weight in zip(prices, weights)]\n", "\n", "    return sum(weighted_prices).item()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "t_GHfTwHXD5f", "outputId": "056b0fc2-5632-4be8-ee24-b6bcefe14ab9"}, "outputs": [], "source": ["improved_model_predict(test[0][\"text\"], device=\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 718}, "id": "W_KcLvyt6kbb", "outputId": "fba4200d-b911-467b-ab3c-17b78aa3b408"}, "outputs": [], "source": ["Tester.test(improved_model_predict, test)"]}, {"attachments": {"0dcb25a7-83fa-4313-a94f-d3a56a0f07bc.png": {"image/png": "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"}}, "cell_type": "markdown", "metadata": {}, "source": ["![image.png](attachment:0dcb25a7-83fa-4313-a94f-d3a56a0f07bc.png)"], "outputs": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 400<PERSON> Fine-Tuned Model"], "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000, "referenced_widgets": ["dd1b57e03f2641d3b702f2cc66942b8f", "e1d477dccbfc44a8a6da301486180e82", "c312a5111a284c3db88f22290869c023", "ce118d8b8146497f9c7fdd3b38188e72", "bc46c271637341bb82d6b87df22ab2af", "602adf3242f54731938b68d3cf68465e", "39fae5e74834421795729a259a046fb8", "0618d8626e2e46cb9a17f86444de3c48", "1cd43b5b2fe445088c84e19773ad861e", "f70a29870ab34f34a1900b2df2bf177e", "41a96c5e35a44b898b872c189f531d3a", "0a524a73d5d6478db81256371bf2bc9b", "275f6179dc624bceaa5d0639fe0b1b00", "79c41b26746344bc9a220f2376360110", "287a6430766c44e5a71dda1048fa2a2c", "3bbe1a454a854747a96fe83e91d6cb3c", "8a93759afe21414fb0d6684f0a591d60", "a3d76b3ce67a495db861bac80cfc0864", "8fc794262ed14fc785c8f06e734c57d4", "7dc967baa0e7427bb66cf3e26849d508", "2d7a6dbd15304347a37dbfb6e5ec7203", "288393e05947444bad11034071015baf"]}, "id": "Kl6n_0sAbU0g", "outputId": "2fb53efb-da22-4c29-a594-c2cf5a079388"}, "outputs": [], "source": ["FINETUNED_MODEL = \"ed-donner/pricer-2024-09-13_13.04.39\"\n", "REVISION = \"e8d637df551603dc86cd7a1598a8f44af4d7ae36\"\n", "fine_tuned_model = PeftModel.from_pretrained(base_model, FINETUNED_MODEL, revision=REVISION)\n", "print(f\"Memory footprint: {fine_tuned_model.get_memory_footprint() / 1e6:.1f} MB\")\n", "fine_tuned_model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 718}, "id": "R0YlorBhbeSE", "outputId": "f42de9bf-d45a-4d2d-c218-fe000d716e54"}, "outputs": [], "source": ["Tester.test(improved_model_predict, test)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["🎉  And there it is — the open-source, quantized, and fine-tuned model outperforms the rest. 🙌 \n", "\n", "📘 We'll continue in [the next notebook](https://github.com/lisekarimi/lexo/blob/main/09_part8_summary.ipynb) with a final wrap-up and summary of key insights.\n"], "outputs": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}