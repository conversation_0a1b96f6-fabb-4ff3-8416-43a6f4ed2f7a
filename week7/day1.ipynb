{"cells": [{"cell_type": "markdown", "id": "c25e5705-7078-4d4a-9fa2-8aaa528ffced", "metadata": {}, "source": ["# Week 7 Day 1\n", "\n", "Fine-tune an open-source model to Predict Product Prices\n", "\n", "Please see this notebook in Google Colab:\n", "\n", "https://colab.research.google.com/drive/15rqdMTJwK76icPBxNoqhI7Ww8UM-Y7ni?usp=sharing"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}