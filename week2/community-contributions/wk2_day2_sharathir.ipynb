{"cells": [{"cell_type": "code", "execution_count": null, "id": "05617f71-449f-42c5-905c-f080d61520ec", "metadata": {}, "outputs": [], "source": ["import gradio as gr\n", "def greet(name):\n", "    return \"Hello \" + name + \"!\"\n", "def shout(name):\n", "    return name.upper()"]}, {"cell_type": "code", "execution_count": null, "id": "c57765d7-5d69-4332-be71-2800296ca8ed", "metadata": {}, "outputs": [], "source": ["#demo = gr.Interface(fn=shout, inputs=gr.Textbox(), outputs=gr.Textbox()) //this works too\n", "demo = gr.Interface(fn=greet, inputs=\"textbox\", outputs=\"textbox\",allow_flagging=\"never\")\n", "demo.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "abbc237a-8da2-4993-b350-8f8a7d807242", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import ollama"]}, {"cell_type": "code", "execution_count": null, "id": "9f021005-2a39-42ec-b671-b24babd0ef1a", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant\""]}, {"cell_type": "code", "execution_count": null, "id": "d1677645-4166-4d77-8567-cae77120f1c3", "metadata": {}, "outputs": [], "source": ["def message_llama(prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    completion = ollama.chat(\n", "        model='llama3.2',\n", "        messages=messages,\n", "    )\n", "    return completion['message']['content']"]}, {"cell_type": "code", "execution_count": null, "id": "33295d15-f4d2-4588-9400-3c1e3c6492f2", "metadata": {}, "outputs": [], "source": ["message_llama(\"what is the date today\")"]}, {"cell_type": "code", "execution_count": null, "id": "38e2594e-6a70-4832-b601-60a6a0d4d671", "metadata": {}, "outputs": [], "source": ["def stream_llama(prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    stream = ollama.chat(\n", "        model='llama3.2',\n", "        messages=messages,\n", "        stream=True\n", "    )\n", "    result = \"\"\n", "    for chunk in stream:\n", "        result += chunk['message']['content'] or \"\"\n", "        yield result\n", "   "]}, {"cell_type": "code", "execution_count": null, "id": "e0ebf588-3d69-4012-9719-23d11fbbf4f5", "metadata": {}, "outputs": [], "source": ["def stream_deepseek(prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    stream = ollama.chat(\n", "        model='deepseek-r1',\n", "        messages=messages,\n", "        stream=True\n", "    )\n", "    result = \"\"\n", "    for chunk in stream:\n", "        result += chunk['message']['content'] or \"\"\n", "        yield result"]}, {"cell_type": "code", "execution_count": null, "id": "7db5aa24-b608-489a-ba26-1a4b627658e2", "metadata": {}, "outputs": [], "source": ["def stream_qwen3(prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    stream = ollama.chat(\n", "        model='qwen3',\n", "        messages=messages,\n", "        stream=True\n", "    )\n", "    result = \"\"\n", "    for chunk in stream:\n", "        result += chunk['message']['content'] or \"\"\n", "        yield result"]}, {"cell_type": "code", "execution_count": null, "id": "d37b5df8-b281-4096-bdc7-5c6a1872cea7", "metadata": {}, "outputs": [], "source": ["def stream_model(prompt, model):\n", "    if model==\"llama3.2\":\n", "        result = stream_llama(prompt)\n", "    elif model==\"deepseek-r1\":\n", "        result = stream_deepseek(prompt)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    yield from result"]}, {"cell_type": "code", "execution_count": null, "id": "eb408edc-6a83-4725-9fb9-1b95ff0c9ed0", "metadata": {}, "outputs": [], "source": ["gr.Interface(fn=stream_model, inputs=[gr.Textbox(label=\"Your Message\"),gr.Dropdown([\"llama3.2\", \"deepseek-r1\"], label=\"Select model\", value=\"llama3.2\")], outputs=[gr.Markdown(label=\"Response\")],flagging_mode=\"never\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "dc7c3aa0-693a-43a0-8f5b-b07c66bb6733", "metadata": {}, "outputs": [], "source": ["gr.Interface(fn=stream_llama, inputs=[gr.Textbox(label=\"Your Message\")], outputs=[gr.Markdown(label=\"Response\")],flagging_mode=\"never\").launch()"]}, {"cell_type": "code", "execution_count": 38, "id": "e45e9b56-5c2f-4b17-bbf4-5691ce35ff15", "metadata": {}, "outputs": [], "source": ["class Website:\n", "    url: str\n", "    title: str\n", "    text: str\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": 55, "id": "f9fcf30e-09c7-4f90-8bf9-8cc588ede95c", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that analyzes the contents of a company website landing page \\\n", "and creates a short brochure about the company for prospective customers, investors and recruits. Respond in markdown.\"\n", "# For Fun\n", "tone_description_fun = \"\"\"\n", "    The tone should be:\n", "    - **Fun and Playful:** Inject humor, use lighthearted language, and maintain an upbeat vibe.\n", "    - **Energetic:** Use active voice, strong verbs, and occasional exclamation points.\n", "    - **Approachable:** Write as if speaking to a friend, using slightly informal language and contractions.\n", "    - **Creative:** Think outside the box for descriptions and calls to action.\n", "    - Avoid sounding childish or overly silly.\n", "\"\"\"\n", "\n", "# For Aggression\n", "tone_description_aggression = \"\"\"\n", "    The tone should be:\n", "    - **Bold and Assertive:** Use strong, direct language that conveys confidence and power.\n", "    - **Challenging:** Pose questions that make the reader reconsider their current solutions.\n", "    - **Urgent:** Imply a need for immediate action and emphasize competitive advantages.\n", "    - **Direct and Punchy:** Employ short, impactful sentences and strong calls to action.\n", "    - **Dominant:** Position the company as a leader and a force to be reckoned with.\n", "    - Avoid being rude, offensive, or overly hostile. Focus on competitive intensity.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": 66, "id": "83dd8aec-f74f-452b-90cc-3ad5bc903037", "metadata": {}, "outputs": [], "source": ["def stream_brochure(company_name, url, model, tone):\n", "    prompt = f\"Please generate a company brochure for {company_name} that embodies the following tone and style guidelines: {tone}. Here is their landing page:\\n\"\n", "    prompt += Website(url).get_contents()\n", "    if model==\"llama\":\n", "        result = stream_llama(prompt)\n", "    elif model==\"deepseek\":\n", "        result = stream_deepseek(prompt)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    yield from result"]}, {"cell_type": "code", "execution_count": 67, "id": "ef1a246f-a3f7-457e-a85c-2076b407f52a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7890\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7890/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["view = gr.Interface(\n", "    fn=stream_brochure,\n", "    inputs=[\n", "        gr.Textbox(label=\"Company name:\"),\n", "        gr.Textbox(label=\"Landing page URL including http:// or https://\"),\n", "        gr.Dropdown([\"llama\", \"deepseek\"], label=\"Select model\"),\n", "        gr.Dropdown([\"tone_description_fun\", \"tone_description_aggression\"])],\n", "    outputs=[gr.<PERSON><PERSON>(label=\"Brochure:\")],\n", "        \n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "0659a1dc-a00b-4cbf-b5ed-d6661fbb57f2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}