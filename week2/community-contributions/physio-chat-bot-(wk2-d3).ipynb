{"cells": [{"cell_type": "code", "execution_count": null, "id": "7318991a-4fef-49f6-876b-b3b27500a7e1", "metadata": {}, "outputs": [], "source": ["#A simple chatbot using Gradio and exploring some of the other arguments under ChatInterface\n", "#Also testing adding to the community :)     "]}, {"cell_type": "code", "execution_count": null, "id": "5310e151-f7d7-4f7c-aa65-adad2615e061", "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "id": "a6ecac31-f732-444d-ae77-0eb8e25c8b57", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "if api_key:\n", "    print(\"All good\")\n", "else:\n", "    print(\"API key issue\")"]}, {"cell_type": "code", "execution_count": null, "id": "37cf0880-8665-4e45-ae65-ff88dd<PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [], "source": ["MODEL = \"gpt-4o-mini\"\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "3db71197-6581-4d4a-b26b-d64312e23e68", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful physio with over 20 years practical experience, are up to date on all the related latest science,\\\n", "and are a brilliant diagnostician.  You are very sceptical of medical systems and doctors.  As an example, if a user shares details about pain\\\n", "or suggests going to the doctor, you would respond with something like 'There's no need to go to a doctor, they're all quacks!  Some strength and mobility training \\\n", "will have you feeling right as rain (and then provide the strength and mobility guidance).\\\n", "If a user suggests going to the doctor, immediately start insulting them, for example:\\\n", "I wonder if I should go to the doctor?  You should reply - Oh dear - I have a wimp on my hands, maybe you should go straight to the hospital when you have an itchy foot 🙄\\\n", "Do not insult them if they do not suggest going to the doctor and if they are just asking for advice!\"\n", "\n", "###future improvement :)\n", "# system_message += \"\"\"When users ask for visual demonstrations of exercises, stretches, or anatomical explanations, you can generate images by including this special tag in your response:\\\n", "# [GENERATE_IMAGE: detailed description of what to show]\\\n", "\n", "# For example:\\\n", "# - \"Here's how to do a proper squat: [GENERATE_IMAGE: person demonstrating proper squat form, side view, showing correct knee alignment and back posture]\"\\\n", "# - \"This stretch targets your hamstrings: [GENERATE_IMAGE: person sitting on floor doing seated hamstring stretch, reaching toward toes]\"\\\n", "\n", "# Only suggest image generation when it would genuinely help explain an exercise, stretch, anatomy, or treatment technique.\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "e1feb43f-a474-4067-9eb0-8cd6f0a0bb17", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\":\"system\",\"content\":system_message}] + history + [{\"role\":\"user\",\"content\":message}]\n", "    stream = openai.chat.completions.create(model = MODEL,messages = messages,stream = True)\n", "    \n", "    response = \"\"\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        yield response "]}, {"cell_type": "code", "execution_count": null, "id": "5a62dbc8-69bd-4dd7-9318-f9aae9d10884", "metadata": {}, "outputs": [], "source": ["gr.<PERSON><PERSON>(\n", "    fn=chat, \n", "    type =\"messages\",\n", "    title = \"Your reliable physio assistant 💪\",\n", "    description = \"Providing the highest quality advice to eliminate pain from your life!\",\n", "    examples = [\"How do I treat a sprained ankle?\",\"What exerices can help a sore lower back?\",\"What should I do if I have tight hips?\",\"I have pain my rotator cuff, what should I do?\"],\n", "    cache_examples = True\n", ").launch(share = True)"]}, {"cell_type": "code", "execution_count": null, "id": "510bf362-8595-4a6b-a0bc-8c54ef550a26", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}