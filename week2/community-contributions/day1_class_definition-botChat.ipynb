{"cells": [{"cell_type": "code", "execution_count": null, "id": "a0adab93-e569-4af0-80f1-ce5b7a116507", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "%run week2/community-contributions/day1_class_definition.ipynb"]}, {"cell_type": "code", "execution_count": null, "id": "4566399a-e16d-41cd-bef4-f34b811e6377", "metadata": {}, "outputs": [], "source": ["gpt_system = \"You are a chatbot who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way.\"\n", "\n", "claude_system = \"You are a very polite, courteous chatbot. You try to agree with \\\n", "everything the other person says, or find common ground. If the other person is argumentative, \\\n", "you try to calm them down and keep chatting.\""]}, {"cell_type": "code", "execution_count": null, "id": "cf3d34e9-f8a8-4a06-aa3a-8faeb5f81e68", "metadata": {}, "outputs": [], "source": ["gpt_startmessage = \"Hello\"\n", "claude_startmessage = \"Hi\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "49335337-d713-4d9e-aba0-41a309c37699", "metadata": {}, "outputs": [], "source": ["print(f\"GPT:\\n{gpt_startmessage}\\n\")\n", "print(f\"Claude:\\n{claude_startmessage}\\n\")\n", "\n", "# startMessage added as user role\n", "gpt=GPT_Wrapper(gpt_system, gpt_startmessage)\n", "claude=<PERSON>(claude_system, claude_startmessage)\n", "\n", "initialMsg = [\n", "    {\"role\": \"system\", \"content\": gpt_system},\n", "    {\"role\": \"assistant\", \"content\": gpt_startmessage}\n", "]\n", "# Replace user for assistant role\n", "gpt.messageSet(initialMsg)\n", "claude.messageSet([{\"role\": \"assistant\", \"content\": claude_startmessage}])\n", "\n", "claude_next=claude_startmessage\n", "for i in range(5):\n", "    gpt.messageAppend(\"user\", claude_next)\n", "    gpt_next = gpt.getResult()\n", "    print(f\"GPT:\\n{gpt_next}\\n\")\n", "    gpt.messageAppend(\"assistant\", gpt_next)\n", "\n", "    claude.messageAppend(\"user\", gpt_next)\n", "    claude_next = claude.getResult()\n", "    print(f\"Claude:\\n{claude_next}\\n\")\n", "    claude.messageAppend(\"assistant\", claude_next)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}