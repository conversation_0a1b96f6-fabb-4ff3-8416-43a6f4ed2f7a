{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Repo link to a LLM App that can help you convert any Excel Spreadsheet with formulas into Pyspark equivalent transformations in a matter of few clicks "]}, {"cell_type": "markdown", "metadata": {}, "source": ["https://github.com/jasj<PERSON>tsinghjaswal/llm_custom_apps"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}