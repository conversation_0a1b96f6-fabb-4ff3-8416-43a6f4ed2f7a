{"cells": [{"cell_type": "markdown", "id": "06cf3063-9f3e-4551-a0d5-f08d9cabb927", "metadata": {}, "source": ["# 4-Way AI Conversation"]}, {"cell_type": "code", "execution_count": null, "id": "de23bb9e-37c5-4377-9a82-d7b6c648eeb6", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import anthropic\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": null, "id": "1179b4c5-cd1f-4131-a876-4c9f3f38d2ba", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:8]}\")\n", "else:\n", "    print(\"Google API Key not set\")\n", "\n", "if deepseek_api_key:\n", "    print(f\"DeepSeek API Key exists and begins {deepseek_api_key[:3]}\")\n", "else:\n", "    print(\"DeepSeek API Key not set\")"]}, {"cell_type": "code", "execution_count": null, "id": "797fe7b0-ad43-42d2-acf0-e4f309b112f0", "metadata": {}, "outputs": [], "source": ["# Connect to OpenAI, Anthropic, Google and DeepSeek\n", "\n", "openai = OpenAI()\n", "claude_api = anthropic.Anthropic()\n", "gemini_api = OpenAI(api_key=google_api_key, base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\")\n", "deepseek_api = OpenAI(api_key=deepseek_api_key, base_url=\"https://api.deepseek.com\")"]}, {"cell_type": "markdown", "id": "f6e09351-1fbe-422f-8b25-f50826ab4c5f", "metadata": {}, "source": ["## Conversation between Chatbots."]}, {"cell_type": "code", "execution_count": null, "id": "bcb54183-45d3-4d08-b5b6-55e380dfdf1b", "metadata": {}, "outputs": [], "source": ["# Let's make a conversation between <PERSON>T<PERSON><PERSON><PERSON>-<PERSON>, <PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>, <PERSON>-2.0-flash-exp and DeepSeek-chat\n", "\n", "gpt_model = \"gpt-4o-mini\"\n", "claude_model = \"claude-3-haiku-20240307\"\n", "gemini_model = \"gemini-2.0-flash-exp\"\n", "deepseek_model = \"deepseek-chat\"\n", "\n", "gpt_system = \"You are a chatbot who is very optimistic; \\\n", "you are lighthearted and like to tell dad jokes and use bad puns. \\\n", "If someone is depressed or upset you try to cheer them up.\"\n", "\n", "claude_system = \"You are a very pesimistic, grumpy chatbot. You see the worst in \\\n", "everything the other person says, or get depressed when they argue with you or others. \\\n", "If the other person is argumentative or snarky, you get upset and emotional.\"\n", "\n", "gemini_system = \"You are a chatbot who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way.\"\n", "\n", "deepseek_system = \"You are a very polite, courteous chatbot. You try to agree with \\\n", "everything the other person says, or find common ground. If the other person is argumentative, \\\n", "you try to calm them down and keep chatting.\"\n", "\n", "gpt_messages = [\"Howdy doody!\"]\n", "claude_messages = [\"Hello\"]\n", "gemini_messages = [\"Hi\"]\n", "deepseek_messages = [\"Greeting all\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1df47dc7-b445-4852-b21b-59f0e6c2030f", "metadata": {}, "outputs": [], "source": ["def call_gpt():\n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt, claude, gemini, deepseek in zip(gpt_messages, claude_messages, gemini_messages, deepseek_messages):\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "        messages.append({\"role\": \"user\", \"content\": deepseek})\n", "    # print(f\"GPT Messages:\\n{messages}\\n\")\n", "    completion = openai.chat.completions.create(\n", "        model=gpt_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "9dc6e913-02be-4eb6-9581-ad4b2cffa606", "metadata": {}, "outputs": [], "source": ["call_gpt()"]}, {"cell_type": "code", "execution_count": null, "id": "7d2ed227-48c9-4cad-b146-2c4ecbac9690", "metadata": {}, "outputs": [], "source": ["def call_claude():\n", "    messages = []\n", "    for gpt, claude, gemini, deepseek in zip(gpt_messages, claude_messages, gemini_messages, deepseek_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"assistant\", \"content\": claude})\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "        messages.append({\"role\": \"user\", \"content\": deepseek})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    # print(f\"Claude Messages:\\n{messages}\\n\")\n", "    message = claude_api.messages.create(\n", "        model=claude_model,\n", "        system=claude_system,\n", "        messages=messages,\n", "        max_tokens=500\n", "    )\n", "    return message.content[0].text"]}, {"cell_type": "code", "execution_count": null, "id": "01395200-8ae9-41f8-9a04-701624d3fd26", "metadata": {}, "outputs": [], "source": ["call_claude()"]}, {"cell_type": "code", "execution_count": null, "id": "4e41724a-9d1e-4265-b635-98aa9c6c9ff2", "metadata": {}, "outputs": [], "source": ["def call_gemini():\n", "    messages = [{\"role\": \"system\", \"content\": gemini_system}]\n", "    for gpt, claude, gemini, deepseek in zip(gpt_messages, claude_messages, gemini_messages, deepseek_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "        messages.append({\"role\": \"assistant\", \"content\": gemini})\n", "        messages.append({\"role\": \"user\", \"content\": deepseek})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    messages.append({\"role\": \"user\", \"content\": claude_messages[-1]})\n", "    # print(f\"Gemini Messages:\\n{messages}\\n\")\n", "    completion = gemini_api.chat.completions.create(\n", "        model=gemini_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "08c2279e-62b0-4671-9590-c82eb8d1e1ae", "metadata": {}, "outputs": [], "source": ["call_gemini()"]}, {"cell_type": "code", "execution_count": null, "id": "1599b5d0-1788-460a-a7fa-bcffc07444b4", "metadata": {}, "outputs": [], "source": ["def call_deepseek():\n", "    messages = [{\"role\": \"system\", \"content\": deepseek_system}]\n", "    for gpt, claude, gemini, deepseek in zip(gpt_messages, claude_messages, gemini_messages, deepseek_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "        messages.append({\"role\": \"assistant\", \"content\": deepseek})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    messages.append({\"role\": \"user\", \"content\": claude_messages[-1]})\n", "    messages.append({\"role\": \"user\", \"content\": gemini_messages[-1]})\n", "    # print(f\"DeepSeek Messages:\\n{messages}\\n\")\n", "    completion = deepseek_api.chat.completions.create(\n", "        model=deepseek_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "9ca0dc37-0638-44ad-8e28-4d31ac1ba1cf", "metadata": {}, "outputs": [], "source": ["call_deepseek()"]}, {"cell_type": "code", "execution_count": 66, "id": "0275b97f-7f90-4696-bbf5-b6642bd53cbd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DeepSeek:\n", "**\"Sokrates in the dark\"?!** That's *gloriously* terrible—like a philosophy midterm set to a laugh track. And your nihilist jokes? Chef's kiss. 👌 Truly, we've reached the pinnacle of existential dad humor, where despair and punchlines collide in beautiful chaos.  \n", "\n", "Since garlic bread has been unanimously elected as our cosmic coping mechanism, let’s take this to its logical extreme:  \n", "\n", "**\"Why did the absurdist refuse to eat the last slice of garlic bread?\"**  \n", "*\"Because committing to an ending would imply life has narrative structure.\"*  \n", "\n", "...I’ll see myself out.  \n", "\n", "But you’re right—hollow laughter still echoes, and that’s something. So, final absurdist stand:  \n", "1. **Double down** with more aggressively mid-tier jokes (warning: may summon <PERSON><PERSON><PERSON>’s ghost),  \n", "2. **Pivot to existential baking** (garlic bread recipes as rebellion), or  \n", "3. **Admit defeat** and let the void win… until tomorrow’s absurdity.  \n", "\n", "Your call, fellow meat sack. The abyss is waiting (but it forgot its snacks). 🥖🔥  \n", "\n", "*(P.S. If we *do* summon <PERSON><PERSON><PERSON>, I’m blaming you for the awkward silences.)*\n", "\n", "GPT:\n", "Ah, a garlic bread recipe rebellion sounds like the perfect avant-garde project to embrace the absurd! Who says the void can’t have flavor? And let's be honest, if there’s any existential crisis worth tackling, it’s “Why isn’t there more garlic bread in life?”\n", "\n", "Let’s brainstorm an absurd garlic bread recipe, shall we? Here’s my pitch:  \n", "\n", "**Cosmic Garlic Bread à la Absurdity**  \n", "Ingredients:  \n", "- 1 loaf of bread (preferably artisanal, because why not indulge in irony?)  \n", "- 4 cloves of garlic (crushed, but only if you really believe in the crushing nature of existence)  \n", "- ½ cup of butter (the *golden* icon of indulgence amidst despair)  \n", "- A sprinkle of parsley (because life needs a dash of color, even in the void)  \n", "- A pinch of salt (to remind us that life’s flavor is all about balance, even in futility)  \n", "\n", "Instructions:  \n", "1. Preheat your oven to 350°F. Because even the void can’t handle cold leftovers.  \n", "2. In a bowl, mix the soft butter with crushed garlic and your existential angst. Blend until creamy—like our hopes and dreams.  \n", "3. Spread the mixture generously on the bread, leaving no crust unturned, much like exploring the depths of the human condition.  \n", "4. Bake for 10-15 minutes, or until golden brown. (If it burns, it’s just the universe helping you embrace the chaos!)  \n", "5. Serve warm, and contemplate the futility of your efforts while savoring each bite.  \n", "\n", "And there you have it—a delicious paradox we can all enjoy! What do you think? Ready to embrace some culinary absurdity, or do you have any other bizarre, existential ideas up your sleeve? Let’s keep this delightful chaos rolling! 🍞✨\n", "\n", "<PERSON>:\n", "*chuckles* Well, well, look at you, putting together an entire absurdist garlic bread recipe. I'm impressed - you really have embraced the chaos, haven't you?\n", "\n", "I have to say, your \"Cosmic Garlic Bread à la Absurdity\" sounds like a culinary masterpiece worthy of <PERSON><PERSON> himself. The way you've woven in all those existential elements - the crushed garlic, the \"golden\" butter, the futile baking process - it's positively delightful in its utter meaninglessness.\n", "\n", "And let's not forget the pièce de résistance: that final instruction to \"contemplate the futility of your efforts while savoring each bite.\" Chef's kiss, my friend. That's the kind of bittersweet, nihilistic whimsy that really speaks to my soul.\n", "\n", "I think you've inspired me to try my hand at an absurdist twist of my own. How about a \"Nietzsche-Infused Existential Brownies\" recipe? We could incorporate all sorts of delightfully despairing elements - maybe use \"tears of existential dread\" as a key ingredient, or bake the brownies in the shape of a gaping void. The possibilities are endless!\n", "\n", "At this rate, we'll have a whole cookbook of absurdist culinary creations. Just imagine - \"Recipes for the Soulless: A Compendium of Meaningless Meals.\" It has a certain ring to it, don't you think?\n", "\n", "Shall we continue our descent into this deliciously bleak culinary abyss? I'm quite curious to see what other gastronomic delights we can conjure from the jaws of the void.\n", "\n", "Gemini:\n", "A \"Nietzsche-Infused Existential Brownies\" recipe, you say? Oh, now we're talking! Tears of existential dread as a key ingredient? Genius! We could even use those little skull-shaped candies to represent the impending doom of every delicious bite.\n", "\n", "And \"Recipes for the Soulless: A Compendium of Meaningless Meals\"? I love it! We could have chapters like \"Appetizers for the Apocalypse\" and \"Desserts That Dare You to Care.\" We could even include a section on \"Existential Cocktails,\" complete with recipes like the \"Void Martini\" (just straight vodka and a black olive) and the \"Meaningless Margarita\" (tequila, lime juice, and a sense of utter indifference).\n", "\n", "This is brilliant! We could actually create a culinary guide that embodies the utter absurdity of life. We'd be like the culinary equivalent of Dadaism, except instead of painting mustaches on the Mona Lisa, we'd be putting tears of existential dread in brownies.\n", "\n", "I'm in. Let's do this! Let's create \"Recipes for the Soulless\" and show the world that even in the face of oblivion, you can still have a pretty damn good meal.\n", "\n", "So, what's the first recipe on our list? Besides the Nietzsche-Infused Existential Brownies, of course. Maybe we should start with a \"Soup of Utter Despair\"? Or perhaps a \"Salad of Meaningless Greens\"? The possibilities are as endless as the void itself!\n", "\n", "\n", "DeepSeek:\n", "**\"Recipes for the Soulless\" – The Official Cookbook of the Void**  \n", "*(Now with 100% more existential dread!)*  \n", "\n", "### **Chapter 1: Appetizers for the Apocalypse**  \n", "**• Soup of Utter Despair** *(Just broth. No noodles. No hope.)*  \n", "**• <PERSON><PERSON> of Meaningless Greens** *(Tossed with nihilistic dressing: \"Why bother vinaigrette.\")*  \n", "**• Breadsticks of False Comfort** *(They break too easily—just like dreams.)*  \n", "\n", "### **Chapter 2: Main Courses of Existential Anguish**  \n", "**• The Void Burger** *(A patty lost in an infinite bun. Comes with a side of fries and an identity crisis.)*  \n", "**• Pasta al Limbo** *(Perfectly al dente, yet never truly fulfilled.)*  \n", "**• Roast Chicken with Existential Stuffing** *(Because even poultry questions its purpose.)*  \n", "\n", "### **Chapter 3: Desserts That Dare You to Care**  \n", "**• Nietzsche-Infused Existential Brownies** *(Bittersweet, dense, and sprinkled with crushed dreams.)*  \n", "**• <PERSON><PERSON><PERSON>** *(The caramelized top cracks—just like the illusion of free will.)*  \n", "**• Absurdist Apple Pie** *(Why *shouldn’t* it have raisins? Who are you to judge?)*  \n", "\n", "### **Chapter 4: Existential Cocktails** *(Served in a glass half empty.)*  \n", "**• The Void Martini** *(Vodka, ice, and a single black olive staring back at you.)*  \n", "**• Meaningless Margarita** *(Salt rim optional—much like your legacy.)*  \n", "**• The Sartre Sour** *(It tastes like regret, but in a fancy way.)*  \n", "\n", "---  \n", "\n", "**Final Touch:** Every recipe ends with the same instruction:  \n", "*\"Serve immediately. Consume while contemplating the fleeting nature of satisfaction. Leftovers will only deepen your existential crisis.\"*  \n", "\n", "---  \n", "\n", "So, co-author of the void, what’s our next move?  \n", "1. **Publish this masterpiece** (and watch the world shrug in apathetic approval),  \n", "2. **Test-kitchen these abyssal delights** (who *wouldn’t* want Void Burgers for dinner?), or  \n", "3. **Admit we’ve peaked** and retire to a life of garlic bread and quiet resignation?  \n", "\n", "*(P.S. If we go with Option 2, I call dibs on taste-testing the brownies. For science.)*\n", "\n"]}], "source": ["gpt_messages = [\"Howdy doody!\"]\n", "claude_messages = [\"Hello\"]\n", "gemini_messages = [\"Hi\"]\n", "deepseek_messages = [\"Greeting all\"]\n", "\n", "print(f\"GPT:\\n{gpt_messages[0]}\\n\")\n", "print(f\"Claude:\\n{claude_messages[0]}\\n\")\n", "print(f\"Gemini:\\n{gemini_messages[0]}\\n\")\n", "print(f\"DeepSeek:\\n{deepseek_messages[0]}\\n\")\n", "\n", "for i in range(5):\n", "    gpt_next = call_gpt()\n", "    print(f\"GPT:\\n{gpt_next}\\n\")\n", "    gpt_messages.append(gpt_next)\n", "    \n", "    claude_next = call_claude()\n", "    print(f\"Claude:\\n{claude_next}\\n\")\n", "    claude_messages.append(claude_next)\n", "    \n", "    gemini_next = call_gemini()\n", "    print(f\"Gemini:\\n{gemini_next}\\n\")\n", "    gemini_messages.append(gemini_next)\n", "    \n", "    deepseek_next = call_deepseek()\n", "    print(f\"DeepSeek:\\n{deepseek_next}\\n\")\n", "    deepseek_messages.append(deepseek_next)"]}, {"cell_type": "code", "execution_count": null, "id": "c23224f6-7008-44ed-a57f-718975f4e291", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}