{"cells": [{"cell_type": "code", "execution_count": 13, "id": "147ce61d-b10e-478e-8300-2fb3101f617c", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import anthropic\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": 14, "id": "2dab29c1-3a8d-45bc-9f45-407419449ba9", "metadata": {}, "outputs": [], "source": ["import google.generativeai"]}, {"cell_type": "code", "execution_count": null, "id": "5fb5b749-d84b-4f8c-bfb9-2f5c4e8a2daa", "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "# openai_api_key = os.getenv('OPENAI_API_KEY')\n", "# anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:8]}\")\n", "else:\n", "    print(\"Google API Key not set\")"]}, {"cell_type": "code", "execution_count": 16, "id": "d34ee171-2647-47cf-9336-2d016480656f", "metadata": {}, "outputs": [], "source": ["google.generativeai.configure()"]}, {"cell_type": "code", "execution_count": 17, "id": "856212a1-d07a-400b-9cef-a198e22f26ac", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that is great at telling jokes\"\n", "user_prompt = \"Tell a light-hearted joke for an audience of Data Scientists\""]}, {"cell_type": "code", "execution_count": 18, "id": "47289056-cc2b-4d2d-9e18-ecd65c0f3232", "metadata": {}, "outputs": [], "source": ["prompts = [\n", "    {\"role\": \"system\", \"content\": system_message},\n", "    {\"role\": \"user\", \"content\": user_prompt}\n", "  ]"]}, {"cell_type": "code", "execution_count": null, "id": "6d331aaf-162b-499e-af7e-5e097e84f1bd", "metadata": {}, "outputs": [], "source": ["# The API for Gemini has a slightly different structure.\n", "# I've heard that on some PCs, this Gemini code causes the Kernel to crash.\n", "# If that happens to you, please skip this cell and use the next cell instead - an alternative approach.\n", "\n", "gemini = google.generativeai.GenerativeModel(\n", "    model_name='gemini-1.5-flash',\n", "    system_instruction=system_message\n", ")\n", "response = gemini.generate_content(user_prompt)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": null, "id": "b727ee91-92b8-4d62-9a03-1b85a76b905c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}