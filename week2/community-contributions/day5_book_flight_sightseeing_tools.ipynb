{"cells": [{"cell_type": "markdown", "id": "ddfa9ae6-69fe-444a-b994-8c4c5970a7ec", "metadata": {}, "source": ["# Project - Airline AI Assistant\n", "\n", "We'll now bring together what we've learned to make an AI Customer Support assistant for an Airline"]}, {"cell_type": "code", "execution_count": 1, "id": "8b50bbe2-c0b1-49c3-9a5c-1ba7efa2bcb4", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": 2, "id": "747e8786-9da8-4342-b6c9-f5f69c2e22ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n"]}], "source": ["# Initialization\n", "\n", "load_dotenv(override=True)\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "MODEL = \"gpt-4o-mini\"\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": 3, "id": "0a521d84-d07c-49ab-a0df-d6451499ed97", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant for an Airline called FlightAI. \"\n", "system_message += \"Give short, courteous answers, no more than 1 sentence. \"\n", "system_message += \"Always be accurate. If you don't know the answer, say so.\""]}, {"cell_type": "code", "execution_count": null, "id": "61a2a15d-b559-4844-b377-6bd5cb4949f6", "metadata": {}, "outputs": [], "source": ["# This function looks rather simpler than the one from my video, because we're taking advantage of the latest Gradio updates\n", "\n", "def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    return response.choices[0].message.content\n", "\n", "gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "id": "36bedabf-a0a7-4985-ad8e-07ed6a55a3a4", "metadata": {}, "source": ["## Tools\n", "\n", "Tools are an incredibly powerful feature provided by the frontier LLMs.\n", "\n", "With tools, you can write a function, and have the LLM call that function as part of its response.\n", "\n", "Sounds almost spooky.. we're giving it the power to run code on our machine?\n", "\n", "Well, kinda."]}, {"cell_type": "code", "execution_count": 4, "id": "0696acb1-0b05-4dc2-80d5-771be04f1fb2", "metadata": {}, "outputs": [], "source": ["# Let's start by making a useful function\n", "\n", "ticket_prices = {\"london\": \"$799\", \"paris\": \"$899\", \"tokyo\": \"$1400\", \"berlin\": \"$499\", \"athens\": \"$599\", \"kastoria\": \"$999\"}\n", "\n", "def get_ticket_price(destination_city):\n", "    print(f\"Tool get_ticket_price called for {destination_city}\")\n", "    city = destination_city.lower()\n", "    return ticket_prices.get(city, \"Unknown\")"]}, {"cell_type": "code", "execution_count": 5, "id": "80ca4e09-6287-4d3f-997d-fa6afbcf6c85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tool get_ticket_price called for London\n"]}, {"data": {"text/plain": ["'$799'"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["get_ticket_price(\"London\")"]}, {"cell_type": "code", "execution_count": 6, "id": "2054c00e", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "# Create a function for the booking system\n", "def get_booking(destination_city):\n", "    print(f\"Tool get_booking called for {destination_city}\")\n", "    city = destination_city.lower()\n", "    \n", "    # Example data for different cities\n", "    flight_info = {\n", "        \"london\": {\"flight_number\": \"BA123\", \"departure_time\": \"10:00 AM\", \"gate\": \"A12\"},\n", "        \"paris\": {\"flight_number\": \"AF456\", \"departure_time\": \"12:00 PM\", \"gate\": \"B34\"},\n", "        \"tokyo\": {\"flight_number\": \"JL789\", \"departure_time\": \"02:00 PM\", \"gate\": \"C56\"},\n", "        \"berlin\": {\"flight_number\": \"LH101\", \"departure_time\": \"04:00 PM\", \"gate\": \"D78\"},\n", "        \"athens\": {\"flight_number\": \"OA202\", \"departure_time\": \"06:00 PM\", \"gate\": \"E90\"},\n", "        \"kastoria\": {\"flight_number\": \"KAS303\", \"departure_time\": \"08:00 PM\", \"gate\": \"F12\"}\n", "    }\n", "    \n", "    if city in flight_info:\n", "        info = flight_info[city]\n", "        status = random.choice([\"available\", \"not available\"])\n", "        return f\"Flight {info['flight_number']} to {destination_city.lower()} is {status}. Departure time: {info['departure_time']}, Gate: {info['gate']}.\"\n", "    else:\n", "        return \"Unknown destination city.\""]}, {"cell_type": "code", "execution_count": 7, "id": "ef334206", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tool get_booking called for London\n"]}, {"data": {"text/plain": ["'Flight BA123 to london is not available. Departure time: 10:00 AM, Gate: A12.'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["get_booking(\"London\")"]}, {"cell_type": "code", "execution_count": 8, "id": "b011afc2", "metadata": {}, "outputs": [], "source": ["sightseeing_info = {\"london\": \"London Eye, Big Ben, Tower of London\", \n", "                    \"paris\": \"Eiffel Tower, Louvre Museum, Notre-Dame Cathedral\", \n", "                    \"tokyo\": \"Tokyo Tower, Senso-ji Temple, Meiji Shrine\", \n", "                    \"berlin\": \"Brandenburg Gate, Berlin Wall, Museum Island\", \n", "                    \"athens\": \"Acropolis, Parthenon, Temple of Olympian Zeus\", \n", "                    \"kastoria\": \"Cave of Dragon, Kastoria Lake, Byzantine Museum\"}\n", "\n", "\n", "def get_sightseeing(destination_city):\n", "    print(f\"Tool get_ticket_price called for {destination_city}\")\n", "    city = destination_city.lower()\n", "    return sightseeing_info.get(city, \"Unknown\")\n", "    "]}, {"cell_type": "code", "execution_count": 9, "id": "3008e353", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tool get_ticket_price called for Kastoria\n"]}, {"data": {"text/plain": ["'Cave of Dragon, Kastoria Lake, Byzantine Museum'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["get_sightseeing(\"Kastoria\")"]}, {"cell_type": "code", "execution_count": 10, "id": "4afceded-7178-4c05-8fa6-9f2085e6a344", "metadata": {}, "outputs": [], "source": ["# There's a particular dictionary structure that's required to describe our function:\n", "\n", "price_function = {\n", "    \"name\": \"get_ticket_price\",\n", "    \"description\": \"Get the price of a return ticket to the destination city. Call this whenever you need to know the ticket price, for example when a customer asks 'How much is a ticket to this city'\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to travel to\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}\n", "\n", "# Book flight function description and properties\n", "\n", "book_flight_function = {\n", "    \"name\": \"book_flight\",\n", "    \"description\": \"Book a flight to the destination city. Call this whenever a customer wants to book a flight.\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to travel to\",\n", "            },\n", "            \"departure_date\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The date of departure (YYYY-MM-DD)\",\n", "            },\n", "            \"return_date\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The date of return (YYYY-MM-DD)\",\n", "            },\n", "            \"passenger_name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The name of the passenger\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\", \"departure_date\", \"return_date\", \"passenger_name\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}\n", "\n", "sightseeing_function = {\n", "    \"name\": \"sightseeing\",\n", "    \"description\": \"Get the top sightseeing recommendations for the destination city. Call this whenever a customer asks 'What are the top things to do in this city'\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to travel to\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 11, "id": "bdca8679-935f-4e7f-97e6-e71a4d4f228c", "metadata": {}, "outputs": [], "source": ["# And this is included in a list of tools:\n", "\n", "tools = [{\"type\": \"function\", \"function\": price_function}, \n", "         {\"type\": \"function\", \"function\": book_flight_function},\n", "         {\"type\": \"function\", \"function\": sightseeing_function}]"]}, {"cell_type": "markdown", "id": "c3d3554f-b4e3-4ce7-af6f-68faa6dd2340", "metadata": {}, "source": ["## Getting OpenAI to use our Tool\n", "\n", "There's some fiddly stuff to allow OpenAI \"to call our tool\"\n", "\n", "What we actually do is give the LLM the opportunity to inform us that it wants us to run the tool.\n", "\n", "Here's how the new chat function looks:"]}, {"cell_type": "code", "execution_count": 13, "id": "ce9b0744-9c78-408d-b9df-9f6fd9ed78cf", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages, tools=tools)\n", "\n", "    if response.choices[0].finish_reason==\"tool_calls\":\n", "        message = response.choices[0].message\n", "        response, city = handle_tool_call(message)\n", "        messages.append(message)\n", "        messages.append(response)\n", "        response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 12, "id": "b0992986-ea09-4912-a076-8e5603ee631f", "metadata": {}, "outputs": [], "source": ["# We have to write that function handle_tool_call:\n", "\n", "def handle_tool_call(message):\n", "    tool_call = message.tool_calls[0]\n", "    print(f\"Tool call: {tool_call}\")\n", "    arguments = json.loads(tool_call.function.arguments)\n", "    city = arguments.get('destination_city')\n", "    price = get_ticket_price(city)\n", "    book =  get_booking(city)\n", "    sightseeing = get_sightseeing(city)\n", "    print (book)\n", "    response = {\n", "        \"role\": \"tool\",\n", "        \"content\": json.dumps({\"destination_city\": city,\"price\": price, \"booking\": book, \"sightseeing\": sightseeing}),\n", "        \"tool_call_id\": tool_call.id\n", "    }\n", "    return response, city"]}, {"cell_type": "code", "execution_count": null, "id": "f4be8a71-b19e-4c2f-80df-f59ff2661f14", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "id": "473e5b39-da8f-4db1-83ae-dbaca2e9531e", "metadata": {}, "source": ["# Let's go multi-modal!!\n", "\n", "We can use DALL-E-3, the image generation model behind GPT-4o, to make us some images\n", "\n", "Let's put this in a function called artist.\n", "\n", "### Price alert: each time I generate an image it costs about 4 cents - don't go crazy with images!"]}, {"cell_type": "code", "execution_count": 14, "id": "2c27c4ba-8ed5-492f-add1-02ce9c81d34c", "metadata": {}, "outputs": [], "source": ["# Some imports for handling images\n", "\n", "import base64\n", "from io import BytesIO\n", "from PIL import Image"]}, {"cell_type": "code", "execution_count": 15, "id": "773a9f11-557e-43c9-ad50-56cbec3a0f8f", "metadata": {}, "outputs": [], "source": ["def artist(city):\n", "    image_response = openai.images.generate(\n", "            model=\"dall-e-3\",\n", "            prompt=f\"An image representing a vacation in {city}, showing tourist spots and everything unique about {city}, in a vibrant pop-art style\",\n", "            size=\"1024x1024\",\n", "            n=1,\n", "            response_format=\"b64_json\",\n", "        )\n", "    image_base64 = image_response.data[0].b64_json\n", "    image_data = base64.b64decode(image_base64)\n", "    return Image.open(BytesIO(image_data))"]}, {"cell_type": "code", "execution_count": null, "id": "d877c453-e7fb-482a-88aa-1a03f976b9e9", "metadata": {}, "outputs": [], "source": ["image = artist(\"Athens\")\n", "display(image)"]}, {"cell_type": "code", "execution_count": null, "id": "728a12c5-adc3-415d-bb05-82beb73b079b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "f4975b87-19e9-4ade-a232-9b809ec75c9a", "metadata": {}, "source": ["## Audio (NOTE - Audio is optional for this course - feel free to skip Audio if it causes trouble!)\n", "\n", "And let's make a function talker that uses OpenAI's speech model to generate Audio\n", "\n", "### Troubleshooting Audio issues\n", "\n", "If you have any problems running this code below (like a FileNotFound error, or a warning of a missing package), you may need to install FFmpeg, a very popular audio utility.\n", "\n", "**For PC Users**\n", "\n", "Detailed instructions are [here](https://chatgpt.com/share/6724efee-6b0c-8012-ac5e-72e2e3885905) and summary instructions:\n", "\n", "1. Download FFmpeg from the official website: https://ffmpeg.org/download.html\n", "\n", "2. Extract the downloaded files to a location on your computer (e.g., `C:\\ffmpeg`)\n", "\n", "3. Add the FFmpeg bin folder to your system PATH:\n", "- Right-click on 'This PC' or 'My Computer' and select 'Properties'\n", "- Click on 'Advanced system settings'\n", "- Click on 'Environment Variables'\n", "- Under 'System variables', find and edit 'Path'\n", "- Add a new entry with the path to your FFmpeg bin folder (e.g., `C:\\ffmpeg\\bin`)\n", "- Restart your command prompt, and within Jupyter Lab do Kernel -> Restart kernel, to pick up the changes\n", "\n", "4. Open a new command prompt and run this to make sure it's installed OK\n", "`ffmpeg -version`\n", "\n", "**For Mac Users**\n", "\n", "1. Install homebrew if you don't have it already by running this in a Terminal window and following any instructions:  \n", "`/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"`\n", "\n", "2. Then install FFmpeg with `brew install ffmpeg`\n", "\n", "3. Verify your installation with `ffmpeg -version` and if everything is good, within Jupyter Lab do Kernel -> Restart kernel to pick up the changes\n", "\n", "Message me or email <NAME_EMAIL> with any problems!"]}, {"cell_type": "markdown", "id": "4cc90e80-c96e-4dd4-b9d6-386fe2b7e797", "metadata": {}, "source": ["## To check you now have ffmpeg and can access it here\n", "\n", "Excecute the next cell to see if you get a version number. (Putting an exclamation mark before something in Jupyter Lab tells it to run it as a terminal command rather than python code).\n", "\n", "If this doesn't work, you may need to actually save and close down your Jupyter lab, and start it again from a new Terminal window (Mac) or Anaconda prompt (PC), remembering to activate the llms environment. This ensures you pick up ffmpeg.\n", "\n", "And if that doesn't work, please contact me!"]}, {"cell_type": "code", "execution_count": 14, "id": "7b3be0fb-1d34-4693-ab6f-dbff190afcd7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["'ffmpeg' is not recognized as an internal or external command,\n", "operable program or batch file.\n", "'ffprobe' is not recognized as an internal or external command,\n", "operable program or batch file.\n", "'ffplay' is not recognized as an internal or external command,\n", "operable program or batch file.\n"]}], "source": ["!ffmpeg -version\n", "!ffprobe -version\n", "!ffplay -version"]}, {"cell_type": "markdown", "id": "d91d3f8f-e505-4e3c-a87c-9e42ed823db6", "metadata": {}, "source": ["# For Mac users - and possibly many PC users too\n", "\n", "This version should work fine for you. It might work for Windows users too, but you might get a Permissions error writing to a temp file. If so, see the next section!\n", "\n", "As always, if you have problems, please contact me! (You could also comment out the audio talker() in the later code if you're less interested in audio generation)"]}, {"cell_type": "code", "execution_count": 16, "id": "ffbfe93b-5e86-4e68-ba71-b301cd5230db", "metadata": {}, "outputs": [], "source": ["from pydub import AudioSegment\n", "from pydub.playback import play\n", "\n", "def talker(message):\n", "    response = openai.audio.speech.create(\n", "      model=\"tts-1\",\n", "      voice=\"onyx\",    # Also, try replacing onyx with alloy\n", "      input=message\n", "    )\n", "    \n", "    audio_stream = BytesIO(response.content)\n", "    audio = AudioSegment.from_file(audio_stream, format=\"mp3\")\n", "    play(audio)"]}, {"cell_type": "code", "execution_count": 17, "id": "b88d775d-d357-4292-a1ad-5dc5ed567281", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\utils.py:198: RuntimeWarning: Couldn't find ffprobe or avprobe - defaulting to ffprobe, but may not work\n", "  warn(\"Couldn't find ffprobe or avprobe - defaulting to ffprobe, but may not work\", RuntimeWarning)\n"]}, {"ename": "FileNotFoundError", "evalue": "[WinError 2] The system cannot find the file specified", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[17], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43m<PERSON><PERSON>\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON>, hi there\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[16], line 12\u001b[0m, in \u001b[0;36mtalker\u001b[1;34m(message)\u001b[0m\n\u001b[0;32m      5\u001b[0m response \u001b[38;5;241m=\u001b[39m openai\u001b[38;5;241m.\u001b[39maudio\u001b[38;5;241m.\u001b[39mspeech\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[0;32m      6\u001b[0m   model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtts-1\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m      7\u001b[0m   voice\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124monyx\u001b[39m\u001b[38;5;124m\"\u001b[39m,    \u001b[38;5;66;03m# Also, try replacing onyx with alloy\u001b[39;00m\n\u001b[0;32m      8\u001b[0m   \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mmessage\n\u001b[0;32m      9\u001b[0m )\n\u001b[0;32m     11\u001b[0m audio_stream \u001b[38;5;241m=\u001b[39m BytesIO(response\u001b[38;5;241m.\u001b[39mcontent)\n\u001b[1;32m---> 12\u001b[0m audio \u001b[38;5;241m=\u001b[39m \u001b[43mAudioSegment\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43maudio_stream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmp3\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     13\u001b[0m play(audio)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\audio_segment.py:728\u001b[0m, in \u001b[0;36mAudioSegment.from_file\u001b[1;34m(cls, file, format, codec, parameters, start_second, duration, **kwargs)\u001b[0m\n\u001b[0;32m    726\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    727\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 728\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[43mmediainfo_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43morig_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mread_ahead_limit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_ahead_limit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    729\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m info:\n\u001b[0;32m    730\u001b[0m     audio_streams \u001b[38;5;241m=\u001b[39m [x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m info[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstreams\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m    731\u001b[0m                      \u001b[38;5;28;01mif\u001b[39;00m x[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcodec_type\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\utils.py:274\u001b[0m, in \u001b[0;36mmediainfo_json\u001b[1;34m(filepath, read_ahead_limit)\u001b[0m\n\u001b[0;32m    271\u001b[0m         file\u001b[38;5;241m.\u001b[39mclose()\n\u001b[0;32m    273\u001b[0m command \u001b[38;5;241m=\u001b[39m [prober, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-of\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mjson\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m+\u001b[39m command_args\n\u001b[1;32m--> 274\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mPopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdin\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstdin_parameter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstderr\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    275\u001b[0m output, stderr \u001b[38;5;241m=\u001b[39m res\u001b[38;5;241m.\u001b[39mcommunicate(\u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mstdin_data)\n\u001b[0;32m    276\u001b[0m output \u001b[38;5;241m=\u001b[39m output\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1026\u001b[0m, in \u001b[0;36mPopen.__init__\u001b[1;34m(self, args, bufsize, executable, stdin, stdout, stderr, preexec_fn, close_fds, shell, cwd, env, universal_newlines, startupinfo, creationflags, restore_signals, start_new_session, pass_fds, user, group, extra_groups, encoding, errors, text, umask, pipesize, process_group)\u001b[0m\n\u001b[0;32m   1022\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext_mode:\n\u001b[0;32m   1023\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr \u001b[38;5;241m=\u001b[39m io\u001b[38;5;241m.\u001b[39mTextIOWrapper(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr,\n\u001b[0;32m   1024\u001b[0m                     encoding\u001b[38;5;241m=\u001b[39mencoding, errors\u001b[38;5;241m=\u001b[39merrors)\n\u001b[1;32m-> 1026\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_child\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpreexec_fn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclose_fds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1027\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mpass_fds\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcwd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1028\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstartupinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcreationflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshell\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1029\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mp2cread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mp2cwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1030\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mc2pread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc2pwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1031\u001b[0m \u001b[43m                        \u001b[49m\u001b[43merrread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1032\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mrestore_signals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1033\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mgid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgids\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mumask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1034\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstart_new_session\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprocess_group\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;66;03m# Cleanup if the child failed starting.\u001b[39;00m\n\u001b[0;32m   1037\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mfilter\u001b[39m(\u001b[38;5;28;01mNone\u001b[39;00m, (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdin, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdout, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr)):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1538\u001b[0m, in \u001b[0;36mPopen._execute_child\u001b[1;34m(self, args, executable, preexec_fn, close_fds, pass_fds, cwd, env, startupinfo, creationflags, shell, p2cread, p2cwrite, c2pread, c2pwrite, errread, errwrite, unused_restore_signals, unused_gid, unused_gids, unused_uid, unused_umask, unused_start_new_session, unused_process_group)\u001b[0m\n\u001b[0;32m   1536\u001b[0m \u001b[38;5;66;03m# Start the process\u001b[39;00m\n\u001b[0;32m   1537\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1538\u001b[0m     hp, ht, pid, tid \u001b[38;5;241m=\u001b[39m \u001b[43m_winapi\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCreateProcess\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexecutable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1539\u001b[0m \u001b[43m                             \u001b[49m\u001b[38;5;66;43;03m# no special security\u001b[39;49;00m\n\u001b[0;32m   1540\u001b[0m \u001b[43m                             \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1541\u001b[0m \u001b[43m                             \u001b[49m\u001b[38;5;28;43mint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mclose_fds\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1542\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mcreationflags\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1543\u001b[0m \u001b[43m                             \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1544\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mcwd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1545\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mstartupinfo\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1546\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m   1547\u001b[0m     \u001b[38;5;66;03m# Child is launched. Close the parent's copy of those pipe\u001b[39;00m\n\u001b[0;32m   1548\u001b[0m     \u001b[38;5;66;03m# handles that only the child should have open.  You need\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;66;03m# pipe will not close when the child process exits and the\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m     \u001b[38;5;66;03m# ReadFile will hang.\u001b[39;00m\n\u001b[0;32m   1553\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_pipe_fds(p2cread, p2cwrite,\n\u001b[0;32m   1554\u001b[0m                          c2pread, c2pwrite,\n\u001b[0;32m   1555\u001b[0m                          errread, errwrite)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [WinError 2] The system cannot find the file specified"]}], "source": ["talker(\"Well, hi there\")"]}, {"cell_type": "markdown", "id": "ad89a9bd-bb1e-4bbb-a49a-83af5f500c24", "metadata": {}, "source": ["# For Windows users (or any Mac users with problems above)\n", "\n", "## First try the Mac version above, but if you get a permissions error writing to a temp file, then this code should work instead.\n", "\n", "A collaboration between students <PERSON> and <PERSON> and <PERSON> got this resolved!\n", "\n", "Below are 4 variations - hopefully one of them will work on your PC. If not, message me please!\n", "\n", "And for Mac people - all 3 of the below work on my Mac too - please try these if the Mac version gave you problems.\n", "\n", "## PC Variation 1"]}, {"cell_type": "code", "execution_count": null, "id": "d104b96a-02ca-4159-82fe-88e0452aa479", "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "                <audio  controls=\"controls\" autoplay=\"autoplay\">\n", "                    <source src=\"data:audio/mpeg;base64,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\" type=\"audio/mpeg\" />\n", "                    Your browser does not support the audio element.\n", "                </audio>\n", "              "], "text/plain": ["<IPython.lib.display.Audio object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["\n", "                <audio  controls=\"controls\" autoplay=\"autoplay\">\n", "                    <source src=\"data:audio/mpeg;base64,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**************************************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\" type=\"audio/mpeg\" />\n", "                    Your browser does not support the audio element.\n", "                </audio>\n", "              "], "text/plain": ["<IPython.lib.display.Audio object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import base64\n", "from io import BytesIO\n", "from PIL import Image\n", "from IPython.display import Audio, display\n", "\n", "def talker(message):\n", "    response = openai.audio.speech.create(\n", "        model=\"tts-1\",\n", "        voice=\"onyx\",\n", "        input=message)\n", "\n", "    audio_stream = BytesIO(response.content)\n", "    output_filename = \"output_audio.mp3\"\n", "    with open(output_filename, \"wb\") as f:\n", "        f.write(audio_stream.read())\n", "\n", "    # Play the generated audio\n", "    display(Audio(output_filename, autoplay=True))\n", "\n", "talker(\"Well, hi there\")"]}, {"cell_type": "markdown", "id": "3a5d11f4-bbd3-43a1-904d-f684eb5f3e3a", "metadata": {}, "source": ["## PC Variation 2"]}, {"cell_type": "code", "execution_count": 19, "id": "d59c8ebd-79c5-498a-bdf2-3a1c50d91aa0", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[WinError 2] The system cannot find the file specified", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[19], line 36\u001b[0m\n\u001b[0;32m     33\u001b[0m     audio \u001b[38;5;241m=\u001b[39m AudioSegment\u001b[38;5;241m.\u001b[39mfrom_file(audio_stream, \u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmp3\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     34\u001b[0m     play_audio(audio)\n\u001b[1;32m---> 36\u001b[0m \u001b[43mtalker\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mWell hi there\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[19], line 33\u001b[0m, in \u001b[0;36mtalker\u001b[1;34m(message)\u001b[0m\n\u001b[0;32m     27\u001b[0m response \u001b[38;5;241m=\u001b[39m openai\u001b[38;5;241m.\u001b[39maudio\u001b[38;5;241m.\u001b[39mspeech\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[0;32m     28\u001b[0m     model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtts-1\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     29\u001b[0m     voice\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124monyx\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# Also, try replacing onyx with alloy\u001b[39;00m\n\u001b[0;32m     30\u001b[0m     \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mmessage\n\u001b[0;32m     31\u001b[0m )\n\u001b[0;32m     32\u001b[0m audio_stream \u001b[38;5;241m=\u001b[39m BytesIO(response\u001b[38;5;241m.\u001b[39mcontent)\n\u001b[1;32m---> 33\u001b[0m audio \u001b[38;5;241m=\u001b[39m \u001b[43mAudioSegment\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43maudio_stream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmp3\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     34\u001b[0m play_audio(audio)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\audio_segment.py:728\u001b[0m, in \u001b[0;36mAudioSegment.from_file\u001b[1;34m(cls, file, format, codec, parameters, start_second, duration, **kwargs)\u001b[0m\n\u001b[0;32m    726\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    727\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 728\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[43mmediainfo_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43morig_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mread_ahead_limit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_ahead_limit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    729\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m info:\n\u001b[0;32m    730\u001b[0m     audio_streams \u001b[38;5;241m=\u001b[39m [x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m info[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstreams\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m    731\u001b[0m                      \u001b[38;5;28;01mif\u001b[39;00m x[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcodec_type\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\utils.py:274\u001b[0m, in \u001b[0;36mmediainfo_json\u001b[1;34m(filepath, read_ahead_limit)\u001b[0m\n\u001b[0;32m    271\u001b[0m         file\u001b[38;5;241m.\u001b[39mclose()\n\u001b[0;32m    273\u001b[0m command \u001b[38;5;241m=\u001b[39m [prober, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-of\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mjson\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m+\u001b[39m command_args\n\u001b[1;32m--> 274\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mPopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdin\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstdin_parameter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstderr\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    275\u001b[0m output, stderr \u001b[38;5;241m=\u001b[39m res\u001b[38;5;241m.\u001b[39mcommunicate(\u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mstdin_data)\n\u001b[0;32m    276\u001b[0m output \u001b[38;5;241m=\u001b[39m output\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1026\u001b[0m, in \u001b[0;36mPopen.__init__\u001b[1;34m(self, args, bufsize, executable, stdin, stdout, stderr, preexec_fn, close_fds, shell, cwd, env, universal_newlines, startupinfo, creationflags, restore_signals, start_new_session, pass_fds, user, group, extra_groups, encoding, errors, text, umask, pipesize, process_group)\u001b[0m\n\u001b[0;32m   1022\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext_mode:\n\u001b[0;32m   1023\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr \u001b[38;5;241m=\u001b[39m io\u001b[38;5;241m.\u001b[39mTextIOWrapper(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr,\n\u001b[0;32m   1024\u001b[0m                     encoding\u001b[38;5;241m=\u001b[39mencoding, errors\u001b[38;5;241m=\u001b[39merrors)\n\u001b[1;32m-> 1026\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_child\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpreexec_fn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclose_fds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1027\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mpass_fds\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcwd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1028\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstartupinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcreationflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshell\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1029\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mp2cread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mp2cwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1030\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mc2pread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc2pwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1031\u001b[0m \u001b[43m                        \u001b[49m\u001b[43merrread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1032\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mrestore_signals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1033\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mgid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgids\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mumask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1034\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstart_new_session\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprocess_group\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;66;03m# Cleanup if the child failed starting.\u001b[39;00m\n\u001b[0;32m   1037\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mfilter\u001b[39m(\u001b[38;5;28;01mNone\u001b[39;00m, (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdin, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdout, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr)):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1538\u001b[0m, in \u001b[0;36mPopen._execute_child\u001b[1;34m(self, args, executable, preexec_fn, close_fds, pass_fds, cwd, env, startupinfo, creationflags, shell, p2cread, p2cwrite, c2pread, c2pwrite, errread, errwrite, unused_restore_signals, unused_gid, unused_gids, unused_uid, unused_umask, unused_start_new_session, unused_process_group)\u001b[0m\n\u001b[0;32m   1536\u001b[0m \u001b[38;5;66;03m# Start the process\u001b[39;00m\n\u001b[0;32m   1537\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1538\u001b[0m     hp, ht, pid, tid \u001b[38;5;241m=\u001b[39m \u001b[43m_winapi\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mCreateProcess\u001b[49m\u001b[43m(\u001b[49m\u001b[43mexecutable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1539\u001b[0m \u001b[43m                             \u001b[49m\u001b[38;5;66;43;03m# no special security\u001b[39;49;00m\n\u001b[0;32m   1540\u001b[0m \u001b[43m                             \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m   1541\u001b[0m \u001b[43m                             \u001b[49m\u001b[38;5;28;43mint\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;129;43;01mnot\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mclose_fds\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1542\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mcreationflags\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1543\u001b[0m \u001b[43m                             \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1544\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mcwd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1545\u001b[0m \u001b[43m                             \u001b[49m\u001b[43mstartupinfo\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1546\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m   1547\u001b[0m     \u001b[38;5;66;03m# Child is launched. Close the parent's copy of those pipe\u001b[39;00m\n\u001b[0;32m   1548\u001b[0m     \u001b[38;5;66;03m# handles that only the child should have open.  You need\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;66;03m# pipe will not close when the child process exits and the\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m     \u001b[38;5;66;03m# ReadFile will hang.\u001b[39;00m\n\u001b[0;32m   1553\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_pipe_fds(p2cread, p2cwrite,\n\u001b[0;32m   1554\u001b[0m                          c2pread, c2pwrite,\n\u001b[0;32m   1555\u001b[0m                          errread, errwrite)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [WinError 2] The system cannot find the file specified"]}], "source": ["import tempfile\n", "import subprocess\n", "from io import BytesIO\n", "from pydub import AudioSegment\n", "import time\n", "\n", "def play_audio(audio_segment):\n", "    temp_dir = tempfile.gettempdir()\n", "    temp_path = os.path.join(temp_dir, \"temp_audio.wav\")\n", "    try:\n", "        audio_segment.export(temp_path, format=\"wav\")\n", "        time.sleep(3) # <PERSON> <PERSON> found that this was needed. You could also try commenting out to see if not needed on your PC\n", "        subprocess.call([\n", "            \"ffplay\",\n", "            \"-nodisp\",\n", "            \"-autoexit\",\n", "            \"-hide_banner\",\n", "            temp_path\n", "        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)\n", "    finally:\n", "        try:\n", "            os.remove(temp_path)\n", "        except Exception:\n", "            pass\n", " \n", "def talker(message):\n", "    response = openai.audio.speech.create(\n", "        model=\"tts-1\",\n", "        voice=\"onyx\",  # Also, try replacing onyx with alloy\n", "        input=message\n", "    )\n", "    audio_stream = BytesIO(response.content)\n", "    audio = AudioSegment.from_file(audio_stream, format=\"mp3\")\n", "    play_audio(audio)\n", "\n", "talker(\"Well hi there\")"]}, {"cell_type": "markdown", "id": "96f90e35-f71e-468e-afea-07b98f74dbcf", "metadata": {}, "source": ["## PC Variation 3"]}, {"cell_type": "code", "execution_count": 20, "id": "8597c7f8-7b50-44ad-9b31-db12375cd57b", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[WinError 2] The system cannot find the file specified", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[20], line 26\u001b[0m\n\u001b[0;32m     22\u001b[0m     audio \u001b[38;5;241m=\u001b[39m AudioSegment\u001b[38;5;241m.\u001b[39mfrom_file(audio_stream, \u001b[38;5;28mformat\u001b[39m\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmp3\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     24\u001b[0m     play(audio)\n\u001b[1;32m---> 26\u001b[0m \u001b[43mtalker\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mWell hi there\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[20], line 22\u001b[0m, in \u001b[0;36mtalker\u001b[1;34m(message)\u001b[0m\n\u001b[0;32m     15\u001b[0m response \u001b[38;5;241m=\u001b[39m openai\u001b[38;5;241m.\u001b[39maudio\u001b[38;5;241m.\u001b[39mspeech\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[0;32m     16\u001b[0m     model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtts-1\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     17\u001b[0m     voice\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124monyx\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# Also, try replacing onyx with alloy\u001b[39;00m\n\u001b[0;32m     18\u001b[0m     \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mmessage\n\u001b[0;32m     19\u001b[0m )\n\u001b[0;32m     21\u001b[0m audio_stream \u001b[38;5;241m=\u001b[39m BytesIO(response\u001b[38;5;241m.\u001b[39mcontent)\n\u001b[1;32m---> 22\u001b[0m audio \u001b[38;5;241m=\u001b[39m \u001b[43mAudioSegment\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43maudio_stream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmp3\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     24\u001b[0m play(audio)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\audio_segment.py:728\u001b[0m, in \u001b[0;36mAudioSegment.from_file\u001b[1;34m(cls, file, format, codec, parameters, start_second, duration, **kwargs)\u001b[0m\n\u001b[0;32m    726\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    727\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 728\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[43mmediainfo_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43morig_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mread_ahead_limit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_ahead_limit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    729\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m info:\n\u001b[0;32m    730\u001b[0m     audio_streams \u001b[38;5;241m=\u001b[39m [x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m info[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstreams\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m    731\u001b[0m                      \u001b[38;5;28;01mif\u001b[39;00m x[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcodec_type\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\utils.py:274\u001b[0m, in \u001b[0;36mmediainfo_json\u001b[1;34m(filepath, read_ahead_limit)\u001b[0m\n\u001b[0;32m    271\u001b[0m         file\u001b[38;5;241m.\u001b[39mclose()\n\u001b[0;32m    273\u001b[0m command \u001b[38;5;241m=\u001b[39m [prober, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-of\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mjson\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m+\u001b[39m command_args\n\u001b[1;32m--> 274\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mPopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdin\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstdin_parameter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstderr\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    275\u001b[0m output, stderr \u001b[38;5;241m=\u001b[39m res\u001b[38;5;241m.\u001b[39mcommunicate(\u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mstdin_data)\n\u001b[0;32m    276\u001b[0m output \u001b[38;5;241m=\u001b[39m output\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1026\u001b[0m, in \u001b[0;36mPopen.__init__\u001b[1;34m(self, args, bufsize, executable, stdin, stdout, stderr, preexec_fn, close_fds, shell, cwd, env, universal_newlines, startupinfo, creationflags, restore_signals, start_new_session, pass_fds, user, group, extra_groups, encoding, errors, text, umask, pipesize, process_group)\u001b[0m\n\u001b[0;32m   1022\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext_mode:\n\u001b[0;32m   1023\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr \u001b[38;5;241m=\u001b[39m io\u001b[38;5;241m.\u001b[39mTextIOWrapper(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr,\n\u001b[0;32m   1024\u001b[0m                     encoding\u001b[38;5;241m=\u001b[39mencoding, errors\u001b[38;5;241m=\u001b[39merrors)\n\u001b[1;32m-> 1026\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_child\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpreexec_fn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclose_fds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1027\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mpass_fds\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcwd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1028\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstartupinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcreationflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshell\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1029\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mp2cread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mp2cwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1030\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mc2pread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc2pwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1031\u001b[0m \u001b[43m                        \u001b[49m\u001b[43merrread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1032\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mrestore_signals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1033\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mgid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgids\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mumask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1034\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstart_new_session\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprocess_group\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;66;03m# Cleanup if the child failed starting.\u001b[39;00m\n\u001b[0;32m   1037\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mfilter\u001b[39m(\u001b[38;5;28;01mNone\u001b[39;00m, (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdin, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdout, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr)):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1538\u001b[0m, in \u001b[0;36mPopen._execute_child\u001b[1;34m(self, args, executable, preexec_fn, close_fds, pass_fds, cwd, env, startupinfo, creationflags, shell, p2cread, p2cwrite, c2pread, c2pwrite, errread, errwrite, unused_restore_signals, unused_gid, unused_gids, unused_uid, unused_umask, unused_start_new_session, unused_process_group)\u001b[0m\n\u001b[0;32m   1536\u001b[0m \u001b[38;5;66;03m# Start the process\u001b[39;00m\n\u001b[0;32m   1537\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1538\u001b[0m     hp, ht, pid, tid \u001b[38;5;241m=\u001b[39m _winapi\u001b[38;5;241m.\u001b[39mCreateProcess(executable, args,\n\u001b[0;32m   1539\u001b[0m                              \u001b[38;5;66;03m# no special security\u001b[39;00m\n\u001b[0;32m   1540\u001b[0m                              \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   1541\u001b[0m                              \u001b[38;5;28mint\u001b[39m(\u001b[38;5;129;01mnot\u001b[39;00m close_fds),\n\u001b[0;32m   1542\u001b[0m                              creationflags,\n\u001b[0;32m   1543\u001b[0m                              env,\n\u001b[0;32m   1544\u001b[0m                              cwd,\n\u001b[0;32m   1545\u001b[0m                              startupinfo)\n\u001b[0;32m   1546\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m   1547\u001b[0m     \u001b[38;5;66;03m# Child is launched. Close the parent's copy of those pipe\u001b[39;00m\n\u001b[0;32m   1548\u001b[0m     \u001b[38;5;66;03m# handles that only the child should have open.  You need\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;66;03m# pipe will not close when the child process exits and the\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m     \u001b[38;5;66;03m# ReadFile will hang.\u001b[39;00m\n\u001b[0;32m   1553\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_pipe_fds(p2cread, p2cwrite,\n\u001b[0;32m   1554\u001b[0m                          c2pread, c2pwrite,\n\u001b[0;32m   1555\u001b[0m                          errread, errwrite)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [WinError 2] The system cannot find the file specified"]}], "source": ["import os\n", "from pydub import AudioSegment\n", "from pydub.playback import play\n", "from io import BytesIO\n", "\n", "def talker(message):\n", "    # Set a custom directory for temporary files on Windows\n", "    custom_temp_dir = os.path.expanduser(\"~/Documents/temp_audio\")\n", "    os.environ['TEMP'] = custom_temp_dir  # You can also use 'TMP' if necessary\n", "    \n", "    # Create the folder if it doesn't exist\n", "    if not os.path.exists(custom_temp_dir):\n", "        os.makedirs(custom_temp_dir)\n", "    \n", "    response = openai.audio.speech.create(\n", "        model=\"tts-1\",\n", "        voice=\"onyx\",  # Also, try replacing onyx with alloy\n", "        input=message\n", "    )\n", "    \n", "    audio_stream = BytesIO(response.content)\n", "    audio = AudioSegment.from_file(audio_stream, format=\"mp3\")\n", "\n", "    play(audio)\n", "\n", "talker(\"Well hi there\")"]}, {"cell_type": "markdown", "id": "e821224c-b069-4f9b-9535-c15fdb0e411c", "metadata": {}, "source": ["## PC Variation 4\n", "\n", "### Let's try a completely different sound library\n", "\n", "First run the next cell to install a new library, then try the cell below it."]}, {"cell_type": "code", "execution_count": null, "id": "69d3c0d9-afcc-49e3-b829-9c9869d8b472", "metadata": {}, "outputs": [], "source": ["!pip install simpleaudio"]}, {"cell_type": "code", "execution_count": 21, "id": "28f9cc99-36b7-4554-b3f4-f2012f614a13", "metadata": {}, "outputs": [], "source": ["from pydub import AudioSegment\n", "from io import BytesIO\n", "import tempfile\n", "import os\n", "import simpleaudio as sa\n", "\n", "def talker(message):\n", "    response = openai.audio.speech.create(\n", "        model=\"tts-1\",\n", "        voice=\"onyx\",  # Also, try replacing onyx with alloy\n", "        input=message\n", "    )\n", "    \n", "    audio_stream = BytesIO(response.content)\n", "    audio = AudioSegment.from_file(audio_stream, format=\"mp3\")\n", "\n", "    # Create a temporary file in a folder where you have write permissions\n", "    with tempfile.NamedTemporaryFile(suffix=\".wav\", delete=False, dir=os.path.expanduser(\"~/Documents\")) as temp_audio_file:\n", "        temp_file_name = temp_audio_file.name\n", "        audio.export(temp_file_name, format=\"wav\")\n", "    \n", "    # Load and play audio using simpleaudio\n", "    wave_obj = sa.WaveObject.from_wave_file(temp_file_name)\n", "    play_obj = wave_obj.play()\n", "    play_obj.wait_done()  # Wait for playback to finish\n", "\n", "    # Clean up the temporary file afterward\n", "    os.remove(temp_file_name)\n", "    \n"]}, {"cell_type": "code", "execution_count": 22, "id": "0d248b46", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[WinError 2] The system cannot find the file specified", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[22], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43m<PERSON><PERSON>\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m<PERSON><PERSON> hi there\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "Cell \u001b[1;32mIn[21], line 15\u001b[0m, in \u001b[0;36mtalker\u001b[1;34m(message)\u001b[0m\n\u001b[0;32m      8\u001b[0m response \u001b[38;5;241m=\u001b[39m openai\u001b[38;5;241m.\u001b[39maudio\u001b[38;5;241m.\u001b[39mspeech\u001b[38;5;241m.\u001b[39mcreate(\n\u001b[0;32m      9\u001b[0m     model\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtts-1\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m     10\u001b[0m     voice\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124monyx\u001b[39m\u001b[38;5;124m\"\u001b[39m,  \u001b[38;5;66;03m# Also, try replacing onyx with alloy\u001b[39;00m\n\u001b[0;32m     11\u001b[0m     \u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mmessage\n\u001b[0;32m     12\u001b[0m )\n\u001b[0;32m     14\u001b[0m audio_stream \u001b[38;5;241m=\u001b[39m BytesIO(response\u001b[38;5;241m.\u001b[39mcontent)\n\u001b[1;32m---> 15\u001b[0m audio \u001b[38;5;241m=\u001b[39m \u001b[43mAudioSegment\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_file\u001b[49m\u001b[43m(\u001b[49m\u001b[43maudio_stream\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mformat\u001b[39;49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmp3\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[0;32m     17\u001b[0m \u001b[38;5;66;03m# Create a temporary file in a folder where you have write permissions\u001b[39;00m\n\u001b[0;32m     18\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m tempfile\u001b[38;5;241m.\u001b[39mNamedTemporaryFile(suffix\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.wav\u001b[39m\u001b[38;5;124m\"\u001b[39m, delete\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, \u001b[38;5;28mdir\u001b[39m\u001b[38;5;241m=\u001b[39mos\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexpanduser(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m~/Documents\u001b[39m\u001b[38;5;124m\"\u001b[39m)) \u001b[38;5;28;01mas\u001b[39;00m temp_audio_file:\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\audio_segment.py:728\u001b[0m, in \u001b[0;36mAudioSegment.from_file\u001b[1;34m(cls, file, format, codec, parameters, start_second, duration, **kwargs)\u001b[0m\n\u001b[0;32m    726\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[0;32m    727\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m--> 728\u001b[0m     info \u001b[38;5;241m=\u001b[39m \u001b[43mmediainfo_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43morig_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mread_ahead_limit\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mread_ahead_limit\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    729\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m info:\n\u001b[0;32m    730\u001b[0m     audio_streams \u001b[38;5;241m=\u001b[39m [x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m info[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mstreams\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m    731\u001b[0m                      \u001b[38;5;28;01mif\u001b[39;00m x[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcodec_type\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124maudio\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\site-packages\\pydub\\utils.py:274\u001b[0m, in \u001b[0;36mmediainfo_json\u001b[1;34m(filepath, read_ahead_limit)\u001b[0m\n\u001b[0;32m    271\u001b[0m         file\u001b[38;5;241m.\u001b[39mclose()\n\u001b[0;32m    273\u001b[0m command \u001b[38;5;241m=\u001b[39m [prober, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m-of\u001b[39m\u001b[38;5;124m'\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mjson\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m+\u001b[39m command_args\n\u001b[1;32m--> 274\u001b[0m res \u001b[38;5;241m=\u001b[39m \u001b[43mPopen\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcommand\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdin\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstdin_parameter\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstdout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstderr\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mPIPE\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    275\u001b[0m output, stderr \u001b[38;5;241m=\u001b[39m res\u001b[38;5;241m.\u001b[39mcommunicate(\u001b[38;5;28minput\u001b[39m\u001b[38;5;241m=\u001b[39mstdin_data)\n\u001b[0;32m    276\u001b[0m output \u001b[38;5;241m=\u001b[39m output\u001b[38;5;241m.\u001b[39mdecode(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutf-8\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1026\u001b[0m, in \u001b[0;36mPopen.__init__\u001b[1;34m(self, args, bufsize, executable, stdin, stdout, stderr, preexec_fn, close_fds, shell, cwd, env, universal_newlines, startupinfo, creationflags, restore_signals, start_new_session, pass_fds, user, group, extra_groups, encoding, errors, text, umask, pipesize, process_group)\u001b[0m\n\u001b[0;32m   1022\u001b[0m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mtext_mode:\n\u001b[0;32m   1023\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr \u001b[38;5;241m=\u001b[39m io\u001b[38;5;241m.\u001b[39mTextIOWrapper(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr,\n\u001b[0;32m   1024\u001b[0m                     encoding\u001b[38;5;241m=\u001b[39mencoding, errors\u001b[38;5;241m=\u001b[39merrors)\n\u001b[1;32m-> 1026\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_execute_child\u001b[49m\u001b[43m(\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mexecutable\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpreexec_fn\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclose_fds\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1027\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mpass_fds\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcwd\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43menv\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1028\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstartupinfo\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcreationflags\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mshell\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1029\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mp2cread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mp2cwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1030\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mc2pread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mc2pwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1031\u001b[0m \u001b[43m                        \u001b[49m\u001b[43merrread\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrwrite\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1032\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mrestore_signals\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1033\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mgid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgids\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43muid\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mumask\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   1034\u001b[0m \u001b[43m                        \u001b[49m\u001b[43mstart_new_session\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mprocess_group\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1035\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m:\n\u001b[0;32m   1036\u001b[0m     \u001b[38;5;66;03m# Cleanup if the child failed starting.\u001b[39;00m\n\u001b[0;32m   1037\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mfilter\u001b[39m(\u001b[38;5;28;01mNone\u001b[39;00m, (\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdin, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstdout, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstderr)):\n", "File \u001b[1;32mc:\\Users\\<USER>\\AppData\\Local\\anaconda3\\envs\\llms\\Lib\\subprocess.py:1538\u001b[0m, in \u001b[0;36mPopen._execute_child\u001b[1;34m(self, args, executable, preexec_fn, close_fds, pass_fds, cwd, env, startupinfo, creationflags, shell, p2cread, p2cwrite, c2pread, c2pwrite, errread, errwrite, unused_restore_signals, unused_gid, unused_gids, unused_uid, unused_umask, unused_start_new_session, unused_process_group)\u001b[0m\n\u001b[0;32m   1536\u001b[0m \u001b[38;5;66;03m# Start the process\u001b[39;00m\n\u001b[0;32m   1537\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m-> 1538\u001b[0m     hp, ht, pid, tid \u001b[38;5;241m=\u001b[39m _winapi\u001b[38;5;241m.\u001b[39mCreateProcess(executable, args,\n\u001b[0;32m   1539\u001b[0m                              \u001b[38;5;66;03m# no special security\u001b[39;00m\n\u001b[0;32m   1540\u001b[0m                              \u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   1541\u001b[0m                              \u001b[38;5;28mint\u001b[39m(\u001b[38;5;129;01mnot\u001b[39;00m close_fds),\n\u001b[0;32m   1542\u001b[0m                              creationflags,\n\u001b[0;32m   1543\u001b[0m                              env,\n\u001b[0;32m   1544\u001b[0m                              cwd,\n\u001b[0;32m   1545\u001b[0m                              startupinfo)\n\u001b[0;32m   1546\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m   1547\u001b[0m     \u001b[38;5;66;03m# Child is launched. Close the parent's copy of those pipe\u001b[39;00m\n\u001b[0;32m   1548\u001b[0m     \u001b[38;5;66;03m# handles that only the child should have open.  You need\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   1551\u001b[0m     \u001b[38;5;66;03m# pipe will not close when the child process exits and the\u001b[39;00m\n\u001b[0;32m   1552\u001b[0m     \u001b[38;5;66;03m# ReadFile will hang.\u001b[39;00m\n\u001b[0;32m   1553\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_close_pipe_fds(p2cread, p2cwrite,\n\u001b[0;32m   1554\u001b[0m                          c2pread, c2pwrite,\n\u001b[0;32m   1555\u001b[0m                          errread, errwrite)\n", "\u001b[1;31mFileNotFoundError\u001b[0m: [WinError 2] The system cannot find the file specified"]}], "source": ["talker(\"Well hi there\")"]}, {"cell_type": "markdown", "id": "7986176b-cd04-495f-a47f-e057b0e462ed", "metadata": {}, "source": ["## PC Users - if none of those 4 variations worked!\n", "\n", "Please get in touch with me. I'm sorry this is causing problems! We'll figure it out.\n", "\n", "Alternatively: playing audio from your PC isn't super-critical for this course, and you can feel free to focus on image generation and skip audio for now, or come back to it later."]}, {"cell_type": "markdown", "id": "1d48876d-c4fa-46a8-a04f-f9fadf61fb0d", "metadata": {}, "source": ["# Our Agent Framework\n", "\n", "The term 'Agentic AI' and Agentization is an umbrella term that refers to a number of techniques, such as:\n", "\n", "1. Breaking a complex problem into smaller steps, with multiple LLMs carrying out specialized tasks\n", "2. The ability for LLMs to use Tools to give them additional capabilities\n", "3. The 'Agent Environment' which allows Agents to collaborate\n", "4. An LLM can act as the Planner, dividing bigger tasks into smaller ones for the specialists\n", "5. The concept of an Agent having autonomy / agency, beyond just responding to a prompt - such as Memory\n", "\n", "We're showing 1 and 2 here, and to a lesser extent 3 and 5. In week 8 we will do the lot!"]}, {"cell_type": "code", "execution_count": null, "id": "ba820c95-02f5-499e-8f3c-8727ee0a6c0c", "metadata": {}, "outputs": [], "source": ["def chat(history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages, tools=tools)\n", "    image = None\n", "    \n", "    if response.choices[0].finish_reason==\"tool_calls\":\n", "        message = response.choices[0].message\n", "        response, city = handle_tool_call(message)\n", "        messages.append(message)\n", "        messages.append(response)\n", "        image = artist(city)\n", "        response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "        \n", "    reply = response.choices[0].message.content\n", "    history += [{\"role\":\"assistant\", \"content\":reply}]\n", "\n", "    # Comment out or delete the next line if you'd rather skip Audio for now..\n", "    # It worked for me only with the first variation of the talker function\n", "    talker(reply)\n", "    \n", "    return history, image"]}, {"cell_type": "code", "execution_count": 20, "id": "f38d0d27-33bf-4992-a2e5-5dbed973cde7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7866\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7866/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# More involved Gradio code as we're not using the preset Chat interface!\n", "# Passing in inbrowser=True in the last line will cause a Gradio window to pop up immediately.\n", "\n", "with gr.Blocks() as ui:\n", "    with gr.<PERSON>():\n", "        chatbot = gr.<PERSON><PERSON><PERSON>(height=500, type=\"messages\")\n", "        image_output = gr.Image(height=500)\n", "    with gr.<PERSON>():\n", "        entry = gr.Textbox(label=\"Chat with our AI Assistant:\")\n", "    with gr.<PERSON>():\n", "        clear = gr.<PERSON>(\"Clear\")\n", "\n", "    def do_entry(message, history):\n", "        history += [{\"role\":\"user\", \"content\":message}]\n", "        return \"\", history\n", "\n", "    entry.submit(do_entry, inputs=[entry, chatbot], outputs=[entry, chatbot]).then(\n", "        chat, inputs=chatbot, outputs=[chatbot, image_output]\n", "    )\n", "    clear.click(lambda: None, inputs=None, outputs=chatbot, queue=False)\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "markdown", "id": "226643d2-73e4-4252-935d-86b8019e278a", "metadata": {}, "source": ["# Exercises and Business Applications\n", "\n", "Add in more tools - perhaps to simulate actually booking a flight. A student has done this and provided their example in the community contributions folder.\n", "\n", "Next: take this and apply it to your business. Make a multi-modal AI assistant with tools that could carry out an activity for your work. A customer support assistant? New employee onboarding assistant? So many possibilities! Also, see the week2 end of week Exercise in the separate Notebook."]}, {"cell_type": "markdown", "id": "7e795560-1867-42db-a256-a23b844e6fbe", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../thankyou.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#090;\">I have a special request for you</h2>\n", "            <span style=\"color:#090;\">\n", "                My editor tells me that it makes a HUGE difference when students rate this course on Udemy - it's one of the main ways that Udemy decides whether to show it to others. If you're able to take a minute to rate this, I'd be so very grateful! And regardless - always please reach out to <NAME_EMAIL> if I can help at any point.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}