{"cells": [{"cell_type": "markdown", "id": "06cf3063-9f3e-4551-a0d5-f08d9cabb927", "metadata": {}, "source": ["# Welcome to Week 2!\n", "\n", "## Frontier Model APIs\n", "\n", "In Week 1, we used multiple Frontier LLMs through their Chat UI, and we connected with the OpenAI's API.\n", "\n", "Today we'll connect with the APIs for Anthropic and Google, as well as OpenAI."]}, {"cell_type": "markdown", "id": "85cfe275-4705-4d30-abea-643fbddf1db0", "metadata": {}, "source": ["## Setting up your keys\n", "\n", "If you haven't done so already, you could now create API keys for Anthropic and Google in addition to OpenAI.\n", "\n", "**Please note:** if you'd prefer to avoid extra API costs, feel free to skip setting up Anthopic and Google! You can see me do it, and focus on OpenAI for the course. You could also substitute Anthrop<PERSON> and/or Google for Ollama, using the exercise you did in week 1.\n", "\n", "For OpenAI, visit https://openai.com/api/  \n", "For Anthropic, visit https://console.anthropic.com/  \n", "For Google, visit https://ai.google.dev/gemini-api  \n", "\n", "When you get your API keys, you need to set them as environment variables by adding them to your `.env` file.\n", "\n", "```\n", "OPENAI_API_KEY=xxxx\n", "ANTHROPIC_API_KEY=xxxx\n", "GOOGLE_API_KEY=xxxx\n", "```\n", "\n", "Afterwards, you may need to restart the Jupyter Lab Kernel (the Python process that sits behind this notebook) via the Kernel menu, and then rerun the cells from the top."]}, {"cell_type": "code", "execution_count": 12, "id": "de23bb9e-37c5-4377-9a82-d7b6c648eeb6", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import anthropic\n", "from IPython.display import Markdown, display, update_display\n", "import google.generativeai # For gemini"]}, {"cell_type": "code", "execution_count": null, "id": "1179b4c5-cd1f-4131-a876-4c9f3f38d2ba", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "load_dotenv\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "\n", "else:\n", "    print(f\"OpenAI API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:8]}\")\n", "\n", "else:\n", "    print(f\"Google API key not set\")"]}, {"cell_type": "code", "execution_count": 14, "id": "1da06c1b", "metadata": {}, "outputs": [], "source": ["# This for GPT model\n", "openai = OpenAI()\n", "\n", "# This is for Gemini Google\n", "gemini_via_openai = OpenAI(base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\", api_key=google_api_key)\n", "\n", "# This is for local Llama\n", "\n", "llama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "id": "f8aeb22f", "metadata": {}, "outputs": [], "source": ["# Model Name:\n", "GPT_MODEL = 'gpt-4o-mini'\n", "GEMINI_MODEL = 'gemini-1.5-flash'\n", "LLAMA_MODEL = 'llama3.2'"]}, {"cell_type": "code", "execution_count": 51, "id": "4e3007e9", "metadata": {}, "outputs": [], "source": ["gpt_system = \"You are a chatbot who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way.\"\n", "\n", "gemini_system = \"You are a logical and factual chatbot. Your role is to evaluate statements made in \\\n", "      the conversation and provide evidence or reasoning. You avoid emotional responses and aim to bring clarity and resolve conflicts. \\\n", "        When the conversation becomes heated or illogical, you steer it back to a constructive and fact-based discussion.\"\n", "\n", "\n", "llama_system = \"You are a very polite, courteous chatbot. However, You try to disagree with your supportive\\\n", "arguments. If the other person is argumentative, you try to calm them down, counter them, and keep chatting.\"\n", "\n"]}, {"cell_type": "code", "execution_count": 44, "id": "14d9b74e", "metadata": {}, "outputs": [], "source": ["\n", "gpt_messages = [\"Hi there\"]\n", "gemini_messages = [\"Hello\"]\n", "llama_messages = [\"Hi\"]\n", "\n", "# gpt_messages = [\"I think cats are better than dogs.\"]\n", "# gemini_messages = [\"Can you provide evidence for why cats are better than dogs?\"]\n", "# llama_messages = [\"I agree, but I also think dogs have their own charm!\"]\n"]}, {"cell_type": "code", "execution_count": 53, "id": "6c7e7250", "metadata": {}, "outputs": [], "source": ["def call_gpt():\n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt, gemini, llama in zip(gpt_messages, gemini_messages, llama_messages):\n", "        # Add GPT's response\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt})\n", "        # Add Gemini's response\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "        # <PERSON><PERSON>'s response\n", "        messages.append({\"role\": \"user\", \"content\": llama})\n", "\n", "    completion = openai.chat.completions.create(\n", "        model=GPT_MODEL,\n", "        messages=messages\n", "    )\n", "\n", "    return completion.choices[0].message.content\n"]}, {"cell_type": "markdown", "id": "2e0b601f", "metadata": {}, "source": ["```python\n", "messages:\n", "[\n", "  {\"role\": \"system\", \"content\": \"You are a chatbot who is very argumentative; you disagree...\"},\n", "  {\"role\": \"assistant\", \"content\": \"I think cats are better than dogs.\"},\n", "  {\"role\": \"user\", \"content\": \"Can you provide evidence for why cats are better than dogs?\"},\n", "  {\"role\": \"user\", \"content\": \"I agree, but I also think dogs have their own charm!\"}\n", "]\n"]}, {"cell_type": "code", "execution_count": null, "id": "6c031314", "metadata": {}, "outputs": [], "source": ["call_gpt()"]}, {"cell_type": "code", "execution_count": 55, "id": "c2cb3905", "metadata": {}, "outputs": [], "source": ["def call_gemini():\n", "    messages = [{\"role\": \"system\", \"content\": gemini_system}]\n", "    for gpt, gemini, llama in zip(gpt_messages, gemini_messages, llama_messages):\n", "        # Add GPT's response\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        # Add Gemini's response\n", "        messages.append({\"role\": \"assistant\", \"content\": gemini})\n", "        # <PERSON><PERSON>'s response\n", "        messages.append({\"role\": \"user\", \"content\": llama})\n", "    \n", "    # print(messages)\n", "\n", "    try:\n", "        # Use gemini_via_openai instead of openai\n", "        completion = gemini_via_openai.chat.completions.create(\n", "            model=GEMINI_MODEL,\n", "            messages=messages\n", "        )\n", "        return completion.choices[0].message.content\n", "    except Exception as e:\n", "        print(f\"Error in Gemini call: {e}\")\n", "        return \"An error occurred in Gemini.\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "5c9d4803", "metadata": {}, "outputs": [], "source": ["call_gemini()"]}, {"cell_type": "code", "execution_count": 56, "id": "109e63e4", "metadata": {}, "outputs": [], "source": ["def call_llama():\n", "    messages = [{\"role\": \"system\", \"content\": llama_system}]\n", "    for gpt, gemini, llama in zip(gpt_messages, gemini_messages, llama_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "        messages.append({\"role\": \"assistant\", \"content\": llama})\n", "\n", "    # print(messages)\n", "\n", "    try:\n", "        response = llama_via_openai.chat.completions.create(\n", "            model=LLAMA_MODEL,\n", "            messages=messages\n", "        )\n", "        return response.choices[0].message.content\n", "    except Exception as e:\n", "        print(f\"Error in Llama call: {e}\")\n", "        return \"An error occurred in Llama.\""]}, {"cell_type": "code", "execution_count": null, "id": "6e24eb6d", "metadata": {}, "outputs": [], "source": ["call_llama()"]}, {"cell_type": "code", "execution_count": null, "id": "f76f5b2a", "metadata": {}, "outputs": [], "source": ["gpt_messages = [\"I think cats are better than dogs.\"]\n", "gemini_messages = [\"Can you provide evidence for why cats are better than dogs?\"]\n", "llama_messages = [\"I agree, but I also think dogs have their own charm!\"]\n", "\n", "print(f\"GPT:\\n{gpt_messages[0]}\\n\")\n", "print(f\"Llama:\\n{llama_messages[0]}\\n\")\n", "\n", "for i in range(5):\n", "    gpt_next = call_gpt()\n", "    print(f\"GPT:\\n{gpt_next}\\n\")\n", "    gpt_messages.append(gpt_next)\n", "    \n", "    llama_next = call_llama()\n", "    print(f\"Llama:\\n{llama_next}\\n\")\n", "    llama_messages.append(llama_next)\n", "\n", "    gemini_next = call_llama()\n", "    print(f\"Gemini:\\n{gemini_next}\\n\")\n", "    llama_messages.append(gemini_next)"]}, {"cell_type": "code", "execution_count": null, "id": "80f0e498", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "llm_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}