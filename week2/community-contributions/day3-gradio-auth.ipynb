{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["Import Required Libraries"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr "]}, {"cell_type": "markdown", "metadata": {}, "source": ["Load Environment Variables"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["load_dotenv()\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if not openai_api_key:\n", "    print(\"OpenAI API Key not set\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Initialize OpenAI Client and Define Model"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "MODEL = 'gpt-4o-mini'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define the System Message"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["system_message = (\n", "    \"You are a helpful assistant, trying your best to answer every question as accurately as possible. \"\n", "    \"You are also free to say you do not know if you do not have the information to answer a question. \"\n", "    \"You always respond in markdown.\"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Define the Chat Function"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    stream = openai.chat.completions.create(model=MODEL, messages=messages, stream=True)\n", "\n", "    response = \"\"\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        yield response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create the Chat Interface"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["demo = gr.<PERSON><PERSON>(\n", "    fn=chat,\n", "    title=\"AI chatbot\",\n", "    description=\"Please login to use the chat interface\",\n", "    type='messages',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["auth_data is a list of tuples, where each tuple contains a username and password."]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [], "source": ["auth_data = [(\"user_1\", \"password_1\"), (\"user_2\", \"password_2\"), (\"user_3\", \"password_3\")]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Add Authentication and Launch\n", "\n", "auth_message is the message displayed to users before accessing the interface."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["demo.launch(share=True,\n", "    auth=auth_data,\n", "    auth_message=\"Please enter your credentials to access the chat interface\",\n", ")"]}], "metadata": {"kernelspec": {"display_name": "llms", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}