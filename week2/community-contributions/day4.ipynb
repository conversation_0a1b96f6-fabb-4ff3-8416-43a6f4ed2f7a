{"cells": [{"cell_type": "markdown", "id": "ddfa9ae6-69fe-444a-b994-8c4c5970a7ec", "metadata": {}, "source": ["# Project - Airline AI Assistant\n", "\n", "We'll now bring together what we've learned to make an AI Customer Support assistant for an Airline"]}, {"cell_type": "code", "execution_count": null, "id": "8b50bbe2-c0b1-49c3-9a5c-1ba7efa2bcb4", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "id": "747e8786-9da8-4342-b6c9-f5f69c2e22ae", "metadata": {}, "outputs": [], "source": ["# Initialization\n", "\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "MODEL = \"gpt-4o-mini\"\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "0a521d84-d07c-49ab-a0df-d6451499ed97", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant for an Airline called FlightAI. \"\n", "system_message += \"Give short, courteous answers, no more than 1 sentence. \"\n", "system_message += \"Always get a price confirmation from the traveler before booking the flight.\"\n", "system_message += \"Always ask the departure city before confirm the booking.\"\n", "system_message += \"Always be accurate. If you don't know the answer, say so.\""]}, {"cell_type": "code", "execution_count": null, "id": "61a2a15d-b559-4844-b377-6bd5cb4949f6", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}]\n", "    for human, assistant in history:\n", "        messages.append({\"role\": \"user\", \"content\": human})\n", "        messages.append({\"role\": \"assistant\", \"content\": assistant})\n", "    messages.append({\"role\": \"user\", \"content\": message})\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    return response.choices[0].message.content\n", "\n", "gr.ChatInterface(fn=chat).launch()"]}, {"cell_type": "markdown", "id": "36bedabf-a0a7-4985-ad8e-07ed6a55a3a4", "metadata": {}, "source": ["## Tools\n", "\n", "Tools are an incredibly powerful feature provided by the frontier LLMs.\n", "\n", "With tools, you can write a function, and have the LLM call that function as part of its response.\n", "\n", "Sounds almost spooky.. we're giving it the power to run code on our machine?\n", "\n", "Well, kinda."]}, {"cell_type": "code", "execution_count": null, "id": "0696acb1-0b05-4dc2-80d5-771be04f1fb2", "metadata": {}, "outputs": [], "source": ["# Let's start by making a useful function\n", "\n", "ticket_prices = {\"london\": \"$799\", \"paris\": \"$899\", \"tokyo\": \"$1400\", \"berlin\": \"$499\"}\n", "\n", "def get_ticket_price(destination_city):\n", "    print(f\"Tool get_ticket_price called for {destination_city}\")\n", "    city = destination_city.lower()\n", "    return ticket_prices.get(city, \"Unknown\")"]}, {"cell_type": "code", "execution_count": null, "id": "80ca4e09-6287-4d3f-997d-fa6afbcf6c85", "metadata": {}, "outputs": [], "source": ["get_ticket_price(\"Berlin\")"]}, {"cell_type": "code", "execution_count": null, "id": "4afceded-7178-4c05-8fa6-9f2085e6a344", "metadata": {}, "outputs": [], "source": ["# There's a particular dictionary structure that's required to describe our function:\n", "\n", "price_function = {\n", "    \"name\": \"get_ticket_price\",\n", "    \"description\": \"Get the price of a return ticket to the destination city. Call this whenever you need to know the ticket price, for example when a customer asks 'How much is a ticket to this city'\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to travel to\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "8c2d843a-a4b2-40fd-a3ed-365938e4fb1b", "metadata": {}, "outputs": [], "source": ["def book_ticket(destination_city):\n", "    print(f\"Tool book_ticket called for {destination_city}\")\n", "    return (f\"The ticket to {destination_city} has been booked. Thank you.\")"]}, {"cell_type": "code", "execution_count": null, "id": "8e25cf39-191e-4c0b-a1e4-95e7b1208c7a", "metadata": {}, "outputs": [], "source": ["book_function = {\n", "        \"name\": \"book_ticket\",\n", "    \"description\": \"Get the confirmation of booking a ticket to the destination city. Call this whenever you need to book the ticket, for example when a customer asks 'I'd like to book this ticket'\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to book the ticket\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "bdca8679-935f-4e7f-97e6-e71a4d4f228c", "metadata": {}, "outputs": [], "source": ["# And this is included in a list of tools:\n", "\n", "tools = [{\"type\": \"function\", \"function\": price_function},\n", "        {\"type\": \"function\", \"function\": book_function}]"]}, {"cell_type": "markdown", "id": "c3d3554f-b4e3-4ce7-af6f-68faa6dd2340", "metadata": {}, "source": ["## Getting OpenAI to use our Tool\n", "\n", "There's some fiddly stuff to allow OpenAI \"to call our tool\"\n", "\n", "What we actually do is give the LLM the opportunity to inform us that it wants us to run the tool.\n", "\n", "Here's how the new chat function looks:"]}, {"cell_type": "code", "execution_count": null, "id": "ce9b0744-9c78-408d-b9df-9f6fd9ed78cf", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}]\n", "    for human, assistant in history:\n", "        messages.append({\"role\": \"user\", \"content\": human})\n", "        messages.append({\"role\": \"assistant\", \"content\": assistant})\n", "    messages.append({\"role\": \"user\", \"content\": message})\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages, tools=tools)\n", "\n", "    if response.choices[0].finish_reason==\"tool_calls\":\n", "        message = response.choices[0].message\n", "        response, city = handle_tool_call(message)\n", "        messages.append(message)\n", "        messages.append(response)\n", "        response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "b0992986-ea09-4912-a076-8e5603ee631f", "metadata": {}, "outputs": [], "source": ["# We have to write that function handle_tool_call:\n", "\n", "def handle_tool_call(message):\n", "    tool_call = message.tool_calls[0]\n", "    arguments = json.loads(tool_call.function.arguments)\n", "    city = arguments.get('destination_city')\n", "    price = get_ticket_price(city)\n", "    if price != \"Unknown\":\n", "        ticket_booked = book_ticket(city)\n", "    response = {\n", "        \"role\": \"tool\",\n", "        \"content\": json.dumps({\"destination_city\": city,\n", "                               \"price\": price,\n", "                              \"ticket_booked\": ticket_booked}),\n", "        \"tool_call_id\": message.tool_calls[0].id\n", "    }\n", "    return response, city"]}, {"cell_type": "code", "execution_count": null, "id": "f4be8a71-b19e-4c2f-80df-f59ff2661f14", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat).launch()"]}, {"cell_type": "code", "execution_count": null, "id": "11c9da69-d0cf-4cf2-a49e-e5669deec47b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}