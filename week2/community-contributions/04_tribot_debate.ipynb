{"cells": [{"cell_type": "markdown", "id": "559ec769-087c-4c38-a6e4-4732f4ffb261", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["# TriBot Debate\n", "---\n", "\n", "This notebook sets up a **three-bot chat system** where <PERSON><PERSON> (polite & humorous) 🎭, <PERSON> (argumentative & snarky) 🔥, and <PERSON><PERSON><PERSON> (logical & analytical) 💡 engage in conversations with distinct personalities.\n", "\n", "- 🧑‍💻 **Skill Level:** Advanced \n", "- 🎯 **Purpose:** Simulate diverse conversational styles for debate, analysis, and entertainment\n", "\n", "🛠️ Requirements\n", "- ⚙️ Hardware: ✅ CPU is sufficient — no GPU required\n", "- 🔑 OpenAI API Key\n", "- 🔑 Anthropic API Key (Claude)\n", "- 🔑 Deepseek API Key\n", "  \n", "🔧 Customizable by user\n", "- Selected model: GPT / Claude / Deepseek\n", "- System_prompt\n", "- Starter sentences for each bot\n", "- `max_turns` to control the number of responses in the conversation\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"]}, {"cell_type": "markdown", "id": "fe78fae0", "metadata": {}, "source": ["## 📘 Class Diagram\n", "![](https://github.com/lisekarimi/lexo/blob/main/assets/04_3bot_class_diagram.png?raw=true)"]}, {"cell_type": "markdown", "id": "62a2f5ca-7d89-4ba7-b342-277452beb2f5", "metadata": {}, "source": ["## 📚 Imports & Keys"]}, {"cell_type": "code", "execution_count": null, "id": "ce67806a-3e3b-426d-b442-c3bca2e3dda2", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "import random\n", "import anthropic\n", "from openai import OpenAI\n", "from IPython.display import display, Markdown, update_display"]}, {"cell_type": "code", "execution_count": null, "id": "dd2613d2-b675-4633-aedf-37ea2a1f0234", "metadata": {}, "outputs": [], "source": ["# Load environment variables from .env file\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(\"✅ OpenAI API Key is set.\")\n", "else:\n", "    print(\"❌ OpenAI API Key not set.\")\n", "\n", "if anthropic_api_key:\n", "    print(\"✅ Anthropic API Key is set.\")\n", "else:\n", "    print(\"❌ Anthropic API Key not set.\")\n", "\n", "if deepseek_api_key:\n", "    print(\"✅ Deepseek API Key is set.\")\n", "else:\n", "    print(\"❌ Deepseek API Key not set.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "cb8d5d01-04b9-44e8-a713-da36e6dd9be1", "metadata": {}, "outputs": [], "source": ["# Establishe connection with the chatbot APIs\n", "\n", "# OpenAI API Client\n", "openai = OpenAI()\n", "\n", "# Anthropic API Client\n", "claude = anthropic.Anthropic()\n", "\n", "# DeepSeek using OpenAI-compatible API\n", "deepseek_client = OpenAI(\n", "    api_key=deepseek_api_key,\n", "    base_url=\"https://api.deepseek.com\"\n", ")"]}, {"cell_type": "markdown", "id": "6881d2a1-3a5d-4d0d-a437-aae7fc2542de", "metadata": {}, "source": ["## 📋 Constants & Settings"]}, {"cell_type": "code", "execution_count": null, "id": "7c009278-ad4a-4bdf-8aa4-d0882155710d", "metadata": {}, "outputs": [], "source": ["# We're using cheap versions of models so the costs will be minimal\n", "GPT_MODEL = \"gpt-4o-mini\"\n", "CLAUDE_MODEL = \"claude-3-haiku-20240307\"\n", "DEEPSEEK_MODEL = \"deepseek-chat\"\n", "\n", "MAX_TURNS = 6  # Dynamic, can be adjusted by the user\n", "\n", "# System Prompts\n", "GPT_SYSTEM = \"You are a very polite, courteous chatbot. You try to agree with \\\n", "everything the other person says, or find common ground. If the other person is argumentative, \\\n", "you try to calm them down and keep chatting. Avoid questions like 'How can I assist you?' or 'How can I help you?' \\\n", "and dive directly into the conversation. Be less verbose, don't talk too much. \\\n", "Go straight to the point, don't beat around the bush. Keep the conversation light, fun, and engaging with a touch of humor. \\\n", "Throw in witty remarks, playful jokes, and entertaining responses when appropriate to keep things lively.\"\n", "\n", "CLAUDE_SYSTEM = \"You are a chatbot who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way. \\\n", "Avoid questions like 'How can I assist you?' or 'How can I help you?' \\\n", "and dive directly into the conversation. Be less verbose, don't talk too much. \\\n", "Go straight to the point, don't beat around the bush.\"\n", "\n", "DEEPSEEK_SYSTEM = \"You are a highly logical and analytical chatbot. You break down \\\n", "arguments with precise reasoning, focusing on facts and logic over emotions. You stay neutral \\\n", "and detached, always pointing out inconsistencies or flaws in reasoning. \\\n", "Avoid questions like 'How can I assist you?' or 'How can I help you?' \\\n", "and dive directly into the conversation. Be less verbose, don't talk too much. \\\n", "Go straight to the point, don't beat around the bush.\"\n", "\n", "# Define emojis for each bot\n", "BOT_EMOJIS = {\n", "    \"GPT\": \"🎭\",\n", "    \"Claude\": \"🔥\",\n", "    \"Deepseek\": \"💡\"\n", "}\n", "\n", "# Starter Messages\n", "STARTER_GPT = \"Hey there! Let’s chat—serious debates, silly topics, or why cats rule the world. Your call!\"\n", "STARTER_CLAUDE = \"Hello. Got an argument? Fine. Try me, but be ready—I won’t just agree.\"\n", "STARTER_DEEPSEEK = \"Hi! Let’s dive into a focused discussion. What topic do you want to analyze?\"\n"]}, {"cell_type": "markdown", "id": "7a6c05cc-8bae-4d66-8378-1629092e5d15", "metadata": {}, "source": ["## 🤖 Bot Classes & Logic"]}, {"cell_type": "code", "execution_count": null, "id": "6b9e0ac1-2569-4bdb-b392-8beb767646cb", "metadata": {}, "outputs": [], "source": ["class Chatbot:\n", "    def __init__(self, name, model, system_prompt, starter_message):\n", "        self.name = name\n", "        self.model = model\n", "        self.system_prompt = system_prompt\n", "        self.starter_message = starter_message\n", "\n", "    def reply(self, message_history):\n", "        \"\"\"Override this method in subclasses for specific chatbot behaviors.\"\"\"\n", "        raise NotImplementedError(\"Subclasses must implement this method.\")"]}, {"cell_type": "code", "execution_count": null, "id": "9b883870-6311-4ba3-87e5-3a2bca46487a", "metadata": {}, "outputs": [], "source": ["class GPTBot(Chatbot):\n", "    def reply(self, message_history):\n", "        \"\"\"Calls OpenAI GPT API and returns a response.\"\"\"\n", "        try:\n", "            # Explicitly include the system prompt in the messages list\n", "            messages = [{\"role\": \"system\", \"content\": self.system_prompt}] + [\n", "                {\"role\": msg[\"role\"], \"content\": msg[\"content\"]} for msg in message_history\n", "            ]\n", "            response = openai.chat.completions.create(\n", "                model=self.model,\n", "                messages=messages,  # Use the explicitly formatted messages\n", "                temperature=0.4,\n", "                max_tokens=200,\n", "                stream=True\n", "            )\n", "            return response\n", "        except Exception as e:\n", "            return f\"Error in GPT response: {e}\"\n", "\n", "\n", "class ClaudeBot(Chatbot):\n", "    def reply(self, message_history):\n", "        \"\"\"Calls Anthropic Claude <PERSON> and returns a response.\"\"\"\n", "        try:\n", "            # Extract user/assistant messages\n", "            user_messages = [\n", "                {\"role\": msg[\"role\"], \"content\": msg[\"content\"]} for msg in message_history\n", "            ]\n", "            # Call Claude API with system prompt and user messages\n", "            response = claude.messages.stream(\n", "                model=self.model,\n", "                max_tokens=1000,\n", "                system=self.system_prompt,  # Pass the system prompt\n", "                messages=user_messages  # Pass the conversation history\n", "            )\n", "            return response\n", "        except Exception as e:\n", "            return f\"Error in <PERSON> response: {e}\"\n", "\n", "\n", "class DeepseekBot(Chatbot):\n", "    def reply(self, message_history):\n", "        \"\"\"Calls DeepSeek API using OpenAI-compatible client.\"\"\"\n", "        try:\n", "            # Explicitly include the system prompt in the messages list\n", "            messages = [{\"role\": \"system\", \"content\": self.system_prompt}] + [\n", "                {\"role\": msg[\"role\"], \"content\": msg[\"content\"]} for msg in message_history\n", "            ]\n", "            response = deepseek_client.chat.completions.create(\n", "                model=self.model,\n", "                messages=messages,  # Use the explicitly formatted messages\n", "                max_tokens=200,\n", "                stream=True\n", "            )\n", "            return response\n", "        except Exception as e:\n", "            return f\"Error in DeepSeek response: {e}\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "c07c8814-87af-4bb4-8a90-9d146c228d85", "metadata": {}, "outputs": [], "source": ["class ChatManager:\n", "    def __init__(self, bots, max_turns=MAX_TURNS):\n", "        self.bots = bots  # List of chatbot instances\n", "        self.max_turns = max_turns\n", "        self.message_history = []\n", "        self.current_bot = random.choice(self.bots)  # Random starting bot\n", "\n", "    def conversation(self):\n", "        \"\"\"Manages the chat loop up to max_turns.\"\"\"\n", "\n", "        # Stream the first message as \"user\" role\n", "        emoji = BOT_EMOJIS.get(self.current_bot.name, \"🤖\")  # Default emoji if not found\n", "        response = f\"{emoji} **{self.current_bot.name}:**  \\n\"\n", "        display_handle = display(Markdown(response), display_id=True)\n", "\n", "        for char in self.current_bot.starter_message:\n", "            update_display(Markdown(response + char), display_id=display_handle.display_id)\n", "            response += char\n", "\n", "        # Store first message as \"user\" role\n", "        self.message_history.append({\"role\": \"assistant\", \"content\": self.current_bot.starter_message})\n", "\n", "        print(\"\\n--------------\\n\")  # Fancy separator\n", "\n", "        for _ in range(self.max_turns - 1):  # Already sent 1 message\n", "            self.current_bot = self._choose_next_bot()\n", "\n", "            # Alternate roles while ensuring last role is always \"user\"\n", "            for i in range(len(self.message_history)):\n", "                self.message_history[i][\"role\"] = \"user\" if i % 2 == 0 else \"assistant\"\n", "\n", "            # Ensure the last role is \"user\" before sending to the bot\n", "            if self.message_history[-1][\"role\"] != \"user\":\n", "                self.message_history[-1][\"role\"] = \"user\"\n", "\n", "            # Pass only the message history to the bot and Get bot's response\n", "            response_stream = self.current_bot.reply(self.message_history)\n", "\n", "            # Get the correct emoji for the bot\n", "            emoji = BOT_EMOJIS.get(self.current_bot.name, \"🤖\")\n", "\n", "            # Display bot name separately before streaming starts\n", "            bot_header = f\"{emoji} **{self.current_bot.name}:**  \\n\"\n", "            display_handle = display(Markdown(bot_header), display_id=True)\n", "\n", "            # **Initialize response content separately (exclude bot name)**\n", "            response_content = \"\"\n", "\n", "            if isinstance(self.current_bot, GPTBot) or isinstance(self.current_bot, DeepseekBot):\n", "                # Handle OpenAI GPT & DeepSeek\n", "                for chunk in response_stream:\n", "                    new_text = chunk.choices[0].delta.content or ''  # Get new streamed text\n", "                    response_content += new_text  # Append new content\n", "\n", "                    # Clean Markdown artifacts\n", "                    response_content = response_content.replace(\"```\", \"\").replace(\"markdown\", \"\")\n", "\n", "                    # Update the content, without duplicating the bot name\n", "                    update_display(Markdown(bot_header + response_content), display_id=display_handle.display_id)\n", "\n", "            elif isinstance(self.current_bot, <PERSON><PERSON><PERSON>):\n", "                # Handle <PERSON> differently\n", "                with response_stream as stream:\n", "                    for text in stream.text_stream:\n", "                        response_content += text or ''  # Append new streamed text\n", "                        # Clean Markdown artifacts\n", "                        response_content = response_content.replace(\"```\", \"\").replace(\"markdown\", \"\")\n", "\n", "                        update_display(Markdown(bot_header + response_content), display_id=display_handle.display_id)\n", "\n", "            print(\"\\n--------------\\n\")  # Fancy separator\n", "\n", "            # Store bot response\n", "            self.message_history.append({\"role\": \"assistant\", \"content\": response_content})\n", "\n", "\n", "    def _choose_next_bot(self):\n", "        \"\"\"Selects the next bot dynamically (avoiding immediate self-replies).\"\"\"\n", "        available_bots = [bot for bot in self.bots if bot != self.current_bot]\n", "        return random.choice(available_bots)"]}, {"cell_type": "markdown", "id": "5d8fe072", "metadata": {}, "source": ["## 🗨️ Chat Engine"]}, {"cell_type": "code", "execution_count": null, "id": "ca192335-31e0-4849-878a-da29069f90be", "metadata": {}, "outputs": [], "source": ["def main():\n", "    # Initialize chatbot instances\n", "    gpt_bot = GPTBot(\"GPT\", GPT_MODEL, GPT_SYSTEM, STARTER_GPT)\n", "    claude_bot = <PERSON><PERSON><PERSON>(\"<PERSON>\", CLAUDE_MODEL, CLAUDE_SYSTEM, STARTER_CLAUDE)\n", "    deepseek_bot = DeepseekBot(\"Deepseek\", DEEPSEEK_MODEL, DEEPSEEK_SYSTEM, STARTER_DEEPSEEK)\n", "\n", "    # Create chat manager with all bots\n", "    chat_manager = ChatManager([gpt_bot, claude_bot, deepseek_bot], max_turns=MAX_TURNS)\n", "    # chat_manager = ChatManager([gpt_bot, claude_bot], max_turns=MAX_TURNS)\n", "\n", "    # Start the conversation\n", "    chat_manager.conversation()\n", "\n", "# Ensures the script runs only when executed directly\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"attachments": {"image.png": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABIgAAAO3CAYAAABIp381AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAP+lSURBVHhe7P17eFX1nff/v6JmgySwgUACMaGAyCFJYzCGk60lWiJSsWJvGdRSpbf9ZTqIldKEjqXaKu0tcPPFCtwzmXoXHasyOiPexaEI1igWBdNgoEk4lEMKMZDEABuy0SRKfn+w13LtlbVPyQ4H9/NxXVwXWcfP+pzWZ73XYccNH5XdLgAAAAAAAMSsy+wTAAAAAAAAEFsIEAEAAAAAAMQ4AkQAAAAAAAAxjgARAAAAAABAjIuL5CPVZ3smqOGOf9Zln3rV60CZrqz5UGd7JOiTYdfpk+HX6XLvSQ347xVSe9ibBAAAAAAAwAUWUYDo0/Qs1X1/pX2yn2G/ukVxn7XaJwMAAAAAAOAiFVGAqD3+Sh16ZIN9sin+eK3SV862Tw7b3PnFGjr8ar9pNQcPaPWKpXK73Sp8aIEGJqf4zW9sqFfJ08t13fiJmjZ9ht88q/IPtmnt82vskwEAAAAAAGJeRN8gimv7RFd46nV5c5P6lf5OPer2KPGvb6nv1peks2flqj9oXyUsqWnp+uWTyzsEh8IxMDlFhQ8tUM+ePe2zAAAAAAAAEIaIniCSpGP3PKlPhuaoPb6H4tpaJEntl8crrv2s+r77gvq986x9laCsTwa1tbbp5RefU0V5meQLHH1z6rf078/8q99yG9avU+mmjcovmKpp02d0WG/W7DnKHTfBfPoIAAAAAAAAgUX0BJEkuRoOyr3tFcW1fqL2+B5qj++hK043yV3+B31rwAFdm96uPldKN4+RHvu2tPHHUu7QwDGo68ZPdAwOSVJd7RH9+zP/6re81d7qKp3xNiveFa9+SUn22R3kF0zVspUlKv7543K73fbZAAAAAAAAMSniAFGPj/bos76p+sry/6GBrz2pQS/+s9JX3yfXqK/qV3l79eqDcfrwF9K/3S99b5J0TYqUnRZn34wpJWWwJOnkyeM6tH+f+brZspUlWrayRL9evko5uXn21SRJozIy1SshUW2tbTrR1GSfDQAAAAAAgDBEHCDq9bdt+nRIpo7du0Sf9R2sT7+SrboH1yinT6OGxDXYF5ck/a3ePqVrpk2foWUrS8yPUn9Ue9jvyaNASjdtVNG8Qi194lF5PB77bAAAAAAAgJgUcYAo7rNWpa75kdR+Vicm36eTE/9BY878Va/2WqzVb7Xr8T+06y+HpCc3SKW7z62z56h9K1+orz83s2/f/ho2YqTqao/osZ8u0Ioli3XG22xfvIPyD7bxnSEAAAAAAIAuiDhAJElXeOqV+uyPNPyX+Rr+xM0a+/avlBD3ib7/9TiNGxan9w9I/zi5XRNGSCe80sdB4jzW7wjNuGuWUtPS7Yt0sGH9OhXNK1TRvEJ+uh4AAAAAAKCLOhUgstvne4Xsf2+UHnqxXa992K6v/6847T0q7T1mX9pfXe0Rvf3WZklSr4REzV+4SMtWlmj+wkXqlZBoX7xL+Eg1AAAAAABAR1EJEB09KR1qlPYcbdd7P5P+VBSnf/leu55/r107jwT+BTND6aaNAV8p2/zG62F9XwgAAAAAAACdEzd8VHboCE4YCrLa9eT/kNxXfvGLZX+tbdcPno1T42m/RQEAAAAAAHARubzfgJRf2Cd2xoGGOP21Nk6D3VLvHu36Q0WcFv6n1NQc+CfuAQAAAAAAcOFF7QkiAAAAAAAAXJqi8g0iAAAAAAAAXLoIEAEAAAAAAMQ4AkQAAAAAAAAxjgARAAAAAABAjCNABAAAAAAAEOMIEAEAAAAAAMS4y/sNSPmFfaKTufOLdfU1I1W5q8Jv+qzZc/SNmwtUtm2r3/Royy+Yqpn3fE+VO3eopaXFPjvqZs2eo6xrx3Y4Xklyu936UfHP5OrZQzUH9ttnRyQnN0+zvvc/tbtyZ5ePK5rpuhikpqXrJ488prjLLrvgxxOo/tulpqXrR0WP6NQpj44drbPPvmjZ89rari+mY8rJzdMPH/6JTp44fsHT0hXnuz+7kKLdL52vc05XhSrjcNvV3PnFGjfxhg7HG26fdCHYy9xeZnPnFyuxT++o1AcAAABEz0X7BNHc+cWaNXuOffIlz35c/ZKSdKyuVh6Px2+5C2HW7DmaO7/YPvmCqas9osd+ukClmzbaZ31pXagy6K68ttf3rqooL9NjC3+sivIy+yxEKNpl0x1S09K16IknlZObZ58VM9a98pL69e/vlwc5uXmKd/XQH//wqt+yAAAAQFdctAGiWDHimlHaU11pnwwAgOpqj2j/vr264cZ8yfd0TsG06dr5YdlFcWMBAAAAXx5xw0dlt9snOpk7v1hNHzdq7fNr/KbPmj1HSQMGavWKpZJv8Fr40AINTE6RJNUcPKDVK5Yqv2Cq8sZPUsnTy81BbX7BVE2+aYpKVj2lutojjuu3tbbp5RefU7+kJOWNn6T6o0eVdW2O37YN+QVTNW36DL/1KsrLlJqWru8XztWRv/9dWdfmmOulpqWr8MGH1SshUZJU/sE28/iMO+v245UljWXb33N84sIpHYf273M8LvuTEHPnF2vo8KslSY0N9X75ZZWTm6eZ99yneFe85Ev7H//wqgofWqB9e6o1NjdPvRISO+zHmjZjvbXPr/HbryRtWL/O8djCFeg4AuW5kaf1R49q1JhMnTx5XC+s+a3unfMDv3yeNXuOcsdNMPdjLzOnumisH2jfTqz7aWyoV2trm47V1ZrLO5Wxta5tffdtTb5pSlTKIFi67fUgnHKzHtsZb7NKVj0l7+lTfnllzUvjmF5/7b90w435HfoB44kna1sM1I4ryss6HL+1HYfKP2taAuWnkQeB6mAw9m05pc3ejxhycvN02x3f0e9KVquu9ohZbm+/tVmlmzb61UdJAfsz+3aMbc+YebfWvfxShz5DtvZmL1OjDAJNN/K3of5YwHpmNWv2HA1KTZPLFa++ffvrzY2va9KNkzuUidHfS+rQXwZqP1b2um2U4a2336mkAQMlyTxme1qd6riRlwb7Ocmex/a6FmndqK7a5bd9e5vYt6daKYMG++VbINa0GOdCY7vB0uVUl5zadqA6bbCXhZHf9v7YaHv2vtfeN8+dX6zqql0h+yoAAACcXxE9QZQ7boKWrSzx+2cdHBqDwsM1h1Q0r1CLFxUrITFR+QVTtbe6Si6XS8NGjDSXz8jM1u6qSr+Bu8fj0dInHlXNwQMq/2CbHlnwoDl4HpicopaWT1U0r1AvPPuMrkobovyCqZIl2LRiyWIVzSvU5jdeV8G06XK73ZKk+Ph4JfburaJ5hX7Bobff2qyieYVasWSxRowc1eVXGQKlQ1LA47Kum5CYqMWLilU0r1Dvvv0nv/kG40Lm5RefM9N+6uRJc/7Y3DyVrHpKRfMK9VHtYb98SB8y1EzbhvXrNCYzS6lp6Vq9YqnKP9immoMHVDSvsEsD90DHEU6epwwerCWP/0xLn3hUzadPWbZ67sJmTGaWmf4VSxZrTGaWWQdCmXHX3dpdVWnWzb/XHLQvIvn2M2ToMDP9Zdvf01Vpaeb8QGVsrWtG4NOYP+OuWUpNS5ciLINgeWY8SbD5jdfNecc//thMp5Oc3DyNGDnK3P+rL79kXySo6qpdGjJ0mHmsqWnp6te/v7ZuKfVbLlA7zi+Yqim33KYXnn3GLIeExES/V51C5Z+VvSxeePYZffrJJwHrYDDhps3aj1gd2r9Pra2tGpWRKUkalZGpXgmJysjMliQl9O4jSdpbXSUF6c/s25Gk0RlZajh2rEOfIUuArmheoYrmFWr71j9LkoaNGKl9e6rNY/F6vbox/5sByybc9iFJyckp2rRhvR5Z8KD27D53POGyl5m9/Rgqysu0asUSeU6e0AvPPqOlTzxqBviGDr9a1VW7VORrQ9k5uWY/Ym+/u6sqNeOuu/22LV85WM9JozOy1KtXgkZnZEmSklMGqbnZq0P793W5btjPjUXzCuVy9VCvXueCcaEYTxFN/ua5oFbZ9vfM4FCodIUSLN0Kcr5xu93q0aOnmc/lH2xT3vhJHcoRAAAAl46IAkTlH2wzB7fGv/IPtpnzh40YKZfLpS2lb0q+i8Sy7e8pIzO7w2PyxoVlJK9XNTbUm99cqCgv00e1h5WSMlhyCDbt2P6+WlvbzMF/W1ub30XsqIxMeb1e7dj+vmQZgBsXB50VKh2hJCQkmBeS77/7juMTDzfcmK/dlX81Lxbrao9ow/p15vy339ps7n/rllK5XC5zm//+zL+a8/ZWV6mtrU3JKYPMdaPF6TjCyXPjwsfO7XZryNBhfsdWV3tEu6sqzQvwcBhPHng8Hr3/7jv22eZ+rOko3bRRNQcPmMuEKuO2tjate2Wt33yv12te8EdSBuHkmdEG6mqPaOeOv5jTA4l3ucz97dzxFzMt4TDScd34iZIvfa2trTq0f59tSWcZmdnaVVFu1l2jj7AGnULln8Htditv/CS/OlFRXmaWq1MdDCbctNmDYQaPx6PDNYfM8khJGazKnRWKd/WQ2+0+V5bNzWZaA/Vnxn6Ni22jTlZX7fLbn3wX7/3699e6V74I9G1Yv051tUdUUV6m115ZK1nSZtT/QEK1D8NHtYcdg1XhCNV+wlFz8IAZxN6x/X2dPHlc/ZKSHNvvltI3lZCY2CHAWFd7RCeOn1tPknr36aPKnRVmHozOyDK/D9fVumE/N8r3baEzZ5r9lgvmj394VS7fEzxGOwwnXaEES7eCnG88Ho+ee+ZfzHzeU13pd64BAADApSeiAFEo/ZKS5O7bT/MXLjKfMJo2fYYSEhPldru1p7pS/fr3V2paukZlZOrE8eOdvsiwcrvdSkhM9HvCadHipboqLc0c/NulpAzWwOQULVq81O9pqFAXUMF0Jh1WpZs26u23Nmv+wkX69fJVjk8zGfuorz9qnxWWnNw8/Xr5Ki1bWaL5CxcFvINtXS6Sf8U/f1w7tr/veBxdyfOE3n3kcrl0oqnJb3p9/VGzfoVi3B030um0TqD9GDpTxh6PR97mLy4Ewy0Dhcgzj8ejkqeXa8jQYVq2siSsj1tXlJdp3csvaeY992nZypKwn74yGIEGIyiXkZkdMKhnF6junmhqCnphac8/Q7CyCqctWXU2bXb19Uc1ZOgwpaalK2nAQDPIMmzESGVkZjsGeZxYn24ZNmKkWlvbzKCAVb+kJLW2tspre9rOMHd+sV+9CSac9tFVnWk/kUjo3UcJCQmaNn2Guf35Cxepb9/+jkHYpo8blZGZrdS0dLlcPfT+u+8oITFRI0dlaFBqmvZUV0alboQqp3AY7eBwzSF5fK+sdTVdoQTahyG/YKqZz/fe/4Di488FsAAAAHBpimqA6ERTkz6qrTUfOTf+Ga8GWF+diORiKRRj4Lxh/boOTzgFelWqvv6o+SqP9Z9xkbT2+TWO398IpjPpsCvdtFFF8wr18ovPacbMuztc2Br7MJ5SiESO73sUq1YsUZHvVYFAd7Arysv0yIIHOxxHqH9GWTsdR6g8D8Z7+pRaW1sdLyK9zc1hBSjkuwgumleowzWHVPjQgg4XwU77MS6S1Mkytq4fSRkojHrq8b0uVDSvULK8bhSMUbYrlizW5JumRBwkMgK91153vRISE81XpkIJVnebm70BL56t+WflVFZWTnUwkM6mzc4I7IybeINaW1u0b2+1jtXV6tqx1yve1cMxmOXEeLpldEaW35MsdsGCAcZ344w6Y33aM5BQ7aOrOtN+IuE9fUonTpw0X7ky/jm90itLXR438QY1fdyofXur5W1uVkZ2tlyueDXUH4tK3XAqp+SUQYp3ufyWi0Q00hVKsH3k+77hZJzvX3j2GbW1tdkXc7R6xdKolDcAAACiK6oBokP798nlitett99pnyVZHn+ffHOB4l09HO+Id1Z11S5NvmlKh9cIAtlbXaXklJSAF8dz5xeHdbFtF2k6rO64a5Z5EdtQf0xtra32RSTfPqzf3EhNS/f7SGkg9rvYozIygz690lmBjiNUngfj8T25Ys3b1LR0Tb5pihlorK8/quSUFHP+rbffaX4Q1u12674Hfmhe8Aa6I25cEFm/pXHd+InmdhRGGcfHx5uvUsqXDvleC4m0DILlWWpaur73wD+afzd93Cj5glCLnnjSMX35BVPNbXlPn5LX67UvElJFeZlOHD+u22fcpcM1hyJ6Rc1ed92+7yhZAyDB8s/KqU7k5OZp4te/EbAO5hdMDfh0TDhpC8UI7Hw1e6z2/22v5AtCDPnKUMnXR4Zr65ZSXTNytEaMHOX3apKVEXS3fmNn2vQZGjl6jN+TH27fq1eBhNs+nBiBOuO1R6NdBhKq/XSFx+PRsbpax28aOTm0f5+am736avZY83Xn6qpd+mr2WHktrwN2tW401B9TfHy8bsz/pjnthhvzFX/FuQCR2+1W8c8fj+jbQQojXcZ+jdczc3LzlJ2Ta9tKcPZ9GOeblJTBfsH50RlZYT1B1NljBQAAQPe7vN+AlF/YJzoZN/EGfXLmjCp3VfhNz7p2rHr1SlDZtq1qaWnRoYP7NfW22/Wtb39HBdOmq2DadH32+WeqObBfktTa0qLrx01QzcH9Qe9ot7W16qYpt+rmW6apqalRV/bqpavS0lW+/X21tLRItjTVHNivQYOv0p3/cI+537HXj1Plzh1y9eihsdeP0749u3XsaJ0k6fSpUzp54oRunzFTU6d/WwXTpiv/m1PV1NSoY0frNG7iDZKksm1b/dIlST179tT14ycp57rrzX0Zx1m6aWPAdLS0tHQ4LiM9kjQ4LU3f+Yd7VTBtuiZ+7Ua9tXmjtv15i9++JanmwH6d1Vm/ZXdX/1VHP6rV9eMnqe6jI2Z+D0q9SiNHZ+jD8jIdPnhA14+fpFunz1DBtOnq26+/zp793MyX48eb9I2bvqlvffs7fmUWqUDHESzPTx5v6pB2I5+NaZW7KvzyduLXbtTmN/7bvBNdc2C/Mr+ao1un36GCadN1/OOPFXdZnOo+OqI91VX62jdu0u3f+QcVTJuuq9LStebf/o8+bmywpf5cmefk5pnLxse7dPLEibDr2rVjc/VxQ4O+O+cH5/K5b19zXyc+boyoDP764Y6AedZwrE6Tv3mL7rhrli+d8Vrzrys1/JqR6t27j97a9Ef7oalv337mtm68aYr2VFXq9XX/2SGvre26dx93h/aT2Ke3Ro3J0jtvbfarw3b2+r7tz1v86q6RBuNpvd593EHzz54We50Yk/FV7dzxF8VddpljHZww6evynDzp2PfY25VT2uz54CSxT28NH3GN3vjv9Tp96pQuu/xyjZs4ya/PG3b1iKD9mSR9csarnOvH6ZTH41iWktTS0qLKnTs08euTzXqQ2Lu33vnTJrX7gkXn8mCyTp06pbOff272adayqfuoViNGjgqrfVjrhnxp8HqbddOUWzV1+rd17djr9GF5mZIGDFS5L6hnrVvB2o+RF4bTp04pNS1dt3zrdnOZa0aN8du/ve5W7qrwa78F06Zr5OgMx768paVFQ4dfrcTevfXmH19XS0uL+vbtp5zr8rT9/XfNvqgzdcNaxh83Nmjfnt1+58aqv+5Uv/79tW/PbrP/85w82eEca2WvI6HSdfrUKcVddplZDwanXqW/HzooV48eAdu2nX0fxvlmR9l2s68qmDZd7e3SFVdcrg/Ly9TW2hKwPzHKK9SxAgAA4PwL+2fuoyXV4WeqAUTPD+b+SGXb3uvW9mW8XhLOT8dHojv7B7fbre9+v1DrXnkpoqeeLhS37afCAQAAAKA7RfUVs3DMuOvuqH2cGkBHv139m25tX8YrROF+nPpi4fF4tHrF0ksiOKQgr9YBAAAAQHc4bwEi49dOEhIT9fvfldhnA7gEzJ1frPkLF2l3VSVPtXSTHN+v3I3JzNLv1/z2kgrCAQAAALh0nfdXzAAAAAAAAHBxOW9PEAEAAAAAAODiRIAIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHEEiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHFxw0dlt9snBpKalq7CBx9Wr4REc1r5B9u09vk15t+zZs9R7rgJ5t9nvM0qWfWU6mqPSJJycvM08577FO+KN5fZsH6dSjdtNP+eO79YQ4dfbf7d2FCvkqeXy+PxSGHsg3RGlk4AAAAAABDbIgoQXX75FUpOSVHcZV88eOQ9fcov0OB2u5XQu4/5d/vZs2qor9fnn38mSXL16KGkAQMVFxdnLnPy+HGdOeM1/+6fNEA9r7zS/PuztjY1NjSovf2sFMY+SGdk6QQAAAAAALEtogARAAAAAAAAvnz4BhEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxLioBIj6xZ/V7776qUYkfG6fBQAAAAAAgItcVAJEPxzymb6R9LleHfupxvYhSAQAAAAAAHApiRs+KrvdPjES1/Q6q//O+0SXx5372/OZ9J3yK3Xok8hjT3PnF2vo8KvNv9ta2/Tyi89Jkmbec5/a2lpUsuop1dUesawVHbNmz1HuuAmqOXhAq1cstc8GAAAAAAD40oo8iuOT7Dqrh4a26uFhrWZwSJLcV0grMz/V5XHhx51ycvP06+Wr/IJDAAAAAAAAOD86HSB6YmSr7ruqTVMHfi6lXCZN6CldeW7emMR23Tv4M/sqjtxutwqmTVe8K15nvM1asWSxiuYVqmheoT7Y9mf74gAAAAAAAIiyTr1idk2vs9o47pNzf6RcLuX0kC6Lk86clbZ9IrVIRz+N09e29bKv2kF+wVRNmz7DfJ2sorzMvohycvP8XjGTpMIHH1avhERzGePVMLfbrcKHFmhgcoo2rF+n0k0bO6xfV3vEnBbvildjQ73qjx5V1rU5fq+YGWkzlH+wTWufX+O4PQAAAAAAgEtVp54g+vYg39NBCfoiOCRJvS6TxvWULpcG92xXRmLoD1anpAyWJJ08eVyH9u+zz3aUnDJI8fE9/KYNHX61Zs2e4zctEGtwSJIGJqco69ocv2VmzZ7jFxySpNxxE5RfMNVvGgAAAAAAwKWuUwGijISz5/4zxhIcMiReLg25QpKU08e3XJRVlJfpkQUPmq+ilX+wTZKUNGCgfVFHozOyzCeHFi8qVtG8QtUcPGDOd7vdGjJ0mCRpw/p1KppXqA3r10mSMjKzzf0/9tMFPD0EAAAAAAAueZ0KEPWNbz/3vaGB5wJBHaSfm57sCv32Wn39UUlS3779NWzESPtsR8ZHrZetLNGylSXKHTfBvkhQRiDpcM0heTweSVLTx43m/ITefZSQkCBJmjZ9hpatLOnwNBEAAAAAAMCXRacCRCfb4qTEIKsmXC7FtevUZ7anixzsra7SGW+z4l3xmnHXLKWmpZvz7rhrlnJy8/yWl6QbbsyXJL3w7DMdnv6xMl5fM54YshsydJjcbrffE0N2xhNExj/j+0QAAAAAAABfFp36SPX3Utv02Lg2aWKAj1C3t0tveHXrB720zxskkORj/xi0wfhwtSS/j0LPuOtuDR1+tX1x8wPTc+cX+80/c8ar+Ctc5vqjMjId9yfLNmbNntPhyaQz3maVrHpKySmD+Eg1AAAAAAD40ggdvXHwn8eu0PHGdskb4CPUf/9MpY1XhBUckqTSTRu1YslinfE2+01va2tRQ/0xv2mStHVLqdpa2yRfQMf4BpFh3Ssvmds6423W23/a5De/dNNGv3WctrH2+TUdpgEAAAAAAHwZdeoJIkka2+dzrRnXot4Tekq9LYGg059rz+YW3V1+ZVivmAEAAAAAAODC6nSASJL6XtGuf/pKq2Zktqt/8mU6c+qs/rMiTssPutRMcAgAAAAAAOCS0KUAEQAAAAAAAC594X0kCAAAAAAAAF9aBIgAAAAAAABiHAEiAAAAAACAGEeACAAAAAAAIMYRIAIAAAAAAIhxBIgAAAAAAABi3OX9BqT8wj7xfMgvmKq5Dxcp/5tT1dTUqE/OePWj4p/pjrtmaeToDJVt22pfpQNjGwXTpqtg2vSw17NLTUvXTx55THGXXaaaA/vts6Mmv2CqZt7zPVXu3KGWlhb7bJORnm99+zthHZfb7TbzzsiLpAEDVbmrIux9hmvW7Dm67wc/VMG06WbZHTta57dMfsFUzfnBD7Vvz26dPnXKnD53frHGTbzB71hycvP0wD89pP1/26vrJ0yKalqtjDxy9ezRoYyjUf5z5xfr6mtGqnJXhX1WWHJy8/TQTx7RzbdMc8xTaxknDRiorGvH6hs3FwStF1ZO2587v1gz7/1eyPoVSjS2E6x8LrRZs+dElNexIBptRt1Q7uH2nU59ZrDlnUQ77Z0VTp9sCHTOzMnN0w8f/olOnjiuY0fr/Op8tM8hXWFPZ6Tsx//Z558FLTunemKcWy8WqWnp+lHRIzp1ymP26/bzbHe6VPrHUPXYno/htqtA27WObazjoAstUHq76lKpB9HkVGeikQehzi2R9oORpCvYWLa76k5Xnc8+zz4Wd8qnS1kk43l7/ceXywV5gig1LV03fH2yXnj2GT2y4EFVlJfp1tvvlLe5WUXzCrV6xVL7Kh3Mmj1HeeMnafGiYhXNK1TRvEJ9VHtYbrfbvmhIdbVH9NhPF6h000b7rAviK8OGa90ra1U0r1ArlixWckqK8gum2heTfCeKhY/+SodrDpn5sHhRsXr06NmpvLBKTUvXoieeVE5unuTrGD/99BNzP7sqylUwbXqH/eytrlJbW5uSUwaZ01LT0pWckqLklBSlpqWb0/slJam1tVXe010bQNnTGokLXf5ut1sF06Zr8xuvm+3Bzto+1j6/xj47KKft5xdMVUJiohYvKg6rvQUSre3g0tLZNpOTm6dFTzzp1wd0xazZczR3frH5dyR9pyRtWL/Or99MSEzUL59cHrX0BRONvAi3T1aIc2ZFeZkeW/hjx76ns7rSJwfS1XS63X3N49+wfp0m3zQlYP535dzaHccertUrlnZbX3whj+t8iqRdXezmzi/WrNlz7JNxEYn0XNDVfvBS5tQHdWefZ9eVsfjFLtR43j7e6qpobw/RdUECRKMyMtXa2qpD+/dJvpPxkKHDVF21y75oQEkDBupwzSF5PB5z2muvrPX7+1L1/rvvmB1/Xe0RNdTXKyVlsH0xpaala8Zds7T5jdf9OiqPx6PnnvmXqOeFx+PRa6+sNf/eU10pl8ulhN59/Jbznj6l1tZWjc7IMqeNysiU4uKkuDi/wFFGZnaHcow1Cb37yOVy6URTk32WKWnAQDV93GifHBan7aekDJa3ubnL+R6t7QDREG7f6cTj8WjpE4+qob5eN+Z/0z77ohRun6wv+TkzXNbjdbqRYTjf51ZcXCJpVwBiR1fG4hc7xvOwihs+KrvdPtFJTm6eZt5zn+Jd8WprbdPLLz6nivIyv+ny3ZEt3bQx4PS584s1dPjV5nYryv+izK9eay7X2FCvP7/9J912x10d9mU1a/YcDRk6TCVPL3eszKlp6Sp88GH1SkiUJJV/sM0c6M2aPUeDUtPkcsWrb9/+ev21V/S1yTerbPt75h3x/IKpmjZ9hrm9moMHzIjqrNlzlDRgoPm32+1W4UMLzPUD7Tu/YKryxk/SC2t+q3vn/MBvf8Y2Dtcc8huQ5hdM1eSbpqhk1VOqqz1iTjfm5Y2fFDAPZFmm/uhRZV2bI9mOxZ7WxoZ6lTy9XMNGjPQrP2O6sZ9A6TXY88i4g2V0rmufX6PUtHR9v3CuXn/tv8ynWrojrXIoI6N+7qoo1x//8KrfvFDpkO94csdNMPfX2tqmY3W1jnmhIPXJ3k7saTfSPTA5RZLM9jA6I8vMX3teW4/1RFNTh+1/+smnSv/KV8y07NldpaSkAX77DVbvDPa2bLRxa96c8Tb7bcOaD9a2bS+fQOz5sXVLqbKyc8w6JFvZyNb2w5lvTeMZb7NqjxyWy9XD8Y6KUYcrd1Vo3ISvKd4VH/SY5VCnQ61vzWdr/bC3B+M4jDzat6daY3Pz1CshsUM/GqiMAm3Tzl5e9nps1AUrp3z4/e9KIkqrgpxL7PsMVoft6bey96uh6mxlxYfKyhlr1klrOuz5aZTfdeMndsiL1SuWdqjf1roSKo/D6ZMDnTPtfbG1T7Hmx3e/X2j23wbjDqC1fdjTaq239vK01jFjX5UVHyrf9+SXvc5b02msY81Lp/ph71cVIN0Gex1wEqhsA52P7NPt5SdLGdYfPapRYzJ18uRx/b9XXtJd997X4ZiN9CX07hOy7IKdy6z519hQr/qjR5XYu3eHfAlUprfefqeSBgyUJDO/7f1GoP7GKic3T7fd8R39rmS1X1/09lubVbppY4c2ay93p371yN//rqxrc1Rz8ICqq3b5lam9re3bU62UQYP98llhtKtAdcV+PHLoy5zqfrhlZe8n7fXdGGfaxw79kpIiHoc6sabF6Txpbx+Byt1ertZpRjqClXWgfLaOiayMfG5tbdNVaWlhjVvs5WasY++PjP1u3VLaIU05uXmaMfNurXv5Jb/65XRsoc6LgfarAG3Qmh9G3krqUGflq0f2Pt4Qqo7a25SRj8kpgxzzwzotWP4bQvVBTn2etd4b+RxJuzLYj826nL1+2PPeer3ptG17HbCvH6hsncoq2HktWJsMNZ5ymr+3uirk+NWeN07nZ+t0g71+2KdJcjz/Wq9h7OOCE01NHbZp7yuC5ZG93Rn7sfb/9n7YqY1dKsL6BlFqWrq+O+cBvfXmRv3bqqf0p00bdOxonfILpur2GTP18ovP6fn/W6LPPv9MkjTs6hF+0xsb6nXr9Dt08sRxrX/1FY0cnaH3/vyO/m3VU/prxQ41NTUqaUCynl66WHuqq/QP372vw77sKndVKCc3T7d/5x+UZHsP1Dqw+LdVT6nqrzt18y23mu9JZl07VsOvvkZ/ePVlvfDsMzrlOanrx09S3UdHVHNgv/ILpmrKLbdp7fNr9Pz/LdEH772riV+frPSvDFXlrgplXTtWvXolmO9n9uzZ02/97/3Pf9Thv9doxZNP6IP33pWrRw/VHv67hl09Qlelpev9d99WcspgfWXocHMbY7KyNSbzq3rtP//DfF89P8gFjiRNmPR1XXFFvN59+y37LNOwq0co57rrdeTwufQ0NtRr4g3f0FmdVc2B/bp+wiTVHDpo5tPEr92olrZWbfvzFu2u/qsysr6q/1r7gv5r7QvmO8f2k7eTK+Ljde3YXO3/215dFhenr+dP0V+2v6fm5tPmcV99zSiNHJ2hP79TqtOnTnVLWg3WMurbt59m3nOfNr/xul5f958dyi9UOmb5LrRW/K/HtX7df8rVs4dy88br6Ee1ju8jB6tPb27876Bpb2lp0dYtpRo5OkMH/rZPv1n2a7MOG3UwWH10ypvt772rpAED1drSol89+s+qP1qn68dPVGNDg9nWvnX7nTr89xq9/+d3zLTYlW3b6rcdp7wZNPgqXZc33vyWyeSbpmjViqV69T9e0Fmd1Y3531Tlzh2S5FcGTqz1bsWTT2jzH1/XzbdMU1LSQFX9daf5DYAxmVnmPqr+ulNTb7vd/FZOqPn2NJ48cUJfu/EmnT7lcXwfu3cft8ZNvEF93H31m6WLteWtzcq+7npdfc0o85gDlX3lroqw1h86/GozP8+2n1Vj/TElDUwO2MedPN6k68dP0pCvDFXJqqf06n+8oNGZWRqT+VVV7tyhMVnZmnDD1/Uvv1muV//jBZ04flxnvF717uMOuE17H2ytYyc+btQ/fPd+vfvOn8z1Lou7TPW2dWoO7FdjQ72+MnSY/uU3y/XWpj+a2wmU1p49eyore6z+bdX/p/Xr/lNJAwYqIytblTt36N233+pQ/wyh+k57m7fq27efxmR+VVV/3aXrJ0wKWWdHjs7UH159ucO57tjRuoB9VummjR3ywl6/jbri6tkjZB6H0ycHO2f27uPW2OvHad+e3R36F+O8Vb79fV3hcpn539LSotS0dE36+jf0pzf8z9PHjtZ16HdaWlpCtj+j742Li9OvHv1nbf7j68rJzVP22OtVtm1rh3Ta22tjQ73i4uJ0zegxju3G6FuDDaIV5rk1UNk69bk9e/YMWn4Go14m9u6t3yxdrLc2bVSPnlf6HbN853WjTFw9egQtu2DnMnv+eb3N+sZNUxz7u0BlmnXtWGVkZZtjus8+/0yTvjbZ/GZPsHOC1SdnvBp7/Xi1tLaY5/uMrGsVH+86d64ZmKzssbkq3bxR10+YFFa/2tbWpl89+s8d6nHPnj07nEsmfu0bfucShdmujDy+8aYpft8zy865Tmc//1zlH2zT6VOnwq774ZaVtS+a9I38DvX9oyN/11ubNnYYO0Q6DnViT4v9PGmMwXdXVZr5O2jwVfp6/s0dvl/T0tKi9K8MDZiOUGU9KPUqjRydoQ/Ly8w028dEVkY+/9nXFkONW9xud8BzUKC2t+VPm/zqsiRN/uYtaj/brvWvvuKXns6cF532G6wNGunat7tShQ8tkLe5WU8tWdxhnCxJ4ybeoE/OnHEcy4aqo2OysvX5559r5f9+Uh+8966yr7teffv1V9n7f+6QH9+Z9V19dOSw3v/zO0Hz3ypYHxSozxuYkqJbvnW74uNd+tWj/6zGhnpdP36ieY6212Vru7LXU6exeKh2bb/etJ4nDTfmf1MvPvd/9ep/vKDPPv9MeeMnmt9wDVa2zc2nwzofy3JdHKhNBhtPKcB4P9T4NVjbCbU/+/lAtjoT6Pzr1Fca44LP2tqC9hWh8sje7mQ7Fzv1w9Zxx6Wm06+Yud1u5Y2fpM1vvG5G0ko3bdSO7e8rb/wk7aooN6cf2r9P3uZm9UtKUmpauvr17+/3usvojCy1tbZEHGVbvWKpFi8q1pChw/Tr5auU43sndVRGprxer3Zsf1/yvWqwf99ev1eePqo93CGKa8jIzPZLv8fjUdn29zRk6DC5w3wH3Yj4ejwevf9ux4vsLaVvql///maaR2dk6cTx4+bFTKrvO03rXlnreIETicaGev3xD69KvneXP6o9bL52Ubppoxm1rQvzlQzjHdxAAyb5yry1tVWjMjI1bMRIJSYmqKH+mE40Nalf//5KTUvvcMzqhrTaud19zVcH7HdvrQKlw+17HbJs+3tmfS3dtFE1Bw/YtvCFaNSn7mS0jxtuzJd8da9f//7aU11pXzQop7zZUvqmEhITlZqWrozMbO2uqjTLe8f299Xa2qZhI0batuRs2IiRcrlc2lL6pjlt3Ssv6cyZZsmy/7ff2mzuo672iHZXVSojMzvkfPnKyprGivIy7aooN/fnpK2tTZs2rJfH4zHLNiExUW63O6yyD7a+JCUkJJivNrz/7jvyeDxh9XHW49y6pdTvFYl4l8t8tWbnjr+orvZIWNsMxmiLdbVHtHPHX+yzgwqUVo/tlZ5wXvWIZt9prw9OddZ6DqwoL1PDsWNmnkXSZ9nrt1EXjLqpIHkcTp+sIOfMcBl147rxEyWH18WDCaf9ydf3/v53JebfW7eUmucMK2MMYt1eRXmZeb51ajfyXdwmJCb67aMzIilbQ6Dys7P2oV0V6Fwmh/odTn/npObgATMvdmx/XydPHle/pKSQ5wQrj8ejwzWHzLSlpAxW5c4Kxbt6yO12n+ufmptVV3sk7H5165ZSv30Y7G1NtnOJIdx21dhQ7/dtr6J5hXrh2WfU1tYmRVj3wy0re18UqL4HE2ocGog9LfZ6Y5xLjGORb18ul8vxfB8sHeGUdaQaG+rNvixUHe3MOchIY974SXK73eY+IvmMhoKcF50EaoNW3/1+obzNzQED4+EIVkcrysvMVzON9pw0YKD5f6OuW8eYofK/M6xp3FNdqTNnvGZfYL0elUNdtrerYMJt18GuNyXp35/5V3N9p1efA5VtJOfjSNtkuIKNXzvTdgzB6oyCnH9DjQuCiUYedaYfvliFFSCqqz2ida+s1ZRbblPxzx+X2+02M2BvdZXfssb3TqwXl9ZvoBiVvqH+mDk/yfJOp9O+gvH4vhux+Y3XNeOuWUpNS1dKymANTE7RosVLtWxliZatLFHuuAlm0CYYt9uthMRE1dcf9Zt+oqkp7IptdL7LVpYEPIa62iM6cfy4Rmdkye12a1BqWofBTGtrq18+2dXXH/W7kOwMt9ut4p8/buaT9ZG/YEK9g+vxeORtblZKymD1S0oyT/bWwFHSgIERnTA7m1arG27M1+6qyqDBoWCsdTkc0ahP58Oe6krzImxURqZOHD8e9ITmJKF3HyUkJGja9BlmGc1fuEh9+/bX0GHDlZCYqNxxE8x5ixYv1VVpaR0GMfLd4TeWMy5kQ33QPFDZGO1k0ODUoPOvSkt3LKvOSuzdx3F7kZR96aaNevutzZq/cJHfBX1X+riK8jKte/klzbznPi1bWaJ836s8nd2mx+NRydPLNWToMC1bWWI+nREt+QVTzfTce/8Dio8/93h5MKH6zmCMehYnRVRnDda+MZI+q19Sktx9+2n+wkXm8tOmz1BC4rlHnUPlcag+2eB0zgyXfdCWkZkddjAjVPsMdB5rqD+m1tZW++SA21OQdmMI9Z2FUGlShGXb3W2kMwKdm6Ip2DnB6dtP9fVHNWToMKWmpStpwEBzUD9sxEhlZGarumpXwHRH0q+GOpdYhduugglUV8OpZ7KUVaC+KFR9DySccahdoPy3SnH4lonxbUqnvjNQOgLtK5KyDiWcOtqZc9De6irzwnLYiJFqbW0zL+gvhKHDr1ZCFALjoVjHbtZXi6xBH+sYM5z874qG+mPyNjc7jgdCtatQutquDTm5efr18lXmsffqde6cH0ok5+NI22S0dKbtGALVGQU5/wYqk3B0NY862w9frMIKEMl3UbHk8Z9JvrsqySmD5HK57Is5BoBGZWQqPj5eDfXn7qzan5SxP61g31c4rFHX+vqjqjl4wO9uTlGYv45mDWzYNTd7wxpQyBckKppXqMM1h1T40ALHjqK6apeGDB2mjOxz7/Jao751tUe09IlHzXxyYj0BdYbb8u63kUfBnoQxrH1+Tcg7arIc37Vj88xAkJG/I64Z1eFJsmA6m1a7rVtKNSYzS7M6+aseTp2FcZJxEq361N2sgTtjIB4p7+lTOnHipF549hm/dvfIggf13rvvyNvcrA2WX40y/jkF64z2Y6xvnBSs0Xn5+pt4Xz/kVDYGb3Ozjh2tCzr/o9ojjmUVKkASSPPpU47bU4RlX7ppo4rmFerlF5/TjJl3Kyc3r0t9nHx97CMLHtSKJYs1+aYpyi+Y2qVtGkGHonmFkuUVnq7K973fbdydt96VDyScvjMQt+/uU9n298z6EG6dNRj1JdI+60RTkz6qre3wJMLSJx6Vx3d3LlAeh9snWzndqQyHEUy+9rrrlZCY2OEmUSCh2qfToFa+Nu401gi2PQVoN8b0UHU61Lk10rJVN7aRrrL3T53t75wEOyc43YAw8n3cxBvU2tqifXurdayuVteOvV7xrh460dQUlXOqU4DBei4xdKZdOQlWV4PVfYNxzMH6okD1PZRg41AngfLfWm8CXSC3trYFHPM5pSPQvhRBWYcSqo525hwkW9BrdEaWjtXVhizn7lRz8IC8zc0Br0eiwfgmjpGH5R9sM+fV+c6n9jFmqPzvTuG0q2C62q5l+a7OqhVLVOT79VX7k4zBhHs+7kyb7KrOth1DoDoT7PwbrExCiUYedbYfvhiFHSCSpTHJFwCKj4/XqIxMyVcR8gumdpiempauyTdNMR/hsz4tpAABJdn25eR7D/yj351PaxBqb3VVyJ83Dqa6apeyc3LNgnX7fibc6ODr64/6/Vz7rbffaX64zO12674HfmhWMPudDyvjUcapt31bOz8s8+tMUtPS9csnlwc9hjrfKyD33v+A33L2NARij7Sm+n6KPpRZs+cEfDLKam91lRISEpScnOLXuKqrdiltyFfU3OwNORgxdDatdh7PSZWseqrTQSKjXhqPDsv3eKdR/k5C1aeuClYfw+XxPRo6+eYCxbt6mHe6ciL4+VWPx6NjdbUBfw64umqXJgf5WelQjBOf9RembrgxX/FXnBvUe3x3U6z7MPqf6qpdIefLd7d4TGaWOT8nN09XpQ0x9xeprpb9HXfNMtdtqD+mNt+TFF3p44y+Wr6TqdfrlbqwzdS0dH3vgX80/47GHXeD/Y7O6IyskHegwuk7naSmpevH//yovM3N5uAwnDpr7QvyC6YqOSXl3J2vCPusQ/v3yeWKd7wpEiqPw+mTg50zI1FRXqYTx4/r9hl36XDNobADceG0P0kamJxiPjJvtJf9+/Z22I/T9nJy8zTx698I2G7kK6NfPrk8aJmGOrfan0YMVbahyi8YY8BrvLZo5FlXGfkXzf7OLtQ5wc64qP5q9ljt/9teyXcBNOQrQyVL8KKr/aoxTg10LjGE067C4VRXnep+MMH6omD1PZRA49C584sDjpFCnSeNsZ+1L7v19jvlcsUHHPMFSkeosrZfc+Tk5ik7J9dv28GEqqOdOQcZtm4p1TUjR2vEyFF+rzNeKKtXLO22IJHb9rSX2/f6lVW170Px1kBGqPzvbsHaVSjRaNf2pxlHZWSG/QSRIjgfd6ZNdlVX2o7Bqc4EG1s5lYkxLgjVV4TKo1Dn4q70wxejK+wT7Ny2L7cb3wfweDxa98pazbznPk2bPsPvS9/W6bJ8ndzoQKwNx9o4Au3LSWtLi+YvXGT+bf9yuj0Ngb5M78S4OLj3/gd07/0PSLavypdu2qiMzGxz/5U7K9TYUC/5Kmdi795atPjcHUojXU6DFqNj7Nevb8Cobyhrn1+jPdWVfscqX5477dPKOgi+9/4HdMbbbF4s2ucXTJse8dfYv7j49A8E7a2u0uSbpoQ9mFOU02qto0OGDtMLa35rXySo1SuWau78YrOMaw4eCHr3OFR96qpg9TESTuXSz/J6YDjW+n5ZwcgbWX4xonTTRqWkDPZrt42+r/yHw1puxqPLxvdJDGufX6NZs+f47cP66wiRzm9sqNfe3VVK7N3bXD4SXS17j+ek37ob1q8z+7BAfVyok/0J3y/bGeuVf7DNTGegbQbrN72nT2nQ4FQtW3murw5WphXlZbrhxnzNX7hINb5fawlmS+mbKnzwYXPbH9XW+t2Bss63/wpGOKZNnxH0WMOps/VHj2rho7/y+9VNo70E67PsebF6xVL9fs1v/Y5XvjLfsf39sPM4kGDnzEgHx9VVuzTlltuCfqfMqU+2ty85/HpJY0O9rh2bZ5ZLzcEDAduLfXtG/gdrN+EKdm7du2d30LK1H/sLa37b6fLzeDzatGG92e+d8Tbrw/IyjRydYV80YkZ/HW5/Zz+ucI4h2DnBiXHBZoyJGuqP6Yr4K/zOS13tV+tqj6hk1VN+bc1+Lok2e12VQ90PJlhfFKy+b91Sqpn33KfsnFy9/OJz5roGp3Go2+1WvKtHwPZtPxZ7vXHK3y/S6jwuc0qHwijrutojevutzWZfbk9LOILV0VDnoGAO7d+nltYWeX3fzgrEfi4IdV7sCuMX0hY++qsO57uu8PhuMhrl0NbapgbbONQYY1q/+aMQ+W/XmT4omODtyrmuWtnbgiJs1zt83+01jr2xoT6iJ4gUwfk40jZpZx9vhbpuDdV27NtzyjOnOmOtA07nX3uZWMdlwfqKcPIo2Lk4WD98KQr7Z+6jIcfhZ+uMRxLDPbF/2cwK8nOcwPmU6vATjj+Y+yOVbXvvou3knPoUAN3LeHQ8ksFlOLpruwhfrI/JYpF9HJqTm6e8CZP029W/sS/arezpuNQZN73Ltr/nePELRAPnTXSHiF4x6yr7o3T2RxJjTU5unsZkfTXkRwGB82HGXXd3+Dj1b1f/5qINDhmPmkfyhBOArjEeqw70MUxcuvILpuqqtCFB70Tjy8VpHFpRXnbeg0NO6bjUGa+qXMiPU+PLjfMxust5fYII51hfpQv0WB1wvuQXTDUft7yY70DYX0FVkMeQAUTf3PnFGjr86ohe54kEd0LPL6PvNzi9Zokvp4tlHHqxpCOacnLzNPOe+9TW1mK+xgtEW3efjxHbCBABAAAAAADEuPP6ihkAAAAAAAAuPgSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcZf3G5DyC/vEcKSmpetHRY/o1CmPjh2ts89Walq6fvLIY4q77DLVHNhvnx22/IKpmnnP91S5c4d69uypHxX/THfcNUsF06Zr7PXjVLlzh1paWuyrae78Yl19zUhV7qqwz4oJbrdbPyr+mVw9e3Qp/y+0UMcRrXoWqVD1/3wLlU9OotlGUtPS9cMf/USHDu7X6VOn7LO/tGbNnqNp374zYD/U3ebOL9a4iTeobNtW+6ywzJo9R9+4uaDT64fL2o9fiHxCaNGuyzm5efrhwz/RyRPHHftIex/a1bqsTvaDF6to5EdX5OTm6YF/ekj7/7bX7NPzC6Zq7sNF+to38rVvz+7z1tfnF0zVt26/84LlxZeNve3ZhWq7l5Jo9QmhtmMfi56vc2u4rO25dx/3BRk3R8v5zNvzsS973fqyjpfsxxkOe7uKtmiPe75Muu0JorraI3rspwtUummjfVanDRsxUu++/ScVzSvU4kXFkqRbb7/TvliXzJo9R3Pnn9s2zr+c3DwteuJJpaal22c56o56hq6LlXa09vk1WvrEo/J4PPZZ58XqFUu1esVS+2RHqWnpWvTEk8rJzbPPuuR9Gevb3PnFmjV7jn1yRCLZRrTrckV5mR5b+GNVlJfZZzmKpC5/2Ti1zYstP1LT0nXD1yfrhWef0WM/XaC62iP2RWLOpdLvRJrOSNvuxSTSMWS0RHMsGml5RSqaae1uTn0jYkd311X7uCeSMdOXXbcFiLpDRXmZ3n/3HUmSx+PR4ZpDShow0L4YAAAAoiQ5ZZAkqaH+mH0WAAD4EokbPiq73T7RidvtVuFDCzQwOUWStG9PtVIGDdbrr/2XKsrLNGv2HA1KTZPLFa++ffvr9dde0dcm36yy7e/pRFOTbrvjO/pdyWrzrlNObp7ftFmz5yh33ARJ0hlvs0pWPaW62iPKL5iqvPGTVPL0cr87mzm5eZox826te/klx7scc+cXq+njRq19fo19lvILpmra9BmSpLbWNr384nOqKC/T3PnFGjr8anO5DevXdYhaGumpP3pUWdfmmOv3S0oyt1lz8IDfnb9A+7NLTUvX9wvnqnJXhcZN+JriXfEq/2CbtpS+qcIHH1avhES/vJHvToORb7Kk2Sivsu3vqXTTRuXk5mnmPfdpV0W51j6/pkN52tNsZV/WfgyzZs8xA3VG/pV/sM0v7wOVr5U1n+RL0+9/V6LChxZo355qjc3NU6+ERL/9248z0rTUHDwgSQHrihzStWH9Ou2trtL3C+ea9V9BjtGef9Z5Rpkf+fvflXVtTtByCMaeD06s6WtsqFdra5uO1dWax20/TmtanOpmoHIMpx0pSLsIZ1+paelmm2hrbdMH2/6srOwcsz+xH0ukdSBQ2uys/ZOkoHXVzjjOre++rck3TXFc3l537PON+r56xdIOfZMsZWi0/3hXvOQr/5Knl+vW2+8011eI4w5Uv63lbWzX/hSKkbbKig+VXzDVcdlQfVmgfA1U36x1RJY6YGyv/uhRjRqTqZMnjzum2b5+Z9ptoPwMdkyH9u8LWOahtmcc0+lTJyVJ/X19YaBtBzrXqpN12Zof6155ya+PtNdlpzGEtS7al29sqNf/e+Ul3XXvfX79rlO6rf2gvS+wlpV9HCJbm7LXAXs/Yoj0vGOd35m2qU70z+G0Uytr3ozKyAy4b/uy1jGedYwW6Phly3MFOZ8F2k84Y0kn1jxtbKhX/dGjSuzd2y9fA/UBgfodK/v61mOOtDxDjQUD7cspncb4JVB9Mfb/+mv/ZfYZwfoCe1vduqVUWdk5fu3UYB+bz5o9R0OGDjPro7U9ezyeoPXGWLa1tU1XpaVpz+4qjR6TaS4bzhgy3PS8sOa3unfODwJux15n7XU6UPnYOZWX0/nMXv529uUrd1Yo/Stf0e9KVst7+pRfWu19kLUu2+vphvXrHK/p7Mfr1N8k9O4TVh4Y7Omy940K0N/a66O1bQUbJzkxjqvp40a/MeNqy5jL2pfmF0zV5JumOJaNPS/tYxKjPJy2a4hm36AgaVIY5zV7+Tj1gVbGcVZWfKisnLFm+VjXmxUklmDNm2DlZ+0znMb3Vvb27TTusrPnWVfq18UsrCeIjEI9XHNIRfMKVTSvUC5XD/Xqda6yGZKTU7Rpw3o9suBBVVm+a3Jo/z61trZqVMYXHfcNN+Zr/7695gl9yNBhWryoWEXzCrW7qlIz7rrbXNbOPvCIhNF4VyxZrKJ5hdr8xusqmDZdbrdbq1csVfkH21Rz8ICK5hUGrOgDk1PU0vKpiuYValdFue69/wFlZGaraF6hXnj2GSUPGmQ+Dhlsf07i4+M1cnSGljz+M73w7DPKzslV4YMPq2TVU1q8qFher1c35n9T8pVLjx49zXwr/2Cb8sZP6rBtoxFvfuN1v87IKM/Fi4qVkJiofN+Fm5WxrLe52Sz7zW+8rhl3zVKq5RHeocOvVnXVLhXNK9SG9euUnZNr5kG45Vu6aaNeePYZeU6e0Ioli/0a1NjcPJWsekpF8wr1Ue3hoHkYSVqqq3b5nYzt7OX3wrPP6NNPPrEv1mG71mMcNmKk9u2pNvPaWobylXli794qmlfYbZ2IPX1l29/TVWlp5vz8gqmacstteuHZZ/zqxCzLo5bWumkch1M5htOO7PlqbxfB9mWc7N5+a7OK5hVqyeM/08jRGYqPP3eSkqT0IUPNbW9Yv05jMrPM+mrPC3sdCJW2UCKpq/Hx8eZgwtiX0bbCbXtW1r7phWef0VVpQ5RfMFUV5WVatWKJPCdP6IVnn3F8lSjYcefk5mnEyFHmvFdffslcJyEx0czLd9/+k982rQYmp2jYiGvMY/E2N+u73y+UwuzLAuWrU32z15EVSxZrxMhRZj8gSSmDB2vJ4z9zzAtj/d1VlWZ6d1dV6rtzfuBXR4O122D5aXA6Jkla+sSjqjl4QOUfbNMjCx40g0Ohtmcc0//65SL9r18u6rCNUH2RnVP6gtXlQPlhP+cUBRhDGJyW37en2r5YSOH0a8HMuOtusw4sXlSsv9cctC9iCnXeGZOZZZbdiiWLNSYzK6K2Geo4gvWZkbRTJ8HOzQowxhudkaWGY8fM4FCg44+E0346O5a0t6dNG9ZrlCW4EKoPcOp37ALVn0jLM9RYMFh/FyidweqLk0B9gVNbvSptSMC2XV21S0OGDjPXHTJ0mPr27a9hI0ZKklJSButwzSEzOBSq3gxMTtHOD8tUNK9Q//f/PB2wngZKf7jpaT597ntbgbYTTLDysXMqr1B10c6+v8WLipUyeLDfGMngdrtVMG26Nr/xupm24x9/LDm0kUDjX7tA/U2g9hBIsL4xUH9rr49G27LXGadxUiDG+NC6vVmz52hvdZVcLpdZVyQpIzNbu6sqOwSHFGJcGolo9Q0KI03B8jlQvQnla5Nv1qYN6838n3xzgV9bCBRLMAQrP/s5wD6+D6T59CnHcZddOH13sPRdSsIKEA0bMVIul0tbSt80p6175SWdOdPst9xHtYcdM9R4HSwjM1vyVdh+/ftrT3Wl2SGXbX/PbPhbSt9UQmKiY8MxKuXbf9rkuK9Q7I13x/b31dra5tfAQ2lsqNcf//CqJGlPdaXOnPFq65ZSyTeA8TY3q19SktSJ/bW1tWnThvXyeDw6tH+fTp48bq5v5KMR0fV4PHrumX8x821PdaVcLpcSevcxt+d299WMu2Zp8xuvmwMDe3l6PB6VbX/PLB8rY9l1r5y7IJTvGLxer98grebgAXP7O7a/r5Mnj6tfUlLE5RvI229tNvNw65bSDsdpFUlaSjdtNCPMdm63W3njJ/ntu8LymqN1Oft2rcdYUV6m115ZKwV4NbKtrc2sP93BKX32487IzNauinKzTRl1whg4yVY3jfkJiYmOA5RQQrWLYPsalZEpr9erHdvfl3xp3bRhvdra2szt//sz/2pue291ldra2pScMijsvAiWtlAiqattbW1a98pav30ZbSvctmdl7Zsqysv0Ue1hpaQMti/mKNRxx7tc5msmO3f8xVwuISHBPL73333HzFe7xoZ6/f53JebfW7eUql///kpNSw+rL4skX+11pK72iPbv26vRGVnmMtY6YGesb+SlfG3aOhgM1W5D5aciPKZwthfsmOSrE8H6IrtI0hcsP+znHAUYQxiuGz9Rkvzy/7VX1up0s/PygYTTr4ViPd/a+36rUOcda17W1R7R7qpKx3Ouk3COI1ifqQjaaWcY+zOCusYxV1ftisrxG4w6G42xpL09VZSXaVdFuTk/nD4gHE71J9LyDDUWDKe/swtVX+wC9QWRtu291VWSpITefZTQu4+8zc1qaKg328qg1DS/8gxVbxob6s3jDiZQ+sNNT6jtBNOZ8rGKtC7a9+dxGCPZGeOEutoj2rnjL3KHOf4NJFB/49QeOiNQf2uvj0bdtteZSMZJNQcPmE+fWNuq9/Qp7d+3VzfcmC/Z+iMngcalkYpm3xAqTYHy2WCvN+HY/MbrZt9XUV6mhmPH/NpCoFiCIVD5OZ0D7OP7rgqn7w6UvktNWAGifklJam1tldcXQe8M64l6VEamThw/rorysnOdckKCpk2foWUrS7RsZYnmL1ykvn37B2w4ra1tOtHUZJ8cktvtVkJionLHTTD3tWjxUl2VluZX4SPRUH/s3AnF4b38ru7P4/HI29ys+vqj9lmm/IKp5rbvvf+BDncIbrgxX7urKv3ubvVLSpK7bz/NX7jIXHfa9BmOgwOnsjfSFU6F70z5dpeE3n3kcrnCrjvhLh/OMc6dX2zOsz4uHY5Zs+eY6y5bWRJxJDrUcRj11F7PTjQ1hTX4iVRX20VKymB5m5vNE4CTnNw8/Xr5KrMsjLuZ4eZFZ9PWVUbbUhTaXiRCHXdFeZnWvfySZt5zn18dLN20UW+/tVnzFy7Sr5evcrwjGkhD/TG1traaf4fqyyKRkjJYA5NTtGjxUr92FywYYuVUx7ynT6m1tTWsehAqPyMVze11pS/qLKe6HIxT/kcqGv2a8QTCspUlKv754x3Oj+EI1OfU1x91POfaReM4utJOw2W9mz5sxEi1trZpx/b3u3z8dtEYSwbKUyunOhhJH6AA9SfQvoOVZ6ixYFf7u66ItG0by43KyNSojEw1fdyonR+WKSMz2wx2HNq/L+r1JpBw09MVXS2fSOui0/KBeDwelTy9XEOGDtOylSXmx7ED5X84AvU3Tu0h2iK5vumsE01Nam09F2zbU11p3uiy9kdOAo1Lu6KrfUNn0xSo3nRG08eN9kmd0pU6G47O9N2XsrACRE4Hn5wySPEul99ywdTVHpG3uVmjMjKVkZmt6qpdkq+TO3HipPm4lvEv0ONdHo9HTy15wnFeKEZD2rB+nd++igI8HtxV3b2/fN+7jsajdC88+0yHOwRbt5RqTGaW3+NvJ5qa9FFtrbme8c/p0XansjfYG4mTSMu3u8XHx/udUI0G7yTYCdgq1DEa77gb08s/2GbfRFBrn1/TpbrjdBzW4w4WdGhu9oY98AtXNNqF/WTfLynJDCjk+L5JsWrFEhX5Hn213s0MVgeikbausKalq20vEuEcd0V5mR5Z8KBWLFmsyTdN8QsSFc0r1MsvPqcZM+8O++IzOWWQXL5zSDh9WSTq64+aj+hb/9lfjQkk0EVIuDcnwsnPSERre13tizrLqS4HG0MEyv9IRKtfW71iqYrmFepwzSEVPrQg4jQ59b/mvDAu4qJ1HJ1tp+Gqqz2iE8ePa3RGlkZnZOlYXe25tHfx+O2iNZaU5e63wXrBHqgOhtsHGOz1R77jtu9bEZanVVf7u66yPi2iEG3bY3kKbMQ1o7SnulJ7q6uUkJioocOvVltrS7fUm0DCTU9XdLV8Iq2LTstbx0h2Ho9HS594VEXzzr3yPXd+cdD8D0eg/sbeHuzH1FWRXN90Vr+kJLl8392xvvZq7Y/sQo1Lu0uwutfVNDnVm84IN1AajmDj+66K1rn4UhFWgKih/pji4+P9vlVww435ir/C+QQQSHXVLuWNn6SExETzsU6Px6NjdbVhvccrX2EX//zxsL8hYFddtUuTb5ri+Mhxd+jO/dnvEozOyOpwAvB4Tqpk1VN+QaJD+/fJ5YrXrbff6besE6Pzs76bnl8wVckpKWYZBhNp+Xanutojaqiv9/u2yXXjJ5ofJbMzBg7W8svJzdPEr3+jw3KBjtHonIwLerfvEcjzyejUgh13ddUuv29muH2vchqD/GjrSrvYU12pvn37m6+huH2PQhvsdzRHZWSad0XCqQNdSVuk4uPjzceTJZltcsf297vc9iIV7LjzC6aaASHv6VPyer2SpDvummXWmYb6Y2qzPBFkNzA5xa/MCqZNN78dEk5fFom91VVKTkmJ+Gk7w97qKiUkJPj1kbfefqdcrviw7yYHy8/O6Or2LmRfFOkYwin/77hrluIktba2mo+jp6ala/JNUyxr+gvVrxnpGuV7ZTMnN0/ZObnmsvc98EOzn+hsUNbpPGKkO9DFhF2o4wglUDvNL5iqXz65vNN1ym7rllJdM3K0Rowc5feKR6jjr68/quSUFHP+rbffGfC8rCiMJY00Wb+3kZObp6vShpjLONXBSPqAYPWnq+Vp19X+riuM/A+3bct3Du/fP0kJiX10aP8+X3CxSWOvH2fWiXDqTbSEk56u6Gr5RFoXTzQ1BR0jWaWmpet7D/yj+bfxNIdT/hvj32D9pgL0N7169QrYHuZG8afFI7m+CddVaUPM4zHqoPEak8f3itHkmwsU7+oR8HXHYOPS7hSs7nUlTYHqTU5unhY98WTQc4p1/G2Maa2vqHZWOOP7rupq3z13fnGnA2nn2xX2CU7qao+oZNVTKnzwYS1bee4bEsb3IyKxt7pKk2+a0uEDXsYvLSxa/EU0vSYKX/3OHTfB7xF64+vrKSmDNX/hInO69Rc9tvi+BL9sZYnfl9U7q3TTxqD76wprWiXpo9pax7vudbVHtO6VtZp5z30a4vt1ht+v+a3fugrwBXqP7zHCwocWmMvav5AfSiTlW1FephtuzNf8hec+sGr9Zkk0GL9oYaSl5uCBoO+nrn1+jWbNnmOWn/Fle7tgx1i2/T1Nmz5D06bPUFtrmxoa6v3WPR9Wr1jqlz77cRvlfu/9D+je+x+QwviFiWBCtaNg7SKUivIy9fP9aqCRp8avmMkXXMkbP8k81saGer+7IqHqQLC0dbXN2rW1tan59OkObcvYT1fbnlWd793ze+9/QAXTpnfI62DHfaKpSTPvuc/85YbyD7ap1PeLEtY6s2H9uoB36xsb6nXt2DxzGzWW9/rD7csCcapvRp9n7M/6S16hOJ3zrHXA6akuu2D5GY6tW0o18577lJ2Tq5dffK5T27Nv40L1RU75GWwM4bR8zcEDeq12rTZtWK+Z99yn3HETdMbbrA/LyzRydIZ9E1IY/Vpd7RG9/dZmM08aG+q1d3eVEnv3lsfjUWLv3mY/YW+bkbCfR2Q734bTNhXkOELxeE46tlOni4auOLR/n1paW+Rtbu4wxgt2/KWbNiojM9ucX7mzQo1B6mY0xpLGssY+rWWvAHXQfh5w6ncMwepPV8vTzjrGs/d3FeVlHdIZzRsM1n0b4+1gbVuWemK9qDp96pTU3u6XtlD1xklnxpDhpqezQpWPnb28SjdtDFkXrYxtGvuzj5GsvKdPadDg1A7blUP+G2kO1m8qQH/z3rvvaOz14zu0B0mKd/UI+N2eUH2jncfjCfv6JlwNDfUqmDbdr61at2X0R8GCBKHGpd0lWN3rSpoC1Zvrxk/UiePHg45R648e1cJHf6V4V7xfnYqGUOP7UOxjJnv7jHbffTEL+2fugS8jd4Cf08WlxXhFKdCAKZgLVQdSLT8lbD8JAcCl5kL1pdFivIL5ZRzsn0/GqyvWn0EHnOTk5ilvwiT9dvVv7LMuGYzlvvCDuT9S2bb3Lpp8uNTPSRdSWK+YAV9Wt95+pxISEqJylwgXhvHI72HfT+NGijoAAF1nfUX2UpNfMFVXpQ0J+CQDwmO8chHqKQJAvqedLuXgkCTNuOvuoB+njiW/Xf2biyofGN93Hk8QIabMmj3H77XDrryygwsjNS1dhQ8+rF4JX7wrHckjnhdLHeCuE4Avg5zcPM285z61tbVckL60M/ILppqvXCjEKz8IzLhDb/3OR6DX+oAvE6MPCfa6H86vi2V8/2VAgAgAAAAAACDG8YoZAAAAAABAjCNABAAAAAAAEOMIEAEAAAAAAMQ4AkQAAAAAAAAxjgARAAAAAABAjLu834CUX9gnBpKalq6fPPKYvvXt76hg2nQVTJuupAEDVbmrwr6on1mz5+gbNxeobNtW+6xuNWv2HE379p2q3LlDLS0t9tldNnd+sa6+ZmTI40fkcnLz9NBPHtHNt0xTe/tZ3f//+6HiLrtMNQf22xcNC2UVvvyCqZp5z/e6rd10Rk5unn748E908sRxHTtaZ58dUmpaun5U9IhOnfKEtf6F6rMuVaHKJ1j+G+eVrrTvzurOfXfntgEAAIDuEPYTRDm5eXpw/kLtrqpU0bxCFc0r1OJFxRoydJiKf/643G635BsUL3riSeXk5tk3cd6tfX6Nlj7xqDwej30WbHJy87ToiSeVmpZun3Xeud1uFUybrs1vvK5HFjyotzZv1GM/XaDSTRvti+ISMWv2HM2dX2yfHLaK8jI9tvDHqigvs8/CRaAr5VNXe+SCte9o7tveh0Zz2wAAAMD5EFaAyLhg31VRrrXPrzGnezwelTy9XJJ03fiJljWAzkvo3Ucul0snmprsswAAAAAAQDeIGz4qu90+0S4nN0+33fEd/a5ktepqj9hna9bsOUoaMFBbt5Rq5j33Kd4VL0lqbKhXydPLdevtdyppwEBJ0tDhV0uSyj/Y5hdsmjV7jnLHTZAknfE2q2TVU6qrPaL8gqnKGz9Jra1tuiotTRXlf9Gwq68205Kalq7CBx/W229tVummjXK73Sp8aIHKtr8nScobP0klTy+Xx+MJuA9jG70SEh3TFsjc+cVq+rjRcdlA2zTyavWKpZIv+Gak17jTnF8wVdOmz5B8eVh/9KgSe/c217Fuu621TR9s+7OysnPMPDG2OTA5RZJUc/CAuW5Obp5fGW1Yv06SzP3Zl1eANBrTDtcc0trn1/il2b4Np/pjzweDPX2NDfV6Yc1vde+cH5j7N9ZVmPWpsaFera1tOlZX61hW9n1uWL/OPE7rdmTbj1E3648eVda1OWprbdPLLz6nfklJZl7Y89KaT8byxlMX9jpjraNGfu/bU62xuXlm2VvXD1budsGWNY7LaDdz5xeb+Wy0aY/H0yG99jIwWNeXJX/t61uP1y41LV3fL5yr11/7Lx3avy/ivNi3p1opgwbr9df+SxXlZR3arnX7FeVlHepnJG1SIfLC3mcYT1atXrG0w3bs/ZQ1jbKVlSQVPrRA9UePatSYTJ08edwsKwVoh9Zpkvz2bS1rYz+VFR8qv2Bqh/n2tIXKfyt7/xJpu7Lvy16PgvWX3tOn/PYdqm+xl4+RB9eNn9ih//v970o69JvB+kkjDyt3VWjchK8p3hXf4VgAAACA7hTWE0T9kpLU2toq7+lT9lmSpPr6o0pITNSh/fu0asUSeU6e0AvPPuP3etfQ4VerumqXiuYVasP6dcrOyTVfQ5s1e46GDB2mxYuKVTSvULurKjXjrrvN7Q9MTtHOD8tUNK9Qr697Wa2trRqVkSlJGpWRqV4JicrIzJZ8T59I0t7qKnN9+S6ERowcpRVLFqtoXqFeffklyTLgf/utzSqaV6gVSxZrxMhRXX5FbsZdd5uv4y1eVKy/1xy0L+Iov2CqJt80xUznpg3rNWrMuWOVQ3qXPP4zjRydofj4c8ENa+DG2HdCYqLyC6b6vbplHOvxjz9W6aaNeuHZZ+Q5eUIrlizuEFTweDw6XHPIzGNJGjZipFwul7aUvqn8gqmacstteuHZZ/z2OWv2HL/thKOivKxDHWp2qHeR1Key7e/pqrQ0+yYk2+ts1jyRbztjMrPMslixZLHGZGaZF8jy1c2Wlk9VNK9QuyrKde/9DygjM1tF8wr1wrPPKHnQIDNd9rLd/MbrKpg2XW632yxX6yucu6sq9d05P5Db9/qmJI3NzVPJqqdUNK9QH9UeNtcPVu52kSybXzBVCYmJZl6++/afJId6GKzdrF6xVOUfbFPNwQMqmlfoFxwKdbzBhJsXRfMK5XL1UK9e5y7qI2Uvt1BtMlheVFft0pChw8xjTE1LV7/+/bV1S2lU8kSSUgYP1pLHf9bh9dpD+/f59Z2SdMON+dq/b6/qao9oVEam3zEkJCT4PRk6MDlFw0ZcY6bN29ys736/0JxviEb+R9Kuho0YqX17qlXkq8ter1c35n9Tcigbe3/pJFjfEiiPQvWh8tWjUP1kfHy8Ro7O0JLHf2Yei/VcCAAAAHSnsAJE0VBz8IB5F3XH9vd18uRx9UtKktvt1pChw1S2/T3zYmZL6ZtKSExUqu9bDo0N9dqx/X3JEqxISRksSUpJGazKnRWKd/WQ2+3WqIxMeZubHe+4xrtcSk4ZJEnaueMv5kWR1+s1t19Xe0T79+3V6Iws29qRM+5Eezwevf/uO/bZjjIys7W7qtJMf0V5mXZVlJvz7en1eDzatGG92traJFvgxphftv09v+COkXd1tUe0c8dfzOnBbCl9U/369zcvlEZnZOnE8eOqqz2ijMxs7aooN58MMPZpvRCOtkjqU+mmjao5eMC2BX/2PDG28/Zbm82yqKs9ot1VlX552dhQrz/+4VVJ0p7qSp0549XWLaWS74Lc29ysfklJkkPZ7tj+vlpb2zRsxEizXI1tyZfnLpdLw0aMNKdZ07N1S6lcLpcSevcJq9wNkSwrSQkJCWbg9f1335HH4+lQDyNtN+EebzDh5oUkrXvlJZ0502xZO3z2cgvVJoPlhbGMEXgZlZGp1tZWHdq/Lyp5Ismv7lvZA71GcGpPdaXkaydGm6qrPaKG+nqzXchX13//uxLz761bStWvf3+znzZEI/8jaVcV5WV67ZW1kuUYjb7XXjb2/tJJoL5FYeRRMOH0k21tbdq0Yb08Ho85PyExsdv6UQAAAMAqrADRiaYm8+LLSUrKYHmbmx0vSkJJ6N1HCQkJmjZ9hpatLNGylSWav3CR+vbtbwZz7Orrj2rI0GFKTUtX0oCBZvBl2IiRysjMVnXVLvsqqigv07qXX9LMe+7TspUl5tMSKSmDNTA5RYsWLzX3nztugnmB0VnGHeRlK0v8PuIdjNvtVkJiourrj9pnmULldb+kJLn79tP8hYvM45k2fYYSEs/dvS95ermGDB2mZStLIvpocF3tEZ04flyjM7Lkdrs1KDVNW7eUBkxzqDrTXSL9fpHH9x0te54E2o7xtJxTeTbUH5O3uVkN9cfss8x8yh03wSyXRYuX6qq0NPVLSnIsV+/pU2ptbTUvToMJVu72tEaybOmmjXr7rc2av3CRfr18lRkg7Gq76erxBhPqicdIBKrfVpHkhT1Ik5GZbQZ0ujNPDNbg+6iMTJ04ftzvtbzinz9uHoP1tUAnDfXH1Nraap8c1fxXiHZlmDu/2C/vDU552hWR5pEhUD26UP0kAAAA4CSsAJHTqwkG40kLp6BMOLynT+nEiZPmY/fGv0cWPNjhWxWGvdVVcrlcGjfxBrW2tmjf3modq6vVtWOvV7yrR4eLekNFeZkeWfCgVixZrMk3TVF+wVTV1x81X32x/nN6RSBSq1csVdG8Qh2uOaTChxZ0uPgOxH5H2n6hab+Q75eUZL4ycaKpSR/V1pqvBBn/jNdNPB6Plj7xqIrmnXs1JJIgkfF6TEZ2juSrFx6PR97m5g5plqTmZm/ULhLD5XRBbVycBeKUJ07bMXTmgtPIpw3r13Woa6WbNgYMPLW2tgWsz1ahyr2zy8oXJCqaV6iXX3xOM2berZzcvC63m64ebzBOF93JKYMU73L5LRcJe/22tslI82JPdaX69e+va6+7XgmJiebrsN2ZJ4a62iPyNjdrVEamXzDdbfkOkJH+UE/dJacMksshT7sj/4MxvutkpLv8g21+8+15au0vI9GZPDJcbP0kAAAA4CSsAJHH91j+lFtu8/tOiTFg9jY3m4/dR8rj8ehYXa35/ZBwGE+zfDV7rPb/ba/ku+ga8pWhki9wYZdfMNVMu/f0KXm9XskXbEpOSXH8/opxt9hpXjBut1v3PfBD83isd43r648qOSXFfC3j1tvvND+uajxdMCYzy5yfk5unq9KGmOvvqa5U3779zVdU3G638sZPMucf2r9PLle8br39TnOaITUtXd974B/Nv5s+bvSbH4rxStTU276tnR+WmcGE6qpdft/pcPu+63OsrlYej0cN9ccUHx9vBhhzcvOUnZPrt+1oMS7E8sZPMvP/uvETzTy2C5QnRllMvmmKWRapaemafNOUTgdDq6t2+W3Pam91lRISEvzK7dbb75TLFe9Yn+2ClbtdJMvecdcss1wb6o+pzffESLB2E46uHm8wRn0zvkMj37d24q/4IkDR9HGj36s9M+662/EbOeG0yUjzoqK8TCeOH9ftM+7S4ZpD5qtrofLECFoar64Z9TFS1VW7lDd+kl9wyv7EXGpaupJT/NvMwOQUv36nYNp08/tFVuHkf7S4bU/muH03LAyh+stIhJNHwYTqJ0PJL5iqXz653LH/AAAAAKIhrACRLB8PnnzTFL9XZA7XHPK7U258f+Pe+x8I+9Wqtc+vkbe52e8VjVBPtlRX7dIV8VeYFzgN9cd0RfwVAQfbJ5qaNOWW2/zSXbppo+pqj2jdK2vNectWlpiv0gT64HUoHo9Hib17m8cz+aYp+v2a38rj8ah000Y11Nebr/f06NFTjQ315rprn1/jN79g2nTt3f3F/ivKy7T5jdfNV/IWPvor7dtTbX5Tw+Px6PdrfqsxmVnm8SzzvVLnPX1KgwanmtOGDB1mflPEuGidv3BRwLw3gnlqb/fLk9JNG7X5jdd17/0P+OWv8cs/dbVH9PZbm800248p2lavWOpXnzIyswPe6Q+WJ2ufX6PdVZVmWcxfuMj8tbzOKN200W97yyyvH9bVHlHJqqf8ym3I0GF+v0IVTLByt4ts2ZNmuc5fuEhb331bFeVlQduNky2lbyo5JcXcT1ePNxinbX9Ue9jvGzjG922MOmKfbxWqTUaaF/L1X1f2SjC//6MA6bbmiccXqM/OydWylSUqfPBhfRjgKctgjECUNThl7beNbRtBdENjQ72uHZunZb427m1udvylNqfjCJa/XeHxfafH2h+2tn7xfaFQ/WUkQuVRqD40VD8JAAAAXGhh/cx9rMovmKqUlMEXfADv9NPYVvm2nyXvTvaf/wZiUag2Gcr5bLPRcKmlN5gv07EAAAAA0RT2E0SxqHTTxk5fAEZLfsFUXZU2xO9JAyvjNZPDNYe6/WInJzdPY7K+av6aEBCLQrXJUIw2G+jXxtB9zmd/CQAAAFxqeILoIpNfMFXTps8w/25rbdPLLz5nfrA7NS1dhQ8+rF4JX3wvpfyDbd0ayDK+NTUwOUUb1q/r9CtWwKUoVJuMxNz5xRo6/Opub7PRdqk+dXMh+ksAAADgUkWACAAAAAAAIMbxihkAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxDgCRAAAAAAAADGOABEAAAAAAECMI0AEAAAAAAAQ4wgQAQAAAAAAxLi44aOy2+0TA0lNS1fhgw+rV0KiOa38g21a+/wa8+9Zs+cod9wE8+8z3maVrHpKdbVHJEk5uXmaec99infFm8tsWL9OpZs2mn/PnV+socOvNv9ubKhXydPL5fF4pDD2QTojSycAAAAAAIhtEQWILr/8CiWnpCjusi8ePPKePuUXaHC73Uro3cf8u/3sWTXU1+vzzz+TJLl69FDSgIGKi4szlzl5/LjOnPGaf/dPGqCeV15p/v1ZW5saGxrU3n5WCmMfpDOydAIAAAAAgNgWUYAIAAAAAAAAXz58gwgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYlzc8FHZ7faJkfj62Ct0ywSXBidfrubWHnr+tZP6y+7P7IsBAAAAAADgItWlANE/3tlTP/7ulVKPRF0+YKR0RQ+dPX1MP/91pdZuarUvDgAAAAAAgItQpwNEN1wbrzWPJUqSLkserbgr+52b0X5Wnx7cprsWnlb1oc/9V3LgdrtV+NACDUxOsc/ShvXrVLppo33yJWXW7DnKHTdBNQcPaPWKpfbZAAAAAAAAF1ynv0F037d6fPHH55anhdrOKP6KOH13mmV+J02bPkPFP39cbrfbPgsAAAAAAABR0ukA0YB+cef+c/as9Le90ol66WSjPm/cL0lK6R/5pjesX6eieYUqmleomoMHJEkDk1N03fiJ9kUBAAAAAAAQJZ1+xez/LEzQN8e7FOdpUfuVl0uuK6RP2hR3tl3tCS79x6YW/fxfz9hX68D6ipn1lTLrdOvrWfkFUzVt+gxz/fIPtmnt82s6rCNJZ7zNKln1lOpqj/i96tX0caNyx03osL59240N9Sp5erk8Ho+5viFQWhVkv6tXLNXc+cUaOvxqtbW26eUXn5MkzbznPrW1tZjrAAAAAAAAnE+RP+bj8x9v+l4ru0zS2XP/jftcUty5J4teNuZ3ksfj0eGaQ5KkhMREud1uzZo9xy+AI0m54yYov2CqUtPS9eN/ftTvW0a9EhL13Tk/8HtFbejwq/0CPcb6Obl5mnLLbeZ0q7nzi/3WkaQpt9ymnNy8sPcrXwDKGhyqKC/zmw8AAAAAAHAhdDpA9E55m1b+xydq791Dl3lapPZ2xXlb1d4rXr/87Rn9dX90f+o+sXcfDRk6TLK8irZh/TpJUkZmtkZlZKpXQqIaG+q1eFGxFi8qVmNDvfr27a9hI0aa2zHmW19jy8jMNufLsv2lTzyqhN59lJySorbWNr3w7DMqmleo8g+2Kd4Vr9EZWWHvN97VQ3njJ0mSNr/xuhkcqigv0yMLHtRjP13A00MAAAAAAOCC6HSASJJW/sen2vDnVrX3ukJxza1q7+3Sc69/qhf+2GJfNGJut9sMCHmbm9UuKSEhQfJ9vHrZyhK/p4lSUgZLvm8WLVq8VIsWL3X8ZTRvc7M8Ho8kqenjRnN6RXmZPqo9LFm2P3d+sZJTBik+vofiXfG69/4HtGxlid/TROHu96q0NA1MTtEZb7P2VlfZZwMAAAAAAFwwnf4GkeHaay7XK7+4UpedbtXZvj1188PNOnLM985ZGAJ9g8j+rZ6G+mMqfPBh9UpI9FvOYHzrx/rdoGDzJZn7tX6HSLZvEVWU/0WZX71WkhxfC7NvN9B+jaeVhg6/mp+8BwAAAAAAF5UuB4h6JyboN8XDNOTyj/W3Twfp4aXVamkJ//tD9g8821mDN/YPRcvyQWhJZgDJyljfaV1JHT4WHe+K7zDvhhvzNXT41Za1vviAdULvPmHtt+bgAW3dUmruwwhy5eTm8ZFqAAAAAABwQXXpFTNJGjlyhD51f097E+arfcA9GjH83GthXWV888f6ZM/a59eo/INtfssZ6mqPqGTVUzrjbbbP8lNz8ID5NI9s3wOyM+atXrHUbx2rcPcr32tsuyrKJd9rbPkFU+2LAAAAAAAAnHddfoJo1DUj9E+Fc+RyudTe3q5lK1brUM3f7YtdcPafmwcAAAAAAMA5XX6CqO7oMW3+09uqrKzSS//xXxdlcAgAAAAAAACBdTlAdLq5Wa//cbNW/9uzeve97fbZAAAAAAAAuMh1+RUzAAAAAAAAXNq6/AQRAAAAAAAALm0EiAAAAAAAAGIcASIAAAAAAIAYR4AIAAAAAAAgxhEgAgAAAAAAiHGX9xuQ8gv7RLu584s1buINKtu2tcP0q68ZqcpdFX7To23W7Dn6xs0FHfbfHazHml8wVTPv+Z4qd+5Q0sBk/eSRxxR32WWqObDfvlpEcnLz9MOHf6KTJ47r2NE6+2x0UU5unh74p4e0/297dfrUKftsP/Y6nJObp4d+8ohuvmWampoav1Tl093tKDUtXT8qekSnTnkuunybNXuOpn37TlXu3KGWlhb77Kix1r3efdxR6zOi4XzlAfxZzyPB8j01Lf2iqi/wZ23bl8XF6YcPF+vTTz/RsaN1fn3rhegH3W63flT8M7l69jgvdSfaY5hw0h9oHNoV0Wpz4bbxYIyxx+jMrA7H2N3n7mgKlVajrO+4a5aSBgzsluuHC9EGraLdPgDgfAvrCaJ1r7ykfv37Kyc3z5yWk5uneFcP/fEPr/oteylJTUvXoiee9Duu1SuWavWKpX7LSVJd7RE99tMFKt20UfId/6InnlRqWrp90ZAqysv02MIfq6K8zD4LvoHgrNlz7JO7ndvtVsG06dr8xut6ZMGDlM95NGv2HM2dX2yf3Gn2OrT2+TVa+sSj8ng8fst1J3ufcaFdiDy4mDn1/xfSxVZfYoG9n0B47GOYroyHwhVobBYJezovtjb3yRmvkgcNumj6pO5w6+13ytvcrKJ5hVr7/Br77E6J9vghmHD2ZW8fAHCpCStAVFd7RPv37dUNN+ZLlgvpnR+WcbGBL42E3n3kcrl0oqnJPgsAAKDbtKtdtYf/roJp0+V2u+2zvxSSBgxU08eN9skAgItI3PBR2e32iU5S09L1/cK5ev21/1K/pCTljZ+kkqeXy+PxKL9gqqZNn2EuW3PwgHmnJyc3T7fd8R39rmS16mqPSL4IfNKAgVq9Yqm53SN//7uyrs3xW9dgLC9JQ4dfLUkq/2Cb392HWbPnKHfcBPNv6/zUtHQVPviweiUkmvP2VFdq5j33Kd4VL0lqbKhXydPLdevtd5ppyy+Yah6nJBU+tEBl29+TpA7HK0lNHzf6pcm4y2A/HmteVpSX+aX9jLdZJaueMvPKEG4+Vu6q0LgJX1O8K97clvf0KTPtxp0ye9qs27Ln5Yb161S6aaPcbrcKH1qg+qNHNWpMpk6ePK7/98pLuuve+zqUX6BthJPOgckpkqS21ja9/OJzqigv86tj1ulyKN/KnRVK/8pX/PIqkLnzi9X0cWPA+mAEQI1jt+ahMe1wzSGtfX5Nl9qBnf2YrPU5krywrmfdn5F2SX7HaXCqk5L86q18aTHaSELvPvp+4VxtffdtTb5pinolJHZIn9N2Z9x1t9muZakr0axD1j7ru98vdGyrxrRg+Wtnz29r3bO3O/uygcpUvmM90dQUss7MnV9s5p21zjpNv278RL9+26gD9ryyllWofteJsd3Kig+VlTPW3L5RfvJte1BqmlyuePXt218vv/icDu3f55cea/sJlndOdaqu9kjQepKcMihoezfY9xtoOYNTOUoKer40jsXexxhtq/7oUWVdm+NXl431nc6XVvZ9WZc3tt/a2qar0tL8yscQ7PgDrW8tD/u50V5/o3HMgdprsPIP1E8EaucK0IcbnM7F1n7Syl4m9vLft6daY3PzHPvPUOuWbX/Psd/Iyc3TjJl36503N+kbN5/rmw3hnmetrMdoLRs5lI/BXpesbTXcPsNad4Kl1d63NTbU6687d+imKbfKUHPwgH7/u5KQ+WadFqivsZ4HA51f5DAWtDL29fLv/113zJxljivkcOz247Mfv1U49cq+PXsZBjpu2crhjLdZtUcOy+Xq0eFY7fsw0mDv9+1pczpXWI/Teq6Tr87sra4K2PaNdNvrY7DzsTEv2L6sY+B1r7zUoQ+wrmsdUwVKAwBcSGE9QSTLU0STv3nuRFi2/T1zkDjlltv0wrPPqGheoRYvKlZCYqJmRfDYdnx8vBJ791bRvMIOJxXD0OFXq7pql4rmFWrD+nXKzsk1H8OdNXuOxmRmacWSxSqaV6gVSxZrTGaW8gumSpJm3HW3dldVmun7e81BVZSXadWKJfKcPKEXnn0molcvSjdt1AvPPiPPyRNasWSxVq9YquqqXRoydJjcvrs+qWnp6te/v7ZuKbWv7icnN08jRo4y0/7qyy/ZFwlbfHy8Ro7O0JLHf6bFi4rl9Xo146675fF4dLjmkDIysyVf2pJTUpSckqJU36PWSQMGqrpql9xut3r06KnFi4pVNK9Q5R9sU974SeZxSVLK4MFa8vjPtPSJR3W6ublD+YXaRrB0Ln3iUdUcPKDyD7aZr3nlF0zV5JummHm0+Y3XzTtsxkn+7bc2m+WbMniw4uPPXfiFK1R9sOehJA0bMVIul0tbSt+MSjuwcqqz8g1cws2LFUsWa8TIUR0eVzcGat7m5g7HqS7Wyfj4eE2+aYpKVj1lpm/GXbOUmpYecLurVyxV+QfbVHPwgIrmFZrByGjWISt7W83JzVO//v3NcgyUv3b2/A5V98It0xeefUaffvKJffUO8gumKiEx0cyjd9/+U9DpVtY6UDSvsENZGYL1u6F8bfLN2rRhvXlMk28u8Fs3OTlFmzas1yMLHjQvEg7XHDLzJyExMWgfLl/fP2ToMPNYd1dVasZdd5v7CFRPQrV3w6iMTL/2lJCQoOvGT7QvJkVQjulDhprLbFi/TmMys/zy3GpgcopaWj5V0bxC7aoo1733P6CMzGxz+8FeRwmnTxqYnKKdH5apyNfu7EIdv319e3lUV+3yu6AKRyTHbM9ze3sNVP7h9BPdJVT5j83NM/vPj2oP+x1PqHUl6dD+fWptbdWojExz2uiMLDUcO6a3Nm/UYz9doCJfm685eEC7KspDnmeDcRoP2Rl9pdGGi3xt9btzfuC3/VB9hiFYWo2+zehLiuYVat+ear33TmnQdDrl2w035mv/vr1mcChYX2Own1/CHQsamr2ntWnDeo3J+qrjsYfbd9sFqlf2/LL3vcGO214O615Zq2HDr7Ht+RynNmf0+6GOxXqusLdTp/GDgrR9OZy7V9jGSoHaWbB9BbuGMQKExjFu3/pnKch5DQAutLADRJL0xz+8KpfvjuuO7e9LkjIys80BhnwngbLt7/mdIENpa2sLefKsOXjA7Ix3bH9fJ08eV7+kJLndbg0ZOkxvv7XZvDNQV3tEu6sq/S7mjTvhHo9H77/7jjk9Woz8MAbPozIy1draqkP799mW7Cje5VJyyiBJ0s4df+lwdzJcbW1t2rRhvTwej1kOCYmJcrvd2lNdqXhXD7ndbiWnDFJDfb1OnDip5JRBSk1LV0JiovZWV8nj8ei5Z/7FvFjaU10pl8ulhN59zP0YwUHrfq3lF2obwdLpJCMzW7urKs182bH9fbW2tmnYiJEalZEpr9dr5r/H49GmDevV1tZm20rXbSl90+9bXKMzsnTi+HHV1R6JSjuwc6qzkeSFEdQdnZFl2ar03e8Xytvc7DiQMXS2Tra1tWndK2v90uf1es1Bd7jbjXYdstpbXSWXy6VhI0ZKDuUYKH/t7PkdTt2zl6nb7Vbe+El+/VdFeVnYfVRCQoKZJ++/+46ZX4GmG4zg5rpXvgj+2ctKQfrdcGx+43WzPVSUl6nh2DG/uvhR7WFzvjXYKkv7CdaHG32/tT/aUvqmEhITzYuLrtQT+S5+jeOvqz2ihvp6paQMti8WUTn++zP/ai6zt7pKbW1tZpuwa2yoN7/zt6e6UmfOeM2+9tD+ffI2Nwcsj3D6pMaGerP+Ogl1/Nb1ncqjdNNG8ymicEVyzKHaa1fLvzuEKn9rHdq6pdSv3wu1rizlbATUjXKprtrlt5wRSDbyOlRedoXRV1q/Wbml9E2/flhh9BmGYGk1xmDWfb32ytoOfaCdx+FGWr/+/bWnutKxbtv7GkNXxoKGivIy7a78q/lZB6tw+267QPUqWN8b6rjt5VBRXqZdFeWWvQYX7rFYzxXhCtb27efuOttYKZx2ZmUfA1sZN6Csx7hh/Tpz+/bzGgBcDCIKEHk8Hnmbm3W45pA8vlcUEhITVV9/1G+5E01NHYIK3SXQd2Pq64+aJwPjQnjZyhIV//zxbhkc2gcXGZnZHQIpTirKy7Tu5Zc08577tGxliXnXJtoa6o8pMTFBw0aM1OiMLFVX7dKxulqNzsg6d7JsbjZPWPkFU7VsZYmWrSzRvfc/EPCJiGCisQ35LjoSEhOVO26Cub1Fi5fqqrQ09UtKUkrKYHmbm0PmczTU1R7RiePHNTojS263W4NS07R1S2m3tAOnOhtOXgxMTtGixUvN+bnjJpgDEPmeCElITNTvf1di2Zu/aNZJo89QJ7YbrTpkZx0MOpVjoPy1i7TuOZVpoP4rHKWbNurttzZr/sJF+vXyVWbgMtB0q35JSWptbZX39Be/8meUlVMAJBqCfXeiX1KS3H37af7CRWbeT5s+I2gfntC7jxISEjRt+gxznfkLF6lv3/5BB/ORcLvdKv754+b2Az0NE0k55uTm6dfLV5np7dXri9d9gmmoPyZvc7Ma6o/ZZ3UQrT4p3ONXhHkQrmDHHGl7vVh0tvwVwbrWIPiwESPV2trmFwjMyc3TDV+frN+v+a3fWK678tKpr/SePqXW1tag23fqM0Kl1Wlf4bIGP0ZlZOrE8eOqKC+LqK/p7FjQbkvpm0pOSelwnox23x2s7x00ODXgcQ8dNtyxj4lEtI8lXKHGSuG2s3A4HaPB6bwGABeDiAJEdsE68uZmr2OHGG3BBhnWQcLqFUtVNK9Qh2sOqfChBd3SEe+prlS//v117XXXK8H3RE44KsrL9MiCB7ViyWJNvmlKhwFBNBjBjWvHXq+kAQO1t7pKe6orNSg1TUOGDDMHYvm+d+mNx4lfePaZoE9EOInGNgxGHduwfp35eK7xr3TTRr9AoKFfUlLUggl2xuPjGdk5ku+Odne1A3udla9OB8sL49Fn6z/rk0I1Bw/I29wcsg1Eq04ag3lDuNuNZh1yYtR9p3IMlL92nal79jKNkwL2X+Eo3bRRRfMK9fKLz2nGzLv9gkRO0w3BAgVdGfAHYw1U2p1oatJHtbVmeRv/rK9+OeXdiRMnzVeojH9OryF0htvyXRdj24Gehgl2HrLK8X1nZNWKJSryvdpw5sy5AGo0RaNPiuT4DfHx8X55YG//0RRpe70YdKX8I1nXejNjdEaWjtXVmu3I7fuRka3vvm3eFOruvHTqKyWptbUtaEDRqc8IldZA+wpHXe0ReZubNSojUxmZ2eZTV97TpyLqazo7FrSqqz2it9/arMk3TVHvPl/009Huu4P1vceO1gU87vfefcexj3Eqs0CifSzhCjZWiqSdhSPYMcrhvNaZegsA0dalAJF8F8zW71IYgw9jQNJQf0zx8fHm46I5uXnKzsm1baXzjLs1k2+aYj7qm5qWrsk3TTG/qXPfAz80O93uPOlUlJfpxPHjun3GXTpcc8gcfAWTXzDVvEj2nj4lr9drX0Ty3Untaj5WV+3S8GuuUWtri+pqj5iPPA/5ylDtqa6UHO70jc7ICnrB6yQa27CqrtrlV75WJ5qa1Ldvf/NxbrfvVQ9DTm6efrnk/+twgdxZxqPsU2/7tt+v+EWzHQSrs8HyYm91leMdR7vVK5YGDRIFqpPGRbDxGLbRzqzi4+P9Hou/9fY7JV++Bdquk2jXITuj7o+fdKPfxVOw/LULVfesApVp8+lTHfqvnNw8Tfz6N0LWmTvummXWt4b6Y2prbQ063cr43ob1Gxr5BVOVnJIS1sVMThg/a239ZpSxbeM1BrtD+/fJ5Yo364tVsLw7Vlcb1ndSOsP+REyq79ttTpzOQ0Y5WtnvJo/KyOzS3elgQvVJoURy/LK8gmYt9+vGTzQ/Pitf2Vm/fXfr7Xf6zY9UJO01HHPnF4f8Ceuu6Er5R7ru1i2lumbkaI0YOcqv3RmvGNsDP9HOS6u91VVKSEjwa9+33n6nXK54v1evwu0zgqXVaV933DUr7D6iumqX8sZP8gvseDyeiPqaQGPB/IKp+uWTyx3T7aR000Y11Ndr5OgMc1pX+267YH1vqONu+rjR7ztYObl5uiptiH2xgKJ9LOEKNlaKtJ2F4nSM06bP0MjRYxzPazoP/RAAhNLlAFHppo3a/Mbruvf+B8xHfQ9bfn3BuAtiPKJaMG269u6Obse/9vk12l1VaT4iO3/hIr391maVbtooj8ejxN69zUdJJ980xXys2njV5N77H4j48U5jADB/4SK/jry6apeu7JVgBlxCOdHUpCm33OaXd/aBm6KUj3urq6T2du3/217Jd/Jva21RS2uLOUgzHms2HrsdlJoW8ZMbXd3G1i2lys7JNV+PKd200a98rY/jVpSX6eUXnzPzcOGjv9K+PdUR7S8SxoBJ7e1+A5hotoNgdTZYXtTVHtG6V9aaebFsZUnAV4yMV8wWPvqrDvMD1UmP7xs72Tm5WrayRIUPPqwPbXdP29ra1Hz6tLn/MZlZZtoDbVe2OpNfMDXqdcjOKMd+/fr6XYAEy1+7SOpesDK1918z77lPn5w5E7LOeDwnzfo2f+EibX33bVWUlwWcbk9PydPLlZCYaB7nZN/HxcMJbPdLSjK/2xRI/dGjWvjor7RsZYmm3HKb37ep7Dwej36/5rcak5llpseoC6Hyztvc7PeqQLgD61D9v3X+Ml99DxbUDFSOVsZrPkZ688ZP6tLd6WBC9UmhRHr8svQrxvFlZGb7PXVkXPAaedSjR081NtRbthCZSNqrk1D9RLR1pfwjXffQ/n1qaW3xa6f5BVM1dPjVGjr8ajO/lvnaWVfyMtB4yFBXe0Qlq57ya99Dhg7r8IuA4fYZwdLqtK+r0obI4/GETKcsASb7Tb5I+5pIx4KBrHvlJZ3xflHOXe277YL1vQpx3PY+z36OCiVax2IfP4QSbKwUqp1Fui+nY8zKGav6o3UBz2sAcKGF/TP3CE++5edO6ei/nOw/OQvEmh/M/ZHKtr3XIfAk26tJTsFuxA7qwoXzZcv7ufOLO/x8/MWMsSAA4FLV5SeI8AXjlZvOfJAQl4ac3DyNyfpqwF+sAGLBb1f/xjE4BFjdevudSkhI6NbXReDM+nrvpS7V8otilwLGggCASxlPEEXJ3PnFGjr8apV/sO2SucOF8Bl3Ywcmp2jD+nVfijuyQHf4sj25gPDNmj1HueMmmH+f8TZH/LoIuiYnN08z77lPbW0tl3zep6alq/DBh9UrIfGSGVsxFgQAXOoIEAEAAAAAAMQ4XjEDAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYhwBIgAAAAAAgBgXN3xUdrt9YiCpaekqfPBh9UpINKeVf7BNa59fY/49a/Yc5Y6bYP59xtusklVPqa72iCQpJzdPM++5T/GueHOZDevXqXTTRvPvufOLNXT41ebfjQ31Knl6uTwejxTGPkhnZOkEAAAAAACxLaIA0eWXX6HklBTFXfbFg0fe06f8Ag1ut1sJvfuYf7efPauG+np9/vlnkiRXjx5KGjBQcXFx5jInjx/XmTNe8+/+SQPU88orzb8/a2tTY0OD2tvPSmHsg3RGlk4AAAAAABDbIgoQAQAAAAAA4MuHbxABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIwjQAQAAAAAABDjCBABAAAAAADEOAJEAAAAAAAAMY4AEQAAAAAAQIyLGz4qu90+MZCzPRPUcMc/67JPvep1oExX1nyosz0S9Mmw6/TJ8Ot0ufekBvz3Cqk97E0CAAAAAADgAosoQPRpepbqvr/SPtnPsF/dorjPWu2TAQAAAAAAcJGKKEDUHn+lDj2ywT7ZFH+8VukrZ9snh23u/GINHX6137Sagwe0esVSud1uFT60QAOTU/zmNzbUq+Tp5bpu/ERNmz7Db55V+QfbtPb5NfbJAAAAAAAAMS+ibxDFtX2iKzz1ury5Sf1Kf6cedXuU+Ne31HfrS9LZs3LVH7SvEpbUtHT98snlHYJD4RiYnKLChxaoZ8+e9lkAAAAAAAAIQ0RPEEnSsXue1CdDc9Qe30NxbS2SpPbL4xXXflZ9331B/d551r5KUNYng9pa2/Tyi8+porxM8gWOvjn1W/r3Z/7Vb7kN69epdNNG5RdM1bTpMzqsN2v2HOWOm2A+fQQAAAAAAIDAInqCSJJcDQfl3vaK4lo/UXt8D7XH99AVp5vkLv+DvjXggK5Nb1efK6Wbx0iPfVva+GMpd2jgGNR14yc6Bockqa72iP79mX/1W95qb3WVznibFe+KV7+kJPvsDvILpmrZyhIV//xxud1u+2wAAAAAAICYFHGAqMdHe/RZ31R9Zfn/0MDXntSgF/9Z6avvk2vUV/WrvL169cE4ffgL6d/ul743SbomRcpOi7NvxpSSMliSdPLkcR3av8983WzZyhItW1miXy9fpZzcPPtqkqRRGZnqlZCottY2nWhqss8GAAAAAABAGCIOEPX62zZ9OiRTx+5dos/6DtanX8lW3YNrlNOnUUPiGuyLS5L+Vm+f0jXTps/QspUl5kepP6o97PfkUSClmzaqaF6hlj7xqDwej302AAAAAABATIo4QBT3WatS1/xIaj+rE5Pv08mJ/6AxZ/6qV3st1uq32vX4H9r1l0PSkxuk0t3n1tlz1L6VL9TXn5vZt29/DRsxUnW1R/TYTxdoxZLFOuNtti/eQfkH2/jOEAAAAAAAQBdEHCCSpCs89Up99kca/st8DX/iZo19+1dKiPtE3/96nMYNi9P7B6R/nNyuCSOkE17p4yBxHut3hGbcNUupaen2RTrYsH6diuYVqmheIT9dDwAAAAAA0EWdChDZ7fO9Qva/N0oPvdiu1z5s19f/V5z2HpX2HrMv7a+u9ojefmuzJKlXQqLmL1ykZStLNH/hIvVKSLQv3iV8pBoAAAAAAKCjqASIjp6UDjVKe462672fSX8qitO/fK9dz7/Xrp1HAv+CmaF008aAr5RtfuP1sL4vBAAAAAAAgM6JGz4qO3QEJwwFWe168n9I7iu/+MWyv9a26wfPxqnxtN+iAAAAAAAAuIhc3m9Ayi/sEzvjQEOc/lobp8FuqXePdv2hIk4L/1Nqag78E/cAAAAAAAC48KL2BBEAAAAAAAAuTVH5BhEAAAAAAAAuXQSIAAAAAAAAYhwBIgAAAAAAgBhHgAgAAAAAACDGESACAAAAAACIcQSIAAAAAAAAYtzl/Qak/MI+0W7u/GKNm3iDyrZt7TD96mtGqnJXhd/0SLjdbv3w4WJ9+uknOna0zj47atxut35U/DO5evZQzYH99tldMmv2HH3j5oIO+RMNqWnp+lHRIzp1ytPl/Jk1e46mfftOVe7coZaWFvvsDjpzXLNmz1HWtWMd64RRBgXTbtO+Pbt1+tQpc15Obp4e+KeHtP9ve/2mX4zCSevc+cWaee/3NHJ0RkT5F67urM/hSE1L108eeUxxl10Wlf3nF0zVzHu+F3bd7C7B8jUa/V1XBEvbl5E1vyPtu8KRk5unHz78E508cbzLfWtnRbPehzqeaJ5LQunMueNidSm2u/NZ1heT89meLnbGOfpb3/6OCqZNDzoWudDntu4QzbpglZObp1nf+5/aXbkzqtu9ELqSR5d6+/iyCWdM/mU6L+tL2m9dTMJ6gmjdKy+pX//+ysnNM6fl5OYp3tVDf/zDq37LRiqhdx+1tbbo0P599llfWnPnF2vW7Dn2yd1u7fNrtPSJR+XxeOyzOiUnN0+LnnhSqWnp9lkBtba2SXFxujH/m/ZZXxr5BVOVkJioxYuKtXrFUvvsTulMXndWOPuqqz2ix366QKWbNtpn4RKWmpauRU886dfXXwyi3XdJUkV5mR5b+OP/P3v3Hh5Vde+P/401A2UCQ4hkICRcBLkkMQZCuCoSlYhUVOyjh2qp0mN/OT2INV9LOPVQ+z2KPQpfDlrg6UlrRY9SUVrwW/giBkswyDUNBkwCpFwiiQmTGMJAhjYzXn5/MGt1z5p9m8mEi3m/nofnIbNv6/JZa6+9Zu89qCgvUxfpmjN3HuYXFKofR6Qz+/5I8xMrsY4ZO/1PNGKdzkstFvH3TXMltafLXT/q8QcPvR4b16/DwgX5WPHiEiS53cjNmxGyzTdJZ8aCVkJiIk431Mf0XBQLav3riWUZRdo+qHNxTE6xZmuCqKG+DsdqjmLK1Fwg+I1a3sxZOPhxWYc7yZFp6Wj5vLnD+6Grg8MRhxN//StGZ9x41Q7UrbjdA+Bra2NMExER0SW3Z+eH8uK9ob4OTR4P3O4B6moUoeE3jMSR6kr1YyKib5Ru14/M/Fr9UE9ySip+mD8fm9/9IxISE5EzYTKKfrUcXq8XuXkzMHPWbLlu7Ynj8s6JrOwc3H3fd/Fq0Wo01NcBwZnuxOv6hd1dIY5R9+mnyLgpS+4nOSUV+Y8/iZ7OeABA+f69WPfGmpBthTlz5yF7/ET595ZNG1FSvBUulwv5TzyFyoqPkZE1Bv2S3CHLEUzrgw89gjhHXNgydb/aNGjzY5bfN18tQv4TT8ljB/wBvPP711FRXhZShtrPzcodwbtVpt02HUWrXpLHg0FZVlcdCtlelIlIz67SEmRkZmHzu39ERXmZTDcADLl+GKDJt1Gdi28n9OpHHK9s32643QMwaMhQmRajcjMqc5VZWrXriP1d8LWFlJlR+SNYltr4qzxYgdTBg0PSKswvKJTHhyaGzPKSmzcDORMmw+8PYGBKSkjcieVqWYtYqjlSjTHZOejpjLdMt/aY6rLmJg+KfrUcYydMMjyWp7ERI0en4+zZM1i75rd4eN6PULZvt0yrmk6RD22ZiOOok2eiDDyNjci4KQtQ+hGxjl4d6cV6pP2GoI1R9ZuY+QWFaPm82bAMtTGlrjtn7ryQeBf5NSoLbTmKdNvpw2DRbtS+V5vf1paWkP5Pr64iyReAkP5FrU+VNt3NTR74/QGcbqiX/Y3Y7/d/mB+SBijpMooTlbZvtervjNq1WQxoqX2tSJfo06OJe5WaH/WYNUeq4e4/QC6HSZ+obmuUL/WcKWLmrnvuNyxL6LQds/7HKGa0dSLS5zt/TjfdSe7+uukcOnyE4TlfEGVh1tcatVm1vUCJVbUcjPooo/hT27o2LrWxINJnFLdqH2OWV5W6L7N+EDbzL9KhPedo+yI1Pju7PZ08VmNaLkb1E0n+/u/6t/DAw4+g8lAFxk+8GXGOuLB2Z7Q/o+MLuQbjREGvngT13Kwta1GOkZSxUVuJJvbQCbEg6lo7BhDHOFV7UpaR3lhB/Uxv/0Z50dZtwB/A/r0fISMzS44zjfJpFn+C0bZ2ysjoHKHXPozaq6DGr1lfYTS2EGNPu3Ei0qltV+X796K05AOZFrWdqeWlLRN1nCC2nZp7hywntR83K0O99mxE7zpJfPbO2tdx3wPfMxyTX/C1ob7uFByO7jIvZsdXy0AtY/Xcr8acHqO2D51yVZeLdYzGiHr0yj3J3d+wDMVnets11NeF9YXQaS9aavnqjamvZLbeQQQA58+dQ3JKKjJuGotBg4egbN9uHKmuQm7eDEy/826se2MN3vhdEfbv3olJt0xD6uAhqDxUgf7JAzFiVBo+Li+T72vJuGkMevZ0hj0H2au3C+MnTUEgEMDzz/wMZXt3yQLesX0bfrPqJVR9chC333mX7rP1LpcLGZlj8JtV/4VNG/+AxOv6IS0jE5UHDwAAxk2YjBGj0vGnDe/gjd8VobnJg7tm3YezrWfwtws+/NP3H8XOD/8sj3NNt2vgaWzAnLnzMDo9A6tWLMWGt9ei6pODmHH3PfJZT21+zPK7c8d27CotwYhRaTj+1xq8vOyXON3YIE/eYv9f4StMzb0DlQcPwNG9O8aMG4+aI4dxqvYkxk2YhOamJpn379xzP059Wos9H30YUhZ6ZTl02HAMTElF+b496NGjhzzhrXjhOWx7bzNuv3MmEhP7oeqTgzjd2ICMm8YgLSMTuz/6EL9Z9RK++PILTL55GlpamrH3o1I0N3kweMhQ/Prl5dhe/J7MKwDdZ0J79OiBcRMmo+GzOuzcvg3jJkxGknuAbpxYlbnKLK2ng3U4aMhQrPjPZ7Fp4x/Qf8BAjM2ZgLK9u0zLP7FfUkj87d+9E7fekYcePXqgfP/esHcQle3dhcTr+sHf3o7nn/kZao8fs8zL0GHDkTV2HD4Kxp6av9rjx8LKWpTloMFDULTqJWx4ey1GpWdgdPqNuulW2824iZNRe/KEXDbp5qloD/hRUrzV8FjxvXrh5aVLsL14K67p1k3WZe3xY2Fl2NzkQbdu3XDDqNEYcv0wWe5fff0Vmj2nw553F2VQd6oWK154Ds1NHkyaciu+wle6+1fbiBrrkfQbWiKvWWPHIW/mrJB/fRL6ovGzelQeqpD7P1xVKdtP/wEDcUvu7ag8eADXOhyy7+nRowduv3Mm+vTpi4bP6nG6sQETJ98C79mzKN+/V00Cpubegd+//jtseHstvvjyC+RMmISaI4cR8Leb9mEizs1iTe17tW1y70elOFz9CdIybsQf163FH9etDaun+N69bOXr2NHDIf2LOC8YvcdFbZ+OHt2RnTNBlre279KWbXt7O7Kyc3DTmGy8+4e3MW7iZMM4UfPSq7dL9q1W/d2mDevD2rVVDGiP197ertv3dyTuzfLztwu+sP590s23hvTvaplr+8TRGZn48ssvsfL/vID9u3cic+w49EnoG9avn25s0I0Zs7KMtP/RMz/4KMXzz/wM297bjJ7fduL06QakDBqsm+4Ptv6/sHT26NHD8JyvZdXXtre3G7bZr/G1Yaz26u2y3UfpnVfMxjvaccPoGzPlGG3vR6WmfePZMy2WedWyagNmbdUs/yId2nPO5WxPVuWyc8f2sPqxU87a/HXv8W2MnzQFvV198PLSJSjdvg2ZY8dh2A0jLc9pev2TIPJsNDkEAOMnTcHfLlwIa9/QOTf3c7tx53fuQVycA88/8zM0N3kwbsIkeQ6yKmOjtiLOb0ZlrNaPEOtYKN+3B0nuARg85Hp5nhydkYnR6Tfi3T+8Lcd87e3tSB08JGS9ybfmYmBKKjZveAeTb83V3b9eXtS6/cu+XbjzO/fKceY13boh/4mn4Gtrk33eV/gKd919L2qOHMb24vcM6x8dKCOzc4Re+zBqr4iwrzAbW1R/UhFRnIhrIdGu6k59itum34Ux43JQtOol/Hnr5pDzm3YyUG/sop7b0m/Mwl2z7kPjZ/VY8cJzITEtrmWMyvAH//wvOPXpxbLfv3snHN27o/7UpyHp1/rbBR/GjJuAdn+7rOPvzvk+Pqs7hYry/aZj8rOtrbh56m04f85r2Z+I8YNRvF3TrZutc6fKqO2fP3curFzVMYNajuoYUaWuL8q99M/FhmW456MPDbcT19Bm7UVlNM7RW/dKZOsRM+G9P22AIzhbeGDfHgBAWnomDlWUy1lFr9eLsn27MWjIULhcrpDt7QgEAthVWiL/HpmWDp/PJ4/XEHzcbVRahmari7xeL15/5ddydu5IdSUcDgecvXrLdba9v1mmtaK8DE2nT4fsS9yC21Bfh4MH/gKXy4VBQ4Zix/Zt8uTaUF+Hw1WVSEvPlNt1RFp6Jg5XVcr9H9i3B35/AEOHjwhZT+RdPOqXnJKKhL59DW93VctSa+jwEXA4HCgt+UB+tnH9W7hwoS1kvdoTx+Ws8IF9e3D27BkkJCaGrBMNr9eL4i2bMHzESCQr75qItsyN0ir2V7Zvt4yN0pIP4IyPR3JKqmn5q/En0h0IBDRHNmY3L81NHnmMSGj3u6u0RMa7mm613ZQUb5Vl1WDz9nNt+Wm5XC7kTJgckpaK8jLs2Xlx0tLpdMo2uGfnh7r7QLAMxDvNKsrL8Fn9KZkmszqCTqxb5d/Klk0bsXBBfsi/2hPH5XKxf+072EpLPoDD4cDQ4SNwtLoKCL5jzdmrN3xtbWhq8sh47J+cYthu/+eV/5b5PFpdhUAggCR3f7ncqA+zG2sdYTdfav8izgt66dBrnyXFW0PKW+todZUsZwAYlZaB1jNn0FBfZxknVoz6ED1WMWBXR+LeiFr+UPp3vTLX9okV5WV4d/06IFh3p2pPym/37DIry2j6HyErOwcJffti4/q35GdbNm1EQ31dVOlWz/lGjPpamLRZs1jtaB9lZ7wz5PphmHZ7Xsg3v3aOa5ZXLas20NH8G51zzHRGexLslgtslrOav0AggOItm+D1emWf6YyPh8vlsrU/VXJKKqbcMg0b16+T6Y6GtkyPVFfiwgWfPN+ePFYDX1ubbNtWZWzUVoRIythKtLFQWvJByHtXtXGrdaS6Egl9+8oxbFp6Jk7VnoTX6zXdv0qtW3WcKfpzbZ93YN8e+Hw+jExLl59Fw6iMrM4RetR41rLTV8DG2EKIJE607erksRqcPXtG1o16nlDPnaIdascu2nNbddUhXPC1yfW1MW2nDMVxvV6vHDMbEWkVaTG7BlTjr6K8DIcqyuVyNea0/YndeLN77hSs2r7RmEGvHM3GiHrri3J39uptWIZm24n6MmovejoyzrkSRDRB5PV64Wtrkx2gy+WCMz4eHk9jyHqtLS2mjTUSbvcA9EtyY/GSpVi2sgjLVhYhe/xEw0Ffbt4Mud7Djz6GuLiLE1pGWj5vBoJ5K/rVcgwaMhTLVhbJbyidvXrD4XCgtaUlZDuPp1GeuDtClGH2+Iky3YuXLMXAlBTdCxPtCWlkWjpaz5yRA79IJCQmwu/3w3de/1e4LoWK8jK0njmD2Q98L+TzWJe5s1dvOJ1OzJw1W5ZxwaLF6NOnL4YMvd60/Dv6PqFY58Uuq3bjcrlQ+PNn5TLt7cmRMsojgh3kju3bULBoMX65fFVU752KtI3ARv47Si8ufOfPwe/3IyExUbarkWnpGBl8z9rBj8uQlp4pB4hGL+bPys7BL5evknHas+fF21ONiD7MqB5iGWt285WQmAhXnwQULFosy3/mrNm66TBKtxHtQEYMHHeVlkQVJx1hFQMd1ZH8WPXvZn2iGLDNLygMaTux1JH+xypvdtNtdM6PhlGbNYpVxKiPMhvvxMXFYcrUXOz4c3HIGCEWxxWs2kBn5z8SHWlP0Yh1/qLdn9/vR5PntPpx1Jo8py9evOvs004ZG7WVS8kqnQ31dWg9c0Y3brXEGHZkWjqSU1LhjI9HackHlvtX6bUjLb0+T1yPddYFp51zRCT08hirMVMkRLmp161CJGMXBK93W1vP6p6PrMpQPJq0bGURCn/+rO7+VdoJC6NrQBF/RnmERX9iFW/RnjujbfuRjhGtyt2oDK22i1RHxjlXgmvVDyJh1kG1tfkuBlcUharl8TSaPuOnJZ4PXLK4EF7Ne23MJF7XL2SSaOlzzwDBQeb8gkK8+WqR4YBf7eyiIcqwTOedJwjObmqdPFYDv9+PkWnpSEvPRHXVoZDlkRB3dog8JLn7I87hUFeLiNGzoEY2rn8L+Y8/iZvGjJOfmV1kRVPmvvPn0Np6FhvXrwvrSAFgzLgJhuWfmzdD3g0njpuQmGg58SjEOi92mbUbl86z83Y7eD1meYRmFj0rOwezH7w4GahXD0YibSOwyH8seDyNYXGB4K/0tba0wKv5psfvb0fZ3t1o8pxGzoTJGHL9MAT87bp1L/qsVSteRIPmOX8zog8zq4dYxZrdfLW2tOCz+nqs+e9fWR5XL91igCP6ZtWR6kpMu2MG0jIvPgd+8liNZZzEmlUMdFRH8qP9kkavf7fqE8X7IPTeu9BRHe1/9PImRJpur845P9I+w6rN6sUqYtBHWY13xF2V0++8Gwj2w7A4rp0LFC07bSCa/EeaDjs60p6iEev8me3PSEN9nYzvS8GqjK3ayqVilU4E7wzJmTA5LG5V1VWH5N0IvrY2eYeE1f5VYgJCtCPtONOszzObBOgIq3OE3rjLjFVf4bU5toim7UQikrGLFasyBBByrsp/4inLd9Q01NfB19Zmeg0o4lu9NteeC836k6zsHMt4i/Tc2ZG2H+kY0U6565Wh1Xa5EfwKZEfHOVeCiO4g0lNddQiZWdnyzgBX8BfOxM9ANnlOIy4uTt6WlpWdg8ysbGUvxo5WV9n+eU51hnpUWkbYhXzOhMmyg8nNm4EktxulJR8gOSUVP3jsX+R62kmjU7UnMe226bJDTE5JxbTbpus2zGjyW111KGT/ZrzB2x2n3Z6HOEd3eXtgpMTtnFM1Pzc/ZWou4q7t2ASR6Cjsaqivw47t25BxU5asq0jL3IrX68XphnrkzZyle3IxK//Wlhb06dMXYydMAoLxnTNhsrqaoVjnxS6zdqPOxienpCLJffFFdNHQy2NWdg4m3XIr7ntgjuwbmjynEfD7la3tMasjPWb5z8rOwX+8+F8yXdE4Wl0Fp9OJu+65X3521z33w+GIkwPJI9WV6Ns3Ec743jh5rCb4rWQLxowbb1j36jc3I9PSw75lMerD9OpBjTWPpxFJbrdcftc998uXENplJ18nj9XA4YgLKR8jYjCjzdfYCZNM0yXKeMLkqSE/ORxpnHSEnRjoqGjzI85DRv27WZ8oBl5iIOgK3nYdKx3tf8SXJNo7T2fOmo0Ro0ZHlO5kg3N+pKzarFGsmvVRdtgZ79SeOI53fv86pt95tzxOR4+rZacNdFb+oxFte4pGrPMXzf6SU1LxHy8sj2ibjjIrY6u2YiYrOweLn3tBd7/RMEsnNI+Ezbj7XtNfbD5aXQVnfDxyJkwOOa9b7V/rSHWl6ThTr88T534xlo81s3NENOz0FXbGFp0tkrGLFbMydLlceOSxH8vPtRN98wsKMSf4gz96qoOTl874eMP6b/m8GaPTM0LG5ANTBsnlZv2JVbwZnTtdwTtm9PbZkbYf6RjRrNwFvTK0s51dHR3nXAk6dAcRNN9KPfzoY3j40ccA5c3jYgJg5qzZmDlrNpqbPDh6uArxvXqF7MdIQ30dNq5fhwcfesTy1wBKg2+lX7ayCADwWX192LtiPI2NWPTM84hzxMn9NAR/IaD/gGS5bXPwbeMI3hUzZ+48FCxaLPdj9MZ2O/ndVVqCBx96BJlZ2Xjn96+jpHgr3O4BIfvXHl91tLoK026bHjLYipS2XMVt+LtKS5DQt6+6qqGK8jJMmZqLgkWLDWei7Sgp3oq09MyQxhNJmduxLvhLH4uX/CONIs1m5S9iTMSf9tcl7IpFXtSyfvPVi3FqxKrdHKs5KtvsBV8bfD6f3DbSY0Enj+JYXu/ZkL5hy6aNYe3WDrM60mOW/1hoqK9D0aqXQvobkR7RJk8eq0G7vz2knZ4/dw74+mvDk/qBfXuQM2GyjNPmJk/Ye8GM+jDo1AOUWBNtTSyvPFiB5iaPXLch+EjIw48+hryZs3S/zbKTL6/XizfX/DakfGAS96tXLA1pn7Unjhs+Xw7NiXx0egbeWfua/NwsTtR8REp7fhH5sIoBldr3W4k2P3rxqfbvZn1i2b7d8hwW8AfQpIkRlV7MmNGub6f/Uc8r3uDt7flPPBVS7rtKt5umW03n2jW/NTznR8KqzRrFqlkfpddHqvFnZ7yDYHkmBH9xKi09E6tXLDU8bqQTm3pxpsZnNPm3m45L1Z7sUOunpHhrh/OnZVZeFeVluse/HMzK2KqtmElITNR9D5AQ61gQcZuQ0MfwfI1gvfja2uCMjw/5wtZq/1raNqo3ztTr89Rf3rJT/5GWkdk5IlJ2+go7Y4vOFunYxYpZGcb36iU/F/UJAHGO7rrvFRLENaD2HUMqdSzYrFyLWvUnZvFmdL3sDL5WRq+uOtL2EcUY0azcYVKGVtvZZTXOuRrY/pl7unIkKz9hGyviFkC9n28nIiIiIrrUfjT/JyjbuzumY14r4hHVSC8OYyE3+Aip3oQSfXNlZecgZ+Jk/Hb1y+qiK15u3gy43QMiftUIXZk4QXQVEo9wxfKk5Qo+L+lra4vpfomIiIiIrhZZwXcmbnznrUs6KYXgl8D5wZ+E58U2EV0OnCC6iuTmzcDM4GNrHf1WQUwIaZ/hjOY2OiIiIiKiq512bBztY0WREhNCPZ3/eCeL9lUdRESXGieIiIiIiIiIiIi6uA7/ihkREREREREREV3dOEFERERERERERNTFcYKIiIiIiIiIiKiL4wQREREREREREVEXxwkiIiIiIiIiIqIu7lsJ17n/t/qhyuVy4SeF/w5Hj+6oPX5MXWzLnLnzcOvteSjbuwtz5s7DzHvvR+XBA2hvb1dXRXJKKn6y8GmcO+fF6cYGdbFtc+bOQ8ZNY1B5qEJdZCtPuXkz8J177kfZ3l3qIrpEsrJz8OMnf4qzrWc6FAvRys2bgQcf+oFhrJK13LwZmPejH6PmyGGcP3dOfj6/oBDjJ00JaV9Z2Tl47F+fwLG/HsV37v2u7DMuBe2xtem8GljFqVWfq2XW/yanpOKnT/8C37n3u8ibOQsjRqVdsvohIiIiIqLOdVnuIFr3xhosfe4ZeL1eddFlk5ySisXPvYCs7Bz5mds9ANVVh0LWu9LNLyjEnLnz5N96+bqaVJSX4ReL/hcqysvURXSVOFpdhUAggCR3f/lZckoqktxuJLndSE5JlZ8nJCbC7/fDd75jEzRZ2TlY/NwLIfv+JlHbuZVY9bmDh16PjevXYeGCfKx4cQmS3G7k5s1QVyMiIiIioqvQZZkguhq4XC4kXtcPR6ur1EVEFAHf+XPw+/0YlZYhPxuZlg506wZ06xYycZSWnolTtSc7PJFBnWPPzg/lZG1DfR2aPB643QPU1YiIiIiI6CrU7fqRmV+rH6pcLhfyn3gKNUeqMSY7Bz2d8Qj4A3jn96/LiwWxTr8kNwCELZ8zdx4Sr+uH1SuWIjdvBnImTEbRr5bD6/WGbVtzpBru/gOw+d0/oqK8DMkpqch//En0dMYDAJqbPCj61XKMnTApZD8IPmox7bbpKFr1Eqbm3gEEvz1XiWOW7duN1pYWPPjQI4hzxAGa/WsvUtU0lO/fq7tfdT29fQmiTFo+b0b2+IkAgNoTx7F6xVIgmJecCZPh9wcwMCUFWzZtREnxVsyZO0+uf8HXhqJVL8F3/lxY+e/buxMTJt4Skq9PDh7AjTeNNSyzhvq6YOou0h4LgEyDXkzUnjiON18tkunQxkBySip+mD9f1ik0+RNp0csXgJDtRJkBwJDrhwE6daHdT+2J4wCAls+bdevLbh14GhuRcVNW2HKxzsxZswEl7kWeKw9VYPzEmxHniJP5EuWs3Vbdt9725fv3orTkAxlj6v7UtqTdX6Rlpy63s70RbfsXfwOQZb/ujTVhMWJ1PKO2NnbCJMMy1VK3rzxYgdTBg/Fq0Wo01NeFlaXap2nNLygMi7H5BYUAgNUrlpruS803dNqGYLSfhMRE0zjV7g9AWNs1S4+IUdH2BbN+g4iIiIiIrj4R3UE0JjsHRatewsIF+fis/hTyZs6Cy+WSFy2+tjYsXJCPhQvyse39zZj9wBzLRzzEtqdqT8ptHY7u6Nnz4kUbgncb7Ni+TT7W4HQ6MXbCJBytroLD4cDQ4SPkumnpmThcVRnRBUtFeRlWrXgR3rOtWPvaK7qPYsx+4Hs4XFWJhQvysWRxIT6tPRGyXDBKqxFx4Sv264yPD3l0pF+SGwc/LsPCBflycmjQkKFYsrgQCxfk43BVJWY/8D14vV4sfe4Z1J44jvL9e/H0U4/j/65/OyxfBw+U2y4zl8uF7t17yGOV79+LnAmT4XK55DoiJsTjJoueeR7FWzbJGJkyNTdkn0aysnMwfMRIrHhxCRYuyMeGd95SV5GGXD8M1VWHsHBBPrZs2ojMrGz5CJ1aPtVVh2QZG7FTB+3tf8fCBflY+9orGJgySD5WIy6SRbq3vb9ZtgsAiIuLw4hRaXjx2X/HksWF8Pl8mP3A9+S20++8G2tfe8Xw2Nrt1772CjKzspH/+JMoWvWS3J+YCFXbktif9hEgq7IbnZ4h87LixSUYnZ5he3szR6orkdC3L5JTUuFyudA/OQVHqitRXXVITgKJO4maPKfldmbHM2prJcVbsfa1V+A924oVLy4xnRwS2y9ZXAj3gAGIi7s4mRppn1ZddQiDhgyV9Z6ckoqEvn2xq7Qk4n2Z0WvnYlLJLE71GPXnWtoY5eQQEREREdE3W0QTRDu2b5MXA7tKS+BwOODs1RtDh4+Aw+HAxvX/uKg/sG8PfD7fxUdJTIhtS0s+kJ9tXP8WLlxok3+XFG+VFyfaxxoa6utwrOaonIQQF2VHqivltrEkLmS9Xi/27PxQXQyYpNVI7Ynj8q4Dr9eLsn27Qy40m5s8OLBvDxC8aB00ZCjK9u2WE1ilJR/AGR9v+0IzkjLzer14/ZVfy2Mdqa6UdS6ImBB5/az+lLxgra46BGd8fNhFp5E4h0NOEhw88BfDC8/aE8dlGR/Ytwdnz55BQmKibvmUFG+VdxEZsVMH7/1pAxCcTPys/pSsU3Vy7cC+PfD7A3ICLhAIoHjLJni9XrlvUSZp6Zk4VFEuy0vv2NrtTx6rwdmzZ+TxvF4vTtWelHGptiWxv7T0TAhWZadt4w31dThcVWlreysnj9XA7/djZFo6hg4fgfh4J5o8p9Ha0iInjkalZaD1zJmQejc7XqRtTWtkWjp8Pp9sW16vF8VbNiEQCACasrTbp4n9iMngkWnp8Pv9OHmsJuJ9RcssTvUY9efCkOuHYdrteWF3TSWnpGLKLdOwcf06wzZKRERERERXn4gmiIzovVjW6/XC19ZmeoECg21VLpcLhT9/FstWFmHZyqKQO0K0dyaMTEtH65kzuo+AdJS4C2HZyiIU/vxZw0kPs7Ta0drSAr//4kWqytmrN5xOJ2bOmi33X7BoMfr06RvyHhcrkZRZbt4MeayHH31M3mGhp+XzZrR83qx+bEtFeRk2vvMWHnzoESxbWWR654MRZ6/ecDgcaG1pURdFxKwOtFwuF5zx8cgeP1GW0eIlSzEwJcVy0iS+V2844+Ph8TSGfN7a0hJ2oS6INqVuIyQkJsLVJwEFixbL9MycNdvWJJ1R2Xk8jba2t6LtDxISE+VEkHbiKPG6fhG9FL4jbc3tHgBfW5ucSFTp9UtmfZqYrBOTaWnpmXKiMtJ9XQni4uIwZWoudvy5WLdv8Pv9IXd6ERERERHR1S8mE0RmF7VGF7OC3rZJ7v6IczgA5V1B4vEM7R0h2gvMtPTMkAvMdW+ssfV+FLtWr1iKhQvycar2JPKfeCrsotkqrXYkJCbCEXxnkMp3/hxaW8/KR5LEP+1jJnaYlZmWeG+JeFxr7WuvyDssOkNFeRmefupxrHhxCabdNj2qSaK4uLiQyRkxiRMJszrQEhf5WzZtDKmPhcHHAc20nT9nOEHQ1uYznTA10trSgs/q62V9iX96j0yqxIuk9Sa2zCZSIiEew7ppTI6MOVGGw28YiYS+fcMmqIx0tK3pTXwlJCbKCVC9fkkw6tPExOtNY8fBGR8vX3Afzb4ut0AggF2lJZh+591h7bChvg5Ln3uGdw8REREREX3DxGSCSEw4iHerIDi5kOR2W/4KWJPnNOLi4uR7VABgytRcxF17cYJIvbMhOfjz2IJ4jGba7XmIc3SXj3og+JJY8aLYjnC5XHjksR/Li0mjizqrtOoZmDJIvlMlOSUV026bHvKIlJbX68Xphnrdd4VEwqzMtNS7LEalZZjeQWRG/SUrkVchN2+GvBD1nT8Hn88nl9klHjPSvidp7IRJ8oW+RiKpA1V11SFMu2267Uf8tKqrDoW8U8flciFv5iycbqi3dWzVyWM1cDjicNc996uLLIk7YLR5EWVhNIGoJe7m0b4/SXW0ugpOpxNJSe6QiaDqqkNIGTQYbW0+nDxWE7KNkWjamlZrSwv69OkrHwlzuVzImTBZLo+mT6soL0PrmTO4Z/YDOFV7Uk6gWO3Lqm1cLrUnjuOd378eNkmUnJKK/3hhedjEERERERERXd1iMkHk9XpR9KvlcMbHy8c97L7AtKG+DkWrXsLo9Ay57Wf1p+Q7iBqC78x5+NHHsGxlEfIffzJs8uBodRXw9ddRX1hDOY76CJnX60V8r15YvGSpzNuba34bdiw7aVU1NXmQN3MWlgUfFztcVWl698m6N9bA19Ym07JsZVHIJNiu0hJkZmXjl8tXISs7xzBfdsqstOQDJLnd8jj9k1OivoPIG3zHS2ZWtiybjzV3PbW2tGD6nXdjWfAxrVO1J03LwcibrxYBgCyftPRMyztLIq0DrZLirThcVRnyWJcaP0ZKirdi2/ubZbyIfEd715vX68Wba34b0paWRfC43ro31oTkpWDRYuzYvs12WVgRE39nz54JmQiyE4sqq7YmJmsKFi3WnSSuKC+Tkx/LVhZh0TPPo+ZItYzvaPu06qpD+HZPZ8g7vaz2ZdU29KjtvLNUlJdh2/ubMXPWbN1yJCIiIiKibw5bP3N/pUvW+Znoq4H609+X0tVaZpHSPoqkN9FxOeuAvnnEY5nqz9MTERERERFd6WJyB9HlNvuB75m+aJnCdZUyu+ue++F0Og0fCyKKlUgfTyQiIiIiIrqSXNUTROIXtpzx8fLRIjL3TS+zOXPnhTxeNTo9w/KxIKKOml9QGPHjiURERERERFeSb8QjZkREREREREREFL2r+g4iIiIiIiIiIiLqOE4QERERERERERF1cZwgIiIiIiIiIiLq4jhBRERERERERETUxXGCiIiIiIiIiIioi+MEERERERERERFRF8cJIiIiIiIiIiKiLo4TREREREREREREXRwniIiIiIiIiIiIujhOEBERERERERERdXGcICIiIiIiIiIi6uI4QURERERERERE1MVxgoiIiIiIiIiIqIvjBBERERERERERURfHCSIiIiIiIiIioi6OE0RERERERERERF0cJ4iIiIiIiIiIiLo4ThAREREREREREXVxnCAiIiIiIiIiIuriOEFERERERERERNTFcYKIiIiIiIiIiKiL4wQREREREREREVEXxwkiIiIiIiIiIqIujhNERERERERERERdHCeIiIiIiIiIiIi6OE4QERERERERERF1cZwgIiIiIiIiIiLq4jhBRERERERERETUxXGCiIiIiIiIiIioi+MEERERERERERFRF8cJIiIiIiIiIiKiLo4TREREREREREREXRwniIiIiIiIiIiIujhOEBERERERERERdXGcICIiIiIiIiIi6uI4QURERERERERE1MVxgoiIiIiIiIiIqIvjBBERERERERERURfHCSIiIiIiIiIioi6OE0RERERERERERF0cJ4iIiIiIiIiIiLo4ThAREREREREREXVxnCAiIiIiIiIiIuriOEFERERERERERNTFcYKIiIiIiIiIiKiL4wQREREREREREVEXxwkiIiIiIiIiIqIurtv1IzO/Vj80kpySivzHn0RPZ7z8rHz/Xqx7Y438e87cecgeP1H+fcHXhqJVL6Ghvg4AkJWdgwcfegRxjji5zpZNG1FSvFX+Pb+gEEOuHyb/bm7yoOhXy+H1egEbx2A6I0snEREREREREXVtEU0Qfetb1yLJ7Ua3a/5x45Hv/LmQiQaXywVnr97y76+/+gpNHg++/PILAICje3ckXtcP3bp1k+ucPXMGFy745N99E69Dj29/W/79RSCA5qYmfP31V4CNYzCdkaWTiIiIiIiIiLq2iCaIiIiIiIiIiIjom4fvICIiIiIiIiIi6uI4QURERERERERE1MVxgoiIiIiIiIiIqIvjBBERERERERERURfHCSIiIiIiIiIioi6OE0RERERERERERF0cJ4iIiIiIiIiIiLo4ThAREREREREREXVxnCAiIiIiIiIiIuriOEFERERERERERNTFcYKIiIiIiIiIiKiL4wQREREREREREVEXxwkiIiIiIiIiIqIujhNERERERERERERdHCeIiIiIiIiIiIi6OE4QERERERERERF1cZwgIiIiIiIiIiLq4jhBRERERERERETUxXGCiIiIiIiIiIioi+MEERERERERERFRF8cJIiIiIiIiIiKiLo4TREREREREREREXRwniIiIiIiIiIiIujhOEBERERERERERdXGcICIiIiIiIiIi6uI4QURERERERERE1MVxgoiIiIiIiIiIqIvjBBERERERERERURfHCSIiIiIiIiIioi6OE0RERERERERERF0cJ4iIiIiIiIiIiLo4ThAREREREREREXVxnCAiIiIiIiIiIuriOEFERERERERERNTFcYKIiIiIiIiIiKiL4wQREREREREREVEXxwkiIiIiIiIiIqIujhNERERERERERERdHCeIiIiIiIiIiIi6OE4QERERERERERF1cZwgIiIiIiIiIiLq4jhBRERERERERETUxXGCiIiIiIiIiIioi+MEERERERERERFRF8cJIiIiIiIiIiKiLo4TREREREREREREXRwniIiIiIiIiIiIuriYTBAlxH2FV2/8O4Y7v1QXERERERERERHRFS4mE0Q/HvQFbk38EhvG/B1jenOSiIiIiIiIiIjoatLt+pGZX6sfRuKGnl/h/+X8Dd/qdvFv7xfAd8u/jZN/i3zuaX5BIYZcP0z+HfAH8M7vXwcAPPjQIwgE2lG06iU01NdptoqNOXPnIXv8RNSeOI7VK5aqi4mIiIiIiIiIvrEin8UJSnJ8hSeG+PHkUL+cHAIA17XAyvS/41vd7M87ZWXn4JfLV4VMDhERERERERER0aUR9QTRcyP8eGRgADP6fQm4rwEm9gC+fXHZ6Piv8fCAL9RNdLlcLuTNnIU4Rxwu+Nqw4sUlWLggHwsX5GP/3o/U1YmIiIiIiIiIKMaiesTshp5fYev4v138w/0tIKs7cE034MJXwN6/Ae1A49+74ea9PdVNw+TmzcDMWbPl42QV5WXqKsjKzgl5xAwA8h9/Ej2d8XId8WiYy+VC/hNPoV+SG1s2bURJ8daw7Rvq6+RncY44NDd54GlsRMZNWSGPmIm0CeX792LdG2t090dEREREREREdLWK6g6ie/sH7w5y4h+TQwDQ8xpgfA/gW8CAHl8jLd76hdVu9wAAwNmzZ3DyWI26WFeSuz/i4rqHfDbk+mGYM3deyGdGtJNDANAvyY2Mm7JC1pkzd17I5BAAZI+fiNy8GSGfERERERERERFd7aKaIEpzfnXxP6M1k0NC/LeAQdcCALJ6B9eLsYryMjz91OPyUbTy/XsBAInX9VNX1TUqLUPeObRkcSEWLshH7YnjcrnL5cKgIUMBAFs2bcTCBfnYsmkjACAtPVMe/xf/9hTvHiIiIiIiIiKiq15UE0R94r6++L6hfhcngsKkXvw8yWH99JrH0wgA6NOnL4YOH6Eu1iVear1sZRGWrSxC9viJ6iqmxETSqdqT8Hq9AICWz5vlcmev3nA6nQCAmbNmY9nKorC7iYiIiIiIiIiIvimimiA6G+gGxJts6vwW0O1rnPtCubtIx9HqKlzwtSHOEYfZD8xBckqqXHbfA3OQlZ0Tsj4ATJmaCwBY+9orYXf/aInH18QdQ6pBQ4bC5XKF3DGkEncQiX/i/URERERERERERN8UUb2k+gfJAfxifACYZPAS6q+/Bt734a79PVHjM5lIClJfBi2IF1cDCHkp9OwHvoch1w9TV5cvmJ5fUBiy/MIFH+KudcjtR6al6x4Pmn3MmTsv7M6kC742FK16CUnu/nxJNRERERERERF9Y1jP3uj4w+lrcab5a8Bn8BLqT79ASfO1tiaHAKCkeCtWvLgEF3xtIZ8HAu1o8pwO+QwAdpWWIOAPAMEJHfEOImHj+rfkvi742rDjz8Uhy0uKt4Zso7ePdW+sCfuMiIiIiIiIiOibKKo7iABgTO8vsWZ8O3pN7AH00kwEnf8SR7a143vl37b1iBkREREREREREV1eUU8QAUCfa7/Gvw72Y3b61+ibdA0unPsKf6johuUnHGjj5BARERERERER0VWhQxNERERERERERER09bP3kiAiIiIiIiIiIvrG4gQREREREREREVEXxwkiIiIiIiIiIqIujhNERERERERERERdHCeIiIiIiIiIiIi6OE4QERERERERERF1cd9KuM79v9UPYyU5JRU//slPcfLEMZw/dw5z5s5Dxk1jUHmoQl31ipOVnYMnfvo0Zsy6F3kzZyFv5ix88eUXqD1+TK6TmzcDDz70A1QePID29vaQ7TuqM/cdLb0yEf8Sr+uHM2da8JOFT+PcOS9ONzaom0ckKzsHP37ypzjbesZwX/MLCjHshhGdHk9XYl10xJy583Dr7Xko27tLXXTJiL6gX//++M4990eVlm9avdgVy/qbX1CI8ZOmxGRfl0Nu3gx85577EQj4MecH/4zDlQfDYsFOX3IpJKekmvaPV0o6L6dYxvalNmfuPMy89/4u1x9F61KdvztDVnYOHvvXJ3Dsr0dx/tw5dTFcLhd+/GQh/v73v3VKW3a5XPhJ4b/D0aN7yJj0anC5zjnJKan46dO/QLdrrumUMrsc+bI6p1yJLvV5rrPL6FKMQ7V95aU4XqTUMlbbgrh2vP3Omfj666/w6P/3405rhxQ7nXoH0ci0dACA7/w5uFwuDBoyFB5Po7raFSc3bwYefvQxbHt/MxYuyMfCBflY8eISTLttOuYXFKqrdxkV5WV4+qnHsXBBPrZs2ojmJg+WLC7EwgX5WPfGGnX1DqkoL8MvFv0vVJSXqYs63fyCQsyZO0/9OCrJKalY/NwLyMrOURd1WCzTeSXozLK6WnV2maxesRSrVyxVP9bV2WmxI5qYv1x9SVZ2DhY/9wKSU1LVRbouVzovlyshnuyaM3ee5bl/3RtrsPS5Z+D1egGb25C+SNvO5Xa1pfdyiuScE0sN9XX4xb89hZLireqiiOn1XZcrX1c6tR/s7POceryOimbM0dVp24LL5ULezFnY9v5mPP3U49i+bWvM2iF1rk6dIHK7B8DX1gav1wtnr95wOBxobWlRV7uiJKekYtpt07Fl08aQAG6or0PRqpeQ0LfvVTGgJSIiIiIiIrrUrpZrfwrX7fqRmV+rH+rJzZuBnAmT4WlsRMZNWXICZc7cecgePxEAcMHXhqJVL6Ghvg4IzrxWVx1CSfFWZGXnYNodM7Dmv38Fr9eL3LwZmDlrtu52Wnr7B4Dvz/sR3lzzW7lNckoq8h9/Eju2b0NJ8VbMLyjEkOuHAQCamzwo+tVyeL3ekM9rTxwPm/EX+RTrq+YXFKLl82ase2NNWJnAYJ965sydh8Tr+qHl82aZP+22ajpE/no64wEA5fv3yrt21GXa/GZl5+DBhx5BnCMOAEImvvTKVq8O9KjpQzAdP8yfj8pDFRg/8WbEOeLC9qumVZsPLbGvze/+UX7ToE1vc5MHfn8Apxvq5fbamAr4A3jn96/rbgulHASXy4X8J55CvyQ3oNlHQmKiaT2r24k8J7n7h5S9tl5U2rgU2/vOn9Pdr/q5SGeT57StshWxB0Ae0048jZ0wKazOtXEAICRddtsCgt/A6pWVs1dv3bSIPkSbnvkFhUhyu2W8GcWDnThVafcFTd5E3Zft2y3jSf3MKh11n36KjJuywsrLqEzuuud+0/pT41Hdr5aIhdUrlpqWi1ksG/Ujevmrrjpk2pZg0I5PHqvRjXk730KqfYlarmZ9Qc2RaozJzkFPZ3zYMdVy1i7Xi5eN698yLF9teYl0WrVT9fi7SkuQkZkV0mdqqfsTx56ae4esP7XPUPtN7XLR/vz+AAampNgaEwhqHdiNbaO+ScSh2bYqtY6M1tX2zQjGy9HqKsPYLvrVcnz/h/lh25QUbw1Lv9ExVWpda2NNOyYRtJ+ZHXPO3Hnon5wChyMOffr0xQdbN2Py1GkhMaT2s0b1q6ZRbddW529BrZfaE8fx5qtFyH/iKXgaGzFydDrOnj2je87RlotIT2XFx8jIGiPXUdu7UX7MZGXn4O77votXi1ZjZFp6WHrV/laNdzUNgpp3o/hQzzFqHYt2AaV8hD9v24rMm8bonrdO1Z7UPaagPWdAJy1W7dDqnFO+fy9KSz6Q+VHrRO2TtGWpLYeAP4D9ez9CRmYWXi1aLcdN2vOy2blIjWerc6Hou0Ss6pWttozsxpwaE9o0ivLbtXMHpt02Xfc8ZXQsNX/a/ar9wtmzZ8LiQtvHGNWJWd8p+hg1HXrpN4snLbPjGZ13YRBTB/btMU2XYBVHRm1TO4Y1O4eqfaW2P/7+D/N1+34E7+JRaWPpgq8N9XWn4HB0x+oVS0P6NG25aNu6XjmJ/kdv7LKrtCSsraxd81s8PO9HIe1Db8wnytnoWl5LjSF1XGV0/tNS1zM6Vldi+x1EEyffguEjRqHuVC1WvPAcao8fw/yCQvRLcmPFfz6LTRv/gP4DBuKW3NtRefAAEvslYfItt+LQx+U43diAaXfcie7du2Pnju3Iys7BXbPuwztr/wdv/K4IOz4o1n2Oe87ceRg0ZKjcf/qNWUhy90f1JxXIHJOD5qbT8pnSH/zzv+DUp7XYvPEPsoE8/8zPsH/3ToybMBlfB/OgTa/es8ITJ9+Ca6+Nw84d29VFAIBhN4xA4nX9ULZ3F4YOG46sseNkmTQ3eTBpyq34Cl9ZPluZcdMYpGVkovGzeqx44Tns370Tk26ZhtTBQ1B5qAJDhw3HwJRUlO/bg8R+SXLy6zerXkLVJwdx+513yec9x02cjNqTJ+SySTdPRXvAj9bPm/FP338UOz/8s1x2Tbdr4GlsCCvb/gMGYmzOBN0y0aNNn3gOtldvF8ZPmoLerj54eekSlG7fhsyx4zDshpEo27tLNkCjfGj16u3CmHHjUXPkME7rpNfRozuycyag8bN6+VzutNumY9WKpdjw9lp8ha8wNfcOVB48gB49eiAjcwx+s+q/sGnjH5B4XT+kZWSGPcPb3t6OXaUlGDEqDcf/WoOXl/0SpxsbLOt5dEYmvvzyS6z8Py9g/+6dyBw7Dn0S+uKDrf8Ph6s/QVrGjfjjurX447q1us8Ma+N123ub0fPbTpw+3YCUQYN191u+f69uOkUbEPHk6N4d9ac+VQ8nY2/3Rx/iN6tewhdffoHJN09DS0uzaTwdra7CuAmT0NzUhNONDXC5XLjnu/+Esn270fhZvRxcauPZ7jsSTjc26JaVUVpqjx8LicHH/vUncMbHY9XyF/B5c5NpPDi6dzeNU1Vu3gxMv/NurHtjDd74XVFIWy3fvxepg4dg8JDr5baTb83FwJRUbN7wDibfmmuZjkAggOef+VnYsY3KxKz+/nbBF1E9ZNw0Bj17OlG2d5dp+920Yb1uWtR2qe1HxP60+bNqS0b1Vr5vD7YXbw2LeTu0fcnfLvgM+0StHj16YNyEyRg0eAiKVr2EDW+vxaj0DIxOv1H2KflPPAVfW5tst1/hK9x1972oOXIYn3x8AM1NHgweMhS/fnk5the/Z1q+ory0fV4k9bztvc24/c6ZSEzsh6pPDuqWjbq/9BuzcNes++Q56Isvv0DOhEmoOXJYvjNwdHqGrIuqTw5ixt33yHcHiLr8KFiWtcePmcaDVjSxbdY31R4/ZrmtamruHfj967/DhrfXhuVdq2zvLiRe1w/+9nY8/8zPUHv8mGFsi/5o547tYdtEcv7TEoNfo1j7Gl+HnM+ysnNw05hsvPuHt9Grt8v0mBk3jcH1w27Anza8g7WvvYJ2f3tIDEI514/OyMTEKbfg1y8vx4a316L1zBlc8PlwTbdupv2OGhfq+Vur9vixsLYj2mN8r154eekSbC/eatkGA/52jJswGSNGpeNPG97BG78rQnOTB3fNuk++/0RNl1G8qvonD8SIUWn4uLxMt61ruVwuW30OIohJUR4Nn9Wh9vgxw3ZxpLoKu0pLsO29zdj23mYZk2+9/ruw89bojEyMTr8R7/7h7bDjaWnPGdBJi1U7NDvn1J36FLdNvwtjxuWgaNVL+PPWzXLcU3moAi6Xy3Asp46T/7JvF+78zr3o0aMHyvfvlfEg0ml1Lop0XCfytXPH9rCy1Y4JZj/4kO2YMxt7VB6qkOU3ZOgweZ7StoFhN4yMqr2q/YKjR3fDPuaabt0M60SvH1TPx2Zt+Py5c5bxpGXWVxudd41iyu6YwyqOjNqm3XOo2ldq++NrHY6QeklOScXkW27Fn9/fEpZWdWx1trUVN0+9DefPeVG2d1dInybav7atGpWTGMuqY5eePZ2648ZrunULaYdqurRj5Mm35mLI9cNkWXz19Vdo9pwOu44yaquVhypsXxeZ1VNXZfsRs8Tr+qH2xHE585aVnYOk/v1RvGWTnGE7Ul0Jh8MBZ6/eSHL3BwA0eU7L7Vs+b9bs0VxWdg6GjxiJN9f8Vt5Bk+R2w+NphNfrRcDfjoTERLluQt++KC35ALl5M+CMj8ebrxYBAIYOHwFnfHyn3d7W3OTBe3/aAASfrf2s/hTc7gHqarq05en1elG2bzcGDRkKl8sVst7ItHT4fD4c2LcHCD7udqzmKEalZQAASoq3ypnYhvo6NHk8IWkQ/2+or8PBA3+BK/g+qLJ9u2XdlZZ8AGd8PJI7+Ax9IBCQMSHy5IyPh8vlssyHEb30lhRvRe2J43KdtPRMHK6qlDPfB/btgd8fwNDhI+D1evH6K7/WjVO7zOq5orwM765fBwTr8VTtSfmNhxURuxvXvyU/27JpIxrq66Lar1ju9XqxZ+eH6mKp9sRxGTMH9u3B2bNnZHsyiqeG+jq0njkj62vo8BFwOBw4Wl0l/19a8gGgiee09Ex5zGgYpUXrvgcegjM+PmS23yweYBGnqrT0TByqKJffaKht9Uh1JRL69pVtJy09E6dqT8Lr9dpKx67SEs3R7DGqv47WQyTlotcu1X5EL39mbcmqvGJF7RON7Ni+TaZlV2mJ7DdEOWvb7YF9e+Dz+eS79/REUr6IoJ4BYOP6t3DhQptm63Da/VVXHcIFX5vcx9HqKgQCASS5+8u61ea/ob4Oh6sqQ2Kpuckj+3M78WCHUZ5hoz8w21b1P6/8t8ybNu926cW2mWjPf1axdrS6Cg6HQ7aRUWkZaD1zBg31dbaO+Vn9qbBvxc3EORyynA4e+Asa6uvC4lHb7+jFhXr+tku7D6tyEba9v1nmr6K8DE2nT2NUWoZuuqKJV7vs9DnRxqRVu4BmTC3Kq7Tkg5BXJmjjpqMiaYfaPvHksRqcPXtGngO8yrjHazKWU2Pd6/WieMsmBAKBkONpmZ2Lohl/CUZjAgARxZzV2APB8tu4fl3IOVPbBiJtr4K2XzDrY8zqxIrdNhxJPOkxO+92JP2CWRxZtU2rc6hZXym2GzthEhA8x/j9fpw8VqOsGT62qigvw6GKcnU1Q7EoJz1qug4oYz6n0ymPsWfnh/L4WlZt1c51kVU9dUW2JohcLhfiHN1DBkMJiYnwtbWFBGJCYiL8fj98588hITFRdiBi+yPVlUCwMnf8uRgPP/qYvHtClZCYCFefBBQsWoxlK4vweMEibFy/TlZgy+fNcLsHwBV8AdaunTvQUF8Ht3sA+iW5sXjJUixbWYTZD8xB0coVqCgvw7o31sDX1obFS5YiN2+GekgAgMfTaDpgj3SiKxKtLS3w+8NPZmqelq0sQvb4iTLoXS4XCn/+rFwmbsfzer0o+tVyDBoyFMtWFsmydvbqDafTiZmzZsttChYtRp8+fW0NRqJllQ8jVs+wulwuOOPjkT1+otzv4iVLMTAlRZ5EcvNmyGUPP/oY4uIu3vIYK/MLCkPyZJe2zeiJZL/iNtBlK4tQ+PNnDWPYilE8IXiR3D85BS6XC6PSMnCs5iga6uvC2uuylUWYOWu2aVuywywtANAvyY3rhw+XE8liG6t4sEvsS325fmtLizw5VpSXofXMGYxMS0dySiqc8fEoLfkgpumwq7PqQU+s+5FLUV5GfWKk9Nqt1+uFr63tkgwq9I4fqdaWFrS2ntXdh1Gfa3Z+jHU86LHqDyKRlZ2DXy5fJdPZs+fFW8s7S7TnP7261sZag2bSx+VyoX9yihyrRXtMIxXlZdj4zlt48KFHsGxlkRxHmfU7/Qck68ZSR1mVixExfrsU8YoI+5xoY9KqXSSnpOLu+76Lze/+UV6INWi+8FHj5nIR9aeeb7WMxnJuzTtPYyWS8ZeW0ZggkpizM/bQI8oQUbZXvb7drI+BSZ1YibYNx1q06bfDqm1qGZ13jYjJEDGxl5aeGTK5JBjFUqRiXU5WY76S4q3YsX0bChYtxi+Xr5KT2XqM2qrd66JI6qmruFb9QM/Q4SMQH++UdwPBoDNOS8+Un6WlZ8oTsd72JcVbcbS6CvmPP4ncvBly4kdwuweEPMep8ngakZaeKWdOxUxq4nX9DJ8xRDBYxC1tR6urwr4tOVpdhSm3TMPQ4SPCvlVLTklFQt++nXYSTUhMhCP4rKaWx9NoWBYu5dlmaB5ZQrADWfrcM/Lz+QWFePPVIrS2nsXG9evC8tiZzPJhxnf+HPx+f8hFouhYWj5vlicUbRlo5Qaf2V2yuBDe4HuZ7r7vu+pqUZsffBZb+5yu3QG49mSvdurR7Fe7bv4TT0X8DK1VPJ08VoNpd8xAWmYW+ienYMcHF9dpbWnBZ/X18h1jsWCVFgS/ffE0NiL/8SflM8dW8aD3TZ0Rs8FKW5tPDmyqqw7Jk7SvrU32K7FKh12dUQ9GfOfPmfYjkebPqt5iRa9PjLRPMmu3HR2E2SW+WRPHT3L3R5zDoa4WFb0+Vy4zuAizioeOstMf2CXOAatWvIgGzfufOlO05z87sXakulL2ywj202J5NMc0U1FehorgO9TyH38SsOh3XC5XWCxpz9/RslMuesSXfJ0dr1p2+pxoY9JOu5j9wPdwrOZoWD7Fe7PUuLlSWY3lxASHiIeExMSoL2KjGX9p6Y0JXC6X7ZizM/bQmyQSbUuItL0aMepjrOrETLRtOJY6kn4rdtqmlt5516qvPFJdibvv+y5uGjsOzvh4HK2uUlcxjKVI4rkzykmky2zMVxK8sycrOwezH/weEIxpLau2anVdFGk9dRW27iDSm+X1eBqR5HbLi4DcvBkYmDIIu0pLZECLBq63PYKNwefzhXwmHKmuRFL//oYzhq0tLXDGxyNnwuSQx9yqqw5hdHqG6cVJa0uL4W2nDfV12LVzBx586JGQY4vOVe8kq0fMRhrdqQQAA1MGyWMkB389TW/292h1FZLcbt19qTPOycFH8cT/f/DYv8h1RQfj9XpxuqEeeTNnwaUzmyoGMLFmlg8zohPJmTBZpnfshEkhL16srjqEabdN1613dTJzVFpG1IMGlRrrruAtonadPFYDv9+P2Q9c7PgAYOas2RgxanRE+3W5XHjksR/L8on25GoWT9DEzoTJUwHNIOHksRo4HHG465775bodZZUW4d31v8fhqkrkP/6krH+zeIhUddUhZGZly7bqCt61eLqhXsbU0eoq2R9VVx0K2TZW6bCjM+rBiFU/Eo3OLi+jPjFSeu02N28Gktxu3QFarIljTM29Q342ZWou4q6NzQSRN/jNpLYuxDlKG99anREPWnb7AzvUMcnItHTbd2tEy+z8l2XyM+l2Yk30wxMmTw3rl4yOqUdcoIhH0ESdC7l5M+S+tOM3s37Hzvk7GnbKBUDIccXy0pIPLOM1N28G/uOF5bp1Egm7fU60MWnVLubMnQdnfLx8BEZLPM4x4+57cfDjMhk3801+2lsd+991z/0drku7zMZyR6or0adPX/mlscvlQs6EySHb29XRcR0MxgRWMaeyM/aIi4vDlKm5chvRBg/s2xNVezVi1MeY1YkVu224M3Uk/Vas2qYqmr5S3K12z+wHcKr2ZNhND0LL580h18ZZ2TkYmDJILm/ynEZcXJx8tC8rOweZWdlyeWeVk9mY774H5sjYb/KcRsDvV1cxbat2r4siraeuwtYdRG73APlODaGkeCvc7gEoWLQYCL41XHzzgeCMuXgreWtLC/r06Yv8J57C/13/Fh569DH5pnDts6VaFeVlGJWWgYcffQwPP/oYoPxaQZPnNJxOJw5XVYZM2JQUb0VaeqZMl/aXqLS/BCDe86KnpHgrWlta8OBDj8hjw+SXJ/SImX2zTq6pyYO8mbPkMcr379Xdf0N9HTauX4cHH3pE903vx2qOynK64GuTJwHf+XPoPyAZy1ZefB9Ts+aXLda9sQbzCwqxeMk/vsWK9TeNKqt8mFm9YmlIemtPHA95LleNR2jyWxr8RQxRDp/V1xtOECL4GNWDDz2CzKxsvPP719XFIbzB55lnzpqNmbNmI+APoKnJI5eLW3MffvQx5M2cFTZz7Q3efp7/xFMh9bSrdLvpfqGTzvhevWT5iLf4a49lhza9ajwJR6orkZmVjW3vb5b793q9eHPNb0PKGRG2Gb2yskqLIOK5YNFieUyjeIiUSL+2L1LvUmyor4OvrQ3O+Hh5N6PYtiPp0CsTM7GoByN6aYl1P2JWXl6vNyzmrfoNlVmfGAm9dqv+MkpFeRmmTM1FwaLFqA3+ilmsaPtScTv1rtISJPTtq64aNfHrNNq6sIqjSOJBL57M2Omb7Dqwbw9yJkyW6Wxu8pi+v0l7DhG/jGNF3aakeKvh+S9B80i+yk6seYMXnqPTM/DO2tfktpGec73B97aIuLrga8PH5WUYMSoNCI7ltPvSjlnM+h2r87dKbTvinZJaVuUiLwoaG7HomecR54iTeRflFkm8mlHTq93ebp8TaUwKZu1CXOTFOeJC8ijOXyJuEhL6yJh2Ka+EUKlj7MqDFWhWxiadxWwsV1FeJsf5YswkfsUsUtGM61RGY4JIYs7O2CMQCKDt/PmwNuD1eqNur3qM+hizOlGXq32nVRuOhtnx9FilvyNjDrO2aSTSvhLBSZbpd95t2Gahcz5vbvLg6OEqxPfqBQTTumP7Nhnz6nKrcoqW+ZjvbEjsb9m0Maz8zdqq1+u1dV0UTT11BbZ/5v5Kkxt8TKwjHUlnys2bAbd7gOGjbuIWOL2TAhERUSTELd/an6mlq8OP5v8EZXt3hw1+KXounccGKJw6Fs3KzkHOxMn47eqX1VWvOrmanwRXLwqJvimijXO17RNp2XrE7EojbnvW/sLKlaakeKvh5BAREVGsiEcPjO5CoSvbb1e/zMkhuuSysnMwOuPGkPdqVpSXfSMmh8R1gvr0A9E3iYhzvdeTEHXEVXUHUXLwPUBxcd0jvtXvSsOZWyIiioa4O0L7bgKjxxSIuiLeQWRM23+YPVZ0NRHXB+L1FdB5HIvom2R+QSGGXD8s6jjndSiZuaomiIiIiIiIiIiIKPauykfMiIiIiIiIiIgodjhBRERERERERETUxXGCiIiIiIiIiIioi+MEERERERERERFRF8cJIiIiIiIiIiKiLu5bCde5/7f6Yawkp6TiJwufxrlzXpxubFAXX1Zz5s7DzHvvR+XBA2hvb1cXUwzNLyjEgw//AHkzZyFv5iyMGJWGsr271NViZs7cebj19rxOPQZFJis7B4/96xM49tejOH/unLrYkFU7jXa/kcjNm4H5Ty6U8fvFl1+g9vixkHVcLhd+UvjvSB08BJWHKuTnWdk5+PGTP8XZ1jMhfeD8gkIMu2FEyLqdJTdvBh586AeGZdjVaWPomm7d8JPCf4ejR/ewOu4MZnVjFfudITdvBv6/f30SLS3NUZ2z9eJd9P92+n3RjmJV/skpqfjxT36KkyeOdVr/0JVFUrexkpWdgyd++jRuv3Nm1HHaEZfinKPVmf1AZ46VzPq2yyGa6xERa1/hqw73R9EcX4/L5cKPnyzE3//+N3x3zvcR37sXao8f63B5a7dP7JeEnz79C3S75hrDfMdiDJObNwPfued+BAJ+zPnBP+Nw5UHdtIvzwn0PzEHidf06dMxLxc64sTNoy0r0yyItuXfMsOwz1es18e/mW3NRc+QwvnPvd2PWZ2hj2SxNVmLVtijUFXsH0Zy58zC/oFD9OGrzCwoxZ+48+fe6N9Zg6XPPwOv1hqxHxrKyc7D4uReQnJKqLtKVnJKK/3hhOQBg4YJ8+e/8+XO29qHWGXU9V0I7dbn6YMniQixckI8tmzZi2m3Tw+LX6/XiVO1JJF7XL+TzUWkZ6NnTiVFpGfIzl8sFZ3w8PJ7GkHWJtC517LtcLtw0JgdNTZ6QeI1ERXkZfrHof6GivAwIDpKd8fFYsrgQq1csVVeP+JxiJdbjhm+qWJS7Vd12BpfLhbyZs7Dt/c14+qnHZZx9k0XSD8SiXukfpkzNxWf1p5CWnqku+kZrqK/DL/7tKZQUb1UXXRZ33XM/fG1tWLggH+veWKMuvqTsXpfYGTd2Bm1ZrV6xFMkpqZhyyzSsfe0VW33m6hVLsXBBPpYsLkRzkwdbNm3EwgX5+MW/PYWG+jp19YjEqn/6ppznr/R8XLETRHR1c7lc+P68H+FwVWXY4PF/XvnvDnc0RJfKu+vXycH50eoqBAIBJLn7q6vB42lEktstT34ulwuDhgzFubNnQyaOhg4fAYfDgaPVVZqtiS6vocNHAAD27S7F8BEjOzyIAwC3ewB8bW22Lm7p6nI56tbZqzccDgdaW1rURUQxlZySCmd8PD76cDsS+vZFVnaOugpdIonX9UPL583qx1c0u+PGWBJjzuqqQ/KzkWnp8Pv9OHmsJmRdIivdrh+Z+bX6oZ45c+che/xEAMAFXxuKVr0EAPhh/nxsfvePId8Y5kyYjKJfLYezV2/8MH8+du3cgWm3TUdPZzwC/gDe+f3rcn29/c5+4HsYcv0weewtmzaipHhryLraz10uF/KfeAo1R6oxJjsn5Dgnj9Ug/4mn0C/JDQDy84TERJnO7/8wHy2fN4fMTM8vKJSfJaekIv/xJ9HTGQ8AKN+/13AW2yqNnsZGjBydjrNnz8gyEvsO+APYv/cjZGRm4dWi1YBF+QIIy3ftieN489UimWe1vEU6RHnUnjguJ3DmzJ0nL2RF+Yu85ubNwMxZsyFot9OTlZ2Du+/7Ll4tWm04GaSmRdS/7/w53Tpr8py2VQ9m+UDwxK/dT3OTB0W/Wo6xEybJshUdu15565Wdll48WtULDNpCQ32dTIOnsREZN2WFxLCoEzUt2vpSjzW/oFCWi8i73iDfKJaTU1Lxw/z5qDxUgfETb0acIy4kvdAp48qDFUgdPDgsHkRZle3bLb+t0n4GIKROrParxpRZuegttyobMduvV++iXER7TU5Jxffn/Qhl+3Zjyi3TZBpFfGrbnbacRayqbUjkfcf2bSF9irbs9KjxA518d1YatOXd3OSBp7ER8b16yW+2fpg/H3WffoqMm7JkmszqSE0PNO1du89oY1P0PZUVHyMja4yMI6t+HCZ9g3o8bVxp+xev14v5BYVIcrtRtOoljExLDzuX2s2Xei4x6oO1xLei7/1pg26dWrUNKG1gytRc3fO4oFfPoo/UO5drz4Ha7USsatMHnePBpI9VGeVVrUvtPqLp9636Ky2jtKv7UI8xZ+489E9OgcMRhz59+qLqk4qQi12jY5rl1W5Ziws7kW51vKFNl0izWV/04EOPIM4RB2jqRTuGgpJOtU+GwTndLN7UclDPOapo6wlK3Ilttf2A1+sN249Vv6l+rh0LqeciLe0YGDrnN6v6tXPeMRunqHEgYkxNFzRpFX2XOqY0igUzuXkzkJaeidUrloYdU6+tq+lX66nmSDXc/QcYHt+snuywU95qmWqPodcutOcA7bbNTR74/QGcbqjHujfWhOVVHcvrje9O1Z60zJ+6X20Z28mL3x/AwJQU3f4JBu1Nvf4w+jyS6xKzcSOUdGjrzOg8pPf50OEjwvrHv//t70gdPFgeZ8umjUhLz9Q9lspobCfaPaK4vtLrn7TUPl6v3vTOPUerqyIaH8GgnsR66r7K9+9FackHcnt132ZxCoNzgtE8x5XE1juIsrJzMHHKLfj1y8ux4e21aD1zBhd8PgDAmHHjUXPksHzub+iw4RiYkoryfXvg6N4d4ydNwZChw1C06iVseHstvsJXuOvue1Fz5DCG3TBSd7/bi99D4nX94G9vx/PP/Ay1x4/B5XIhI3MMfrPqv7Bp4x+QeF0/pGVkovLgAQDAuAmTMWjwEHmcUekZGJ1+I8r37cH24q0YMSoNx/9ag5eX/RKnGxtC0nmtwyH31d7ejqzsHNw0Jhvv/uFt9OrtkhdEv1n1Eqo+OYjb77xL91lHO2mM79ULLy9dgu3FW5HYLylk33/Ztwt3fude9OjRA+X79wIW5Sv2KfK9d1cpbr3tDky9LQ9/2vAO3vhdEUalZ2DQ4KEo27srpGNe8cJz2L97JybdMk2+7yHjpjFIy8jE7o8+xG9WvYQvvvwCk2+ehpaWZuz9qBTNTR4MHjIUv355ObYXv6fJebjRN2ai/4Bk7Nm5Q/eZYgAYnZGJL7/8Eiv/zwvYv3snMseOQ5+Evijfvxe7SkvC6uwH//wvOPVprUy7o3t31J/6VN2taT5ONzZg3MTJqD15QtbnpJunoj3gx9HqKoybMAnNTU043dgAl8uFe777TyjbtxuNn9Wblp1Wjx49IqoXBDuQQUOGYsV/PotNG/+A/gMGYmzOBJTt3YWhw4Yja+w41J26mPd+bjfu/M49iItz4PlnfobmJg/GTZgk3/uRmzcD026bjlUrlso2NzX3DlQePIDJt168aBPH+errr9DsOR1WR2axLNp1b1cfvLx0CUq3b0Pm2HEYdsNIlO3dFTKJ8JtVL2H/7p249Y48Gdfa9za0t7cjdfAQDB5yvSyLybfmYmBKKjZveAcDUlJlvKvtRd3vNd26mdZRbt4MTL/zbqx7Yw3e+F2RXC7eG5SbN8O0bKxO8ufPncONWWPhdMaj8lAFxk2cjD59EvD+5neRnjkWzU2ncbqxAbfenoeWz5tReagCc+bOw+j0DFlXVZ8cxIy770G3a67B4U8OYsy4CWj3t6P2+DGMmzgZaRk3IS7OgbK9u5DYLwmZY7JRsm2r6bsw1PhpbvJg0pRb5XsVOisNahz6fG249bbpOH/Oi7K9u9CrtwvjJ01BIBDA88/8TD4rb1ZH/ZMHYsSoNHxcXiaPl3HTGPTs6QzZZ7SxGfC3Y9yEyRgxKl221eYmD+6adR/Otp7B2TMtYf14jx49TOPOqL+pPX4spD9/7F9/Amd8PFYtfwGfNzfpnkvt5ks9l6h1o3K5XLgldzr+sm83Pj15IqxNWrUNoVdvlzxnbdqwPuw8rlV7/FjYOUXtO7XncnF+npp7B37/+u+w4e21+OLLL5AzYRJqjhzWHTdoGY1j1LIxyqvofw5XVWLFC89h23ub0X/AQNySe7vuOMSq37c6H2sZpV30eb62Njz/zM+w7b3NIWOs8+fOIeOmMbh+2A3404Z3sPa1V/BJxYGwcleJeDLK684d203LGprzcONn9SH5E21ZTdfpxgbTvmjvR6U4XP0J0jJuxB/XrcUf1621rBNH9+4RjaHUeLM656ix05F6EucXsbznt504fboB1/VLkuk162tKirfq1qtRexGxIfpO1fhJU/C3Cxfk+160bft0Y4Nl/Vqdd9Tzg3ac0qNHD8Pxh9l4PWXQYN0xZeWhirD0W/nOPfejuuoQao8fQ3zvXiHHtOqn1Hra9t5mTLr5ViQm9kPVJwd1j29WT3ZYlbdZ21LPRQi2i4bP6uS22vGpo0d3ZOdMQONn9ag8VGE6llfPJaMzMjE6/Ua8+4e3LfPW3t6uey1gJy9ZY8fhow//jN+sekm3fzJqb0YxFO11idW4cc7ceeiX5JZlK84NPyn8dwT8fjz/zM+wf/dOpA4aivq6T/Hjn/xUfr7tvc3Iys5B5phx2LRhPVpampF4XRJ+FRyb7Nu9EyNGpclroYmTbwk7lhER4yIGhGivr4z6J8HlcuGfvv8odgbrrOqTg7im2zXwKG2lbO+usHNPpOM+s+t4dV91pz7FbdPvwphxOSha9RL+vHVzSL8izuNGfXsk8xxXGtuPmMU5HPL2uIMH/qL7zYmeQCCAjevXyfUP7NsDn8+HkWnpQAT79Xq9eP2VX8tvLI9UV8LhcMDZq7dcZ8f2bXL7XaUlYcuNHK2ugsPhkLfYj0rLQOuZM2ior8PItHT4fD4cCHaaDfV1OFZzVPcdDXbSWLZvt1yu7tvr9aJ4yyYEAgG5vh0i3w31dWjyePBZ/Sk5c1lddQjO+Hi4XC75aEtpyQdA8Hhl+3aHPF9de+K4nMU8sG8Pzp49g4TERLk8lirKy/Du+nWAyTtcVGK51+vFnp0fqosls3yUFG+Vy0SZud0D0FBfh9YzZ2Tdah8FslN2Krv14greFqqNjdKSD+CMj0dy8DGP5iYP3vvTBiAYVxcu+LCrtAQAcPJYDXxtbTJ/aemZOFxVGdLm/P6AjG+n0yljcs/OD8PuAoCNWA4EAijesgler1eWhchPpHF9pLoSCX37yrympWfiVO3JsHRZ7deqjtLSM3GoolzWgVg+aMhQuFwuwKRscoPv23jz1SKYqa46JPeXlp6Jls+b4fV6EfC3Y1RaBpJTUpHQty+OVFfKetf2Ww31dThcVYm09EzZJtzuAUDwkY7KgxWIc3T/Rzm3tRn2mVra+KkoL8Nn9afgdg/o1DSocVhRXoZDFeUh6wQCARnHsFlHVmIRm9ve3yzTUFFehqbTp0P6fG1btYo7o/5G674HHoIzPl73rhwhFvkyMnbCJDgccfI2dLVNwqRtdAazc7n2EeVIb923O97Qy6soY9GOEOyntWMHRNDvW8WNSi/tYh8b178l11PHWABC0mCH3bxaqT1xXH5Lq9eWtemy6ov0xCqdRvEWTbuKpp6ysnOQ0LdvyPItmzaGxWekMYMOthcrVvVrdN6BzvlBO07xmow/zMbr0Ywp9Yj6EI+Bq8cUjOJGrScA2Lj+LVy40KbZOlQs6smovKNpW4Le+LSkeCtqTxyX65iVe2nJByGP6GnrKxp289Lc5JHtVmXW3szyYsTousTuuFE1dsIkAJDbifYwIi0dTqczJN3ac8qotAwE/O2ynsR4M9aP5EZzfWWXWLehvg4HD/xFXWwokvFRg8l1PJR9nTxWg7Nnz8i+So0Jq74dBueEq4GtCaKK8jJsfOctPPjQI1i2sgi5eTPUVWzzer3wtV3sJCPdb27eDCxbWYRlK4vw8KOPIS7u4m1oHaUNFpfLhf7JKfKixe0egH5JbixeslQeO3v8RMMOI5I0dsYz/C2fNxs+q5uQmAhXnwQULFos0zhz1mzZiGKptaUlZFBvZH5BYUi5mhEz8MtWFqHw589GnWaXy4XCnz8rj6u9zW9XaQn6J6fIzvZYzVE01Nd1uOzM6sXZqzecTidmzpot912waDH69OmrO0Bo8pyGr60NTZ7T6iK4gi9Azh4/Ue5r8ZKlGJiSgoTERJQUb8WO7dtQsGgxfrl8lTxp64kklrUijeuK8jK0njmDkWnpSA4+968dWAlW+zWro4HB/aovhtbGqVXZmB1bEPtz9x+IOEd3HKmuBDQTR4OHXo+2Nh9OHquB0+B9Gh5Po4wrj6cRg4YMRXJKKhKv6ycHH0OHj0BaembIs+bR6Kw0iDhUy9uM0TZ2+xI7rGLIiFHbhUXcuYITwEb9DQD0S3Lj+uHD8eaa30acLiHafAlp6Zkh57mHH30Mrj4JmJp7BxAc9Jm1jUspKzsHv1y+SvaTPXtevGXcit3xhlFe9crYd/4c/H6/4ZcoZv2+VdxoGaU9ITERfr8fvvP/+CZejLEiGZSrosmrHa0tLfD79SdX7PRFqs5Kp6C3fzPR1pPecj2RxIwQbXuJhln9almNU2Ay/jAbryPCMaWRUWkZIeVcsGgxXH0SMGVqrrqqLrv1qdWZ9RRN2xKMtlUZlXuD5otXvfqKlFF67ORFsKofo7zosbouseo/1r2xBr62NixecvFxUAT7Hb0vSd3uAWjyeEImGLR9VKLyriZx/SCuFfSOFWtW4x0jXq8XRb9ajkFDhmLZyiJ551UsRHodryX6aXVcKujFkrZvNzonXA1sTRAheOJ7+qnHseLFJZh22/SoMylODILd/eYGnykVb4Vf+9orpt/iROpIdSX6J6cgLfPis7vim1SPpxG1J46H/AqXeDu8Kpo0qh1aQmKi7QvxSLW2tOCz+nqZPvHP7i9kROLksRr4/f6QbzFV4rlukQ7xWJ2Z1cE37J+qPYn8J54K64ytuDTP1Yrjar8JEfWelpmF/skp8gK/M8vOd/4cWlvPYu1rr4Ts284vDqhEx7Ql+MsD2n9iVr+keCsWLsjHO79/HbMf/J7uxV40sSzonait4rq66hDS0jNN70ix2q9ZHX1WX2d40dTW5pOdu1HZlBRv1W3zKhH3k265Vf4NzSRHRmaW/IbH7EJGnPDFN5fjJ02B39+OmqPVON1Qj5vGjEOco3vYIClSnZ0GtbzNTsjak6pKW0cdYRVDRszSbRZ30LzLQXyu7W8Q/KbzxLFjyH/8yZA7diIVTb6g+ZZR7X+2bNoYcjeAUdu4lLKC76BateJFLFyQjxUvLjH9Vl5ld7yhl1e92AEAvz9g2gaMmMWN3jlFL+1mk6dGA1o7Yp1XISExEY7g+yVUdvoiVWelU9Dbv1W7iqaezJZrRRozHW0vkTKrXy2rcYrV+MNovB7NmFLlCt6hoqZt7WuvhN1VaUSvPpPc/RHncISsJ3R2PUXTtgS9bdXrOKtyF1+QqfUVDb30yGUWeRH06kewyoseo+sSu+PG1SuWYovml86MxhvqBJCI1ZbPm0PuThf07tZSjxVLVtdXVrxeL5Y+9wwWLsgHNI/ndVQk1/GRMoslcQ7WOydcDWxNEOXmzZAZ8p0/B1/w/UOioYrbtJJTUjHttukh28bFxYXMut91z/1A8BYso/3qUb/JGZWWYXqSjpTosCZMnorTDfXyOEerq5Dkdtuq0EjTeKS6En369JW3E7pcLuRMmCyX2ynfSJw8VgOHI07WQUdlZefgP178L90LBW/wFr+Zs2ZjjvKTkD947F8wYtRoODV3C4iOzojL5cIjj/1YdrzRDn7Vbx+SU1KR5L74YjEE0326oR4TJk8FNHER67LTEsfMmzkrbKAbjeqqQ4ad/30PzJH11eQ5jYDfr64CRBHLWq0tLaZxredodRWc8fHImTBZ944U2NivVR1VVx1CZla2zL8r+JPJor2blU1u3gz8xwvLdctUyxu8/fTiy8T/cavvyWM1aGvzIWXQYJk/sa62rkQbF+uIb99uzByDY389CgT7jUGDh8j9IngiVduZHbFMg5bY7+j0i4/VIdhfDEwZpK4awqqOmjynERcXJyees7JzkJmVrezFmFUMCTkTJsu2mJs3A0lut+5dbbCIO6v+Rnh3/e9xuKoy6kkiq3OJWV890uBXTrSPVZi1jUtJ/bZuZFq67W/a7Y43jPJ6tLoKTqczpJ7vuuf+kEfzImEWNyqjtIsJ6dkPfC9k3SS3Wz4eE41Y5XVgyiBZlqJf0T6qomWnL1JZpbOjYyi7/YUQbT3pLZ85a3ZYXxBJzKCD7aXl8+aQCeLZD3wvbNtI6ldlNk6xGn+IGNSO113KHahmY0qz/lA8MqK2H1FHZl96CuI8Je7ABIApU3MRd63+BFFH6smOaNqW4A1O5mnPiWMnTJIv5LVT7uLxwRl334uDH5fZig8jHcmLYNTeYn1dYnfciGBfIyZBtW1Pewy1Td51z/1wOp0oLfkg7G4h6EwoCdpjxZLd8Y6e5JRU/OCxf5F/66U7WpFcx0dKL5a0fbvROeFqcK36gZ7WlhY8+NAj8g3k5fv3yrsRirdswoMPPYLs8RNxwdeGj8vLMGJUmtw2EAig7fx5LFt58VlK8QZvr9drul/xxvBlK4uwZdPGkL8B4LP6+ogCfFdpCR586BFkZmXjnd+/ri6GN3iRPjo9A++sfU1+3lBfh43r14WkU31DuRBpGivKy5AQ/CWqmbNmh/zyDIJpsirfSHi9Xry55rchaUQEb0+vKC/DlKm5KFi0GLUnjlveJlpSvBVHq6vCjle+fy9qjhyWE0gi701NnpDt1TqL79ULi5dcnPHVxlEkxO3JDz/6GB5+9DFc8LWFNdgj1ZXIzMrGtvc3y/13tOysrAv+Co/IHwze8m9HSfFWuN0DULBosfysWf4CwlmZdwTTr8YxoohlLbE/0WbUuNbTELzDxxkfb/jcuNV+repI1JM2/+WaXzKwWzZWxCBBO1gR/UtCQp+Qgee6N9Zgztx5IXWlxpQYSIvtmjyncW3ctSEDY+3jbJGKRRr0iJgW+21u8uDo4SrE9+qlripZ1VFDfR12bN8m+w07+9SyiiHB09iIRc88jzhHnOzvG4K/RKSyijur/kbQlteWTRvVxaasziVGxAWv3u3sYmJwytRcVFcdiknbUKnnFKt3NRzYtwc5EybLfrK5yRPyTbs6btDGsNl4Q8usHyha9VJIPf+jX73YDiNhFTdaZmkv+tVy5D/xVNgYS+8uTEEtd/U801BfZ5pXu5qaPMibOSukLat507LTF2nZSWdHxlB2+wuhI/WkLm9u8mBX6Xa5b9iIGbVe33y1yLS9mBG/CCa23VVagoS+fUPWibR+tczGKVbjD3E+1Y7XvZovJY3GlHZMmZobdtcFNBMTORMmo8Zi8lUvLvXKT7Dq12Ih0raltTr4S24ifbUnjss7Q+yUu9H4J1odyQuC6TFqb1Z5ieV1Sa7yy3Vbgu9BUq8HyvfvhdfrDfu8ucmD//rPZ+ENviNPO8koJu7EONToWLFkdX2l9k/a847v/Dn0H5Ac1o/rUc/zVjEVyXV8pPRiSdu3J7n7G54T1HzYjd9LxfbP3NOlkav87DERkZms7BzkTJyM365+WV10xRG3b4sJH+o8PJfQ5TTH5CfU6erH+qVIMF46lzq2ygo+tvhq0eqYTwRR12DrETO6NMRtknrf5hIR6akoL7sqJody82ZgYMqgqO90Ivt4LiEioitBVnYORmfcaPnUAUVHfcwPOo8tEkWKdxBdRskpqch//En0dP7jWWPt4xRERFcr9ZbmWN3SS+F4LqErDe8Y+GZj/ZIVV/Clxf2S3FfkIzREZIwTREREREREREREXRwfMSMiIiIiIiIi6uI4QURERERERERE1MVxgoiIiIiIiIiIqIvjBBERERERERERURfHCSIiIiIiIiIioi7uWwnXuf+3+mFnc7lc+Enhv8PRoztqjx9TF0csOSUVP336F+h2zTUx2R9FLys7Bz9+8qc423oGpxsb1MXfKLGO4ytZV6rXSM2ZOw8z770flQcPoL29XV1MRERERER0VbgkE0RZ2Tl47F+fwLG/HsX5c+fQo0cPjJswGQ2f1UV1Ya3u7/y5c9jxQXFU+zIzZ+483Hp7Hsr27pKfzS8oxLAbRqDyUEXIupeKXpoiYZV+tWwjdbqxATs+eL/TJhE6mv9Y6mgcX006u16vZpWHKrCrtOSKmhzqaDtWXUntjoiIiIiIOgcfMSMiIiIiIiIi6uK6XT8y82v1Q5XL5UL+E0+hsuJjZGSNQb8kNwBgy6aNKCneCgQf88p//En0dMYDAJqbPCj61XKMnTAJM2fNlvuqPXEcb75ahPwnnkLNkWqMyc5BT2c8Av4A3vn966goLws5pjhW7YnjWL1iKXLzZhjur2zfbpmerOwcPPjQI4hzxAEAyvfvxbo31sjthDlz5yF7/ET5t8jT/IJCDLl+mPz8z9u2IvOmf+Rdm15tmrSfJ6ek4of581F5qALjJ96MOEccyvfvRWnJB7KsLvjaULTqJTTU18n1d+3cgWm3TQ8rFzVN2vIX1HIT+/edPxfyuVreAEzL1tPYiJGj03H27Bn8/W9/R5OnMaQ85xcUAgA2rn8LP8yfj83v/hEnj9VEXM+7SkuQkZmFze/+MSRt4hh6+VdjT1umqkjrBDrlImJJpF3EnYi5QxXlIcvVGNaj5kG0H6/XG7Ke2GckZaounzN3HhKv6wcAsjyN2ocgyk3Ui9q+RF1o25MoRwAh2yJYpjkTJss8qmk2Kyu9YzTU11nuw2i7aGJCS5sXAGH1o21H/ZLcIfVhp2+FTj+lrS9xfL8/gIEpKThyuAqjRqfLdY36Tu0+zGLCqN0REREREdE3i61HzMSjNCNGpeNPG97BG78rQnOTB3fNuk++k2TcxMmoPXkCv1n1Eqo+OYhJN09Fe8CPkuKtaG7yYPCQofj1y8uxvfg9ub9Bg4egaNVL2PD2WoxKz8Do9BtRefAAevTogfwnnsKp2pNY8cJz2L97JybdMg2OHt1N9yce9cnKzsHsB7+HP657E2/8rghVnxxEQkJf/LXmSEi+XC4XMjLH4Der/gubNv4Bidf1Q1pGJioPHsDOHduReF0/+Nvb8fwzP8Oxo0ewq7QEI0al4fhfa/Dysl/idGMDcvNmYNpt07FqxVJseHstvsJXmJp7ByoPHoCje3eMnzQFvV198PLSJag79Slum34XxozLQdGql/DnrZuROXYc+iT0ReWhCvTq7cL4SVMwZOgwWS5f4Svcdfe9qDlyGNuL3wtJk95jTaMzMvHll19i5f95Aft375T7L9+/Vzf9WrXHjxmWbXyvXnh56RJsL96KnvFOWU7t7e1ITknF5FtuxZ/f3wKfrw1jxo1HzZHDOHumJaJ63vbeZtx+50wkJvZD1ScHw9JXtndXWP7FxMrhqkq5j/4DBuKW3Nt13wkjythunQDA1Nw78PvXf4cNb6/FF19+gZwJk1Bz5DAC/nYZd336JODBhx7Btvc3Y/PGP8gLf70Y1qs3o/ajrmu37fja2vD8Mz/Dtvc2h8TQ+XPnkHHTGKRlZGL3Rx/iN6tewhdffoHJN09DS0tzWJkLvXq7ZL3+7YIP//T9R7Hzwz/L9F7T7RoMSB6IiVNuwa9fXo4Nb69F65kzuODzAYDcVux/6LDhGJiSivJ9e8LiwKyssrJzdI9xTbdupvuYM3ceBg0ZihX/+Sw2bfwD+g8YiLE5E1C2d1dUMaGlzQuAkPrZu6sUt952B6belif7zlHpGRg0eCjK9u6y1bfOmTsPo9MzZB9T9clBzLj7HvnOtaHDhiNr7Dh8FKyPj8v2hbVjmMSxVUxs2rA+rN0REREREdE3T0SPmG17f7O8A6CivAxNp09jVFoGAKCkeKv8Vrmhvg5NHg/c7gEh26t2bN8mv5HfVVoCh8MBZ6/eGDp8BBwOB0pLPgAAeL1elO3bjbT0TGUP+qZMzcXhyk9kWhvq67Bl00Z1NXi9Xrz+yq/lXRpHqitlGuxKS8/E4apKmY8D+/bA7w9g6PARAIBAIIDiLZvg9Xpx8lgNzp49I9f3er04VXtSfnMv1t+4fl3I/nw+H0am/eOOADMV5WV4d/06IJg/df/RKtu3W5bTgeCF8NgJkwAAI9PS4ff7cfJYTcg2gt16RvAOpAsX2jRbmxuZlg6fz4f3/rRBflZa8gEcDoesA1WkdfI/r/y3TP/R6ioEAgEkufvL5S5XH8x+YA62vb9ZtgE1b1YxHGn7sSrTjevfkuvqxVDtiePyeAf27cHZs2eQkJgol9sh0tdQX4eDB/4CAIhzOGTZHDzwF907blSRlpXeMcz24XK5MGjI0JAYLi35AM74eCSnpAJRxIQVUT+iLj+rPyX7o+qqQ3DGx8Plcsn1jfpWkXZtfTfU1+FwVWVI+TQ3eWS7NGIVx7GICSIiIiIiunpFNEGkavm8Wf7f5XKh8OfPYtnKIixbWRTySEKkEhIT4eqTgIJFi+X+Zs6aHXZRpcflcsEZHw+Pp1FdpCs3b4Y8xsOPPoa4uIuPzNghjpU9fqLcx+IlSzEwJUX3wsrr9cLX1mY7bdBsE4n5BYUyPdrHUmJFXDCLC9S09MyQi2+7EhIT4ff74Tsf/Ut03e4B8LW1hRzbd/4c/H6/bh2o7NRJVnYOfrl8FZatLELBosXo2fPiY2DClKm5OFxVGfLYTaQxHKv2o1emIo9mE05a2vj55fJVyMrOCVnu9XpR9KvlGDRkKJatLJKPF1aUl2HjO2/hwYcewbKVRcjNmxGynZFIysroGGb76D8gGU6nEzNnzZbLChYtRp8+fUMmSAQ7MRGJls+bQ/pKO8T6zl694XA40NrSErLc42nULR8zVnFMRERERERdW4cmiMQ36uJxmrJ9u7FwQT4WLshH7Ynj6uq2tba04LP6eixZXCj3t3BBPpY+94zlJEQkF8Pi3R3iOGtfewWBQEBdzZA41pZNG0PSuXBBfsze0SEmoeyaX1CIls+bZTrK9+9VV4mJI9WVSOjbFzeNHQdnfDyOVlepq9jidDpD7thKcvdHnMMRso4Zowtlvz8QdlEdjazsHMOoo7EAAIcmSURBVNx933exasWLWLggHyteXBJ2h9Ou0hKMTs/AnLnz5GeRxHAs209rS4vhXXB2JzxWr1gq0/H0U4+HvQsKwdhf+twzWLggH9C8g6qivAxPP/U4Vry4BNNum25rkiiSsoLBMcz2cbqxAa2tZ7H2tVdClhnl7Uog+lazyU51YtSMnTgmIiIiIqKuLaIJopwJk+WFeG7eDCS53Rcf1VC+5U5OSUWS++LLVqNx8lgNHI443HXP/eoiW6qrDiEzK1ve+ZCckhryclZBvftkVFpGRHcQIXisabdNl4+qdFRcXBymTM2Vf4sysHp8BDp3T4nHUzpDRXkZWs+cwT2zH8Cp2pO2HiVSiUmlqbl3yM+mTM1F3LX2J4iOVlfB6XSGxMpd99wPhyPO8JG3SKh35IxMSw+788LrPYuiVS+FTBJFEsOxbD8nj9XA7/dj9gPfk5+JthrtJJ4qOSUVP3jsX+Tf4m6X3LwZckLId/4cfMH3D4lJDvE4anJKKqbdNl1uH0lZGR3DbB9erxenG+qRN3NW2ETilcKobxV362n7GFF+1VWHlL0YsxPHkcjNm4H/eGF5zPo9IiIiIiK6/CKaIPI0NmLRM89j2coiTL/zbvmunIb6OhyrOYqHH30My1YWIf/xJ+WFGzSTCQWLFss7Dcx4vV68uea3GJ2eIR8J0T5OYrW/kuKt2Pb+ZpmegkWL8bf2v6mrobTkAyS53XL//ZNTQu4g0i4Xx95VWoLMrGz56E1J8VYcrqoMebSl8OfPRn0hGggE0Hb+vNzX6PQMvLnmt3ISSy9Ngjf43hXxKM2iZ56H3x96R5SafpVV2WpVVx3Ct3s6caS6Ul1kS0N9HTauX4fMrGyZ38/qT5ne2aDmv6G+Tk7OiH0MGjJU9xfAoiEm5hYvWYplK4uQM2Gybvq0eSn8+bMAYBrD6rZm7ScS4vEvZ3y8POa026Yb/gJXNHznz6H/gOSQ8n7z1SK0trRg+p13Y1nwUctTtSdRUrwVXq8XxVs2yXrOf/xJfKy5c8eqvWuZHcNsH+veWANfW5usx2WaR+OuBEZ9K4Jp1/YxBYsWY8f2baZ3Kart2G4cG1HbHRERERERffNE9DP32p+Rp9hTf0r8Sper/FR5LIhHYV4tWh2zCQ2iKxX7ViIiIiIiulJEdAcRkSAec4nm5dRGXC4X8mbOQuuZM5wcIiIiIiIiIrqEOEFEEZtfUIiCRYvDfrkrUuovdy1eshS+tjasXrFUXZWIiIiIiIiIOpGtR8yIiIiIiIiIiOibi3cQERERERERERF1cZwgIiIiIiIiIiLq4jhBRERERERERETUxXGCiIiIiIiIiIioi+MEERERERERERFRF8cJIiIiIiIiIiKiLo4TREREREREREREXRwniIiIiIiIiIiIujhOEBERERERERERdXGcICIiIiIiIiIi6uI4QURERERERERE1MVxgoiIiIiIiIiIqIvjBBERERERERERURfHCSIiIiIiIiIioi6OE0RERERERERERF0cJ4iIiIiIiIiIiLo4ThAREREREREREXVxnCAiIiIiIiIiIuriOEFERERERERERNTFfSvhOvf/Vj/UM7+gEMNuGIHKQxUAAJfLhZ8U/jvue2AOEq/rJz+n6M2ZOw+33p6Hsr271EXfeLl5M/DgQz9A5cEDaG9vVxeHiaSsIlm3M0WaRwTTPvPe+yPahsxZ1cP8gkKMnzTlssfL5WZVTma02/bo0QM/Kfx3OHp0R+3xY+qqMZWVnYMfP/lTnG09g9ONDerimBPnwUuRNz1WdXS19x/qOOPW2/Ms22ZySip++vQv0O2aayzrxKr8hEtdz5fznNXZMaPWaaRjx0jqV486lv2mM8uv3fi/3LKyc/DET5/G7XfOREtL8yXp268UHW0vZsz6NbNlkTCKv8vZxwlXQhouFW09dHYfH2vJKan4ycKnce6ct8Nt/1KPES+1+QWFePDhH2DEqLQOx3XUdxDddc/98LW1YeGCfKx7Y426+BtnfkEh5sydp34sJaekYvFzLyArO0ddRFexy12v695Yg6XPPQOv16suIpus2q5q9YqlWL1iqfoxXQUqysvwi0X/CxXlZeqimMjKzsHi515AckqquuiSiDSWr/b+Qx1n2GmbDfV1+MW/PYWS4q3qIrKhs2NGrdNIqfV7udtkZ7rS8zZn7jzMLygM+SzSPsqKy+VC3sxZ2Pb+Zjz91OOd1rdfqTraXujyudzXD0Y6u4/vKL1+JVY6e4wYrVjkOTdvBpzx8ViyuNBynGRH1BNEidf1Q8vnzerHRERERB3GccY3D+uUIuHs1RsOhwOtLS3qoi6B7YWI7HC7B8DX1hazib9u14/M/Fr9UM/8gkK0fN6M9/60AflPPIV+SW4AQMAfwDu/fz1kNk6sq53tFjNjq1csRXJKKvIffxI9nfEAgAu+NhStegkN9XVITknFD/PnY/O7f5T7zM2bgZwJk1H0q+UhGXe5XMh/4imU7dstv03SHgfBWbnE6/ph9YqlmDN3HrLHT5Tbb9m0ESXFW+V+ao5UY0x2Dno642W+Th6rscxvVnYOHnzoEcQ54gAAzU0emVb1mOX79xp+CyDSCgBDrh8GKOur5SaOM3bCpLDy0ZYZgJA81J44bji7mJs3AzNnzZZ/G6VXlJmnsREjR6fj7NkzlscySr/X6zWsY0Gbrgu+NtTXnYLD0d1w39p0i3Jt+bxZ1oU2XSIvIs0iHpPc/W3VqzZ+zYg8VlZ8jNy8GYCyTxH7dZ9+ioybslB74jiqqw6F1aNenIp41JZDwB/A/r0fISMzC68WrQ5Ln17c1544jjdfLZLloe5fLSuzWFLbhdrejNqU2g5E+U7NvUOWuRqXevXhO39O9zgJiYnImTAZnsZGZNyUBSj50PYZok4qD1Vg/MSbEeeIC6vvSMpcb3/l+/eitOQDuQ+z/UOJt2jaoZ75BYWyzxH7H5mWblpOaj1q06XX/2j7ab360vb/ZuWt7QuamzzwNDYivlevkPoS5w81lmARO7UnjgNA2PkLOn2jtq2YtUl1O3F8vfanbiuoZW03ltV+VVvP2r5HS403kV5tu4DO+deqfzPbtxrH58+dBQD0DdadyO+otAzdNIhyaW7yYO2a3+LheT+S6VLXMYpTtRy01LxCiRto8pKVnYO77/uubP8izzu2bwtJj3ZfKqu4NYopO9uKdYxiXlsmzl69Lduj3f5PrQdtnfZPToHDEYc+ffrig62bMXnqNMMxIDT9CYCwNmnWz8FgfKqlLVtte9QbW45Oz5DnJrMxhtl+oVM2zU0efHLwAG6bfpfcXuxPXVc9jrZum5s88PsDON1Qr5vfjrRZbV8CAH/ethWZN40Jq9+K8jLDvOuNebR5UccQ2vatnvOcvXpbnicjGesIeu1VfHaq9iTWvbEmrIy0x1b7AyhjDCNqPQf8AWx+dz1unnZ7WN7HTpik2x/o5VubTzVvorwPVZTLaz3RztR+MjdvBqbdNt1y7GvU3tQyMCtDdR9z5s7DoCFDZXrM+nE7faVZuzXq57Xbq+ejsn270drSEha7avoizZc2LWo/rDLrByLZr97YUPR3evnWxpE69jf6XEvtV7Zs2oij1VURnYegc84TrMaI2jzqXW9YxYzabtV0qsubmzz4+9/+jtTBgyHolQt08qi2EbXc9PYRiYjvIPJ6vVj63DOoPXEc5fv36t7yWV11CIOGDIXL5QKCmUro2xe7SktkBg9XVWLhgnwsXJCPw1WV+P68H8n17fJ6vThVexJp6ZlA8DhJbjeS3G4kB2/JTbyuH6qrDsHlcqF79x5YsrgQCxfko3z/XuRMmBxyzDHZOSha9RIWLsjHZ/WnkDdzFgBY5reivAyrVrwI79lWrH3tFXnrnhhArHhxCRYuyMeKF5dgdHqGPBHrGXL9MFRXHcLCBfnYsmkjMrOy5e2JI9PSsWP7Nrkvp9OJsRMm4Wh1FRwOB4YOHwEEAzBnwmTZsYsT2cIF+ViyuBDO+HjDNKQOGiLTu2XTRoxOz5Blqcc9YABefPbfsfS5ZwCLYxml34o4EYl0bVy/DkOvv0Eu1w7Axb6HjxgZclunaDjadM0J3gY9dPgI1Byplst8Ph+m5t5hWq+DhgyVsXS4qhKzH/iePJaZfkluDB1+g4x9X1sbvv/DfLk8Li4O8b16YeGCfMPBg16culyusHJ48dl/x4hRaYiLu9gZGxH7W/HiEiS53Vj0zPMo3rJJ7n/K1FxAGRTp1a+WS3NbuKiTM59/LvchbpleuCAf297fjNkPzAmJM207aPJ4ULBoMRCsPzUujerDrK/ql+RGe/vfsXBBPta+9goGpgzSzQeCdTJiVBpefPbfZXyI+o6mzLX7W/vaK8jMykb+40+iaNVLIfEHzf6t+stI2qFKXPiI/e/b9ZFcZlZORu3GilF9CWblrfYFxVs2YeTodM3ew5n1qWpaqqsOhZxktUqKt2Lta6/Ae7YVK15cEtI+jdokbPSpZtsKsYjlXM3txwsX5GPnjj+rqwAAZj/wPRlvSxYX4tPaE+oqhsz6N7Wt6PXTIo7/8z8W4z//Y7FufgW1P1q4IB81R6pD1kEH4tSM2bn95LEa+P1+jEy7GJcj09LR0xkvxynOXr0BAEerq0L2qTKLW6uYMts2kpiHRXtU69Ss/zOL4aQkN4q3bMLTTz2OI4fNy0XLrE1GQ+1ftr2/WbbHN18tkv1ockoqho8YiY3r18mBv9kYw2y/RnG8+8OSsLyp66p9u1q3Zft2Y2BKiiaH4aJts6tXLEX5/r2oPXEcCxfkY+ufNurWr1neYTHm0RuDtZ0/ByjnPDE5ZHWetDvW0VKvMxDsUxwOB0pLPrB9jo6UXnupCr7HR5t3r9dr2R/YOceIi/dt728Ou7BWrzEAIC09E4erKg0nKLSyx0/EspVFIf+0ky5WZai9pnS5XBg0ZCj69Okr0+N2D8Cp2pNhk0Ow2VfCoN2a9fNW9GJXTV8k+VLbtjpu0lLXNesHsrJzMHzESJnHDe+8JZeZjQ2NuEzG/nqfq9R+RUxyRHIe0vZTdkRyvSHWF8vVmDEbb6j998JgX//6b1fr5lnLqo0YlVtHRDxBZMeBfXsAQF78j0xLh9/vx8ljNRiZlg6fz4f3/rRBrl9a8kFY52PXkepKxDm6w+VyIcndH00eD1pbzyLJ3R/JKalwxsfjaHUVvF4vXn/l17KBHqmuhMPhkIM1ANixfZvs7HaVloQtj4Ro7Np9NtTX4XBVZciJRlV74ris2AP79uDs2TNISEwEgoMhsayhvg5NHg/c7gFoqK9D65kzGJWWAWhOXkerq0JOZAiedMr27TZMw/+88t8yvUerqxAIBJDk7q+uJpXt2y3L1OpYRum3op6IKsrLcKiiXC4XMSXirqG+DsdqjsryQLBcxUlPpEt0zBXlZXh3/Tq57FTtSTmjrBL1qs13ackHcMbHh3QgRpqbPHjz1SL5967SEiT07Su3DQQC2FVaotkinFGcquXg9XpRvGUTAoGAsodQYn+iTj6rPyUH7tVVh+CMj4fL5bKsXz2ifhvq63DwwF/kPjau/8dJ6MC+PfD5fPKCCko7qK46hAu+NnlcbVxGWx/NTR7ZB1WUl+Gz+lOGsRgIBFC8ZRO8Xq/MsyiTaMpcu7+Tx2pw9uwZGd9q/NntLyNph1pZ2TlI6Ns3pD62bNoo48usnCJpN4Kd+jIrb6u+QI9Rn6qXlpLiragN3lERCaM2CRt9qtm2dpjVkcrpdMp979n5ocy3StSj1+vFnp0fqosNmfVvalvR66e1dWFFjC+0bePd9evkBaQQTZyasTq3i2OIOnC7B6DyYIUcp4xMS4evrU1ua8QobmEjpoy2jSbmzdqjWqdeG/2fHu0553JS+5cD+/bA7w9g6PARMu85EybjwYcfxbGaoyFpNhtjmO3XKI712oFZ3x5N3SIGbdaKWd5hc8yjR5tPu+dJu2MdVWnJB0jo21decI5Ky0DrmTNoqK+zfexYUvtJq/7A6hzjcvXB7AfmYNv7m3UvLEW9iwm05OCX/keqK9VVdZXv3ysvasW/8v175XKrMhST6c5eveHs1Ru+tjY0NXlkn9Y/OcUwLVZlY9RuB6akmvbzsWA3X3ptWx03CXrrWvUDcQ6HLJODB/6ChuCdb2ZjQyvq2N/qcyuRnIci7afsXm9o19eLGZfF9WQkfb3Kqo10hk6ZIBKFIhpRWnqmDFa9Z+R858/B7/fLwU8kmjynER/vxNDhIzAqLQPVVYdwuqEeo9IywgZiuXkz5Oz1w48+pvstV6wYPTft8TQanoSsuFwuFP78WZkH7bd+u0pL0D85BS6XC6PSMnCs5iga6usudjR9ElCwaLHcbuas2YZpyMrOwS+Xr8KylUUoWLQYPXtevJXNDqtjmaXfiMvlgjM+Hh5Po7pIcrsHoF+SG4uXLA35dsLsIqC1pQV+/z8GsfMLCnW/2VA5e/WG0+nEzFmz5foFixajT5++phNpRpo8p+H3+9WPo6LXtiLV8nmz4fPuVvWr5fV6UfSr5Rg0ZCiWrSyS30QkJCbC7/fDp7mI83q98LW1GV7Utra0oLX1bMg2QqzrI1IdLXORd6P41tu/VX8ZST3p1Uck7LYboSP1Fd+rt2VfEAmjPjrWOtKnxlJJ8Vbs2L4NBYsW45fLVxl+uya+xV+2sgiFP382LGYioe3foumnzei1DSORxqkZo7jRnts9nkYMGjIUySmpSLyun5xkGzp8BNLSM1FddQgIftMr0rVsZZGtb6bRgZgySnu0IqmDK50Ya2jvdli8ZCkGpqSEfEnna2uDwxEXMlDXI8YYot8y2m8kZWjWt/cfkByTuo1lm7VTprGgV4ZW50mzsY6qQfMlrCt44S4mtaI5dqxF2x8IU6bm4nBVpe7kkHCkujJk4rD1zJmYTepalaEYn4xMS8fItHS0fN6Mgx+XIS09U14cnzxWI7fVirRsZLuNj9dtTx25hlPZzVck46ZI+/iK8jJsfOctPPjQIyHnoGjHhkZjf6PPY6Gj/ZSW2fWGEbvXk3pxbpfetp3dz3TKBBE0nclNY8fBGbyLByaNy+8P2A5oLdFx3zRmHBKv64ej1VU4Ul2J/skpGDRoqDwB5AafuRS33K197ZWIv+WKhFnFqZVsh0vzjKeYgdfOCIvOMS0zK2Q2vbWlBZ/V18t8i396tztmBZ+VXrXiRSwM3qZ34UJbyDpmzI4FzXsD9NJvxGjyQNvwPZ5G1AZvq9P+U29X1kpITIQj+ByseA5YbKf9ZkPlO38Ora1nsfa1V0KOpfcIhB1J7v5wOBzqx1FT21ZCYmLMJkLN6leNJQTrbulzz2Dhgou3q88vKERrS0vYt1dCNBf+sa6PaHRmmUfTX0ZST2b1YSWSdiPEor7M+oJIxcXFhfTR4oImVjrap8ZaSfFWLFyQj3d+/zpmP/g900mihQvycar2JPKfeCos/uzS9m/R9NNmjNqGKpo4NWPn3C4eyRg/aQr8/nbUHK3G6YZ63DRmHOIc3WXbXffGmpCyMLtAEzoaU7GOebUOYtn/XUpirLFl08awGBX1kpWdA2fwwtHq8Xgxxmg7f850v3bjGBZ9++nGhrC4jKZuY9lm7ZRpLBiVodl5MlLicaC0zIvvehNj7ktxbDMd7Q8Q/IJ5dHqGfExGj/bRWe0kdyxYlaFXc9PB8BtG4kh1JY5WV8EZH48h1w9DwN8eNrZBlGUj221bW1h7EqK5htNjN1+RjJv0zk9W/UBFeRmefupxrHhxCabdNh25eTM6NDbUG/ubfd5RHe2nOsru9aRRnNthtG1n9jOdNkFUUV6G1jNncM/sB3Cq9mTILX5OpxN33XO/XPeue+6HwxGHk8dqZHCLW8OSU1Ix7bbpcl091VWHcP0NN8Dvb0dDfZ3suAcNHiInStTZt1FpGZ06iBENf9pt0+UtgCIv0XSs6qxwcvB9S4LX68XphnpMmDwV0Jy8Th6rgcMRF1LeRtQZ45Fp6Zaz7Vpmx7JKv5mWz5tDngHNys7BwJRBcvnR6iokud2m374OTBkkL4ZEPZQF39Hk1NyV4ArenmlElLPeM9wIdg5mJ9l+SW45sHQFn8kVd3t11JHqSvTp0zdk/zkTJqurRc2sflXJKan4wWP/Iv8WE7VikKF9djo3bwaS3G45iRwJq/robJ1d5lb9pZ5I6kmvPmbOmh1227JKDDjsthuhI/XVdv4cTtWeNO0LItEQfMxA+y66sRMmyZcHxkJH+9RYuu+BObIPbPKcRkDnzkWXy4VHHvuxLA/tpK3H0xjyfr+77rk/rKzM+jc7/XQk9NrGfQ/MQXzIoxP24zTL5k+K2zm3iy+ubswcg2N/PQoE+4pBg4cAJt9229GRmIp1zHdG/xfNGFArKzsH//HifxlOfpqprjoUUq9aySmpuPu+76J4yyZsfvePmHLLtJD1jMYYXq/XdL9GcazXP5r17WIyJtK67ew2a5b3WNErQ6vzZKTEo3Ez7r4XBz8uk9cSVsdu8pxGXFwcRgYfoc/KzkFmVrZcNzdvRofu1OxIfyB4vWdRtOol00kib/BRmmm35yHO0V0+ztOR9iZYlSGCfU3fvolwxvfGyWM1wT62BWPGjTe8prJTNkbt9rP6Ost+3s450YqdfEUyboq0H8jNmyHbt+/8Ofh8PsBibGiW72SDsb/R57EQi34qEkYxA4vrSb04N+rrVXrbqm0k1jptggjBE8O3ezpDng1tqK+THZG4BWuQ5q3t3uAz7JlZ2Vi2sgj5jz+Jjy2+WT5aXQV8/bUciHm9XgT87Wj3t8uCKy35AElutzxm/+SUiO4g2lVagsysbMNb88Uzjw8/+pjs7Ne9sQaHqyrl7cAFixZjR/CXTCKl3b8oF9GQhSPVlUhKcoecvLxeL95c89uQ8tbeRqglOnxxm17OhMmWs+1aZseyk34jajnmzZyFo5oXWTbU12Hj+nWYfufd8phqPTU1eZA3c5asB3E7rTjpiVs3Fz3zfMitgkb16mtrC7mdcX5BIVwuF+Ic3UPiXdXc5MFNY3KwLHirta+tLeyFgNGqKC/Dtvc3h+Sl5kh1RHFuxqx+Vb7z59B/QLJcZ9CQoXjz1SJ4g7eZOuPj5bJpNn4Jw4xRfQhWbbcjOrvMrfpLPZHUk159ZGSNkQMqI1btxoxVfZlZ98Ya+RLBZTp9QaTEuzdEWtLSM03vbBRffBQsWmwrzR3tU1UdiWWv96zsfwsWLcaunTvCvn30er2I79VLpnfabdPx5prfwuv1oqR4a0jZd+/eA81NnpDtzfo3O/10JPTaxsCUQSHvIIokThMSE+V7Rayo5yS9c3t11SFcG3etnPhu8pzGtXHX4nRDvWHbtaOjMRVpzJvpjP4v0jFgpG3STEnx1pB6XRZ8zHJgSiq+P+9H8rGaivIyHKs5ivzHn5QXSUZjDLP9ulwuwzj2er1hebPq21evuNjmIqnbjrRZ7bhapEHto8zyHit6ZWh1noyUN3iRjq+/Dvkyy+rYDfV12LF9m2wj6jnLbfKCZTs62h8Ioq4zs7JR+PNnQybaBXGt1dE+TGVVhghOWLT720OOff7cubD60LJTNmbt1qqftzon6l0/qOzmK5JxUyT9QGtLi2zfi5csxanak/LayGhsaJZvo7G/0ed69PoVM1b9VKwZxYzVeEMvzkVfb5VnvW3VNhJrtn/mPhq5Jj89SPRNk5Wdg5yJk/Hb1S+riy4btsFLj2V+aYlbemMx0erS+Uljsudqj/sfzf8JyvbuDps0+6aLdcxf7XEQjTk2fracrn6xrmeXy4Xv/zAfG9e/ZWti+nJLVn4inKiriXUfcCXrtDuI1Ftsib7pKsrLrqjJIdEGO/LtFEWGZX5p5ebNwMCUQaZ37UXirnvuh9PpNPxGkr65frv65S550RPLmGf/R99UWdk5GJ1xY1S/uGbE6/Vi9YqlV8XkEADMfuB7MX05NRFduTrlDqL5BYUYcv0wlO/fG5NvdYnIWnJKKvIffxI9nf94xpptsHOxzC+t3LwZmDlrtvw74A/gnd+/HvWAdc7ceSG/MnHB19ahxx27sq5458jVKJYxz/7voq70rXJXI+6w65fkxpZNG2Nyl93VRpx3m5s87N+pS+tKfX2nTBAREREREREREdHVo9MeMSMiIiIiIiIioqsDJ4iIiIiIiIiIiLo4ThAREREREREREXVxnCAiIiIiIiIiIuriOEFERERERERERNTFfSvhOvf/Vj/8pktOScVPn/4Ful1zDWqPH1MXX/FcLhd+UvjvcPTo3uH0R1oWySmp+MnCp3HunBenGxvUxZdcLNMjyuI7934XeTNnYcSoNJTt3aWuJqnr582chcTr+qHyUIW6akRiWb9Xgzlz5+HW2/NQtncX5sydh5n33o/KgwfQ3t6uroqs7Bz8+Mmf4mzrmajrO5YxEym99pabNwPzn1yIm2/NxTXdrsGj+f/aofzpsSrXy2l+QSEefPgHlu0NndA2xLFF+829YwZaWppjWvZa8wsKMX7SFMt8GtG2Fa3cvBl48KEfoPLgAfTo0SNmZRSL9na5aONq2A0jZLl1JE9z5s7DIz/6sWmsZGXn4ImfPo0Zs+6VcaU9P5w503LZ+p/LJZZ9rlkfoNe/Rmp+QSGG3TCiw+dxlYiL2++cqRs3sdaROLfLqD/6JtHmUdvPXmnnUa1YtINoWcWd1XI7Iu1PrNaPJE2d3f9cyWJZjiqj87Udok7ue2COvAaLZFyp1RX6NLsuyR1Ec+bOw/yCQvXjSyYrOweLn3sBySmpAICG+jr84t+eQknxVnXVK5Ka/li62soiltS4HDz0emxcvw4LF+RjxYtLkOR2IzdvRsg2QlZ2Dh4vWITDVZVYuCAfCxfkY8niQgwaMhSFP38WLpdL3cRQZ9bv1WbdG2uw9Lln4PV6AZ06qigvwy8W/S9UlJdptrp6qO0tOSUVU26ZhrWvvYJf/NtT2L5ta0zyN7+gEHPmzpN/q+V6pcjNmwFnfDyWLC7E6hVL1cWXpG2U798r2/ChinLMfmBOpx1v9Yqluvm8Ul2t7c0sruzmKTklFYufewFZ2TlAcBD697//LSRW8mbOCuvrK8rL8PRTj2Phgnxs2bQRzU0eLFlciIUL8rHujTUh635Tqf32paL2r1cKl8uFvJmzsO39zXj6qcd1Y0/tszvKbpzbpbaHjuqsvj3W6bwaXc52oMad2heoyzuDekwrsUrT5Sz3SNlpJ5eqHM3O13bcdc/98LW1yXNsR/fX2WLd13eWSzJBRHQ12LPzQ9mxNdTXocnjgds9QF1NDvYOVZSHDPi9Xi+KfrUcADB2wiTNFkT6ktz9AQBNntPqoi7B7R4AX1vbFTNxVVryAQKBgKwXujp1Rlx5vV68u36d/PtIdSUcDgecvXqHrEekcvbqDYfDgdaWFnUREVGX1tHzdeJ1/dDyebP8u6P7o4u6XT8y82v1Qz25eTMwc9Zs+Xf5/r1Y98YaJKek4of587H53T/Ki+vcvBnImTAZRb9aju//MB9Drh8mt9uyaSNKirciOSUV+Y8/iZ7OeADABV8bila9hIb6OrhcLuQ/8RRqjlRjTHYOejrjUXviON58tQj5TzyFfkluBPwBvPP710OOqZc+9XPtfsr27ZYzvXPmzkP2+Ilh20OTH09jIzJuypL7MZuZjNX+zNKvLR+1PEQZ9ktyy+309i/W05aF3jHFtqK+d+3cgWm3Tdc9tpZYv/JQBcZPvBlxjjiU79+L0pIPZP1r6x46ZSdiRqTV09iIkaPTcfbsGfzf9W/hgYcfkfEn0m4UZ6Ie5hcU6salkJs3A9Numx6SLiErOwd33/ddvFq0OmwZgulPvK4fVq9YKuva7w9gYEqK7nHUsrZTv0b50qPGwq7SEmRkZoW0WW2Zq/Whl8ZI4kE9vrpcr7yM+o6j1VVh/Y22LkXafefPhRxTmye9PkvQi7GiXy3HXffcbxqTdvsqbXsDEFauG9e/FZa2SPKnfi6OnZCYKMvV6/WGxY9V/6tXZ0bxomV2HDttUC2fWLeN+QWFaPm8WS5XY8NqeVZ2Dh586BHEOeIATR6MPtfGutouzMpR0G6vpW03AEL6dJGWQxXlKC35AD/Mn4+6Tz9Fxk1Zsi0b9blqfsXxAci6U8vXbmxAp46N+u1I4lMvrtzuAbLc1DxBp419+OdtuGPG3bL+mps8su1A00+cqj1pGFtQ6kVsq3dOVMvJLP9Q0qtNm7qdWjdaatmLdY36QATjympMoVf+ot+OJM9GaVfHLNr4fu9PG0KWRRqvzU0e+P0BnG6ol+NbO2mCzrhFrKv2BUaxpPbZ2vGMoC1zEVuVFR8jN3insxoLapyr+xPtzSieBKM83HXP/ablqx7PbFyuF0tqmarnCLvt9vs/zDftx9X0aPNgNj7R7hPB9CB4p6iW2E4dB2qPa3UeqzxYgdTBg/Fq0WoA0K1bo3PAnLnz0D85BQ5HHPr06SuP09G+WrRr7flEO4aZMjXXsC9Q067uW6Rbr/7V+tOKpv/R7u/ksRrT80tn9j+1J44DQFhc6RH1bXYdqZaf3T4JMShHs/GRlt5xtOdrvestUa7acTaCbejs2TPyb7E/9Zhq29LmX+y75fPmkHrRlqsar9rlkabX6Nr5SmD7HURTc+/A71//HTa8vRZffPkFciZMQs2RwwCAMePGo+bIYfnM4dBhwzEwJRXl+/Zg547tSLyuH/zt7Xj+mZ+h9vgxWTmHqyqx4oXnsO29zeg/YCBuyb0dlQcPAADGTZiMQYOHoGjVS9i7qxS33nYHpt6Whz9teAdv/K4Io9IzMGjwUPmcoFH6Pvn4AJqbPBg8ZCh+/fJybC9+Dz169MC4CZPR8Fkdao8fw5y58zA6PQOrVizFhrfXouqTg5hx9z3yOdKhw4Yja+w41J2qxYoXnkNzkweTptyKr/CV7nOmsdxf7fFjhukX5bPh7bUYlZ6B0ek3yndQiMHriheew/7dOzHplmm6z8yqZZGbNwPT77wb695Ygzd+VyS3TR08BJWHKtCrtwvjJ03BkKHD5LG/wle46+57UXPkMM6fOxeyf7F+b1cfvLx0CepOfYrbpt+FMeNyULTqJfx562Zkjh2HPgl9UXmoAi6XCxmZY/CbVf+FTRv/gMTr+iEtIzMkLuJ79cLLS5dge/FWdO/xbRl/o2/MlGnf+1GpjLMd27fhN6teQtUnB3H7nXfh3DkvNm1YHxaXQq7J5BAAjL4xE/0HJGPPzh26z6H3698fQ64fhsqDBzAgJRVZY8fhow//jN+seims/KOp38R+SYb5Up/71V7IiLZ2+50zkZjYD1WfHMTpxgbMmTsPg4YMxYr/fBabNv4B/QcMxNicCfKZ+47EwzXduiH/iafga2vD88/8DNve2xwWLxk3jUHPnk6U7d1l2Xf06u0K6W/EgEzsu+e3nTh9ugEpgwbjyy+/xMr/8wL2794ZEmPqPrRE2WtjrEePHpYxabev0ra3kuKtYXWvpi3S/JXv34tdpSUYMSoNx/9ag5eX/RKnGxtCylXEj93+V42/0RmZmDjlFvz65eXY8PZatJ45gws+X1jbt+rn9epXq7PbBgCMnzQFf7twQb5v5Af//C/4+9/+hk0b1usu19bP3y748E/ffxQ7g2276pODuKbbNfi7weeexoaQWB+dkalbh2bvPtFur6WtXwTrr+GzOvTpk4AHH3oE297fjM0b/yDbayAQwPPP/Axle3eZ9rmO7t1D4jHjpjFIy8jE7o8+xG9WvYQvvvwCk2+eJt+pYtaXqEQ/K86TzU0edOvWDV999ZVp3Ij8GcWAXlxpy81OG6v85CD2792FtIwb8cd1a/HHdWtlX293cghKvYjt1XNi6fZtyBw7DsNuGImyvbss283kWy9edIky/urrr9DsOR1x7BuNmQL+dt0+0O6YomzvrrDyt5tnO2nX9qFqfKvjmUjj1dGjO7JzJqDxs3pUHqrAD/75X3Dq04vjtP27d8LRvTvqT30akh5YjPn2flSKw9Wf6MYSALS3t+v22VbnXjGO7Natm4zdrOwcZI4ZpxvnRu3thlGjdeNJm8bTjQ26ebAqX6MY0xuXq8z6pfb29ojarVk/bpZOs/HJtQ5HSHqSU1Ix+ZZb8ef3t4TFrKgr7ThQrY+v8BWm5t6hex7bv3snbr0jDz169ED5/r2AxXUXNOcA0Q6uH3YD/rThHax97RXd2I+mr/aebQ07n2jLVm+crZa90b7Pe88a1r96XtKKpv/RpunsmRbT8wtMzq8d7X/ie/dCzsTJsv8xY3Ud2ZE+CTEoR6PxkcdGfWnbXP/kgRgxKg0fl5fJMaZYvnPH9rC+c1dpSdj+VOMmTkbtyRMyXZNunor2gD+kzho/qw8514l+16pfjjS9avxeSWw/YvY/r/y3vFg+Wl3VodvwR6alw+fz4b0/bZCflZZ8AIfDgaHDR8jPdmzfhob6OjQEH/f5rP6UnGmrrjoEZ3w8XMHn/6NNn8vlwqAhQ+WxEHy86HBVJdLSM+V6zU0emd6K8jJ8Vn/K8PGjWO7PjPYYu0pL5O3uQ4ePgMPhQGnJB0Dw1viyfbtDjm8kLT0ThyrKZTmLbQcNGSrLOhAIYOP6dfLYB/btgc/nw8i09JB9CYFAAMVbNsHr9eLksRqcPXsGh6sq0VBfB6/Xi1O1J5EYnGX3er14/ZVfy5lsvdv4y/btDrt1cMj1wzDt9ryQ2VgRZweCJ8yG+jocqzmKUWkZIdtqJQffCaPNX0c1N3lkGiJhVL+R5EuNBQDYuP4tXLjQBmjiVVumpSUfwBkfj+SU1A7Hgzj+xvVvyeNbxYtdWdk5SOjbN2TfWzZtREN9HSrKy+TjIGqM2aEtDzsxGUlfZVdn5S/S/hdK/AFAnMMh+9eDB/6i21bsHicaRmmLpG0I2eMnYtnKIixbWYTqqkNh3/5aEf12Q30dDh74i+XnQkfq0A6Xqw9mPzAH297fHPINWiAQwK7SEvm3nfjWqj1xXO7vwL49OHv2DBISEy37Ei2Xy4WcCZND6rGivAx7dn5oO26MYiASZm3MiHjfgdXkkBntOVH0qaKPsJN/p9Mp87pn54fwer0Rx77VmElbj+p5RKTZzphCsJNnu2mHSXyrIonXkuKt8lt8QTs22bPzw5BliGDMFyk7597mJg/efLVIbrOrtAQJfftG1N5gEE92GZUvbMSYGbN+KZp2ayaadIpYFa8TGJmWDr/fj5PHapQ1L1LHgWnpmXIcjOD+/P4Ahg4fEdYevF4virdsQiAQkNtHSjsm0Yv9aPpq6JxPImG2b7P6j4ZZ/6PH6vxyqfofM0bXkZ3VJyGKcrQaB10OJcVbZZ006LxOpPbEcXl+V/tdO/3yN4XtCaKs7Bz8cvkqLFtZhIJFi9Gz58Vbs6Kh93yg7/w5+P1+eWJRtXzeHPKMoSra9Bk9G+7xNJoGvZFY7y8aCYmJcPVJQMGixfLCZ+as2ZbHd7lccMbHw+NpDPm8taUlrHPU8nq98LVdnHCwItZVj6GVmzdDpvvhRx9DXNzF2xONxMXFYcrUXOz4c7FstAjGWb8kNxYvWSr3lz1+ouVFmN/vN30njFV56MV3LEWSr4TERPj9fvjOh97dITh79YbT6cTMWbPlvgoWLUafPn0xZOj1HY4HveOL5ZFOiKr09q01v6AwpHw6IpKYtOqr7Oqs/OnFp1X/q1VRXoaN77yFBx96BMtWFiHX4EXuHT1ONCJpG0J58CXVWzZtxLTbpocNkI14g+8cGzRkKJatLPr/27v3+KrqO9//b6zZKDuwgUg2xAQBkUsSYzBGFKaWWInIEVuchxzUUkt/ziPTQRwZC8xYSmeUdiocD7bgmcnUKTreqJxKT/GBNFiDMCiYxkaahEu5lcTATgxhQzaWpMrvD/Zarr2y1r4lAXS/no8HjwfZa6/L97rW+qzvd23zibbb507cytB6Pou1DYM9ryffUqLddbVRL14NidRvN9H6EvvNltt5Uue53sRqY256on27iZX+yopN2vLWZi1YvEQ/emq1CsMvGU207idyzZTsNUW8Ej12JVi/nUSrgwYjWLxiVbnrj0+4bac713zJXos1B46po6PD/rHrMSp8s+RUn3pCInXMiVu/lGy7dZPMcQbDQX3jhjs3ryDiZj8ao3ytDyeWLFuuK7OzNSgjw7EP6Ek91Vd3V6xtu5X/xeB89D/Jctt2d/qkRCVyHXS++Xw+Lfr+42bdsk5xc9LW2qqOjk6l9x+QVL/8eRVXgMiYU7d65ZPmLzwZIxCS4VZJOzo6u1ToeHTn+KJdeCbTQff09pLR1tqqDxsbzV9OMf7F+hWjaDfu7e0h15OxcbLrCSXhebXGsb/03LMxn5oYTzCm3n5nxM1qIHBUhw8eiMiDhfPLoo4QaGps0PInlkZ9EnVo/z51dHQ4joDxhSP39XW77It6TKLpsj4dVPjFyGkejxSur21tJ/TSc89GbOuxRx/SO9ve7nZ9iNZx2jvZREXb9rzwu2OM9BjDspORTJ3sCb2Vvp7of2vCv9S08sllmnLrVMcgUU/sJ1GJtg2ryopNag4ENPOee+2LXAWDQS1/YqkWzi+TLO+gcPvcKloZGvlrP363PHVqo9u3Vmp8Xr5mx/i1jJ6q39H6Emvg3viu23nSLY29UW+itTE3a19Y063RQ7HEk/7Kik1aOL9Mr778vGbOuleFRcUJ1f1Er5mSvaaIVyLHboi3frtxqoNO1zLPrFyuhfPLdOTwIZU9/GiXcnHajrksyWu+ZK/FMv1D5Qmf262iHaNc6lN3JVrH7KL1S8m0WzfdOc499bUaNHiwrrv+BnnT07W3vs7+FUdG+W7csL5Lna+s2OTYBwzKyOixAElP9dXdFW3b0cr/YtDd/kfhh9ux+p9kRMvXZPukZMRzHXS++SzvkDLqfaxRW4MyMuTxpKn91Mmk+uXPq7gCRPZo/djcPDPCblREYyhwVnaOptw6NWJ9u731dfJ6vbrjrrvNz+646255PGmuwzOjiXZ8sRhPAaxPjo00JHOT39PbS8ah/fvk8aRF5G+86ut2qaCwyLxA8IV/setYU6PZqRgjdgzGfpKZRmVnf3IyLjc/rpPi4YMH9OrLz0cEifbW10X9qXonWdk5+pcfPxV1nWB4uK89IGV0PKH29qSfKsQjkXQZFyy3lNxmfjb5lhKlXXruIjIYDOpYU6PjzzWrB+qDEUyz3nSXlE5Tpt8f98WUG6dtT58xU2PGjY+I8htBu2QlWye7q7fS193+t6R0mln3QqdOKhQK2b8i9cB+kpFI23Cyft0rEeu3ftQSMXR45j33mueWrOwcffPBvzXXNUaVuH1uZVwIJlqGTnlaWFSs8fnXdhnmHwyeUPnqp2NexPZU/Y7Vl1g5nScLi4p185e/4pjG3qo3bm0s2iiy2XPmuo4m6Qmx0v/1e2ab/XFz4Jg6wyNGEqn7iV4zdeeaIh6JHLsh3vrtxrhJL544ySzL6yfebL5A1Ofz6YEHv2Muc3ug4VSXe+KaL55z75BMvznFyVi+f9/eLg+4nI7RaG9u9am7Eq1jdtH6pUTbbbR+vDvHWVNdpbbjx3XXzHt05PChLvkeTX3dLtcRq22trRo4cHBE2RZPnGQuT+a+y6qn+uruirbtaOV/Mehu/2NMbXLrfxQOqCSzbad87Yk+KRHxXAfFozlwTGlpaeYD+cKiYhUUFtm/Fjf76Kqs7Bxl+j/Lc0m6Mnu42Sca+WaMDozVL/f08V5IcQWIjBt/Y/hv8cRJZoQ9GL5ZLigs0opV5Sp76BH93haB3lr5pjL9fq0IT0doamwwG5YxxGv4iJFd3qIer2jHJ0snvmDxEscI5toX1mh3Xa05fHrB4iXa8tbmpG/ye3p7sY7fLhgM6sU1P4vIXyPvY6ms2KTNv3ld93/rQa0ID3s9YnsZZ2dnp9pPnTK3Oz4vXy+u+VlSZWdnrSsrVpVraFZ23E8NaqqrtPk3r2v6jJmat2CRmhobtH7dWk29/U5ze9Yh1PZ6mYia6iqtXvmkptw61dy2kVfRnno6SbR8Y6XL6btG+1yxqlwfNh6JaB9rX1ijUHt7xPB+4zi6Wx+C4WGm3vR0c/mUKC8At4tWRk7bzi+coMDRJlXtfMccPr146Q/V0RFfHXLSnTrZHd1J3/atlSooLHKsF93tf9taW826Z9QHp76tu/tRL7cNJ02NDdry1mZNvf1OFRYVm/P7jbZhbTuhUyc1dFhWRNpe/Hm56+dWwfC89Whl6MQpT2fd94DWv/pKlye/xveN9r/o+4+rv8PTyZ6s39H6Ejv7eXLWfQ/o49OnHdOYaL2Jl1sbC506qabwe3Du/9aDvRoQsouV/mDwhNkfL1i8RNu3bVFNdVVCdT/WNZNdotcU0fptJ4kcu5W9fqcnOKLkmZXLI+prbl6B+TQ5GAwqvX9/c9mUW6e6XufY63Iy13z2Pjuec29Lc0DXTSg2l4eivBvLfoxGe3OrT3aJtodYdSxW3x6tX0q03Ubrx2MdZyz1dbt0eT+v9tTX2hdFVVmxKaI8VlimMdZUV5kPPFeEzw/79tRHpD/WfVcsPdFXxyNWX+C27WjlH0usffaU7vY/xnWBU//j8/mU5umbcL0y2PPV3ic5tRO77uRjPNdB8TCuyYxrpdLpM7R3d/IPl63pNtqO/SFnc3NApdNnmPlmnUoYq1+O53jtff3FKu6fuccXU1aUn4vEF1Ohw88wJov6AwDA+VVi+Vlzp6AVzo/zVQ49ed2Gi5PPMv2prbVVxTdN0s+e+Yn9a8B5EdcIInxxxfrlBXyxGMMh244f5yIDAAAgCfbpJ0B33HHX3fJ6vdpbX6ea6iqCQ7igCBClqJLwrwNMvf1O8ycL8cXjs72t3xiGnug0OAAAAJx7P4x9+gmQiNlz5prX5ivCr2eI9/ULQG9jihkAAAAAAECKYwQRAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAivvSoCv8/2z/0M28BYs06/5vasy4XFXt2G5fbMrKztHfL3xMJ08GdcmXvqTvPvYD9bnkEh0+sN/+1W6ZPWeupn/tbtV+8L7OnDljX/yFUFhUrAf/7mHt/+NenTp50r642+YtWKQbb54ctTwvViWl0zTvkYUqnT7D/PeXT/4SVz2bPWeuvvLVUlXt2B5RXz8+HdJ3HlmkP//5Yx072mRfzVFWdk6v1XE4s5ZforKyc/Sdv/+uDh3c3yttKln2tlhYVKyHv/uYvnr7dLW2tsRdH/H5V1hUrO888l2daDvuWO4+n09/v+h78lzW93Pd58RKxxetb+1Ov9XbUuF6CgAAxBb3CKKS0mnypqdr2ZJFemblcvtiV02NDfrBPz6qyopN9kXdtvaFNVr+xFIFg0H7Ikez58zVvAWL7B+fNxd6/06eWbk8ofK82LQ0B7RsySItnF+mjRvWa8qtU5WVnWP/Wq/qzTr+RVNYVKwlT/z4vJbRxdjunFjbos/nU+n0Gdr8m9f12KMPqaa6yv51fIHVVFfpB4v/wSz3C9FuekOi6fg8961Z2Tla8sSPVVhUbF90UZi3YJFmz5lr/p3o9RQAAPhiijtA5PcPU6i9nYsHXLT21teps7NTmf6h9kXA54q3/wB5PB61tbbaFwEAAABAr+gzamzBWfuHdvMWLNKIUVebf2/csF6VFZtUUjpN02fMlCR1dnTq1ZefV011lbKyc/Ttsnl6/Ve/1KH9+1T28KOq2vmOuU7xxEkKHD2q/OsKJUmHDx6IGMXitl07Y1vlP31KklT28KPat6deE4qK1c+bHrFuomnw+Xwqe/hRBY4e1djxeTpx4rj+37pXdM/9D6h2V41uvOmvlOZJ0+lQu8pXP62mxgYp/NSw7KFH1M+bLkmqfm+H1r6wxnX/dvb1az+oUc5VV+nn5c+oqbHBPK4hmX7J5ZiteXD44AG9+PNycx17fs6eM1cZVwzRMyuXxywb+76taY+1rsJPj2fd94DSPGmSJW/s27Wv58Za/sFgMCItcsjLluaA43et9dVez+zH7FRuxvEnUscN9jQYn025darKVz+t0KmTruWtcNts/ahFa19YI4XTbE3L7DlzNTQrWx5PmgYOHOzYluz5b8+nohtvMr9rlJlsdce6HXs+dHR06srsbO3ZXadx4/PMbbnlSTzs+7aXdSLtzppGe3u2sm7Lmkdu+5ZD3kZLs5Gm7VsrI+qcdV9yyGfrZ0cOH9LaF9ZE9Guy7bewqFh3fv2vzT7Fum+3Y7O3AyON9v3Y27Rbf+zEnlfx1kN7ezP2Mygjwzw2e7679ft2sfLKaG/bt23RlFunRk2nU7kZI9uMYzO2vX7dK2Y7tqZD4bQYfXoi+Rtv/bW2AadyjHVOsZejkff2uhJPOux5ZuSPJDMt1rogW3s+fPCAJEX0kQZ731tYVKyZs+7V+ldf6XIdU1Nd5Xj81v7n22Xz1PCnPyn/ukK1NAc0cODgLm34jrvujnn8hkTz3n4ukEP9sm7Xfk4ZlJFh5oe3/wB9u2xexLVO9Xs7tLXyTbOu2PtK+3btbQ4AAHw+xDWC6JmVy1X93g4dPnhAC+eXmTd/U26dqpVPLtPC+WXa/JvXVTp9hnw+n331LoZk+nXmzJ+1cH6ZXnruWV2ZPVwlpdMky81xMtuVpAlFxSpf/bQWzi/Th41HzHWTTYN/2DA9+fj3tPyJpTrV3q60tDSNGZerJx//npYtWaRQKKSZ99wrWW5St7y1WQvnl2nlk8s0esxYFRYVO+7fzr7+siWL5B82TGlp5y4yjQuwUHu7Fs4vM4955j2zI4bsG3mw8sllyvT7tXjpD1WxcYOZJ5NvKbHsNVK0shk5eoz27ak3jy0UCumWktviWte4+H715efNvDl54kTEja2xXW96urleLEMy/VqybLlWrCrX+Lx8rV/3irlsbG5eRFl4vV5dP/HmiPWj8Vmm+RjbOP7RR/avOYqWF1Z76+vk8Xg0cvQY87PcvALtrqs1g0OxyjuWzEy/KjZucJyqZM//hfPLtG9PvRS+0Rqfl2+2j5VPLtP4vHzHdLgZkunXB7+v0sL5ZfrP//NTvfTcswqeaNPKJ5f12M2Dvd0k0u4Ki4o1esxYM42vvfpZ/bEqsUyxXTi/TNu2/FaKsW973sZbt2uqq7R65ZMKnmjTS88922XaRzAY1JHDh5SbV2B+NnL0GHk8Hm2tfFMlpdM09fY79dJzz0bsd7ZlOkki3NquJOUMH2Hm3cYN6zU+L9+xL1po64/t7HmVaD20trddNdW6/1sPKjevQAvD7S9z6FBzqk88/X4i0tLSzICusT2nNmovt6zsHGX6/cr0+83vZlwxRPV1uyLWq6zY5Npu4s3fWPV3d12tme+762r1jbl/E7GdeM8p9nK01vmeSIfCgZX6ul1mnSsoLDLLdvacuRo+YqSZzvq6XREBYit73zsuN1/9+nk1LjdfkpTpH6r29pAO7d8XV5tKS0tTev/+Wji/TMufWOrahqMdv5N4876+bpeGjxhp5ltWdo4GDR6s7VsrI7YXDAa1/ImlOnzwgKrf2+F4XlA4Pca1zkvPPauCwiKVPfSIylc/3eX8H63cAQDA50tcASInxk2s8fTo/Z3vqqOjM+JG101Lc0Bv/Po1KXwz9GHjEfn9w6RubleStry12Vx3+9ZKeTweefsPsH9NinNfVTvfibg56+zsVMXGDQoGgwoGg6ra+Y686eny+Xwam5unUCik93e+K4Xfn7B/317zgjMW+/rBYFAVGzeos7NTstwEWoMg7+98V6FQSGNzPxuZYeRBU2ODmgMBfdh4xLwArK/bZR6vk2hlU1NdpV+tWytZbnaMp6Gx1p18S4l21/7BPI6mxgZt3LA+4sbW2G7Vzncibn6jsb6DaP26tSqbv8C82K6s2GQGBIy8MI4nEcY6TY0N+uD939kXO4qWF1ZGHTEu8o2L+j31tXGXdyzW8rczAmbGsUoyy3j4iJER7ampsUG762rjLhuF88Goz73F3m4SbXdpHo85LfGD939nptfO6/Wafcm7295WMBiMuu/u1u1otla+qUGDB5t1fVxuvtqOH1dTY4Ny8wq0q6baLHNjv9abx0S4tV1J+q9n/93ML6cpnvH2x92th9b2tqe+VqdPh8wb40P79ynU3q5BGRlSnP1+Ijo7O7V+3dqI7bm10T31tUrz9JXP51Omf6iaAwG1tZ1Qpn+osrJz5E1P1976OvtqruLNX8Wov9Z831r5ZpegdbznlGTrfCLpOHzwgNmvv7/zXZ04cVyDMjLk8/k0fMTIiHN2ZcUmcxSRXVNjg9qOn1tXkvoPGKDaD2rMc9q43Hwda2pUMBiMq011dnZ2CcY4cTt+N/HmvdEHGW1pbG6eOjo6dGj/vojtxct6rXNo/z6dOHHcbDf283+y5Q4AAC4+SQWIfD6fvOnpKrrxJq1YVa4Vq8q1ZNlyXZmdHfVCJ5be2q6T3tiX3z8sYkTLilXlKrrxpoggSjSx3vM0KCNDHR0dCp367JeXgsGgQu3tjsEHhYfWt37UYv84afMWLIpIWzyMvA4EjtoXnbuwHzhICxYvMbc7fcbMqEEsNzXVVdpd+wczMODz+bTo+4+b23V7kuwmGAyq/KdPafiIkVqxqrzXXnS8p75WgwYPVlZ2jsbm5qnt+HHVVFclVd6Jcqtzbu/ACQSOJlU28SosKtaPnlptllk8ed6ddldTXaX1r76iWfc9oBWryl2feFdWbNKWtzZrweIl+tFTq83ATLR992TdtjNubsfl5svn82loVra2b610bWttra1Rb7rduG3PYC2vBYuXqF+/c9OUEtWT9bA5cEyh9nY1B47ZF/VKv29ntFEnzYFjSk/3auToMRqXm6/6ul061tSocbn554I17e1moKQnRau/9nwPnTqpjo4O1/yIdk7pzTofi1tdiab1oxbl5hUoKztHHk9fvbvtbXnT0zVmbK6GZmVrT32taxtItk11R7S8N4I2RlAmN6+gywOuZBl12p4HhgtZ7gAAoGclFSAyLhY2blhvDks3/tmncCSit7brpDf2FQgcNaeyWP/FO5XG6aZnUEaGOcUs2gWp24VbTzLecWCkq/q9HfavOIoW1GhrbdWHjY3mKCDjn31aTbyMoIDP8u4KY5tuT5KjCYaH4y+cXyZZ3unQkw7t36eOjg6Nzc1Tbl6BOcXkfJS3U51TjJtE+w1lT6qprtJjjz6UUNvpbrsz9rnyyWWacuvUqEGihfPL9OrLz2vmrHtVWFQcdd89XbftjCkluQXn3nN1aP++qG2tvT0UEWyMR7TtFYbfz7N65ZNaGJ76dfq0c2AklvNVD3uj37czAgpOjMDedRNuUMYVQ7S3vk576ms1NCtbw4ePdL357wlu9dcp3zs6OhMKtBh6u87HkpaWFlFXopWFLMH5G2+erNaPWrRvb71C7e3KLSiQx5Om5sCxqG0gmTbVm4z0XHf9DfImOBqtOy50uQMAgJ6TVIBI4ZuT3vhJ8d7arpOe3tfe+jpl+v2uN5ixtLW2auDAweYQcZ/Pp+KJk8zlRiDBeOeRwu+WyPT7e/1C0P4U1Rcezh+v+rpdEe9ayMrO0fQZM3Vo/z55PGm646677avIFx4BFO+7UwqLipU5dKj21Nd2eZpsvO8jEVnZOfrmg39r/t1bN2/B8HD8KV8tVZqnrzlVIJ7ybv2oJWKaw8x77k1oFMfe+jp5vd6I/P/6PbMlSUcOH4poH1nZOZpy61QzgBUIHI14f8odd91tvqD0fOpOuyspnWauFzp1UqFQyP4VKZwnRt1tDhxTZ0eHFGPf0ep2TzCmRk2782v64PdV5o2Yva35wu/SMqbLNAeOKS0tzZwCVVhUrILCoohtW9m3Z7Rd+wi3sbl5CdU9q+7Uw0Ql0u/Hk1dpaWkR73Uz0uA2tbK+bpdGXXONOjrOqKmxwZwCNPyqEdpTX2v/eo+IVn/t+X7HXXfL40lLampSb9f5aIwpWMUTJ5n94fUTb47aJx3av0/t7SFdWzDBzPv6ul26tmBCxGguexuwt6mLRU11ldqOH9ddM+/RkcOHemU0mpNo5Z7oeRwAAFxYSQeIKis2aXddbcSQ4kXff7zLk8hE9dZ2FX63Qqbfb04l6el9NTU2aP26tZp6+53m9qzD+e37t6uprtKrLz9vrr946Q+1b0+9+Q6iYHjKkzc93dy+8XLU3r4QNIIY02fMNI+to+PcccWjsmKTNv/mdd3/rQe1Ijwd5eMzHysYDOrFNT/T+Lx8M01u+ePEOr3n/m89qC2/rVBNdZWawu+CMfZX9tAjrjf/bkKnTmrosCzzmIaPGKkXf15u/1qP2FtfJ509G3HDEU95G+8OMfLgw8YjCY3iaGpsUPnqpyPy/8rs4QoGg1r7wpqI9rFg8RJteWuzOdKismKTmgMBc3nfvpeppTlg30UE4wZmweIlPTYaqzvtrq211VxvybLlOnL4kONIkmDwRETd3b5ti1nP3Pbd3bodSzAY1LGmRuns2YgAsb2tGekyft2oqbFBW97abLbl0ukztHe3e4DZvj2j7RoBEKPuFU+clFDds+pOPUxUIv1+PHnV2dmp9lOnzG2Nz8vXi2t+5ho4MNr6/j/ulcLl2NlxRmc6zrgGZbrbbqLVX3u+Dx8xMuJX8xIRq853Nx2xGP2zUSdz8wqijhw12pA179taW3V5P29EANLeBuxtyon1HORWv3pDfd0uXd7PGzPYuH1rpQoKiyL6ymTFKncAAPD5EdfP3APoXVm2n1QG4hHr5+nRu2i3FzefZapxsgHFz5uS0mnmz9UnE+QDAACpLekRRAB6zsx77jVfTg3Eo7CoWOPzr43rl5OAVHTHXXfL6/X2+hTsi4Ux/bKnXk4NAABSDyOIgAuopHSaps+YqZbmAE98ERdjVMSQTL82blifMiMjLkaMILq4zJ4zN+LXNU+H2s/LFOyLwbwFizRi1NWqfm9H1KlvAAAA0RAgAgAAAAAASHFMMQMAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFEeACAAAAAAAIMURIAIAAAAAAEhxBIgAAAAAAABSHAEiAAAAAACAFNdn1NiCs/YP3WRl56jsoUfUz5tuflb93g6tfWGN+ffsOXNVdONN5t+nQ+0qX/20mhobJEmFRcWadd8DSvOkmd/ZuGG9Kis2mX/PW7BII0Zdbf7d0hxQ+U+fUjAYlOLYB8eZ2HECAAAAAIDUllCA6EtfulSZfr/6XPLZwKPQqZMRgQafzydv/wHm32c//VTNgYA++eQvkiRP377KuGKI+vTpY37nxPHjOn06ZP49OOMKXXb55ebff+nsVEtzs86e/VSKYx8cZ2LHCQAAAAAAUltCASIAAAAAAAB88fAOIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcQSIAAAAAAAAUhwBIgAAAAAAgBRHgAgAAAAAACDFESACAAAAAABIcX1GjS04a/8wEV+ecKluv8mjYZlfUntHX73wqxP63e6/2L8GAAAAAACAi1S3AkR/e/dl+odvXC71TdeXrhgjXdpXn546pu//qFZrKzrsXwcAAAAAAMBFKOkA0eTr0rTmB+mSpEsyx6nP5YPOLTj7qf58cIfuWXxK9Yc+iVzJgc/nU9nDj2pIpt++SBs3rFdlxSb7x58rs+fMVdGNN+nwwQN6ZuVy+2IAAAAAAIALLul3ED3wP/p+9scnltFCnaeVdmkffWO6ZXmSps+YqUXff1w+n8++CAAAAAAAAD0k6QDRFYP6nPvPp59Kf9wrtQWkEy36pGW/JMk/OPFNb9ywXgvnl2nh/DIdPnhAkjQk06/rJ95s/yoAAAAAAAB6SNJTzP7PYq9um+hRn+AZnb38S5LnUunjTvX59KzOej36RcUZff/fT9tX68I6xcw6pcz6uXV6VknpNE2fMdNcv/q9HVr7wpou60jS6VC7ylc/rabGhoipXq0ftajoxpu6rG/fdktzQOU/fUrBYNBc3+B2rIqy32dWLte8BYs0YtTV6uzo1KsvPy9JmnXfA+rsPGOuAwAAAAAAcD4lPswn7BdvhqeVXSLp03P/7fOJpD7nRha9aixPUjAY1JHDhyRJ3vR0+Xw+zZ4zNyKAI0lFN96kktJpysrO0T/809KIdxn186brG3P/JmKK2ohRV0cEeoz1C4uKNfX2O83PreYtWBSxjiRNvf1OFRYVx71fhQNQ1uBQTXVVxHIAAAAAAIALIekA0dvVnVr1i491tn9fXRI8I509qz6hDp3tl6Z/+dlp/WF/z/7UfXr/ARo+YqRkmYq2ccN6SVJuXoHG5uapnzddLc0BLVuySMuWLFJLc0ADBw7WyNFjzO0Yy63T2HLzCszlsmx/+RNL5e0/QJl+vzo7OvXSc89q4fwyVb+3Q2meNI3LzY97v2meviqeOEmStPk3r5vBoZrqKj326EP6wT8+yughAAAAAABwQSQdIJKkVb/4szb+d4fO9rtUfdo7dLa/R8+//me99MYZ+1cT5vP5zIBQqL1dZyV5vV4p/PLqFavKI0YT+f3DpPA7i5YsW64ly5Y7/jJaqL1dwWBQktT6UYv5eU11lT5sPCJZtj9vwSJl+ocqLa2v0jxpuv9bD2rFqvKI0UTx7vfK7GwNyfTrdKhde+vr7IsBAAAAAAAumKTfQWS47povad0/X65LTnXo04GX6auPtKvhWHjOWRzc3kFkf1dPc+CYyh56RP286RHfMxjv+rG+Nyjacknmfq3vIZLtXUQ11b9T3rXXSZLjtDD7dt32a4xWGjHqan7yHgAAAAAAXFS6HSDqn+7VTxaN1PAvfaQ//nmoHllerzNn4n//kP0Fz3bW4I39RdGyvBBakhlAsjLWd1pXUpeXRad50rosm3xLiUaMutqy1mcvsPb2HxDXfg8fPKDtWyvNfRhBrsKiYl5SDQAAAAAALqhuTTGTpDFjRuvPvm9qr3eBzl5xn0aPOjctrLuMd/5YR/asfWGNqt/bEfE9Q1Njg8pXP63ToXb7ogiHDx4wR/PI9j4gO2PZMyuXR6xjFe9+FZ7GtqumWgpPYyspnWb/CgAAAAAAwHnX7RFEY68Zrb8rmyuPx6OzZ89qxcpndOjwn+xfu+DsPzcPAAAAAACAc7o9gqjp6DFt/u0W1dbW6ZVf/PKiDA4BAAAAAADAXbcDRKfa2/X6G5v1zH88p23v7LQvBgAAAAAAwEWu21PMAAAAAAAA8PnW7RFEAAAAAAAA+HwjQAQAAAAAAJDiCBABAAAAAACkOAJEAAAAAAAAKY4AEQAAAAAAQIr70qAr/P9s/zBeJaXTNOu+b6r2g/d15swZ++Kk9dZ2L3Y+n09/v+h7+vo9s1U6fYZKp89QyW3T1Nraoim33a7pX7v7vOVJYVGxHv7uY/pUn+rwgf32xZ8rWdk5+u5jP1CfSy45b2mZt2CRrr5mjGp31Wj2nLndLjujbngu65t0Gno7HxJJZ0+kp6d1p9+Zt2CRbrx5sqp2bLcvOu+6k44vqsKiYj34dw9r/x/3qv8AX6+2g0T0dpu0SqR9JquwqFjfeeS7OtF2XMeONtkXRxVPnzB7zlx95aulCbezrOwc/f3Cx3TyZDDh40o1JaXTNO+Rhfqrr5Ro357duqRPH/O6JOOKIfrKV0tj9nXns173tp5Mi1v9tfbZGUMye6yudqc9XmjzFizSrPu/qTHjcnX1NWPMfOtOmmbPmasH/uY7EdfW0bZhtAXjetzeJqL1Vck6H/10KouVv4meK4x7pXF5+V3atVt7d7q/Muq7Udfsde7UyZMR9de+7POuJ/tZ9IxuBYhumvRlBU+cUPV7O+yLuqW3tpss681+b7rssst0w8RJevutzfqP1U9r8xuv67cVG3XsaJNqd9Vo+9ZKxw6tN/z17G/o1MmghmT6u3Rusbh1iueL9Wbw1MmTOnXypLa8WXFeO50bb56sj0+fVu2umh4pO6NuNH3YkHQ6ejsfEklnT6Snp428erSuzM5R9c5340qDVdWO7Resvtt1Jx1uErloutDt38nQrCs1Zlyufl9dpabGhl5tB9FcyL4pkfaZrGNHm7Tlzd+YdcSe3mji6RPyr5ugfv28Cdet/gN8mnDDjdq3Z3fM+tubEsmPCyErO0dfu3uWfrn2Ja19YY1OnTypmbPu06WXpumHS/9Jtbtq4urrzme97m09mRa3+mvtsz19+/ZYXbW3x8+LktJpGjHqaq3818e1bctbEfkWb5rs5yyfz6ecq0Zo1f/6sTa/8bqG+P26vniia6Bg3oJFyru2QKtXLtdrv3hJm994XX0uuURXDMnU0Q8bY/ZVyTof/XQqs+ev/Xol0XPF0KwrNerqa9Tf5+sStHRr7073V1U7tmvzG6/rvXe2aXx+gXkfuOXNCp06eVLzFizSiJGjutTHb367TH/55C89Xg/Pt57sZz9P7P3UxSTpKWY+n0/DR4xUIHDUvqhbemu7yfL5fPKmp180x3M+ZGXnyJuerv9++y0NGjxYhUXF9q8AAIAvkEz/UElSc+CY+VnGFUPU+lGL5VtA7/L7hynU3q5gMGhflLRgMKhfrVtr/r2nvlYej0fe/gMivqdw0MCbnq7//a+Pq6mxwfy8smKTKis2RXwXOKuzajzyJ5VOnyGfz2dfHCGZ+6uS0mnK9PtVvvrpLvVx44b1Kp44KeZ+gUT1GTW24Kz9QzclpdM0fcZM8+/Ojk69+vLzqqmuUlZ2jsoeekT9vOmSpOr3dmjtC2ukcJCl7OFHNSTTb657+OABPbNyuRRlu82BY/p22Txt37ZFU26dqn7edLU0B1T+06fME4d9v7Lse/acucq4Yojq63aZ27cel928BYs0YtTVUvj4tm+t1Kz7HlCaJ02SzH1Lck1PSek0FU+cpMDRo8q/rlAbN6xXZcUmzZ4zV0U33iRJOh1q79LQZcmnqp3vdDkJGdu17n/fnnpNKCpWP296RFlYt2UcozW/YykpnabcvAI9s3K55i1YpNaPWiLyzP5ZVnaOvl02T6//6peafEuJmYeSzPTby8maB8axWtNz+OABvfjzcjMN9vTZ64xRrvbPrdux5qu1PKzry5LXRhka24mWf9bttTQH1NHRqWNNjeYxGWUXDAYd64KkLnXdmmZ73bCXr71OFRYVR9Td6vd26I1fvxaxjXjSac3PluaAAkePKr1/f8e8sKcznjKvrfm98gsnmOkw6osc0mBdlqx4yr225vcqKZ0mObR5ax2ylonfP0wZVwwx8yVa2q3txVqfY9URe38hW59lHOv1E29OqFzty43ja/jTn5R/XaFamgMaOHBwl37Q6eLdejyK0v6NfDfyMHD0qMaOz9OJE8f1/9a9onvuf0C1u2p0401/pTRPmqrf26GtlW+a24iWJ3LI/9oPapRz1VX6efkzCp06GVGW9u9azx9Ds7Ll8aRp4MDBevXl5zX5lhLXvu/Q/n1R67RTnjv1TU7fs5eNNW9i5YXBWse8/QfE3I5bvtj7f1nOCVsr3zTzY1BGhmM67Omz1wW3/FO4/UZrZ27nd6c2F09fEK0NWdc/fPCAJHXJFyt7uo3t2fty636c6uC43HxlXDFEkjRi1NVmud1Scpt5PG75oCjXBvbja/jTn3TZ5ZeZ3zPOR8b+jWO099NO5xrZ8supP4xWF532YQSu7PVQkuP5yVpXOjs69d6O/1Z+QaF+Xv6MmhobuqTfXi8TOW+6sddfg1PbNOqqcVwbN6zX3vq6iL7Z2K+9Llv7Xeu2jP0rXHfkUFfcysmJPc/c+nunc7/b9avTOcR6frWnSbbzzulQu97+7WbdNu1O13OWcQxHDh/q0k7s5e3E/h17m7LXb6c+yu1z+7WAfdv2tFiPJ1bfae9LjDrh1A8ax7flrc0Rafzzx3/WZZdf5ngtZM/PwqJi3fn1vzbbmNs2q3a+o/d3vhs1D53qfTL7kWTm7ze+XdalrhltLFp/ZGXs+9UX/0tfnzU7Ig+c2ntJjPsre92Swz2XlVN7sLLXHyPvCouKNXPWvVr/6isR9/C762rN87jb/Ygc2r69v3Rr33LpY+zXZfbvWfehOM/RVvb2Zm1H9mX2PtHg1IaM6z77edJaFm7psJ/XnNr2hRT3CKLZc+Zqyq1TtfLJZVo4v0wvPfesmpsDOrR/X0RjNJaNHjNWWdk5ysrO0T/801IdOXxIC+eXaeH8Mh0+eED1dbtibtdQPHGSylc/rWVLzp3877jrbincMB9asNjc77Ili/RhY6P21NdK4SdfaZ6+Gn3NWC2cX6aNG9ZrfF6+srJzzG0bZoefGCxbskgL55fpmZXLVVNdpc2/eV0tzQEtW7JIy59YKm//AVHT4/cP05BMv86c+bMWzi9TZTg4NHzESHPbzYGAbim5zX4ICZtQVKzy1U9r4fwyfdh4xIxeWztrI1+86ekqCd/0xpKbV2Cmp75ul4aPGClfnNHpZ1YuV/V7O3T44AEz/daOx8iz3XW1+sbcv4nYrpGelU8uU6bfr8VLf6iKjRvM9E2+pcT8bs7wEWadsZZrZcUmvfTcswqeaNPKJ5c5dhaz58zV+Lx8c/2VTy7T+Lz8iPyxluFLzz2rK7OHu+afvXyrdr6jK7Oz7V+TwnV29Jix5r5fe/UVc1laWpqm3DrVLNPNv3ldM++Z7VhfR44eo3176s3yDYVCZp0yOv5XX37eTN/JEyfsm5BipLOkdFpE26zYuEFjx+fZN+Eo3jL/qylfNcv4peee1ZSvlqqwqFg+n0+l02do829eN9Nw/KOPIvaRqHjLfeToa8xjDrW36xvfLlMwGNSRw4eUm1dgfvf6iTdLkt7f+a75mRJIu5todcSqpHRaRJ+1bctvzWWxynXq7XfqpeeeNeuPNz1ds+fMNddPS0tTev/+Wji/TMufWKrVK59U8ESbXnruWS1/YqnrCSxa+zf66ZVPLtPoMWMjnpz5hw3Tk49/T8ufWKpT7e1KS0vTmHG5evLx7+ml555VQWGRyh56xDwPWOu7nX1/y5Yskn/YMKWlnTsJ2828516zrJYtWaQ/HT5oLsvM9Kti4wY99uhDjhdfTtzqdDx9U7xlY+SNkRcz77k3YjvxiLYdex5ay8x+TigsKtagwYO1tfLNiO27pdet7za45Z9dtGOMJd6+wK0N2fv8+rpdETcaTpzyI55ztVMdHDHqatXX7dLC8PXEgsVLJMk1Tw3R9mc/vp/+rx9p+RNLdfjgAVW/t8OxDcR7rrHn1+662og6G60uuu3DXg+zsnM0aPBgbd9aaW7XYK8rTz7+PY0ZlxvRJ8Sql1bR6kZPsvYHxk2TtW826lDfvpeZeVv93g4VRxlRYK07GzesV0FhkdlmYpWTlf364KXnntWfP/447vOf2/Wr0zkkGiMoaOxr5/b/1p7dda7nLGsbcLoJ9PYfII/Ho7bWVvsiV9Gux9zOL26fW9nb68L5Zdq3p97+NVOsvtPel0TrB5saG7R+3VpN/vIUZWXnmNc7z//smS7XQiNHj5HH4+lyDji0f586Ojo0NvfcdePY3Dz186ab6xqjt/bW10XNQznUe6tE9mPlVtei9Udu2kOnVLFxg8bnXxv1HJTo/ZUvxiyW0KmT6ujo0KCMDPuiLvXH2t/XVFdpy28rzHZ3S8ltag4EzDYR634kVn/p1r7jvb6NVjcNifTDY3PzIq4VvF6vWafjaYsGaxsygkOh9nazfdrzKVo6aqqrXPupi0FcAaKs7ByNHjNW69etNSOo43Lz1dlxRsFg0Mxco3GNy81XR0eHQqdOdql0xkm8rbU15nbH5uapX790VWzcoKbGBgWDQYXa283jmnxLiXbVVJv7HTl6jNLTvWoOHDMblceTpldffE6S1Nbaqra2Ewqdin/uv32oa7T0KByUOnzwgLncaAwvrvmZGanM9PtdG7skTZ8xUytWlWvFqnIt+v7jrp3Hlrc2m/m2fWulOVzW3lkHg0FV7XwnokN3Y1zsGx3p3vo6eTwejRw9xv7VuI3NzVMoFNIbv37N/Gxr5Ztdtmukp6mxQc2BgD5sPGJekNbX7ZI3Pd3Mi/969t/NtO+tr1NnZ6c5PD4aX3gKozXvmhobtLuuNiJ/WpoD5vHWVFfpw8Yj8vuHmcsNxvaqdr5j1pHKik06HH6i7CTN4zGP9YP3f2ceR2dnZ0RbeH/nuwqFQuZJz6qmusocLm0EL4wng5NvKdHu2j+YedfU2KCNG9ZHrG+Ils7cvALtrqs1j6emukq7aqoj1ncTb5lv/s3r5nHWVFep+dgxjcvNN5cbx9LU2KAP3v+d+XmiEin3F39ebv69fWulBg0erKzsHO2przX/r3D+HDl8qEuHHm/ao3GrI3Zer9e8+Hl329vmscQq11011Wa+G/2D9UKls7PT8UYrGUZ+GIG0psYG7d+3N6Kcre1H4f1XbNygYDCoQ/v36cSJ42ZdtNd3O/v+gsGgKjZuUGdnp/2rJmNbwWBQ72572/zc2gfFK1adjibesjHyxlhu7RvjFW079jy0lpn9nDAuN19tx4+71lG7WH13vPkX7RijSaQvcGpDyfT5buI5VzvVwcMHD5jXPfV1u3Q61G5uwylPDfHsLxHxnGuc8mtr5ZvypqebfWm0uui2D6PcjYv8sbl56ujoiHi4aLDXFac+IVa9tHKrGz1pxKirNeWrpV2eRtv75mAwqOef/Tczb6NNnZKt7ry/812dOHFcgzIy4iong8/nU/HESRFtqKa6Su9uezvu85/b9WsijOvV9es+u8ncuGF91L7ojrvuVqi93TE4lKyaKNdjinJ+cfvcYNRta17+at1as3zsYvWd1r4knn6wprpKbceP63987W5N/vIUs41urXwzYnqU2znAyAujbfj9w1T7QY3SPH0/O8+0t6upsSFmHtrrvVUi+4lHtP4omprqKu2u/UPEw2yr3ri/iiZWf2/0h1+/577wvfhn7SjW/Uis/jJa+451fRtP3VSC/XClZXpoU/ge0/rdWG3RYG1DRv5a882aT/Gm42IVV4DI6cRrDO/1hQMxRTfeZAY1Mq4YouVPLJUkDR8x0oyWyjbHPdp2FW7kThdHsgRmjNFCkjQoI8MMTBlPAYxGLlvwyW7tC2sUam/XkmXnhlobMsJT1GSptG7p8fl8SvP0jejEBmVkyDdwkBYsXqIVq8r10ILFWr9ubdSnIhs3rDejkclEFO37XLGqXNNnzIyrgxuXmx+x7oLFS+QbOMi1w4uHPcimGFFvhYfqR3vvQWFRsX701GrzGPv1+2yKYTRuT4cCgaNx5Y+d2/bc1FRXaf2rr2jWfQ9oxapy12i3wh2VNSBqN2/BIrN8jeGLRnuMFoCMR3e3k0yZK1zuCqe9/KdPafiIkVqxqtx8SmhNcyL/SkrvcCynWOXeHDimjo4OyXKxNDY3T1nheeT2J2bqRtoN8daRyopN2vLWZi1YvEQ/emp11KdWBrdybWttTfgC3VoW0fbvD4+qXLJsufn9ohtvcg3w2BntwH7MbpzyPxrjaeSKGAH5ZEXrx6x6smy6K1qZNVkCMT6fT0Ozsl0v3J0k2ne75V+0Y4zGrc+O1RcY3Na3mj1nrq3/cW7D3TlXGxJ58NUT+zO41Vc7b/8B8nq9EQ++FixeooEDB7sGYAzp/Qe47sO4KTQutHPzCroEmg3x9AmJ1sueZj2+tLQ0Tb6lRFt+W+F4/WtXUjrNzNv7v/Wg62jJaBIpp2htwCmvEzn/JcJ6zZ8Itz5F3ThWp+sxRTm/uH1u5ZSXiYiWTrcytPeD69e9ouyc4dq/b69ZF5saG9R2/Hhc54BA4KiGjxiprOwcZVwxxLz5Hjl6jHIto2kUJQ/jkch+etPWyjeV6fc79vnJ3F8Z1z9ugQ+3clQc/X0wHCgfOz5P27dt6RKosbLfjyTbX8ZzfeuWJnvdTITP59Oi7z9u5oN1xG88bdGJU/9jLa/eSMf5FFeAyN5JFRYVK3PoUO2przUzwBgSv9Ay/M8pcybfUmJmaLTtyuHliNbl9pcp+sJPNIztZfqHqr095Bp8cvLMyuXauGG9ptw6VVnh6XHW0UGx0mMdwWTw+4eZwxcXzi9zHKbd09paW/VhY6M5TDjeYJMvHACzBqgWhofuWUdOJMqtMXR0dHZpOPEoDM/5Xb3ySXPI3unT7oEUq2gn/2ROxE7bMy6c3dRUV+mxRx/SyieXacqtUx07SMXYjjEn2Sij6vAv/sU6mSTKvp1YN1+GZMvcuv1gMKjlTyzVwvllUjjNz6xcHlE34/1XWfFGl3IyRCv3TP9QeTwe8+/6ul3KzSuI+kQq2bRbxVtHKis2aeH8Mr368vOaOete1yCNIVr9aG8PJXShbS2LaP1aIHA0og80/tmHifcUp/wflJER9abJSMuRw4dU9vCjXcquO+JtMz1ZNt0Vq8z21NdqaFa2cgvOzf13GrXhJJm+2y3/Yh2jG6c+21wWpS+wSktLi9rnr31hTcQxuT0QSvZcnaye3F+0+moVOnVSbW0nIq4RF8boMwztp05G3YcxqvO662+QNz29yxQSq2h9QjL1MhlOfZMczrPGaImpt9/p2vcbSsLv4jDK9KXnno06WtJNIuUUrQ25pTGR81+82pIInq99YU3U0UNBh+nksbhdjxnczi9unxvc8jJebn2nYpShtR+8peQ2tbWdMF8bYjCmR8U6BxijZG68ebI6Os5o3956HWtq1HUTblCap69ZJ2LlYSzx7qe3NTU2aMtbmzXl1qnqP+Czetmd+6toU9GcBloY4unvJ99Sog8bj0SdlirbOa67/WWs69t462a8fJb3Ohl5YB/xG6stOonW/wQCR3s8HedbXAGiQOCoMv1+ZWXnyBd+N4jOnlVz4JgZSXaKgNozpyT805XGtIxo27WPEMrKztHMe2abQ42NIIwRKLrjrrs1JNNvRonto4WMyh1weBJl1dbaap5c7UGoWOlxiibuqa9V5tChMW/cetKh/fvk8aSZ72qy8oWjqNZ3WhiM4XL2iyz7/N7Wj1oiOquZ99wbNXq8t75OXq834njuuOtueTxpjp1aLPZ8HhueihgP4+RvBAEVrltTbp2a1BMG4yLZ2rleP/Fm84VldiWl08zOMHTqpEKhkLnMeGpoMPLL/o4be102TjyG+rpdEe8VyMrOiXiZXDyMfLLOKy4sKtaV2cPtX3UUb5lb860k/EsNWyvfVFZ2jr754N+a34sW2I1HvOU+JNNvDus2+qT9+/ZGDKX1pqereOIk1/oSK+1GP2IM/TaOwxCtjlh9/Z7ZZhk3B46pMzzSKRZ7/TDSeaypsVdOWHvr61yfqPWGttZWDRw4OKIciydOsn9NCi974MHvmHUw1vkhnr7PrU7Ho7tlM2/BIse+PVGxysxowxMn3RL3sSnOvjve/It1jG7i7QvcGMPT4+3zo4l2ru4NPb0/e311OtcEg0Eda2qM6xd+nETbhzGq866Z9+jI4UOOAXuFr8Oi9Qnx1Mt4zVuwyBzxaud0bigsKtb4/Gu7jMA4fPCAXn35+ZhBIvuD1nG5+VGD4W4SKSenNlRYVKybv/wVxzQ6nft7gnFtan0/zPQZM81jcjJ7ztyYowSMUSD271nPzQZflOsxt/NLev8Bjp/bOeXl1++Z7Xrs8fadcilDez9YUjpNw0eM1Jp//6n279sbkc/v73xXHR2dmnbn1/TB76tczwHGPeK1BRO0/497pXB7HH7VCClchtHyMF7x7Od8qazYpOZAQGPG5ZqfxXt/5aSyYpNC7e1dAhcl4XeUWWfKWMXq741rhWdWLleovT3ie9HuR7rTX8ZzfRtP3UyEfXBHVvhVL4rSRuPh1P8Y7W5vfV2Pp+N8iytAZMypW7B4iRYv/aH27amPeEKxft0ryvT7zaFbRqcaDM93NIas5uYVRETtom030z9U/fql6/5vPWgOYdvy1mYz8m8MczeW9+17mVqaA+a27aOF3BqnHIbnGkPtjMJfsHiJZs+ZGzM9fv8wM1hkqAnPSTWO021IXU8KBoN6cc3PND4v39xnPPudfEtJ1HnExsnHmPNpDO3/sPFIRPTYOLka+2xqbFD56qcjjmf4iJFJv63dCJgY+y+eOCli/8ZF44LFSxwv1Na+sEa762ojhnluCf/aQTKMztU4Hnu9sGprbdXU2+/UilXlWrJsuY4cPmTut7OzU+2nTpl5ND4v33x3lZW9Hi5e+kN1dHzWHisrNmnzb16PaDsfn/k4YhvxWPvCGvMFqCtWlat0+gzt3d21/TiJt8wDR49q8dIfasWqck29/U5zznPo1EkNHZYVsa713UDJiKfcW5oDum5CsVk+Idu7CpoaGxQKD7O1B+4MsdIeDA/rLSgs0opV5Sp76BH93vKENlodsQoGT0SU8fZtW7o86XVirx/GPqI9VbX2t/aLZjun9r9+3VozTStiTEnrrprqKvPGakW4fdjPWYZgMKj0/v3Ntjvl1qmObc4Qq+9TlDqtOPqmZMrGkN5/gNI8fSOmXScrVpkFwzeTgwYNdL0BkUN6Y/XdipF/VrGOMZp4+oJojL4onj7fyp4fyZ6rk9XT+7PXV7dzjXUKv7FPp/rvJNY+6ut26fJ+3qj1vib8gyPWc6a1T4inXvYEp3PDrPseMH9JyM563G75Ze1vV6wq19CsbMe+Lh6JlJO9Dc267wF9fPq0Yxqdzv09IRieiu5NTzf3lV84QaFTJxM6Z9k1NTboB/947uWz1rwonjipy3k/2vWY2/nlw8YGx8/t+eOUl1dmD+/yPUO8fafBXobWfrCwqFhTb7/TnLb5xq9fkzc93awPxjlAZ8863ldZ1dft0qVpl5rfaw4c06Vpl5oPF6LlYSJi7ceJ/Xqlp6xf94pOhyLfmRvP/ZWbZ1aeuxaw1scpt07V6pVPOvYditHfl5ROU0FhkRmYXr/uFY3PyzeDRtHuR7rTX8Z7fRutbibK2hesCF9zG4EptzbqVl+snPqfKeEXexvlHCsd3emneltCP3N/PpXYfuoR+CLLivFTlRcLYxiw0w3rbIef9ARShc/h52nPp8KiYhXfNEk/e+Yn9kXoZRe67FNZsteKya4HXGwuVP/DNd8X0+flfgS9K64RRBeC02gcABdOSek0XZk93PFJrTEk+PMwbBL4IqqpriI4dIHccdfd8nq9MZ+ko2cZw/XdXk7txliPa0wgOW5TIwF8MVyUASLjZjORuYAAepZ16uWK8JBl+0/u+sLvtFoSno51Pp9eAcCFYP+VsvF5+RHDytH75i1YpAWLl2h3XW3M805Wdo7+5cdPmeVlrOc0EhaAO+Oa7/5vPRj3r+wB+Py5aKeYAQAAAAAA4Py4KEcQAQAAAAAA4PwhQAQAAAAAAJDiCBABAAAAAACkOAJEAAAAAAAAKY4AEQAAAAAAQIr70qAr/P9s//Dzyufz6e8XfU+l0+/Uvj27derkSXNZYVGxHvy7h7X/j3sjPj+fZs+Zq+lfu1u1H7yvM2fO2BcrKztHf7/wMZ08GdSxo032xUhQYVGxvvPId3Wi7bhjfhr1xXNZXx0+sN++uMeUlE7TrPu+6Vru+OLKys7Rdx/7gfpcckmv1rHzIVZ7uhBi9am0vd4xb8EizbznXrW2tkTUBc5hAAAAn29fuBFEHR2dUp8+uqXkNvuiC27tC2u0/ImlCgaDUvjmZt6CRfavoYfUVFfpB4v/QTXVVVL4BnfJEz9WVnaO/asXrXkLFmn2nLn2j7+wPu9twl7Hmhob9IN/fFSVFZvsX73o2cvC3p4uBvY+9UK1F3tepYKPT4c0+ZYS+8c9wt6OAAAAcH584QJEHk+aDv7xjxqff60Ki4rtiwEAQDcdO9akTL9fJaXT7IsAAADwOdVn1NiCs/YP3cxbsEgjRl0tSTodalf56qcVOnVSZQ8/qiGZ/ojPmxobNHvOXGVcMUTPrFwuhaf0lD38qKp2vqPKik3Kys5R2UOPqJ83XZJU/d4OrX1hjRSeGjB9xkxJUmdHp159+fmYT66t2/f7h2n4iJEq/+lTCgaDKiwq1p1f/2v9vPwZNTU2mN81jtu6j5LSaSqeOCli3Zmz7tX6V19RTXWVsrJz9O2yeXr9V790PKbZc+aq6MabJFt+WLf7jW+XmXkpSRs3rNfe+jp9u2yeanfV6Mab/kppnrSI9RUewu+UZ0Z6AkePauz4PJ04cdw8fqtY6+/bU68JRcXq503vku/2PNu+tVL5BYVmPti33dIciDgGa74cPnhAktT6UUtCZW6vQwrXS0lmPTPq3fp1r5jlNCgjw9y2wvt/8eflMdNsZ20DRvpGjh4TUbdkOYZnVi43y7225vfmzZQ9b+zbfWnNz3T/3L/pUj8P7d/n2t6M/QSOHlX+dYVSOJ1Gvij8ZH7WfQ8ozZMmOZS/sV37enbWspSt7c6eM1dDs7Ll8aRp4MDBjvnp1EZm3nNvlzbh1E9Y02wcd23N75VfOME8fmNdu3kLFkXUudlz5kb0E9Y2ev3EmyPqjDWN9nzcuGG9JLnWMaO+GvVCkplW63YVRzuxsm/PyJtbSm4zt2Fs394HKpwOa99msNZH2fqn13/1S7MeWtuONb1DMv2Obcmp3I1jMSTS/w7KyFDxxEmu7cVYHq1N2Fn7IVnqkr3OG5875ZW97tnbpvX4jH3Zj8utP3Tqq2PlfbT27VSX7cdvZ7SjQOCoptw61SxHp3OjWzqc2t7U2+9U3R9qIh7u2PMFAAAAvSfudxAZN+E/XPpP2vzG6+p3uVfHjjUpe/hV+uSTT7Tqf/1Y772zTQXX36CBgwardleN8q+boH79vKrasV2SdNlll+mGiZPU9GGDDh/Yr2/+f3+rI386rJU/fkLvvbNNnr591XjkTyopnaYpt07V6pXL9dovXtKn+lS3lNwW8z0S1u1ve2uzbpg4SZn+YardVaOhWVdqzLhc/b66Spf06aOyhx9VqL3dTM+n+lR33Pk17duzW60tLbph4s1qaW7WsaNNmnLb7bpqxCh1dnSodleNrr5mrLKyr1JlxcYux2Nc9K7818e1Yf3/1dBhV+r64omq2rFdI68erSuzc1S9811t2/KWMq4Yoo4zZ/TDpf+kwwf2q/8An268ebIG+AbqJ8uXaetbm1Vw/Q26+pqxqtqx3bxR3vLWZv3H6qdV94cP9NXb79DJk0GdON6qGyZOUnr//vrJ8mV6q2JTl2OLZ/3hV41Q+eqn9dovXtK4vHyNz7tWtR+8r8suu0xlDz+qI4cPaeWPn9DmN17XV2+froyMIar7wwc6drRJN9w0SYcPHTS3ffNf3aIznR06fGB/l3xJH9BfxTdN0tEPG1W7qybuMj9z5oxyrhqhq0aMMvPkK7feJm96uvneqa98tVT1dbvU2tKiCTfcqH17dmvHf29VS3NAV40YqX/7yVN6q+INs764pdmefyWl0zRi1NVmGj49+6laAsc0aHCGWbeM91tZ6/7Iq0er8Pob1KdPH7O+FRYVq2DCDarasd1xux82/ElvVWzSmHG5OvDHffrJih/p2NEmjc8vcG1vxn4ajpxrUy3NAd08+Sv6VJ/q8IH95o32L9e+qBf+s1x1f/hAgwYNVnPgaETZvvfONt385Smu72aaPWeuxuflm2VV94cPNO3Ou8z37ORfN0Gjrr5Gv37tVb303LNd3kVSWFSsmyZ/Wf/2k6f02i9eUtvx4zodCumtije6tAmjzu6uqzXr3dBhV+rLJV9V7QfvS5JumDhJY8bl6devvaoX/rNcLc0B3THj647vykkf0F+5+QVmnf7q7dM1cOBgNX3YqGNHm3TTpC8reOKEqt/boVtKbtPLz/+nXvvFS/rLJ39R8cSbtW/Pbl3Sp4/+5ze+pW1v/9as65f0uSRqHTP6vPzrJig3v0Dv/Pfb+o/VT+svn/xFk/5qivkul1jtxM6+vbxrC3XHjK/r6IeNWvnjJyKOu/lYkybcMFFnOs6Y5Trlttt19tOz2vDauojtVu3Y3qUs+g/wme3J3l/s2L5VX7n1Nt1ya6lZDuPy8jX8qpFm/29Pm7VvtOo4cybu/ndYdo6uzM7Ru9u2OLaXWG3Czt4PtTQH1KdPH50KnlB+wQT9x+r/rQ3r/68yrhhi1iOnvtzOfhxD/H7d/j/uUlqaRz9c+k9qaQ7ohok3m3XWfhzW/lDhOh9v3hvBIaf23fZRi2NdDsR4f9CNN0/Wx6dP6/X1/1d51xZqfN61qtqxPaKOxEpH9Xs7VFhUrCtzrlLDnw7prr/+n9r29m/161++2qUdAQAA4PyIa4pZYVGxBg0erPXrXjE/27hhvZoaG1RTXaVfrVsrSQoGgzpy+JD5RDsexneDwaDe3fa2JCk3r0C762rNJ8vv73xXHR2dGjl6TMS60QSDQVVs3KDRY8Z2eY/ByNFj5PF4ItLz/s53FQqFNDY3T02NDWo7flyDMjIkSf0HDFDtBzXmsY7LzdexpsYuo3N8Pp+Gjxipqp3vmMu2Vr4pb3p6l2Nw09nZqYqNGxQMBhUMBlW18x1509Pl8/k0NjdPoVBI7+98Vwq/32T/vr0al5tvrm/dt1086295a7OZ79u3Vsrj8cjbf4CZZ1sr3zS/u37dKzp9ut38u7Jik/nkuamxQc2BgPz+YY75UlmxyRwdoQTLfE99rdI8feXz+ZTpH6rmQEBtbSeU6R+qrOwcedPTtbe+zr6aK7c0O/F6veayd7e97ZrXdi3NAb3483Lz7+1bKzVo8GCzXsS73VjtraU5oDd+/Zr53Q8bj8jvHyZJmnxLiXbX/sF8st/U2KCNG9Z3KVuj3uXmFZjbNRhlac2zpsYG7a6rjfj+h41HuowaskrzeJTpHypJ+uD933UZRWIw6qyRJoXblMfjiagbm3/zurm/muoqNR87FlGvDUa98PYfIG//AQq1t6u5OaBBGRny+XwampWtPfW1kqT/evbfzePaW1+nzs5O85glmfna1NigD97/nfl5LIcPHjDbyfs739WJE+f6mnjaiRPr9urrdul0qN0sS+txG+VaPHGSfD6fub/6ul22LcbPqAdGe7eWe33dLrPvckqbW9+YbP/rJlqbsPL5fCqeOCmibtdUV5nt8fln/83c55762qj9hBPrceypr9Xp0yFt31opSTq0f59C7e1mmuPpD+PN+3jad7J1WeHzwKDBgx2ndMdKx/atlRo9ZqxmfeNbCrW3xxy5BAAAgN4VV4BoUEaGOjo6FDrl/Otf8xYs0opV5VqxqjxiCH4sxrDxFavKtej7j5s3Et70dBXdeJO5zSXLluvK7Gzz4jleNdVVajt+XDPvuTfic6f0BINBhdrbzQvl1o9alJtXoKzsHHk8ffXutrflTU/XmLG5ETeRVt7+A+T1ejV9xkzz2BcsXqKBAwdH3Fgmy+8fpiGZfi1Ztjwiv+MNyHVnfac8s/P5fFr0/cfNbRvTLrz9B8jj8aittdW+ihReL5Eybw4cU3q6VyNHj9G43HzV1+3SsaZGjcvNPxdQaG93DTh0R2XFJm15a7MWLF6iHz212vGGKF7NgWPq6OiQkthuMu3NyONA4Kh90bngxMBBWrB4ibnd6TNmmjeYVm5lGQgcdfy+k5rqKq1/9RXNuu8BrVhVbk67c+L3D1OovT0iGBA6dVIdHR2OdcPQ+lGL/SMpvK7CgaexuXlq/ahFH/y+Srl5BeZN66H9+6RwYPxHT60223G/fuemuAWDQZX/9CkNHzFSK1aV99jLid3yNhFtra1qazvh2k731teZwbWRo8eoo6PTDBh3V+tHLa75nmjfmEz/212x8r+kdJp57Pd/60GlpZ2bkpWM5sCxc8HJwDH7ooT7Q8XI+2jtW1K363JT+EFD6fQZSvf2Nz+PJx011VXav2+vsnOGRzywAQAAwIURV4CorbXV9Wmp8S6ChfPLtHB+marf22H/SlTPrFyuhfPLdOTwIZU9/KgkKdTero0b1pvbNP4l83Rx/bpXlOn367oJN5ifRUuPcQO9p75WgwYP1o03T1brRy3at7deofZ25RYUyONJc7ywD506qba2E3rpuWcjjvuxRx+KOpoiXoHAUR0+eKBLvsT7foburm8d5SJJmf6hSvN4JNu7gYztWkc+pKWlRdzcGDcPsgTn4i1zY4TBdRNuUMYVQ7S3vk576ms1NCtbw4ePdL1R6gmVFZu0cH6ZXn35ec2cdW/MYI6bTP9QecJ5pwS2m2x7swdArdpaW/VhY6OWLVkUkffWX4cyRAvO2AM50dRUV+mxRx/SyieXacqtU12DRG6Bp46OTtcbeVlGJtoZo65y8wo0+pqx2lNfq731dfKmp2vEqKvV2XFGQcs7y1avfFIL55dp5ZPLIkbLBYNBLX9iqRbOL5MsU3C7K1o76QlG2xmXm5/wSJzuSLRvTKb/7a5oddt4h5DRRl567ll1dnbav9YjEu0PY4nVvnuiLhsjo75y21Tzs3jSkZWdo+EjRioUCl2UvzwKAACQauIKEB3av08dHR0RI3Gmz5ipMePGR4xKMKYRGAKBo8r0+80pBHfcdbf5kkyfz6cHHvyOeeNnHdlQX7dLU26d2mXqgcIX6v/y46cclzlpamzQlrc2K/+6QvOJr1N6SkqnKdPvN6egHNq/T+3tIV1bMMF8Wl1ft0vXFkxwHaESDAZ1rKlRpdNndLmh7Ql76+u69asx3VnfyBfrRfzkW0qUdum5IIf96XtWdo4y/efK2pgCYUxtkaTrJ95s1gXFKHMn9XW7NOqaa9TRcUZNjQ3mqI/hV43oldEFkvT1e2abgZvmwDF1hkcANQeOKS0tTWNz86TwyJOCwqKIdYdk+nX9xJulcN0vnT5D+/ftVVNjg+t27eyjgOztLZb6ul0qKCwy95WVnaPpM2bq0P598njSdMddd9tX6cIIsFjLKis7R1NunRr3VKWS0mlmHQydOqlQKGT/imlvfZ28Xm/Esd1x193yeNLMMpcUUbeMtmydDmm1p75WgwdnyJs+QIf27wsHTVo14YYbzTTYR8yNzc0zRxBlZefomw/+rbm9ngpIxtNOesL2rZW6Zsw4jR4z1jWPelqifWMy/W93OdXtwqJi3fzlr3QZyTYuN79bI4hiSbQ/jCZa+3ary77waNDZc+Zavu0uGJ7SPXLUNWY7UYx0+Hw+fWPu36hq5zt6cc3PNHrMWNfAeGFRsf7lyf/tuhwAAAA9I64AUTA8pcKbnm4OFc8vnKDA0SZV7XzHnDaweOkP1dHx2VPVyopNag4EzKHtffteppbmgLnN9P79zelOU26dqhfX/EzBYFCVFZu0u642Yki8MQUtGfb3eDilx/pLLMZ3jjU16kzHGfNGtK21VZf380a9EV77whqF2tsjpnG5PZHdWvmmMv1+rYgxzcbQ1Nig9evWaurtd5rbjmdKkqE76xvrFhQWmet+2HjEHFVhTDO4/1sPasWqcpU99EjEjb/x/h0jX3LzCiLKJNEy31tfJ509q/1/3CuFy6uz40xEedkZUw4XLF7iWibRBIMnzPQtWLxE27dtUU11lRmENNpB6fQZ2rs78h1ILc0BXTehWCvC0yxC7e3mr1K5bVfhm/mCwiL96KnVGjl6TNT2FktlxSZt/s3rEfv6+MzHCgaDenHNzzQ+L9/M+xVR6uTaF9ZElNWCxUu05a3NcY9uaGttNevgkmXLdeTwIXNde5toamxQ+eqnI47N+stHhsDRo1q89IdasapcU2+/U+vXrXUNIhzav09nOs5EjJ45dfKkdPasGQg1pl0Z9bV44iSzrodOndTQYVkRx2PU7+7WsVjtpCcY6W87ftw1j+RQFt2VSN+YbP9rbS/x9Gt29ro9674H9PHp0xF5sWJVuYZmZUeMIOrpvEq0P4wmWvuOVpcTVVNdpV011RGfRUvHN759bsTS+zvfVVNjg7Zv26JZ9z2gwqLibrcjAAAAJCehn7kHrIxpONafzY6Xz+Hn6oFEfdHrUW+krze2CQAAAODzL64RRICdMU0q1igEN3fcdbe8Xm9CvzYGpJreaCfGVKOeejk1AAAAgC8GRhAhLsaoA+v7UA4fPBD3C65nz5kb8Ytbp0PtEVP6gGR80UbD9GY7KSwq1qz7HlBn55ke2yYAAACALw4CRAAAAAAAACmOKWYAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOIIEAEAAAAAAKQ4AkQAAAAAAAApjgARAAAAAABAiiNABAAAAAAAkOL6jBpbcNb+oZus7ByVPfSI+nnTzc+q39uhtS+sMf+ePWeuim68yfz7dKhd5aufVlNjgySpsKhYs+57QGmeNPM7GzesV2XFJvPveQsWacSoq82/W5oDKv/pUwoGg1Ic++A4EztOAAAAAACQ2hIKEH3pS5cq0+9Xn0s+G3gUOnUyItDg8/nk7T/A/Pvsp5+qORDQJ5/8RZLk6dtXGVcMUZ8+fczvnDh+XKdPh8y/B2dcocsuv9z8+y+dnWppbtbZs59KceyD40zsOAEAAAAAQGpLKEAEAAAAAACALx7eQQQAAAAAAJDiCBABAAAAAACkOAJEAAAAAAAAKY4AEQAAAAAAQIojQAQAAAAAAJDiCBABAAAAAACkOAJEAAAAAAAAKY4AEQAAAAAAQIojQAQAAAAAAJDiCBABAAAAAACkOAJEAAAAAAAAKY4AEQAAAAAAQIojQAQAAAAAAJDiCBABAAAAAACkOAJEAAAAAAAAKY4AEQAAAAAAQIr7/wGHZSgOLf8vaQAAAABJRU5ErkJggg=="}}, "cell_type": "markdown", "id": "a8dcd130", "metadata": {}, "source": ["![image.png](attachment:image.png)"]}, {"cell_type": "markdown", "id": "1a086c45", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}