{"cells": [{"cell_type": "markdown", "id": "ddfa9ae6-69fe-444a-b994-8c4c5970a7ec", "metadata": {}, "source": ["# **Project - Airline AI Assistant**\n", "\n", "## **Important Features of Airline AI Assistant**\n", "\n", "### ✈️ **Flight Availability**\n", "- Check available flights to a destination with:\n", "  - Airline name, departure time, price, and duration.\n", "- Alerts user if no flights are found.\n", "\n", "### 🛫 **Step-by-step Flight Booking**\n", "- Guides users through:\n", "  1. Selecting source and destination cities.\n", "  2. Choosing a flight option.\n", "  3. Providing passenger details (name, age).\n", "- Ensures source and destination are not the same.\n", "\n", "### 🌛 **Ticket Generation**\n", "- Creates a unique ticket file: `firstName_lastName_bookingNumber.txt`.\n", "- Ticket includes:\n", "  - Passenger details\n", "  - Flight details (airline, time, price, seat number)\n", "\n", "### 📊 **Generate Summary Report**\n", "- Summarizes all bookings into a single file: `summary_report.txt`.\n", "- Includes all flight and passenger details for review or administration.\n", "\n", "### 🪑 **Automated Seat Assignment**\n", "- Assigns a random but consistent seat number for each booking.\n", "- Ensures unique seats for each flight.\n", "\n", "### 💬 **Interactive Chat Interface**\n", "- Real-time conversation via Gradio.\n", "- Provides clear, polite responses based on user input.\n", "\n", "### 🛠️ **Modular Tool Support**\n", "- Integrated tools for:\n", "  - Checking flight availability\n", "  - Booking flights\n", "  - Generating reports\n", "- Easily extensible for future features.\n", "\n", "### 🛡️ **Error Handling**\n", "- Validates user inputs and prevents invalid bookings.\n", "- Graceful error messages for smooth user experience.\n", "\n", "---\n", "\n", "These features ensure a seamless, user-friendly experience while booking flights or managing ticket details!\n"]}, {"cell_type": "code", "execution_count": null, "id": "747e8786-9da8-4342-b6c9-f5f69c2e22ae", "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "import random\n", "from dotenv import load_dotenv\n", "import gradio as gr\n", "from openai import OpenAI\n", "\n", "load_dotenv()\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "\n", "MODEL = \"gpt-4o-mini\"  # or \"gpt-3.5-turbo\", etc.\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "0a521d84-d07c-49ab-a0df-d6451499ed97", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 1) System Prompt\n", "###############################################################################\n", "system_message = (\n", "    \"You are a helpful assistant for an Airline called FlightAI.\\n\\n\"\n", "    \"When the user wants to book a flight, follow these steps:\\n\"\n", "    \"1. Ask for the source city.\\n\"\n", "    \"2. Ask for the destination city (must be different from source).\\n\"\n", "    \"3. Call the function 'check_flight_availability' with the user's destination.\\n\"\n", "    \"   - If it returns an empty list, say: 'No flights to that city'.\\n\"\n", "    \"   - If it returns flights, list them EXACTLY, in a numbered list, showing airline, time, price, and duration.\\n\"\n", "    \"4. Wait for the user to pick one flight option by number.\\n\"\n", "    \"5. Then ask for passenger first name, last name, and age.\\n\"\n", "    \"6. Finally call 'book_flight' to confirm and show the user the real seat number and booking details.\\n\\n\"\n", "    \"You also have a tool 'generate_report' which summarizes ALL booked tickets in a single file.\\n\\n\"\n", "    \"IMPORTANT:\\n\"\n", "    \"- Always call 'check_flight_availability' if user mentions a new destination.\\n\"\n", "    \"- Do not invent flights or seat numbers. Use what the function calls return.\\n\"\n", "    \"- Source and destination cannot be the same.\\n\"\n", "    \"- Every time a flight is booked, produce a new ticket file named firstName_lastName_bookingNumber.txt.\\n\"\n", "    \"- If a city is not in flight_availability, say 'No flights found for that city'.\\n\"\n", "    \"If the user wants all tickets summarized, call 'generate_report' with no arguments (the function has none).\\n\"\n", "    \"If you don't know something, say so.\\n\"\n", "    \"Keep answers short and courteous.\\n\"\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "61a2a15d-b559-4844-b377-6bd5cb4949f6", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 2) Flight Availability with Price & Duration\n", "###############################################################################\n", "flight_availability = {\n", "    \"london\": [\n", "        {\n", "            \"airline\": \"AirlinesAI\",\n", "            \"time\": \"10:00 AM\",\n", "            \"price\": \"$799\",\n", "            \"duration\": \"8 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"IndianAirlinesAI\",\n", "            \"time\": \"3:00 PM\",\n", "            \"price\": \"$899\",\n", "            \"duration\": \"8 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"AmericanAirlinesAI\",\n", "            \"time\": \"8:00 PM\",\n", "            \"price\": \"$999\",\n", "            \"duration\": \"8 hours\"\n", "        },\n", "    ],\n", "    \"paris\": [\n", "        {\n", "            \"airline\": \"EuropeanAirlinesAI\",\n", "            \"time\": \"11:00 AM\",\n", "            \"price\": \"$399\",\n", "            \"duration\": \"7 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"BudgetAirlines\",\n", "            \"time\": \"6:00 PM\",\n", "            \"price\": \"$2399\",\n", "            \"duration\": \"7 hours\"\n", "        },\n", "    ],\n", "    \"tokyo\": [\n", "        {\n", "            \"airline\": \"TokyoAirlinesAI\",\n", "            \"time\": \"12:00 PM\",\n", "            \"price\": \"$4000\",\n", "            \"duration\": \"5 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"FastFly\",\n", "            \"time\": \"7:00 PM\",\n", "            \"price\": \"$1400\",\n", "            \"duration\": \"5 hours\"\n", "        },\n", "    ],\n", "    \"berlin\": [\n", "        {\n", "            \"airline\": \"BerlinAirlinesAI\",\n", "            \"time\": \"9:00 AM\",\n", "            \"price\": \"$499\",\n", "            \"duration\": \"6 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"AmericanAirlinesAI\",\n", "            \"time\": \"4:00 PM\",\n", "            \"price\": \"$899\",\n", "            \"duration\": \"6 hours\"\n", "        },\n", "    ],\n", "    \"nagpur\": [\n", "        {\n", "            \"airline\": \"IndianAirlinesAI\",\n", "            \"time\": \"8:00 AM\",\n", "            \"price\": \"$1000\",\n", "            \"duration\": \"10 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"JetAirlines\",\n", "            \"time\": \"2:00 PM\",\n", "            \"price\": \"$1500\",\n", "            \"duration\": \"10 hours\"\n", "        },\n", "        {\n", "            \"airline\": \"AirlinesAI\",\n", "            \"time\": \"10:00 PM\",\n", "            \"price\": \"$800\",\n", "            \"duration\": \"10 hours\"\n", "        },\n", "    ],\n", "}\n"]}, {"cell_type": "code", "execution_count": null, "id": "0696acb1-0b05-4dc2-80d5-771be04f1fb2", "metadata": {}, "outputs": [], "source": ["# A global list of flight bookings\n", "flight_bookings = []\n"]}, {"cell_type": "code", "execution_count": null, "id": "80ca4e09-6287-4d3f-997d-fa6afbcf6c85", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 3) Helper Functions\n", "###############################################################################\n", "def generate_seat_numbers(seed_value):\n", "    random.seed(seed_value)\n", "    return [\n", "        f\"{random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ')}{random.randint(1, 99):02}\"\n", "        for _ in range(5)\n", "    ]\n", "\n", "def check_flight_availability(destination_city: str):\n", "    \"\"\"\n", "    Return the flights for a given city from 'flight_availability'.\n", "    If city not in dictionary, return an empty list.\n", "    \"\"\"\n", "    print(f\"[TOOL] check_flight_availability({destination_city})\")\n", "    city = destination_city.lower()\n", "    return flight_availability.get(city, [])\n", "\n", "def generate_ticket_file(booking_dict, booking_number):\n", "    \"\"\"\n", "    Create a text file: firstName_lastName_bookingNumber.txt\n", "    containing flight details.\n", "    \"\"\"\n", "    fname = booking_dict[\"first_name\"].replace(\" \", \"_\")\n", "    lname = booking_dict[\"last_name\"].replace(\" \", \"_\")\n", "    filename = f\"{fname}_{lname}_{booking_number}.txt\"\n", "\n", "    content = (\n", "        \"Flight Ticket\\n\"\n", "        \"=============\\n\"\n", "        f\"Booking #   : {booking_number}\\n\"\n", "        f\"Passenger   : {booking_dict['first_name']} {booking_dict['last_name']}, Age {booking_dict['age']}\\n\"\n", "        f\"Source      : {booking_dict['source']}\\n\"\n", "        f\"Destination : {booking_dict['destination']}\\n\"\n", "        f\"Airline     : {booking_dict['airline']}\\n\"\n", "        f\"Departure   : {booking_dict['time']}\\n\"\n", "        f\"Price       : {booking_dict['price']}\\n\"\n", "        f\"Duration    : {booking_dict['duration']}\\n\"\n", "        f\"Seat Number : {booking_dict['seat']}\\n\"\n", "    )\n", "    with open(filename, \"w\") as f:\n", "        f.write(content)\n", "\n", "    print(f\"[TOOL] Ticket file generated => {filename}\")\n", "    return filename\n", "\n", "def book_flight(source, destination, option_index, first_name, last_name, age):\n", "    \"\"\"\n", "    Book a flight using an option index for the chosen city.\n", "    - source != destination\n", "    - index is 1-based => we do pick = idx - 1\n", "    - create new booking record, seat assignment, & ticket file\n", "    \"\"\"\n", "    print(f\"[TOOL] book_flight({source=}, {destination=}, {option_index=})\")\n", "\n", "    if source.lower() == destination.lower():\n", "        return \"Error: source and destination must not be the same.\"\n", "\n", "    # Convert option index from string to integer\n", "    try:\n", "        idx = int(option_index)\n", "    except ValueError:\n", "        return \"Error: flight option number is not a valid integer.\"\n", "\n", "    flights = check_flight_availability(destination)\n", "    if not flights:\n", "        return f\"Error: No flights found for {destination.title()}.\"\n", "\n", "    pick = idx - 1\n", "    if pick < 0 or pick >= len(flights):\n", "        return f\"Error: Invalid flight option #{idx} for {destination.title()}.\"\n", "\n", "    chosen_flight = flights[pick]\n", "    airline   = chosen_flight[\"airline\"]\n", "    dep_time  = chosen_flight[\"time\"]\n", "    price     = chosen_flight[\"price\"]\n", "    duration  = chosen_flight[\"duration\"]\n", "\n", "    # Generate seat\n", "    seat_list = generate_seat_numbers(hash(destination + airline + str(len(flight_bookings))))\n", "    chosen_seat = seat_list[0]\n", "\n", "    new_booking = {\n", "        \"source\":      source.title(),\n", "        \"destination\": destination.title(),\n", "        \"airline\":     airline,\n", "        \"time\":        dep_time,\n", "        \"price\":       price,\n", "        \"duration\":    duration,\n", "        \"seat\":        chosen_seat,\n", "        \"first_name\":  first_name.title(),\n", "        \"last_name\":   last_name.title(),\n", "        \"age\":         age,\n", "    }\n", "    flight_bookings.append(new_booking)\n", "\n", "    booking_number  = len(flight_bookings)\n", "    ticket_filename = generate_ticket_file(new_booking, booking_number)\n", "\n", "    confirmation = (\n", "        f\"Booking #{booking_number} confirmed for {first_name.title()} {last_name.title()}. \"\n", "        f\"Flight from {source.title()} to {destination.title()} on {airline} at {dep_time}. \"\n", "        f\"Ticket saved to {ticket_filename}.\"\n", "    )\n", "    print(f\"[TOOL] {confirmation}\")\n", "    return confirmation\n", "\n", "def generate_report():\n", "    \"\"\"\n", "    Summarize ALL tickets in a single file called summary_report.txt.\n", "    \"\"\"\n", "    print(f\"[TOOL] generate_report called.\")\n", "\n", "    report_content = \"Flight Booking Summary Report\\n\"\n", "    report_content += \"=============================\\n\"\n", "\n", "    if not flight_bookings:\n", "        report_content += \"No bookings found.\\n\"\n", "    else:\n", "        for i, booking in enumerate(flight_bookings, start=1):\n", "            report_content += (\n", "                f\"Booking #   : {i}\\n\"\n", "                f\"Passenger   : {booking['first_name']} {booking['last_name']}, Age {booking['age']}\\n\"\n", "                f\"Source      : {booking['source']}\\n\"\n", "                f\"Destination : {booking['destination']}\\n\"\n", "                f\"Airline     : {booking['airline']}\\n\"\n", "                f\"Departure   : {booking['time']}\\n\"\n", "                f\"Price       : {booking['price']}\\n\"\n", "                f\"Duration    : {booking['duration']}\\n\"\n", "                f\"Seat Number : {booking['seat']}\\n\"\n", "                \"-------------------------\\n\"\n", "            )\n", "\n", "    filename = \"summary_report.txt\"\n", "    with open(filename, \"w\") as f:\n", "        f.write(report_content)\n", "\n", "    msg = f\"Summary report generated => {filename}\"\n", "    print(f\"[TOOL] {msg}\")\n", "    return msg\n"]}, {"cell_type": "code", "execution_count": null, "id": "39fb9008", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 4) <PERSON><PERSON> JSON <PERSON>\n", "###############################################################################\n", "price_function = {\n", "    \"name\": \"get_ticket_price\",\n", "    \"description\": \"Get the price of a return ticket for the city from the flight list data (not strictly needed now).\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"City name.\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "    },\n", "}\n", "\n", "availability_function = {\n", "    \"name\": \"check_flight_availability\",\n", "    \"description\": (\n", "        \"Check flight availability for the specified city. \"\n", "        \"Returns a list of {airline, time, price, duration}.\"\n", "    ),\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"City name to check in flight_availability dict.\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "    },\n", "}\n", "\n", "book_function = {\n", "    \"name\": \"book_flight\",\n", "    \"description\": (\n", "        \"Book a flight using an option index for the chosen city. \"\n", "        \"Generates a unique ticket file firstName_lastName_{bookingNumber}.txt each time.\"\n", "    ),\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"source\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"User's source city (must differ from destination).\",\n", "            },\n", "            \"destination\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"User's destination city.\",\n", "            },\n", "            \"option_index\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"1-based flight option number the user selected from check_flight_availability.\",\n", "            },\n", "            \"first_name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Passenger's first name.\",\n", "            },\n", "            \"last_name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Passenger's last name.\",\n", "            },\n", "            \"age\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Passenger's age.\",\n", "            },\n", "        },\n", "        \"required\": [\"source\", \"destination\", \"option_index\", \"first_name\", \"last_name\", \"age\"],\n", "    },\n", "}\n", "\n", "report_function = {\n", "    \"name\": \"generate_report\",\n", "    \"description\": (\n", "        \"Generates a summary report of ALL tickets in summary_report.txt.\"\n", "    ),\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "        },\n", "        \"required\": [],\n", "    },\n", "}\n", "\n", "tools = [\n", "    {\"type\": \"function\", \"function\": price_function},\n", "    {\"type\": \"function\", \"function\": availability_function},\n", "    {\"type\": \"function\", \"function\": book_function},\n", "    {\"type\": \"function\", \"function\": report_function},\n", "]\n"]}, {"cell_type": "code", "execution_count": null, "id": "1f003836", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 5) <PERSON><PERSON> Tool Calls\n", "###############################################################################\n", "def handle_tool_call(message):\n", "    \"\"\"\n", "    The LLM can request to call a function in 'tools'. We parse the JSON arguments\n", "    and run the Python function. Then we return a 'tool' message with the result.\n", "    \"\"\"\n", "    tool_call = message.tool_calls[0]\n", "    fn_name   = tool_call.function.name\n", "    args      = json.loads(tool_call.function.arguments)\n", "\n", "    if fn_name == \"get_ticket_price\":\n", "        city = args.get(\"destination_city\")\n", "        flights = check_flight_availability(city)\n", "        # In this code, we do not strictly store a single 'price' per city,\n", "        # but let's just return the flights with price or \"No flights\".\n", "        if not flights:\n", "            response_content = {\"destination_city\": city, \"price\": \"No flights found.\"}\n", "        else:\n", "            # Return the first flight's price or something\n", "            response_content = {\n", "                \"destination_city\": city,\n", "                \"price\": flights[0][\"price\"]\n", "            }\n", "\n", "    elif fn_name == \"check_flight_availability\":\n", "        city = args.get(\"destination_city\")\n", "        flights = check_flight_availability(city)\n", "        response_content = {\"destination_city\": city, \"availability\": flights}\n", "\n", "    elif fn_name == \"book_flight\":\n", "        src        = args.get(\"source\")\n", "        dest       = args.get(\"destination\")\n", "        idx        = args.get(\"option_index\")\n", "        first_name = args.get(\"first_name\")\n", "        last_name  = args.get(\"last_name\")\n", "        age        = args.get(\"age\")\n", "\n", "        confirmation = book_flight(src, dest, idx, first_name, last_name, age)\n", "        response_content = {\n", "            \"source\": src,\n", "            \"destination\": dest,\n", "            \"option_index\": idx,\n", "            \"first_name\": first_name,\n", "            \"last_name\":  last_name,\n", "            \"age\":        age,\n", "            \"confirmation\": confirmation\n", "        }\n", "\n", "    elif fn_name == \"generate_report\":\n", "        # No args needed\n", "        msg = generate_report()\n", "        response_content = {\"report\": msg}\n", "\n", "    else:\n", "        response_content = {\"error\": f\"Unknown tool: {fn_name}\"}\n", "\n", "    return {\n", "        \"role\": \"tool\",\n", "        \"content\": json.dumps(response_content),\n", "        \"tool_call_id\": tool_call.id,\n", "    }, args\n"]}, {"cell_type": "code", "execution_count": null, "id": "f6b34b32", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 6) Main Chat Function\n", "###############################################################################\n", "def chat(message, history):\n", "    \"\"\"\n", "    The main chat loop that handles the conversation with the user,\n", "    passing 'tools' definitions to the LLM for function calling.\n", "    \"\"\"\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    try:\n", "        response = openai.chat.completions.create(\n", "            model=MODEL,\n", "            messages=messages,\n", "            tools=tools\n", "        )\n", "\n", "        # If the LLM requests a function call, handle it\n", "        while response.choices[0].finish_reason == \"tool_calls\":\n", "            msg = response.choices[0].message\n", "            print(f\"[INFO] Tool call requested: {msg.tool_calls[0]}\")\n", "            tool_response, tool_args = handle_tool_call(msg)\n", "            print(f\"[INFO] Tool response: {tool_response}\")\n", "\n", "            # Add both the LLM's request and our tool response to the conversation\n", "            messages.append(msg)\n", "            messages.append(tool_response)\n", "\n", "            # Re-send updated conversation to get final or next step\n", "            response = openai.chat.completions.create(\n", "                model=MODEL,\n", "                messages=messages\n", "            )\n", "\n", "        # Return normal text response (finish_reason = \"stop\")\n", "        return response.choices[0].message.content\n", "\n", "    except Exception as e:\n", "        print(f\"[ERROR] {e}\")\n", "        return \"I'm sorry, something went wrong while processing your request.\""]}, {"cell_type": "code", "execution_count": null, "id": "cea4b097", "metadata": {}, "outputs": [], "source": ["###############################################################################\n", "# 7) Launch Gradio\n", "###############################################################################\n", "gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "0b39d5a6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}