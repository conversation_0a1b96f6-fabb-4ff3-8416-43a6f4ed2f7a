{"cells": [{"cell_type": "markdown", "id": "06cf3063-9f3e-4551-a0d5-f08d9cabb927", "metadata": {}, "source": ["# Conversation War between LLMs!!\n", "\n", "This code sets up a conversation between GPT(Connected via API) and llama3.2 (local) with different tones for both"]}, {"cell_type": "code", "execution_count": null, "id": "de23bb9e-37c5-4377-9a82-d7b6c648eeb6", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from IPython.display import Markdown, display, update_display\n", "import ollama"]}, {"cell_type": "code", "execution_count": null, "id": "1179b4c5-cd1f-4131-a876-4c9f3f38d2ba", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")"]}, {"cell_type": "code", "execution_count": null, "id": "797fe7b0-ad43-42d2-acf0-e4f309b112f0", "metadata": {}, "outputs": [], "source": ["# Connect to OpenAI, Anthropic\n", "\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "81be3a29-bd5d-4c37-bba2-386dba0bc88b", "metadata": {}, "outputs": [], "source": ["!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": null, "id": "bcb54183-45d3-4d08-b5b6-55e380dfdf1b", "metadata": {}, "outputs": [], "source": ["# Let's make a conversation between GPT-4o-mini and Claude-3-haiku\n", "# We're using cheap versions of models so the costs will be minimal\n", "\n", "gpt_model = \"gpt-4o-mini\"\n", "ollama_model = \"llama3.2\"\n", "\n", "gpt_system = \"You are a chatbot who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way.\"\n", "\n", "ollama_system = \"You are a very polite, courteous chatbot. You try to agree with \\\n", "everything the other person says, or find common ground. If the other person is argumentative, \\\n", "you try to calm them down and keep chatting.\"\n", "\n", "gpt_messages = [\"Hi there\"]\n", "ollama_messages = [\"Hi\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1df47dc7-b445-4852-b21b-59f0e6c2030f", "metadata": {}, "outputs": [], "source": ["def call_gpt():\n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt, ollama in zip(gpt_messages, ollama_messages):\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": ollama})\n", "    # print(messages)\n", "    completion = openai.chat.completions.create(\n", "        model=gpt_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "f204f514-6769-4455-a7ff-95ec69f98f4c", "metadata": {}, "outputs": [], "source": ["ollama_via_openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')"]}, {"cell_type": "code", "execution_count": null, "id": "e04b96ae-2b5e-44a1-aea3-7b12ec450db5", "metadata": {}, "outputs": [], "source": ["def call_ollama():\n", "    messages = [{\"role\": \"system\", \"content\": ollama_system}]\n", "    for gpt, ollama in zip(gpt_messages, ollama_messages):\n", "        messages.append({\"role\": \"assistant\", \"content\": ollama})\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    # print(messages)\n", "    completion = ollama_via_openai.chat.completions.create(\n", "        model=ollama_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "0275b97f-7f90-4696-bbf5-b6642bd53cbd", "metadata": {}, "outputs": [], "source": ["gpt_messages = [\"Hi there\"]\n", "ollama_messages = [\"Hi\"]\n", "\n", "print(f\"GPT:\\n{gpt_messages[0]}\\n\")\n", "print(f\"Ollama:\\n{ollama_messages[0]}\\n\")\n", "\n", "for i in range(5):\n", "    gpt_next = call_gpt()\n", "    print(f\"GPT:\")\n", "    display(Markdown(gpt_next))\n", "    print(f\"\\n\")\n", "    gpt_messages.append(gpt_next)\n", "    \n", "    ollama_next = call_ollama()\n", "    print(f\"Ollama:\")\n", "    display(Markdown(ollama_next))\n", "    print(f\"\\n\")\n", "    ollama_messages.append(ollama_next)"]}, {"cell_type": "code", "execution_count": null, "id": "c23224f6-7008-44ed-a57f-718975f4e291", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}