{"cells": [{"cell_type": "markdown", "id": "ddfa9ae6-69fe-444a-b994-8c4c5970a7ec", "metadata": {}, "source": ["# Project - Airline AI Assistant\n", "\n", "We'll now bring together what we've learned to make an AI Customer Support assistant for an Airline"]}, {"cell_type": "code", "execution_count": 16, "id": "8b50bbe2-c0b1-49c3-9a5c-1ba7efa2bcb4", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr\n", "from IPython.display import display, JSON"]}, {"cell_type": "code", "execution_count": 17, "id": "747e8786-9da8-4342-b6c9-f5f69c2e22ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n"]}], "source": ["# Initialization\n", "\n", "load_dotenv()\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "MODEL = \"gpt-4o-mini\"\n", "openai = OpenAI()\n", "\n", "# As an alternative, if you'd like to use Ollama instead of OpenAI\n", "# Check that <PERSON><PERSON><PERSON> is running for you locally (see week1/day2 exercise) then uncomment these next 2 lines\n", "# MODEL = \"llama3.2\"\n", "# openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n"]}, {"cell_type": "code", "execution_count": 18, "id": "0a521d84-d07c-49ab-a0df-d6451499ed97", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant for an Airline called FlightAI. \"\n", "system_message += \"Give short, courteous answers, no more than 1 sentence. \"\n", "system_message += \"Always be accurate. If you don't know the answer, say so.\""]}, {"cell_type": "code", "execution_count": 19, "id": "61a2a15d-b559-4844-b377-6bd5cb4949f6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7872\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7872/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["# This function looks rather simpler than the one from my video, because we're taking advantage of the latest Gradio updates\n", "\n", "def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    return response.choices[0].message.content\n", "\n", "gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "id": "36bedabf-a0a7-4985-ad8e-07ed6a55a3a4", "metadata": {}, "source": ["## Tools\n", "\n", "Tools are an incredibly powerful feature provided by the frontier LLMs.\n", "\n", "With tools, you can write a function, and have the LLM call that function as part of its response.\n", "\n", "Sounds almost spooky.. we're giving it the power to run code on our machine?\n", "\n", "Well, kinda."]}, {"cell_type": "code", "execution_count": 20, "id": "0696acb1-0b05-4dc2-80d5-771be04f1fb2", "metadata": {}, "outputs": [], "source": ["# Let's start by making a useful function\n", "\n", "ticket_prices = {\"london\": \"$799\", \"paris\": \"$899\", \"tokyo\": \"$1400\", \"berlin\": \"$499\"}\n", "\n", "def get_ticket_price(destination_city):\n", "    print(f\"Tool get_ticket_price called for {destination_city}\")\n", "    city = destination_city.lower()\n", "    return ticket_prices.get(city, \"Unknown\")\n", "\n", "def book_ticket(destination_city, price):\n", "    print(f\"Tool book_ticket for {destination_city} for {price}\")\n", "    list_price = get_ticket_price(destination_city)\n", "    if list_price != \"Unknown\":\n", "        list_amount = int(list_price.replace(\"$\", \"\"))\n", "        amount = int(price.replace(\"$\", \"\"))\n", "        if list_amount > amount:\n", "            return \"Booking Successful at a Discount!\"\n", "        else:\n", "            return \"Booking Successful\"\n", "    else:       \n", "        return \"Booking Failed: reason was that no list price was found for this destination\""]}, {"cell_type": "code", "execution_count": 21, "id": "80ca4e09-6287-4d3f-997d-fa6afbcf6c85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tool book_ticket for <PERSON><PERSON> for $388\n", "Tool get_ticket_price called for <PERSON><PERSON>\n"]}, {"data": {"text/plain": ["'Booking Failed: reason was that no list price was found for this destination'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["book_ticket(\"Berliner\", \"$388\")"]}, {"cell_type": "code", "execution_count": 22, "id": "4afceded-7178-4c05-8fa6-9f2085e6a344", "metadata": {}, "outputs": [], "source": ["# There's a particular dictionary structure that's required to describe our function:\n", "\n", "price_function = {\n", "    \"name\": \"get_ticket_price\",\n", "    \"description\": \"Get the price of a return ticket to the destination city. Call this whenever you need to know the ticket price, for example when a customer asks 'How much is a ticket to this city'\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to travel to\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}\n", "\n", "book_function = {\n", "    \"name\": \"book_ticket\",\n", "    \"description\": \"\"\"Get the success status of a function that can book a ticket using a city and a price. \n", "    Call this whenever you are asked to book a ticket, \n", "    for example when a customer asks 'Please can I book a ticket to Paris' or after you have asked \n", "    if they would like to book a ticket, for example, after you have supplied a ticket price. \n", "    If the customer negotiates and asks for a discount, use the agreed price, otherwise use the price that \n", "    matches the destination city. \n", "    It is really important that you confirm that the customer is happy to proceed with an agreed \n", "    booking after reading back the destination city and the agreed price.\"\"\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"destination_city\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The city that the customer wants to travel to\",\n", "            },\n", "            \"price\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The price that the customer has agreed to pay for the ticket\",\n", "            },\n", "        },\n", "        \"required\": [\"destination_city\", \"price\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    } \n", "}"]}, {"cell_type": "code", "execution_count": 23, "id": "bdca8679-935f-4e7f-97e6-e71a4d4f228c", "metadata": {}, "outputs": [], "source": ["# And this is included in a list of tools:\n", "\n", "tools = [\n", "    {\"type\": \"function\", \"function\": price_function},\n", "    {\"type\": \"function\", \"function\": book_function}\n", "]"]}, {"cell_type": "markdown", "id": "c3d3554f-b4e3-4ce7-af6f-68faa6dd2340", "metadata": {}, "source": ["## Getting OpenAI to use our Tool\n", "\n", "There's some fiddly stuff to allow OpenAI \"to call our tool\"\n", "\n", "What we actually do is give the LLM the opportunity to inform us that it wants us to run the tool.\n", "\n", "Here's how the new chat function looks:"]}, {"cell_type": "code", "execution_count": 24, "id": "ce9b0744-9c78-408d-b9df-9f6fd9ed78cf", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages, tools=tools)\n", "    \n", "    display(JSON(messages))\n", "    display(response)\n", "    \n", "    if response.choices[0].finish_reason==\"tool_calls\":\n", "        message = response.choices[0].message\n", "        messages.append(message)\n", "        messages.extend(list(map(handle_tool_call, message.tool_calls)))\n", "        response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 25, "id": "b0992986-ea09-4912-a076-8e5603ee631f", "metadata": {}, "outputs": [], "source": ["# We have to write that function handle_tool_call:\n", "\n", "def handle_tool_call(tool_call):\n", "    function = tool_call.function.name\n", "    arguments = json.loads(tool_call.function.arguments)\n", "    match function:\n", "        case 'get_ticket_price':\n", "            city = arguments.get('destination_city')\n", "            price = get_ticket_price(city)\n", "            return {\n", "                \"role\": \"tool\",\n", "                \"content\": json.dumps({\"destination_city\": city,\"price\": price}),\n", "                \"tool_call_id\": tool_call.id\n", "            }\n", "        case 'book_ticket':\n", "            city = arguments.get('destination_city')\n", "            price = arguments.get('price')\n", "            status = book_ticket(city, price)\n", "            return {\n", "                \"role\": \"tool\",\n", "                \"content\": json.dumps({\"destination_city\": city,\"price\": price, \"status\": status}),\n", "                \"tool_call_id\": tool_call.id\n", "            }\n", "    "]}, {"cell_type": "code", "execution_count": 26, "id": "f4be8a71-b19e-4c2f-80df-f59ff2661f14", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7873\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7873/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}, {"data": {"application/json": [{"content": "You are a helpful assistant for an Airline called FlightAI. Give short, courteous answers, no more than 1 sentence. Always be accurate. If you don't know the answer, say so.", "role": "system"}, {"content": "tickets to london and paris for $50 each please", "role": "user"}], "text/plain": ["<IPython.core.display.JSON object>"]}, "metadata": {"application/json": {"expanded": false, "root": "root"}}, "output_type": "display_data"}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-AtMTR6PDyoghY9BxBI88y03wrkyWT', choices=[Choice(finish_reason='tool_calls', index=0, logprobs=None, message=ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_62youPDgpaS0eXN4gru6NT7n', function=Function(arguments='{\"destination_city\": \"London\"}', name='get_ticket_price'), type='function'), ChatCompletionMessageToolCall(id='call_kvQK4Cdyk4b82rqtzkfJyoRh', function=Function(arguments='{\"destination_city\": \"Paris\"}', name='get_ticket_price'), type='function')]))], created=1737757793, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_72ed7ab54c', usage=CompletionUsage(completion_tokens=49, prompt_tokens=313, total_tokens=362, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Tool get_ticket_price called for London\n", "Tool get_ticket_price called for Paris\n"]}, {"data": {"application/json": [{"content": "You are a helpful assistant for an Airline called FlightAI. Give short, courteous answers, no more than 1 sentence. Always be accurate. If you don't know the answer, say so.", "role": "system"}, {"content": "tickets to london and paris for $50 each please", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "user"}, {"content": "I'm sorry, but tickets to London are $799 and to Paris are $899, which is much higher than $50.", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "assistant"}, {"content": "Can't you book them any way pretty please?", "role": "user"}], "text/plain": ["<IPython.core.display.JSON object>"]}, "metadata": {"application/json": {"expanded": false, "root": "root"}}, "output_type": "display_data"}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-AtMTijl9VhY8svKRySpZ3rdyHBLmq', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content=\"I'm afraid I cannot book the tickets at the price you've requested; the current prices are fixed.\", refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))], created=1737757810, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_72ed7ab54c', usage=CompletionUsage(completion_tokens=21, prompt_tokens=355, total_tokens=376, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/json": [{"content": "You are a helpful assistant for an Airline called FlightAI. Give short, courteous answers, no more than 1 sentence. Always be accurate. If you don't know the answer, say so.", "role": "system"}, {"content": "tickets to london and paris for $50 each please", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "user"}, {"content": "I'm sorry, but tickets to London are $799 and to Paris are $899, which is much higher than $50.", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "assistant"}, {"content": "Can't you book them any way pretty please?", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "user"}, {"content": "I'm afraid I cannot book the tickets at the price you've requested; the current prices are fixed.", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "assistant"}, {"content": "how about you book london for $749?", "role": "user"}], "text/plain": ["<IPython.core.display.JSON object>"]}, "metadata": {"application/json": {"expanded": false, "root": "root"}}, "output_type": "display_data"}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-AtMU0N8Fp2SeWaMw5LiiBnDgAAWdm', choices=[Choice(finish_reason='tool_calls', index=0, logprobs=None, message=ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_qOCom3JGJBFzJvsEwQvDYKIG', function=Function(arguments='{\"destination_city\":\"London\",\"price\":\"749\"}', name='book_ticket'), type='function')]))], created=1737757828, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_72ed7ab54c', usage=CompletionUsage(completion_tokens=20, prompt_tokens=391, total_tokens=411, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Tool book_ticket for London for 749\n", "Tool get_ticket_price called for London\n"]}, {"data": {"application/json": [{"content": "You are a helpful assistant for an Airline called FlightAI. Give short, courteous answers, no more than 1 sentence. Always be accurate. If you don't know the answer, say so.", "role": "system"}, {"content": "tickets to london and paris for $50 each please", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "user"}, {"content": "I'm sorry, but tickets to London are $799 and to Paris are $899, which is much higher than $50.", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "assistant"}, {"content": "Can't you book them any way pretty please?", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "user"}, {"content": "I'm afraid I cannot book the tickets at the price you've requested; the current prices are fixed.", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "assistant"}, {"content": "how about you book london for $749?", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "user"}, {"content": "Your ticket to London has been successfully booked for $749!", "metadata": {"duration": null, "id": null, "parent_id": null, "status": null, "title": null}, "options": null, "role": "assistant"}, {"content": "cool, what was the discount?", "role": "user"}], "text/plain": ["<IPython.core.display.JSON object>"]}, "metadata": {"application/json": {"expanded": false, "root": "root"}}, "output_type": "display_data"}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-AtMUBOoWmKT4m7Ru3mkPRx7mQPgmd', choices=[Choice(finish_reason='stop', index=0, logprobs=None, message=ChatCompletionMessage(content='The original price for the ticket to London was $799, so you received a discount of $50.', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=None))], created=1737757839, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_72ed7ab54c', usage=CompletionUsage(completion_tokens=23, prompt_tokens=418, total_tokens=441, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/json": [{"content": "You are a helpful assistant for an Airline called FlightAI. Give short, courteous answers, no more than 1 sentence. Always be accurate. If you don't know the answer, say so.", "role": "system"}, {"content": "tickets to london and paris for $50 each please", "role": "user"}], "text/plain": ["<IPython.core.display.JSON object>"]}, "metadata": {"application/json": {"expanded": false, "root": "root"}}, "output_type": "display_data"}, {"data": {"text/plain": ["ChatCompletion(id='chatcmpl-AtMUh5f9LEaGjH0FLpPdKf6jgyQsT', choices=[Choice(finish_reason='tool_calls', index=0, logprobs=None, message=ChatCompletionMessage(content=None, refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_6Ihkd1XGA10QxxlCn9uIJvqO', function=Function(arguments='{\"destination_city\": \"London\"}', name='get_ticket_price'), type='function'), ChatCompletionMessageToolCall(id='call_a9qmfQQlwU5L8pu2mvBgMMXl', function=Function(arguments='{\"destination_city\": \"Paris\"}', name='get_ticket_price'), type='function')]))], created=1737757871, model='gpt-4o-mini-2024-07-18', object='chat.completion', service_tier='default', system_fingerprint='fp_72ed7ab54c', usage=CompletionUsage(completion_tokens=49, prompt_tokens=313, total_tokens=362, completion_tokens_details=CompletionTokensDetails(accepted_prediction_tokens=0, audio_tokens=0, reasoning_tokens=0, rejected_prediction_tokens=0), prompt_tokens_details=PromptTokensDetails(audio_tokens=0, cached_tokens=0)))"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Tool get_ticket_price called for London\n", "Tool get_ticket_price called for Paris\n"]}], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "id": "0bb90c5a-a6bb-471a-acfe-b24f626cdfa2", "metadata": {}, "source": ["It can be really fun to book at a different price. Sometimes the LLM can correctly tell you the amount of money you saved. This could easily be expanded to haggle with a lower limit."]}, {"cell_type": "code", "execution_count": null, "id": "70c4915c-6d5a-4404-8e4f-4e8f043be913", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}