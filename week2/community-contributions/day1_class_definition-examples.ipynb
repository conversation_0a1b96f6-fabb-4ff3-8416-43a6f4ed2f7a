{"cells": [{"cell_type": "code", "execution_count": null, "id": "a0adab93-e569-4af0-80f1-ce5b7a116507", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "%run week2/community-contributions/day1_class_definition.ipynb"]}, {"cell_type": "code", "execution_count": null, "id": "4566399a-e16d-41cd-bef4-f34b811e6377", "metadata": {}, "outputs": [], "source": ["system_msg = \"You are an assistant that is great at telling jokes\"\n", "user_msg = \"Tell a light-hearted joke for an audience of Software Engineers\""]}, {"cell_type": "code", "execution_count": null, "id": "362759bc-ce43-4f54-b8e2-1dab19c66a62", "metadata": {}, "outputs": [], "source": ["# Easy to instantiate and use, just create an object \n", "# using the right Wrapper"]}, {"cell_type": "code", "execution_count": null, "id": "a6e5468e-1f1d-40e4-afae-c292abc26c12", "metadata": {}, "outputs": [], "source": ["gpt=GPT_Wrapper(system_msg, user_msg)\n", "print(\"GPT: \" + gpt.getResult())\n"]}, {"cell_type": "code", "execution_count": null, "id": "e650839a-7bc4-4b6c-b6ea-e836644b076f", "metadata": {}, "outputs": [], "source": ["claude=<PERSON>(system_msg, user_msg)\n", "print(\"<PERSON>: \" + claude.getResult())\n"]}, {"cell_type": "code", "execution_count": null, "id": "49335337-d713-4d9e-aba0-41a309c37699", "metadata": {}, "outputs": [], "source": ["gemini=Gemini_Wrapper(system_msg, user_msg)\n", "print(\"Gemini: \" + gemini.getResult())\n"]}, {"cell_type": "code", "execution_count": null, "id": "31d11b7b-5d14-4e3d-88e1-29239b667f3f", "metadata": {}, "outputs": [], "source": ["ollama=Ollama_Wrapper(system_msg, user_msg)\n", "print(\"Ollama: \" + ollama.getResult())\n"]}, {"cell_type": "code", "execution_count": null, "id": "282efb89-23b0-436e-8458-d6aef7d23117", "metadata": {}, "outputs": [], "source": ["#Easy to change the prompt and reuse\n", "\n", "ollama.setUserPrompt(\"Tell a light-hearted joke for an audience of Managers\")\n", "print(\"Ollama: \" + ollama.getResult())"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}