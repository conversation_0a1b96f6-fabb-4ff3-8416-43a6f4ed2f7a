{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Import libraries as needed & keep your gemini api key ready"], "metadata": {"id": "2UAcHYzT6ikw"}}, {"cell_type": "code", "source": ["#!pip install gradio"], "metadata": {"id": "XW0IY4xK6JZ1"}, "execution_count": 14, "outputs": []}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "dwoPNMMP4ZSh"}, "outputs": [], "source": ["from google import genai\n", "from google.genai import types\n", "from google.colab import userdata\n", "\n"]}, {"cell_type": "code", "source": ["def get_trip_itinerary(budget: int) -> str:\n", "    \"\"\"\n", "    Returns a trip itinerary based on the given budget.\n", "    \"\"\"\n", "    itinerary_dict: Dict[int, str] = {\n", "        500: \"Paris: 3-day budget trip covering Eiffel Tower, Louvre, and Seine River Cruise.\",\n", "        1000: \"Tokyo: 5-day adventure covering Shibuya, Akihabara, Mount Fuji day trip.\",\n", "        2000: \"New York: 7-day luxury stay covering Times Square, Broadway show, and helicopter tour.\",\n", "        3000: \"Dubai: 7-day ultra-luxury trip with <PERSON><PERSON><PERSON> VIP tour, desert safari, and yacht cruise.\",\n", "    }\n", "\n", "    return itinerary_dict.get(budget, \"No itinerary found for this budget. Try another amount!\")\n"], "metadata": {"id": "cnYD07T24ueV"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["from google.genai import types\n", "\n", "config = types.GenerateContentConfig(tools=[get_trip_itinerary])\n", "\n", "from google import genai\n", "\n", "client = genai.Client(api_key=userdata.get('gemini_api'))\n", "\n", "response = client.models.generate_content(\n", "    model='gemini-2.0-flash',\n", "    config=config,\n", "    contents='Based on the user budget suggest trip itinerary'\n", ")\n"], "metadata": {"id": "3WRUXvD45VFC"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["import gradio as gr\n", "\n", "# Chat function using Gemini\n", "chat = client.chats.create(model='gemini-2.0-flash', config=config)\n", "\n", "def chat_with_ai(user_input: str):\n", "    response = chat.send_message(user_input)\n", "    return response.text\n", "\n", "# Gradio Chat Interface\n", "demo = gr.Interface(fn=chat_with_ai, inputs=\"text\", outputs=\"text\", title=\"AI Trip Planner\")\n", "\n", "demo.launch()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 645}, "id": "5fE700z96DHs", "outputId": "3e35423c-8b2b-4868-8113-00d9d3a7a2ba"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Running Gradio in a Colab notebook requires sharing enabled. Automatically setting `share=True` (you can turn this off by setting `share=False` in `launch()` explicitly).\n", "\n", "Colab notebook detected. To show errors in colab notebook, set debug=True in launch()\n", "* Running on public URL: https://079a23f363400da700.gradio.live\n", "\n", "This share link expires in 72 hours. For free permanent hosting and GPU upgrades, run `gradio deploy` from the terminal in the working directory to deploy to Hugging Face Spaces (https://huggingface.co/spaces)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<IPython.core.display.HTML object>"], "text/html": ["<div><iframe src=\"https://079a23f363400da700.gradio.live\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"]}, "metadata": {}}, {"output_type": "execute_result", "data": {"text/plain": []}, "metadata": {}, "execution_count": 13}]}, {"cell_type": "code", "source": [], "metadata": {"id": "XC9zzq8X5u8m"}, "execution_count": null, "outputs": []}]}