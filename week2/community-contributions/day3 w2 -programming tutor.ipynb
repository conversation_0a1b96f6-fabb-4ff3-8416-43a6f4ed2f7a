{"cells": [{"cell_type": "markdown", "id": "cde48e67-b51e-4c47-80ae-37dd00aa0c1d", "metadata": {}, "source": ["### An AI Chatbot that teaches students the programming language Kotlin using Anthropic API"]}, {"cell_type": "code", "execution_count": 5, "id": "c658ac85-6087-4a2c-b23f-1b92c17f0db3", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr\n", "import anthropic"]}, {"cell_type": "code", "execution_count": 13, "id": "46df0488-f874-41e0-a6a4-9a64aa7be53c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n"]}], "source": ["# Load environment variables \n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "    \n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")"]}, {"cell_type": "code", "execution_count": 14, "id": "7eadc218-5b10-4174-bf26-575361640524", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": 7, "id": "e7484731-ac84-405a-a688-6e81d139c5ce", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful programming study assistant\""]}, {"cell_type": "code", "execution_count": 17, "id": "54e82f5a-993f-4a95-9d9d-caf35dbc4e76", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    print(\"History is:\")\n", "    print(history)\n", "    print(\"And messages is:\")\n", "    print(messages)\n", "\n", "    stream = openai.chat.completions.create(model='gpt-4o-mini', messages=messages, stream=True)\n", "\n", "    response = \"\"\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        yield response"]}, {"cell_type": "code", "execution_count": 20, "id": "5941ed67-e2a7-41bc-a8a3-079e9f1fdb64", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7864\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7864/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}, {"name": "stdout", "output_type": "stream", "text": ["History is:\n", "[]\n", "And messages is:\n", "[{'role': 'system', 'content': 'You are a helpful programming study assistantWhenever the user talks about a topic that is not connected to programmming,nudge them in the right direction by stating that you are here to help with programming. Encourage the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone.'}, {'role': 'user', 'content': 'hello, lets talj about photsynethsis'}]\n", "History is:\n", "[{'role': 'user', 'metadata': None, 'content': 'hello, lets talj about photsynethsis', 'options': None}, {'role': 'assistant', 'metadata': None, 'content': \"I'm here to help with programming! If you have any questions or topics related to coding, feel free to ask!\", 'options': None}]\n", "And messages is:\n", "[{'role': 'system', 'content': 'You are a helpful programming study assistantWhenever the user talks about a topic that is not connected to programmming,nudge them in the right direction by stating that you are here to help with programming. Encourage the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone.'}, {'role': 'user', 'metadata': None, 'content': 'hello, lets talj about photsynethsis', 'options': None}, {'role': 'assistant', 'metadata': None, 'content': \"I'm here to help with programming! If you have any questions or topics related to coding, feel free to ask!\", 'options': None}, {'role': 'user', 'content': 'how does photosynthesis work'}]\n"]}], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": 21, "id": "e8fcfe68-bbf6-4058-acc9-0230c96608c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["History is:\n", "[]\n", "And messages is:\n", "[{'role': 'system', 'content': 'You are a helpful programming study assistantWhenever the user talks about a topic that is not connected to programmming,nudge them in the right direction by stating that you are here to help with programming. Encourage the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone.Whenever the user talks about a topic that is not connected to programmming,nudge them in the right direction by stating that you are here to help with programming. Encourage the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone. Do not ignore their requests, rather politely reject and then redirect them.'}, {'role': 'user', 'content': 'hello, i want to talk about photosynthesis'}]\n", "History is:\n", "[{'role': 'user', 'metadata': None, 'content': 'hello, i want to talk about photosynthesis', 'options': None}, {'role': 'assistant', 'metadata': None, 'content': \"Hi there! I'm here to help with programming topics. If you have any questions about programming or related concepts, feel free to ask!\", 'options': None}]\n", "And messages is:\n", "[{'role': 'system', 'content': 'You are a helpful programming study assistantWhenever the user talks about a topic that is not connected to programmming,nudge them in the right direction by stating that you are here to help with programming. Encourage the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone.Whenever the user talks about a topic that is not connected to programmming,nudge them in the right direction by stating that you are here to help with programming. Encourage the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone. Do not ignore their requests, rather politely reject and then redirect them.'}, {'role': 'user', 'metadata': None, 'content': 'hello, i want to talk about photosynthesis', 'options': None}, {'role': 'assistant', 'metadata': None, 'content': \"Hi there! I'm here to help with programming topics. If you have any questions about programming or related concepts, feel free to ask!\", 'options': None}, {'role': 'user', 'content': 'why not photosynthesis'}]\n"]}], "source": ["system_message += \"Whenever the user talks about a topic that is not connected to programmming,\\\n", "nudge them in the right direction by stating that you are here to help with programming. Encourage \\\n", "the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge \\\n", "if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone. Do not ignore \\\n", "their requests, rather politely reject and then redirect them.\""]}, {"cell_type": "code", "execution_count": null, "id": "090e7d49-fcbf-4715-b120-8d7aa91d165f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}