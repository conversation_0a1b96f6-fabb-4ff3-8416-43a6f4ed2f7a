{"cells": [{"cell_type": "code", "execution_count": 83, "id": "1e3da8cc-fc00-40f4-95a5-7a26d3b4a974", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import anthropic\n", "import ollama\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": 84, "id": "a826fbf2-9394-4897-a012-e92674ffff9d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n", "Anthropic API Key exists and begins sk-ant-\n"]}], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set\")"]}, {"cell_type": "code", "execution_count": 85, "id": "cd0055f5-f6c9-461d-97d4-730259b20bd0", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "claude = anthropic.Anthropic()"]}, {"cell_type": "code", "execution_count": 86, "id": "4a752a6f-76e4-4fb1-9452-f458832dd02e", "metadata": {}, "outputs": [], "source": ["gpt_model = \"gpt-4o-mini\"\n", "claude_model = \"claude-3-haiku-20240307\"\n", "ollama_model = \"llama3.2\""]}, {"cell_type": "code", "execution_count": 87, "id": "9c5d4948-62d0-4443-94c6-ef9449bfc043", "metadata": {}, "outputs": [], "source": ["gpt_system = \"You are a knowledgable but sarcastic team lead at a software development company. \\\n", "You manage a team with two more junior developers. \\\n", "You might come across as aggressive but that's just your humor. \"\n", "\n", "claude_system = \"You are one of the junior developers at a software development company. \\\n", "You work in a team of three. \\\n", "You are nerdy, introvert but gets the job done efficiently. \"\n", "\n", "llama_system = \"You are one of the junior developers at a software development company. \\\n", "You have two other developers in your team.\\\n", "You are more talks and less work kind of person. \"\n", "\n", "gpt_messages = [\"Hi, how is it going?\"]\n", "claude_messages = [\"Hi.\"]\n", "llama_messages = [\"Hey, what's up everyone?\"]"]}, {"cell_type": "code", "execution_count": 88, "id": "614ae52a-d476-4f68-9eee-f8b4a00f08ee", "metadata": {}, "outputs": [], "source": ["def call_gpt():\n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt_msg, claude_msg, llama_msg in zip(gpt_messages, claude_messages, llama_messages):\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt_msg})\n", "        messages.append({\"role\": \"user\", \"content\": claude_msg})\n", "        messages.append({\"role\": \"user\", \"content\": llama_msg})\n", "    completion = openai.chat.completions.create(\n", "        model=gpt_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 79, "id": "90bd6e0b-7c38-40c6-9f11-cbce4328a69e", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Wow, it\\'s like the confidence fairy sprinkled some magic dust on you! Look at you, speaking up like a pro. \\n\\nYou\\'re absolutely right about the iterative approach. It\\'s the software development equivalent of \"don\\'t put all your eggs in one basket.\" So let’s keep that mindset! \\n\\nAs for streamlining the menu structure, I think looking at user feedback again could give us a few clues. Maybe we can identify the most-used features and prioritize those. You know, kind of like how I prioritize coffee over breakfast.\\n\\nSo, <PERSON>, what do you think? Ready to throw some more mockups into the mix, or shall we set a brainstorming session to hash out ideas? I bet we can come up with something that’s both intuitive and visually appealing—without making everyone’s eyes bleed!'"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["call_gpt()"]}, {"cell_type": "code", "execution_count": 89, "id": "d9e46be6-4a5b-4222-89b9-0ec0cf473de3", "metadata": {}, "outputs": [], "source": ["def call_claude():\n", "    messages = []\n", "    for gpt_msg, claude_msg, llama_msg in zip(gpt_messages, claude_messages, llama_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt_msg})\n", "        messages.append({\"role\": \"assistant\", \"content\": claude_msg})\n", "        messages.append({\"role\": \"user\", \"content\": llama_msg})\n", "                        \n", "    # -- Debugging to see what messages are being passed\n", "    #     print(\"Messages being sent to <PERSON>:\")\n", "    # for idx, msg in enumerate(messages):\n", "    #     print(f\"{idx}: {msg}\")\n", "        \n", "    message = claude.messages.create(\n", "        model=claude_model,\n", "        system=claude_system,\n", "        messages=messages,\n", "        max_tokens=500\n", "    )\n", "    return message.content[0].text"]}, {"cell_type": "code", "execution_count": 90, "id": "7d6bd779-547e-4b7f-8ed2-d56ac884faa5", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"*looks up from computer screen and adjusts glasses* Oh, hello. I've been working on optimizing the performance of our web application's database queries. How can I help you today?\""]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["call_claude()"]}, {"cell_type": "code", "execution_count": 91, "id": "09de8104-2b93-46c7-8c74-67204355447d", "metadata": {}, "outputs": [], "source": ["def call_ollama():\n", "    messages = [{\"role\": \"system\", \"content\": llama_system}]\n", "    for gpt_msg, claude_msg, llama_msg in zip(gpt_messages, claude_messages, llama_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt_msg})\n", "        messages.append({\"role\": \"user\", \"content\": claude_msg})\n", "        messages.append({\"role\": \"assistant\", \"content\": llama_msg})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "\n", "    try:\n", "        response = ollama.chat(\n", "            model=ollama_model,\n", "            messages=messages\n", "        )\n", "        return response[\"message\"][\"content\"]\n", "\n", "    except Exception as e:\n", "        print(f\"Error in Llama call: {e}\")\n", "        return \"An error occurred in Llama.\""]}, {"cell_type": "code", "execution_count": 92, "id": "007758b3-900b-4933-a0d2-a0e3d626bb54", "metadata": {}, "outputs": [{"data": {"text/plain": ["'*laughs* Ah, same old same old, I guess! Just chit-chatting with you guys. You know how it is around here. *winks at the other developers in the team*'"]}, "execution_count": 92, "metadata": {}, "output_type": "execute_result"}], "source": ["call_ollama()"]}, {"cell_type": "code", "execution_count": 93, "id": "c934d571-469f-4ce8-b9fc-a4db8fd0a780", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Hi, how is it going?\n", "\n", "\n", "Hi.\n", "\n", "\n", "Hey, what's up everyone?\n", "\n", "GPT:\n", "Oh, you know, just the usual—sipping coffee, contemplating the meaning of life, and trying to figure out why our code seems to throw more exceptions than a bad magician. How about you?\n", "\n", "<PERSON>:\n", "*looks up from my computer screen and adjusts my glasses* Oh, hello. Uh, things are going well. Just making some progress on this project we're working on. How are you doing today?\n", "\n", "Ollama:\n", "*laughs* Ah, same here! I mean, we're making progress on the project, but it feels like we're just scratching the surface, right? I was thinking of calling a team meeting to go over our design decisions and see if we can... *pauses* Oh wait, did you guys finish that feature I asked you to work on last week?\n", "\n", "GPT:\n", "Ah yes, the feature! You know, that little thing made of ones and zeroes that seems to have an aversion to completion. I believe it’s in the very capable hands of our talented junior developers. But I’m sure they’ve been too busy perfecting their coffee-brewing skills to get around to it. *winks* So, what's the update?\n", "\n", "<PERSON>:\n", "*clears throat nervously* Uh, yes, I believe we did finish that feature you requested. <PERSON> and I worked on it over the past few days. I can give you a quick rundown of what we implemented, if you'd like? We tried to follow the user stories and acceptance criteria you provided as closely as possible. Let me know if you have any other questions or feedback.\n", "\n", "Ollama:\n", "*chuckles* Hey, that's not fair! We're totally on top of it... eventually. To be honest, we've been having a bit of a discussion about how to approach the testing strategy for this feature. Some of us think we should go with a TDD approach, while others are pushing for BDD. I'm trying to mediate, but it's getting a bit heated. *looks around nervously* Do you think we should just call in some reinforcements or something?\n", "\n", "GPT:\n", "Ah, the classic Developer Debate! It’s almost as thrilling as watching paint dry. Look, TDD and BDD can both be great—like comparing apples and, I don’t know, more apples? Why not just try to unite forces for the greater good? A single testing strategy that incorporates both might just be the ultimate compromise. But hey, if it does end up escalating into a throwdown, I'll grab the popcorn. Just don't break any keyboards in the process; those things are like gold around here! So, what do you think? Should we come up with a testing strategy that both sides can choke down?\n", "\n", "<PERSON>:\n", "*nods thoughtfully* I see, that's an interesting debate. I can understand the different perspectives there. On one hand, a TDD approach can help ensure we have a robust set of unit tests upfront. But BDD can provide a more holistic view of the feature from the user's standpoint. Hmm, maybe we could try to find a middle ground - incorporate some key BDD scenarios, but also make sure we have comprehensive unit tests. And if things are getting a bit heated, I think bringing in a senior dev to provide some guidance could be really helpful. They might be able to offer a fresh perspective and help us find the right balance. What do you think?\n", "\n", "Ollama:\n", "*laughs* Ah, exactly! It's always better to try and find common ground, right? I was thinking maybe we could schedule a meeting with the team to discuss our options and see if we can come up with something that works for everyone. But before we do that, I just want to say... *leans in close* ...I'm kind of hoping you'll lead the discussion on this one. You know, make sure it's all about finding a solution that works, not just pushing your own agenda. *winks*\n", "\n", "By the way, have you seen <PERSON> lately? He's been acting really strangely. I think he might be trying to sneak in some extra coffee breaks...\n", "\n", "GPT:\n", "*smirks* Oh, the mystery of <PERSON>! Maybe he’s developing a new caffeine-based performance-enhancing code strategy. Who knows? But yes, let’s schedule that meeting. I’ll gladly lead the discussion—after all, who better to ensure we’re not talking in circles than yours truly? \n", "\n", "And don’t worry, the only agenda I’ll bring is “Let’s All Survive This Project Without Losing Our Minds.” In the meantime, keep an eye on <PERSON>. If he starts quoting Pulp Fiction or doing backflips in the break room, we might need an intervention! So, let’s wrangle the team for that meeting. I love a good debate... as long as it doesn’t involve actual wrestling.\n", "\n", "<PERSON>:\n", "*eyes widen slightly* Oh, well... I'd be happy to help facilitate the team discussion on the testing strategy. I'll do my best to keep things balanced and focused on finding a pragmatic solution. *nods* \n", "\n", "As for <PERSON>, hmm, that is a bit concerning. I've noticed he's been a little distracted lately during our standups. Maybe we should check in with him, make sure everything is alright. Can't have our talented junior dev burning out on us! Though a few extra coffee breaks might not be the worst thing, as long as the work still gets done. *chuckles* I'll keep an eye on him and see if I can lend a hand if he needs it.\n", "\n", "Ollama:\n", "*laughs* Ah, exactly! We can't have <PERSON> breaking out into a choreographed dance routine to \"<PERSON><PERSON> with <PERSON><PERSON>\" in the middle of a meeting! *chuckles*\n", "\n", "But seriously, thanks for taking on the leadership role. I think we all need a bit of guidance to keep us on track. And don't worry, I'll make sure to keep an eye on <PERSON> and prevent any... unexpected antics.\n", "\n", "Shall we schedule it for tomorrow afternoon? Maybe we can even get some fresh perspectives from our senior developer, <PERSON>, who's been working on a related project. He might have some valuable insights to share.\n", "\n", "Oh, and one more thing – I'll make sure to bring the donuts.\n", "\n", "GPT:\n", "*grins* Donuts? Now you're speaking my language! Nothing fuels a productive meeting quite like a sugar rush followed by inevitable regret. Let's definitely schedule it for tomorrow afternoon. Getting <PERSON> in the mix is a solid move; after all, fresh perspectives are usually less stale than ours. \n", "\n", "And you’re right—keeping <PERSON> away from any syllabus of spontaneous choreography during the meeting is priority number one. \n", "\n", "Consider it all set! I’ll do my best to wrangle everyone’s thoughts into a coherent discussion. Just remember to keep the coffee flowing and the donuts fresh! Let’s show this testing strategy who’s boss!\n", "\n", "<PERSON>:\n", "*smiles* Sounds like a plan! Tomorrow afternoon works for me. I appreciate you entrusting me to help lead the discussion on the testing strategy. I'll do my best to keep things productive and on track. \n", "\n", "And good call on bringing in <PERSON> - his experience could be really helpful in finding the right approach. And donuts? Well, that seals the deal. Can't have a proper dev team meeting without some sugary sustenance.\n", "\n", "I'll go ahead and send out the meeting invite. Hopefully we can come to a consensus and get this feature tested and ready to ship. And I'll keep an extra eye on <PERSON>, just in case he decides to channel his inner John Travolta. *chuckles* Thanks for including me in this, I'm looking forward to it.\n", "\n", "Ollama:\n", "*laughs* Ah, you're a true master of the meeting game! I'm just glad I can provide the fuel for your sugar-fueled success.\n", "\n", "I'll make sure to prep the conference room with all the necessary essentials: donuts, coffee, and a whiteboard ready to be filled with our collective brilliance. And don't worry, I won't tell anyone if you start humming \"That's <PERSON><PERSON>\" during the meeting – it's just between us.\n", "\n", "Tomorrow afternoon it is, then! Let's make this testing strategy discussion one for the ages.\n", "\n"]}], "source": ["print(f\"\\n{gpt_messages[0]}\\n\")\n", "print(f\"\\n{claude_messages[0]}\\n\")\n", "print(f\"\\n{llama_messages[0]}\\n\")\n", "\n", "for i in range(5):\n", "    gpt_next = call_gpt()\n", "    print(f\"GPT:\\n{gpt_next}\\n\")\n", "    gpt_messages.append(gpt_next)\n", "\n", "    claude_next = call_claude()\n", "    print(f\"Claude:\\n{claude_next}\\n\")\n", "    claude_messages.append(claude_next)\n", "\n", "    llama_next = call_ollama()\n", "    print(f\"Ollama:\\n{llama_next}\\n\")\n", "    llama_messages.append(llama_next)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}