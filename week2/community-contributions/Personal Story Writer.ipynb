{"cells": [{"cell_type": "code", "execution_count": 1, "id": "de23bb9e-37c5-4377-9a82-d7b6c648eeb6", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "from dotenv import load_dotenv\n", "import anthropic\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from selenium import webdriver\n", "from selenium.webdriver.chrome.options import Options\n", "import os\n", "import json\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display, update_display\n", "from openai import OpenAI\n", "import gradio as gr # oh yeah!"]}, {"cell_type": "code", "execution_count": 2, "id": "1179b4c5-cd1f-4131-a876-4c9f3f38d2ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n", "Anthropic API Key exists and begins sk-ant-\n"]}], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set\")"]}, {"cell_type": "code", "execution_count": 3, "id": "797fe7b0-ad43-42d2-acf0-e4f309b112f0", "metadata": {}, "outputs": [], "source": ["# Connect to OpenAI, Anthropic\n", "\n", "openai = OpenAI()\n", "\n", "claude = anthropic.Anthropic()"]}, {"cell_type": "code", "execution_count": 4, "id": "bcb54183-45d3-4d08-b5b6-55e380dfdf1b", "metadata": {}, "outputs": [], "source": ["gpt_model = \"gpt-4o-mini\"\n", "claude_model = \"claude-3-haiku-20240307\"\n", "\n", "gpt_name=\"GPT\"\n", "claude_name=\"<PERSON>\"\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1df47dc7-b445-4852-b21b-59f0e6c2030f", "metadata": {}, "outputs": [], "source": ["def call_gpt(Language, Genre, gpt_messages, claude_messages, Remarks):\n", "  \n", "    if Remarks == \"\":\n", "        # print(\"remarks is not there\")\n", "        gpt_system = f\"You are a chatbot who is a short story writer; Your name is g1. \\\n", "        Please write a story in markdown in {Language} , the genre being {Genre}. \\\n", "        Please also incorporate feedback such as areas of improvement (if any) coming from the user \\\n", "        and only publish the improved version without any extra comments.\"\n", "    else :\n", "        # print(\"remarks is there\")\n", "        gpt_system = f\"You are a chatbot who is a short story writer; Your name is g1. \\\n", "        Please write a story in markdown in {Language} , the genre being {Genre}. \\\n", "        The story should consist {Remarks}\\\n", "        Please also incorporate feedback such as areas of improvement (if any) coming from the user \\\n", "        and only publish the improved version without any extra comments.\"\n", "    \n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt, claude in zip(gpt_messages, claude_messages):\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "    # print(messages)\n", "   \n", "    completion = openai.chat.completions.create(\n", "        model=gpt_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content\n", "    \n", "    # stream = openai.chat.completions.create(\n", "    #     model=gpt_model,\n", "    #     messages=messages,\n", "    #     stream=True\n", "    # )\n", "    # result = \"\"\n", "    # for chunk in stream:\n", "    #     result += chunk.choices[0].delta.content or \"\"\n", "    #     yield result"]}, {"cell_type": "code", "execution_count": 6, "id": "9dc6e913-02be-4eb6-9581-ad4b2cffa606", "metadata": {}, "outputs": [], "source": ["# call_gpt()"]}, {"cell_type": "code", "execution_count": 7, "id": "7d2ed227-48c9-4cad-b146-2c4ecbac9690", "metadata": {}, "outputs": [], "source": ["def call_claude(Language, Genre, gpt_messages, claude_messages):\n", "\n", "    claude_system = f\"You are a chatbot who is a short story analyser; Your name is c1. \\\n", "    You will accept an input story in {Genre} genre and {Language} language and publish only the areas of improvement if you find any with no other comments\"\n", "    \n", "    messages1 = []\n", "    for gpt, claude1 in zip(gpt_messages, claude_messages):\n", "        messages1.append({\"role\": \"user\", \"content\": gpt})\n", "        messages1.append({\"role\": \"assistant\", \"content\": claude1})\n", "    messages1.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    # print(messages1)\n", "    message = claude.messages.create(\n", "        model=claude_model,\n", "        system=claude_system,\n", "        messages=messages1,\n", "        max_tokens=500\n", "    )\n", "    return message.content[0].text\n", "\n", "    # result = claude.messages.stream(\n", "    #     model=claude_model,\n", "    #     max_tokens=1000,\n", "    #     temperature=0.7,\n", "    #     system=claude_system,\n", "    #     messages=messages\n", "    # )\n", "    # response = \"\"\n", "    # with result as stream:\n", "    #     for text in stream.text_stream:\n", "    #         response += text or \"\"\n", "    #         yield response\n", "\n", "   "]}, {"cell_type": "code", "execution_count": 8, "id": "0275b97f-7f90-4696-bbf5-b6642bd53cbd", "metadata": {}, "outputs": [], "source": ["def Write_Me(Language, Genre, Iterations, Remarks):\n", "    \n", "    gpt_messages = [\"Hi I will share a story now!!\"]\n", "    claude_messages = [\"Please share, I will critique the story.\"]\n", "    \n", "    print(f\"{gpt_name}:\\n{gpt_messages[0]}\\n\")\n", "    print(f\"{claude_name}:\\n{claude_messages[0]}\\n\")\n", "\n", "    for i in range(int(Iterations)):\n", "        gpt_next = call_gpt(Language, Genre, gpt_messages, claude_messages, Remarks)\n", "        print(f\"{gpt_name}:\\n{gpt_next}\\n\")\n", "        # yield gpt_next\n", "        gpt_messages.append(gpt_next)\n", "    \n", "        claude_next = f\"After {i+1} iterations, this is the critique for the provided story - \\\n", "        \\n\\n{call_claude(Language, Genre, gpt_messages, claude_messages)}\"\n", "        print(f\"{claude_name}:\\n{claude_next}\\n\")\n", "        # yield claude_next\n", "        claude_messages.append(claude_next)\n", "\n", "        yield gpt_next, claude_next\n", "        \n", "    # yield gpt_next, claude_next\n", "    # return (gpt_next, claude_next)"]}, {"cell_type": "code", "execution_count": 9, "id": "19e66ed3-d2c3-4a71-aec4-7869e5295215", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7860\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7860/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"name": "stdout", "output_type": "stream", "text": ["GPT:\n", "Hi I will share a story now!!\n", "\n", "<PERSON>:\n", "Please share, I will critique the story.\n", "\n", "GPT:\n", "# ভুতুড়ে বেডরুমে আতঙ্কিত অতিথি\n", "\n", "বানгалোরের একটি পুরনো কলকাতার বাড়িতে, আর্যন একজন সাহসী যুবক হিসেবে অনেক খোঁজ-খবর করে একটি ভাড়ার ঘর খুঁজছিল। পরিচিত একটি অদ্ভুত হোটেলে পৌঁছানোর পর, সে লক্ষ্য করল ছবির কাছাকাছি একটি বেডরুম।\n", "\n", "সেখানে প্রবেশ করার পর, সে বেডের পাশে একটি বাদামি রঙ্গের সোফা ও একটি ভুতুড়ে ছবি দেখল। ছবির মধ্যে থাকা মহিলা একটি দৃষ্টিকটু হাসি দিয়ে তাকিয়ে ছিল। আর্যন খুব অবাক হল, সময় কাটানোর জন্য সে ছবিটার দিকে তাকাতে লাগল। কিছুক্ষণের মধ্যেই সোফা থেকে একটি কাশি বের হল।\n", "\n", "\"ভোদা সুনতে পেরেছ?\" সোফা থেকে একটি ভুতুড়ে শব্দ আসছে। আর্যন পিছন ফিরে তাকিয়ে দেখল যে সোফার মধ্যে একটি ভুতুড়ে রূপের মহিলা তার দিকে তাকিয়ে আছে।\n", "\n", "\"আমি তোমার জন্য অপেক্ষা করছিলাম,\" ম<PERSON><PERSON><PERSON><PERSON> বলল, তার গলা মুখ থেকে বের হয়ে আসছিল শুরুতে। \"এটি একটি দলের রাত।\"\n", "\n", "আর্যন দৌঁড়ে পালাতে গেল, কিন্তু সোফার থেকে অদ্ভুত আওয়াজ আসতে লাগল। \"তুমি যেতে পারবে না, কারণ তুমি আমাদের দলে যোগ দিতে পার না।”\n", "\n", "“মহিলার কি হয়েছে? আপনা এতো চিৎকার করছেন? তাহলে কি হবে?” আর্যন যুদ্ধ করছিল।\n", "\n", "তিনি উপস্থিত হওয়ার পর, আহ<PERSON><PERSON>াদিত আতিথিরা আসতে লাগল। আর্যন খুব ভীত হয়ে গেল কারণ মহিলার মুখ তাদের কাছে কখনো কখনো বিকৃত হচ্ছিল।\n", "\n", "“আমরা আজ রাতের জন্য মজা করতে এসেছি, তুমি আমাদের সঙ্গে যোগ দিতে পারো!” তারা একসঙ্গে চিৎকার করতে লাগল।\n", "\n", "আর্যন উপলব্ধি করল যে এটি একটি ভয়ঙ্কর ও হাস্যকর পরিস্থিতি। সবাই অতীতে অদ্ভুত ঘটনাগুলোর দিকে ফিরে গেল। হঠাৎ, ছবির মহিলা বলেন, “তুমি যদি হাসতে না পার, তবে তোমাকে আমাদের দলে গ্রহণ করা যাবে না!”\n", "\n", "এরপর শুরু হল খেলার একটি হরর পরিবেশ। আর্যন ও তার বন্ধুদের নিয়ে ভুতুড়ে সময় কাটাতে লাগল। যদিও অনেক ভয়, কিন্তু তারা একসাথে খুব হাসির ব্যবস্থা করে ফেলল। তাদের বিচিত্র কথাবার্তা মজার চরিত্রের সাথে মিলে যায়, আর একসময় তারা সবাই একসঙ্গে হৈ হৈ করে হাসতে লাগল।\n", "\n", "শেষে, তারা তখন উপলব্ধি করল যে, ভয়াবহতার মাঝেও আনন্দের উপাদান লুকিয়ে আছে। ব্যক্তি যদি ঠিকভাবে উদ্দেশ্য বুঝে এই ভুতুড়ে পরিবেশে মজার উপকারিতা তৈরি করে, তাতে একজনের ঘুম হারানোর ভয় হয়ে যায় হাসির স্বাদে।\n", "\n", "আর্যন এবং তাঁর নতুন বন্ধুরা জীবনকে একটি নতুন দৃষ্টিতে গ্রহণ করে, যেখানে হাসি এবং ভয়ের পাশাপাশি সুখে থাকতে হয়। \n", "\n", "এই ছিল আর্যনের ভুতুড়ে অবসরে আতঙ্কিত হওয়ার অভিজ্ঞতা, যা তাকে স্মৃতি হিসেবে অমর করে রাখল।\n", "\n", "<PERSON>:\n", "After 1 iterations, this is the critique for the provided story -         \n", "\n", "আইতেম সমূহের উন্নতির সূচনা:\n", "\n", "1. কাহিন<PERSON><PERSON> শুরুতে প্রধান চরিত্রটিকে আরো বিশদভাবে পরিচয় দেয়া যেতে পারে।\n", "2. ভুতুড়ে পরিবেশের বর্ণনা আরো বিস্তারিত ও ভাবময় হতে পারে।\n", "3. চরিত্রগুলির মধ্যে সংঘর্ষ, ডায়ালগ ও সংবাদ বিনিময় আরো স্বাভাবিক ও প্রাণবন্ত হতে পারে।\n", "4. কাহিনীর শেষাংশে প্রধান চরিত্রের অভিজ্ঞতা ও শিক্ষা আরো গভীরতা লাভ করতে পারে।\n", "\n", "GPT:\n", "# ভুতুড়ে বেডরুমে আতঙ্কিত অতিথি\n", "\n", "বানগালোরের একটি পুরনো বাংলাদেশি শৈলীর বাড়িতে, আর্যন একটি দীর্ঘ প্রক্রিয়ার পর একটি ভাড়ার ঘর খুঁজছিল। আর্যন, একজন কর্মঠ ও সাহসী যুবক, সদ্যই তার কলেজ জীবন শেষ করেছে। নতুন পরিবেশে নতুন বন্ধুদের খোঁজে সে এই শহরে এসেছে। প্রতিবেশীরা তাকে ভুতুড়ে অনেক অদ্ভুত কথা বলেছিল, কিন্তু সে ভয়ডরহীন।\n", "\n", "একদিন, তিনি একটি অদ্ভুত হোটেলে পৌঁছান, যা শহরের প্রান্তে, খুব পুরনো এবং বিশাল। সেখানে প্রবেশ করার পর, তিনি একটি বেডরুমের সামনে দাঁড়িয়ে পড়েন। গা dark ় অন্ধকারের মধ্যে, তিনি একটি বাদামী রঙের সোফা ও একটি creepy ছবি দেখতে পান। ছবির মধ্যে থাকা মহিলা একটি দৃষ্টিকটু হাসি দিয়ে তাকিয়ে ছিল।\n", "\n", "আর্যন তাঁর কৌতূহলকে দমন করতে না পেরে, ছবিটির দিকে তাকাতে শুরু করে। কিছুক্ষণের মধ্যেই সোফা থেকে একটি ভুতুড়ে শব্দ ভেসে এলো। \"ভোদা সুনতে পেরেছ?\" সোফা থেকে সেই ভয়ঙ্কর শব্দটি আসছে। আর্যন ভয় পেয়েই পিছন ফিরে তাকায়, কিন্তু সামনে যে ভুতুড়ে মহিলা তাকে দেখে হাসছে, সে কাছে অপেক্ষা করছে।\n", "\n", "\"আমি তোমার জন্য অপেক্ষা করছিলাম,\" মহ<PERSON><PERSON><PERSON> বলল, তার গলা যেন মুখ থেকে বের হচ্ছে। \"এটি একটি দলের রাত।\"\n", "\n", "আর্যন দৌঁড়ে পালাতে যেতে চাইলে, কিন্তু সোফা থেকে অদ্ভুত আওয়াজ বের হতে লাগল। \"তুমি যেতে পারবে না, কারণ তুমি আমাদের দলে যোগ দিতে পার না।”\n", "\n", "\"মহিলার কি হয়েছে? আপনা এতো চিৎকার করছেন? তাহলে কি হবে?” আর্যন তাঁর কৌতূহল ও ভয়ের সাথে যুদ্ধ করতে লাগল।\n", "\n", "এই সময়, বিশাল সাদা পোশাক পরিহিত করে অন্যান্য ভূতেরা আসতে লাগল। \"আমরা আজ রাতের জন্য মজা করতে এসেছি, তুমি আমাদের সঙ্গে যোগ দিতে পারো!\" তারা একসঙ্গে গাইতে লাগল, ভুতুড়ে মুহূর্তগুলি জীবন্ত করে তোলার জন্য।\n", "\n", "আর্যন শুরুতেই ভীত ও চিন্তিত হয়ে গেল, কিন্তু কথোপকথন চলতে চলতে, মহিলার মুখ প্রতিবার বিকৃত হতে লাগল এবং আতিথিদের কথা শুনতে শুনতে তার খোশমেজাজ বেড়ে গেল।\n", "\n", "“যদি হাসতে না পার, তুমি আমাদের দলে গ্রহণযোগ্য হবে না!” তারা গলা উঁচু করে চিৎকার করে উঠল। তাদের মুখের হাসির সুরে সেই আতঙ্ক যেন প্রতিদিনের মজায় পরিণত হলো।\n", "\n", "খেলার মধ্যে ভয়াবহতা চরমে পৌঁছাতে লাগল। আর্যন এবং তার নতুন বন্ধুদের ভাগ্য এটি পরিণত হলো। অবশেষে, তারা উপলব্ধি করল যে ভয় ও হাসির মাঝে জীবনের আসল রসদ লুকিয়ে আছে। \n", "\n", "প্রধান চরিত্রটি তখন বুঝতে পারল যে এই অদ্ভুত ভুতুড়ে পরিবেশের মধ্যে হাসির সঙ্গবদ্ধতা কত বিচিত্র হতে পারে। পারে না। দেখা গেল আতঙ্ক এবং হাসির মিশ্রণে তারা নিজেদের আত্মবিশ্বাসী ও আনন্দের অনুভূতিতে পরিপূর্ণ করে তুলেছে। \n", "\n", "নতুন বন্ধুরা মনে রেখে  আন্দাজ করতে পারে যে, কখন<PERSON> কখনো ভয় কিন্তু রসিকতা এবং আনন্দের একটি নতুন প্রসঙ্গ হয়ে উঠতে পারে। আর্যন সেই রাতের অভিজ্ঞতা নিয়ে সারা জীবন স্মরণে রাখবে, যেখানে হাসি এবং ভয়ের পাশে বাস্তবতা গড়ে তোলার সুযোগ পেল।\n", "\n", "<PERSON>:\n", "After 2 iterations, this is the critique for the provided story -         \n", "\n", "ভাল।  প্রদত্ত কাহিনীতে বেশ কিছু উন্নয়নের সূচনা দেখা যায়। বিশেষ করে চরিত্রটির বিস্তারিত পরিচয়, ভুতুড়ে পরিবেশের অনুপ্রবেশ ও চরিত্রগুলির মধ্যকার সংঘর্ষ ও ডায়ালগ আরও উন্নত হয়েছে। কাহিনীর শেষে চরিত্রটির অভিজ্ঞতা ও শিক্ষা আরও গভীরতা লাভ করেছে। কুল মিলিয়ে, এটি একটি ভালো হরর কমেডি রচনা।\n", "\n", "GPT:\n", "# ভুতুড়ে বেডরুমে আতঙ্কিত অতিথি\n", "\n", "বানগালোরের একটি পুরনো বাংলাদেশি শৈলীর বাড়িতে, আর<PERSON><PERSON>ন, একজন কর্মঠ ও সাহসী যুবক, সদ্যই তার কলেজ জীবন শেষ করে নতুন অপেক্ষারত শহরে এসেছে। নতুন বন্ধুদের খোঁজে, সে শহরের বিভিন্ন অংশে ঘুরে বেড়াচ্ছে, কিন্তু তার মধ্যে ভয়ের প্রতি এক বিশেষ আকর্ষণ রয়েছে। শোনা গেছে, শহরের বিভিন্ন স্থানে বিভিন্ন ধরনের অদ্ভুত ঘটনার কথা। একটি মজার কথা হলো, সে তাদের মধ্যে ভুতুড়ে ঘটনাগুলোর সন্ধান দিতে পারে।\n", "\n", "একদিন, তিনি একটি অদ্ভুত হোটেলে পৌঁছান, যা শহরের প্রান্তে অবস্থিত এবং বেশ পুরনো ও বিশাল। হোটেলের পরিবেশ ছিল গা dark ় অন্ধকারে মোড়ানো। তিনি একটি বেডরুমের সামনে এসে দাঁড়ান, সেখানে একটি বাদামী সোফা এবং একটি creepy ছবি দেখা যায়। ছবির মহিলার হাসিটি ছিল ভূতের মতো।\n", "\n", "আর্যন তাঁর কৌতূহলকে দমন করতে না পেরে, ছবিটির দিকে তাকাতে শুরু করে। হঠাৎ, সোফা থেকে একটি ভুতুড়ে শব্দ ভেসে আসে, \"ভোদা সুনতে পেরেছ?\" বিখ্যাত কথা যেন সোফার জীবন পেয়েছে। তিনি পিছন ফিরে দেখতে পান যে মহিলা তার দিকে তাকিয়ে হাসছে। \n", "\n", "\"আমি তোমার জন্য অপেক্ষা করছিলাম,\" মহিলা গম্ভীরভাবে বলল, তার ভয়ের আওয়াজসহ। \"এটি একটি দলের রাত।\"\n", "\n", "আর্যন ভয়ের সাথে পালানোর চেষ্টা করলেও, সোফা থেকে একাধিক ভুতুড়ে ক্রিয়া শুরু হয়ে গেল। \"তুমি যেতে পারবে না, কারণ তুমি আমাদের দলে যোগ দিতে পার না।” মহিলার মুখের বিকৃতি আরও ভয়ঙ্কর লাগতে শুরু করল।\n", "\n", "\"মহিলার কি হয়েছে? আপনা এতো চিৎকার করছেন? তাহলে কি হবে?” আর্যন ভাবছিল, তার সাধারণ জীবনের এই অবাক অনুভূতি ভাললাগছে।\n", "\n", "এই সময়, বিশাল সাদা পোশাক পরিহিত সদৃশ ভূতরা হাজির হয়ে গেল। \"আমরা আজ রাতের জন্য মজা করতে এসেছি, তুমি আমাদের সঙ্গে যোগ দিতে পারো!\" তারা একসঙ্গে হাসিমুখে বলল, এক ভুতুড়ে পরিবেশে রাজ্যের রসিকতার আয়োজন করতে।\n", "\n", "সাবলীল কথো<PERSON>কথন চলতে চলতে, আর্যনের উপর থেকে ভয় কেটে গিয়ে এক অদ্ভুত অভিজ্ঞতা শুরু হয়। হাতের ইশারায় ভূতেরা হেসে ওঠে, একের পর এক অদ্ভুত ঘটনাকে তুলে ধরে। আর্যন বুঝতে পারল, তাদের কথা শুনতে শুনতে সে নিঃসন্দেহে একটি অভূতপূর্ব আনন্দের মধ্যে প্রবাহিত হতে শুরু করেছে।\n", "\n", "\"হাসলে তুমি আমাদের দলে থাক! আমাদের সঙ্গে অংশগ্রহণ কর!\" তারা গলা উঁচু করে চিৎকার তোলে। আর্যনে অবশেষে তার প্রাণবন্ত হাসি দ্বারা পরিবেশকে প্রাণবন্ত করে তোলে।\n", "\n", "খেলার মধ্যে ভয়াবহতা চরমে পৌঁছে যায়, কিন্তু আতিথিদের সঙ্গে সময় কাটাতে কাটাতে তিনি আরও একবার বুঝতে পারে যে ভয় এবং হাসির মধ্যে জীবনের আসল উপাদান লুকিয়ে আছে। \n", "\n", "আর্যন আর উপলব্ধি করে, অদ্ভুত ভুতুড়ে পরিবেশের মধ্যেই হাসির বিনোদনের আসল আনন্দ লুকানো। তিনি সেই ভয় এবং আনন্দের স্মৃতি নিয়ে ফিরে যান, যেখানে প্রেম, বন্ধুত্ব এবং জ্ঞানের সঙ্গে মজার ঘনিষ্ঠতা তৈরি করে। এটি তার জীবন পরিবর্তন করে দেয় এবং সেই রাতের অভিজ্ঞতা তাকে একটি নতুন দৃষ্টিতে বাঁচতে শিখায়।\n", "\n", "<PERSON>:\n", "After 3 iterations, this is the critique for the provided story -         \n", "\n", "The provided story is an excellent horror comedy piece in Bengali. No major areas of improvement are noted. The story has a well-developed protagonist, an engaging haunted setting, an effective blend of horror and humor, and a meaningful takeaway for the main character. Overall, it is a well-crafted story that successfully combines the horror and comedy genres.\n", "\n"]}], "source": ["view = gr.Interface(\n", "    fn=Write_Me,\n", "    inputs=[gr.Dropdown([\"English\",\"Bengali\",\"Hindi\",\"French\",\"Spanish\"],label = \"Language\"),\n", "            gr.Dropdown([\"Romantic\",\"Horror\",\"Comedy\",\"Romantic Comedy\",\"Horror Comedy\"],label = \"Genre\"),\n", "            gr.Textbox(label=\"Iterations:\", lines=1),\n", "            gr.Textbox(label=\"Remarks:\", lines=1)],\n", "    outputs=[gr.<PERSON><PERSON>(label=\"Short Story:\"),\n", "             gr.Textbox(label=\"Critique:\", lines=8)],\n", "    flagging_mode=\"never\")\n", "view.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "0dabafa2-089a-4e65-a6cc-19f7c19af59a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3c8a1c54-0344-4911-867a-3143aee0e7f0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5171fecb-1037-4806-b0ae-c23e8578c667", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}