{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a9e05d2a", "metadata": {}, "outputs": [], "source": ["# ----- (My project)\n", "# Date: 09.01.25\n", "# Plan: Make a Gradio UI, that lets you pick a job on seek.com, then scape key words and come up with a \n", "# plan on how to land jobs of the type selected."]}, {"cell_type": "markdown", "id": "312c3746", "metadata": {}, "source": ["# My project"]}, {"cell_type": "code", "execution_count": null, "id": "394dbcfc", "metadata": {}, "outputs": [], "source": ["#pip install markdown"]}, {"cell_type": "code", "execution_count": null, "id": "15f1024d", "metadata": {}, "outputs": [], "source": ["\n", "import os\n", "import requests\n", "import json\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from bs4 import BeautifulSoup\n", "from IPython.display import Markdown, display, update_display\n", "import gradio as gr\n", "import markdown\n", "\n", "# ---- 1\n", "# Initialize and constants & set up Gemini Flash LLM\n", "load_dotenv()\n", "api_key = os.getenv('GOOGLE_API_KEY')\n", "import os\n", "import google.generativeai as genai\n", "genai.configure(api_key= api_key)\n", "# Create the model\n", "generation_config = {\n", "  \"temperature\": 1,\n", "  \"top_p\": 0.95,\n", "  \"top_k\": 40,\n", "  \"max_output_tokens\": 8192,\n", "  \"response_mime_type\": \"text/plain\",}\n", "model = genai.GenerativeModel(model_name=\"gemini-1.5-flash\",\n", "  generation_config=generation_config,)\n", "chat_session = model.start_chat(history=[  ])\n", "\n", "\n", "# ---- 2\n", "# A class to represent a Webpage\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\"\n", "\n", "\n", "# ---- 3\n", "# Data + set up\n", "def get_all_details(url):\n", "    result = \"Landing page:\\n\"\n", "    result += Website(url).get_contents()\n", "    return result\n", "\n", "system_prompt = \"You are an experience recrutiment and talent management assistant, who will be provided a list of roles on offer.\\\n", "You will display those roles along with a high level summary of the key steps you suggest to land those roles. \\\n", "Output is to be in markdown (i.e. a professional format, with bold headders, proper spacing between different sections, etc.)\\\n", "Include suggested next steps on how to successfully apply for and land each of these jobs.\"\n", "\n", "def get_brochure_user_prompt(url):\n", "    user_prompt = f\"Here are the contents of your recruitment search. Please list out individual roles and your best advise on landing those roles.\"\n", "    user_prompt += f\"Please provide output in a professional style with bold text for headings, content nicely layed out under headings, different content split out into sections, etc.)\\n\"\n", "    user_prompt += get_all_details(url)\n", "    #user_prompt = user_prompt[:5_000] # Truncate if more than 5,000 characters\n", "    user_prompt = user_prompt[:7_500] # Truncate if more than 5,000 characters\n", "    return user_prompt\n", "\n", "def create_brochure(url):\n", "    response = chat_session.send_message(system_prompt + get_brochure_user_prompt(url))\n", "    result = response.text\n", "    html_output = markdown.markdown(result)\n", "    return html_output\n", "\n", "# ---- 4 \n", "# Gradio UI\n", "with gr.Blocks(css=\"\"\"\n", "    #header-container { text-align: left; position: fixed; top: 10px; left: 0; padding: 10px; background-color: #f0f0f0; }\n", "    #input-container { text-align: left; position: fixed; top: 100px; left: 0; right: 0; background: white; z-index: 100; padding: 8px; line-height: 0.5;}\n", "    #output-container { margin-top: 160px; height: calc(100vh - 280px); overflow-y: auto; }\n", "    #output-html { white-space: pre-wrap; font-family: monospace; border: 1px solid #ccc; padding: 5px; line-height: 1.2;}\n", "    .button-container { margin-top: 10px; } /* Space above the button */\n", "    .output-label { margin-top: 10px; font-weight: bold; } /* Style for output label */\n", "\"\"\") as iface:\n", "    with gr.<PERSON><PERSON>n(elem_id=\"main-container\"):\n", "        # Add header and description\n", "        with gr.Row(elem_id=\"header-container\"):\n", "            gr.<PERSON>(\"# Job seeker guide\")\n", "            gr.<PERSON>(\"1.0 Works best with recruitment site https://www.seek.com.au/ (but can try others).\")\n", "            gr.<PERSON>(\"2.0 Search for jobs of your choice, copy URL from that search & paste in input field below to get helpful advice on how to land those roles.\")\n", "\n", "\n", "        \n", "        with gr.Row(elem_id=\"input-container\"):\n", "            input_text = gr.Textbox(label=\"Input\", elem_id=\"input-box\")\n", "        \n", "        with gr.<PERSON><PERSON>n(elem_id=\"output-container\"):\n", "            output_label = gr.Markdown(\"<div class='output-label'>Output:</div>\")\n", "            output_text = gr.HTML(elem_id=\"output-html\")\n", "        \n", "        # Move the button below the output box\n", "        submit_btn = gr.<PERSON><PERSON>(\"Generate\", elem_id=\"generate-button\", elem_classes=\"button-container\")\n", "    \n", "    submit_btn.click(fn=create_brochure, inputs=input_text, outputs=output_text)\n", "\n", "iface.launch(share=True)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "21c4b557", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}