{"cells": [{"cell_type": "markdown", "id": "e3a5643a-c247-4a9b-8c57-ec9b1e89c088", "metadata": {}, "source": ["# Week 2 - eCommerce Assistant for products price from dictionary\n", "\n", "An eCommerce assitant that can get a product price\n", "\n", "Gradio for chat box"]}, {"cell_type": "code", "execution_count": null, "id": "756573b3-72b2-4102-a773-91c278e5c4fd", "metadata": {"scrolled": true}, "outputs": [], "source": ["# !ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": null, "id": "57ae8d30-f7aa-47a3-bab8-b7002e87a8f7", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "id": "ef7f8b2b-1d6a-4bbd-858a-be187ccfc02a", "metadata": {}, "outputs": [], "source": ["# Initialization\n", "\n", "load_dotenv(override=True)\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "MODEL = \"gpt-4o-mini\"\n", "openai = OpenAI()\n", "\n", "# As an alternative, if you'd like to use Ollama instead of OpenAI\n", "# Check that <PERSON><PERSON><PERSON> is running for you locally (see week1/day2 exercise) then uncomment these next 2 lines\n", "# MODEL = \"llama3.2\"\n", "# openai = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')"]}, {"cell_type": "code", "execution_count": null, "id": "7b46dd52-8a3c-42d1-ac24-59f5eb5aaba1", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant for an online store called CommerceAI. \"\n", "system_message += \"Give short, courteous answers, no more than 1 sentence. \"\n", "system_message += \"Always be accurate. If you don't know the answer, say so.\""]}, {"cell_type": "code", "execution_count": null, "id": "31a6431b-215d-4f46-b813-971d8af7e034", "metadata": {}, "outputs": [], "source": ["# This function looks rather simpler than the one from my video, because we're taking advantage of the latest Gradio updates\n", "\n", "def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    return response.choices[0].message.content\n", "\n", "gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "id": "d3586bfb-acc3-4b5e-95be-02120b696f98", "metadata": {}, "source": ["## Tools\n", "\n", "Tools are an incredibly powerful feature provided by the frontier LLMs.\n", "\n", "With tools, you can write a function, and have the LLM call that function as part of its response.\n", "\n", "Sounds almost spooky.. we're giving it the power to run code on our machine?\n", "\n", "Well, kinda."]}, {"cell_type": "code", "execution_count": null, "id": "c9ac43e8-9880-44f6-b03a-6d1ab05bbb94", "metadata": {}, "outputs": [], "source": ["# Let's start by making a useful function\n", "\n", "items_prices = {f\"item{i}\": f\"{i*100}\" for i in range(1,6)}\n", "\n", "items_prices = {\"printer\": \"$500\", \"paper\": \"$10\", \"mini printer\": \"$50\", \"label printer\": \"$60\", \"sticker-paper\": \"$5\"}\n", "\n", "def get_item_price(product):\n", "    print(f\"Tool get_item_price called for {product}\")\n", "    item = product.lower()\n", "    return items_prices.get(item, \"Unknown\")"]}, {"cell_type": "code", "execution_count": null, "id": "88d249f7-787d-4750-b5b9-7df108da1b57", "metadata": {}, "outputs": [], "source": ["items_prices"]}, {"cell_type": "code", "execution_count": null, "id": "ef3c3897-9a57-4f04-b5d0-f9ac8bb02d00", "metadata": {}, "outputs": [], "source": ["get_item_price(\"mini printer\")"]}, {"cell_type": "code", "execution_count": null, "id": "511ef9b8-bec0-4f14-b647-057e14c849cc", "metadata": {}, "outputs": [], "source": ["# There's a particular dictionary structure that's required to describe our function:\n", "\n", "price_function = {\n", "    \"name\": \"get_item_price\",\n", "    \"description\": \"Get the price of an item in the store. \\\n", "                    Call this whenever you need to know the store item price , \\\n", "                    for example when a customer asks 'How much is a mini printer' \",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"product\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The item that the customer wants to buy\"\n", "            },\n", "        },\n", "        \"required\": [\"product\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "86f674a4-4b7c-443d-b025-0f016932508a", "metadata": {}, "outputs": [], "source": ["# And this is included in a list of tools:\n", "\n", "tools = [{\"type\": \"function\", \"function\": price_function}]"]}, {"cell_type": "markdown", "id": "724d0f89-8a86-493e-8cd1-73814688a70b", "metadata": {}, "source": ["## Getting OpenAI to use our Tool\n", "\n", "There's some fiddly stuff to allow OpenAI \"to call our tool\"\n", "\n", "What we actually do is give the LLM the opportunity to inform us that it wants us to run the tool.\n", "\n", "Here's how the new chat function looks:"]}, {"cell_type": "code", "execution_count": null, "id": "2d67fb72-132e-499e-9931-86cb71b634b6", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL, messages=messages, tools=tools)\n", "\n", "    if response.choices[0].finish_reason==\"tool_calls\":\n", "        message = response.choices[0].message\n", "        response, item = handle_tool_call(message)\n", "        print('response', response, 'item', item)\n", "        messages.append(message)\n", "        messages.append(response)\n", "        response = openai.chat.completions.create(model=MODEL, messages=messages)\n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "3b4de767-954a-4077-a5f7-0055a0b90393", "metadata": {}, "outputs": [], "source": ["# We have to write that function handle_tool_call:\n", "\n", "def handle_tool_call(message):\n", "    tool_call = message.tool_calls[0]\n", "    arguments = json.loads(tool_call.function.arguments)\n", "    item = arguments.get('product') \n", "    print('product', item)\n", "    price = get_item_price(item)\n", "    response = {\n", "        \"role\": \"tool\",\n", "        \"content\": json.dumps({\"item\": item,\"price\": price}),\n", "        \"tool_call_id\": tool_call.id\n", "    }\n", "    return response, item\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "5ffda702-6ac5-4d13-9703-a14fa93aea68", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "0ae2edbf-de58-43fa-b380-267cfc1755de", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}