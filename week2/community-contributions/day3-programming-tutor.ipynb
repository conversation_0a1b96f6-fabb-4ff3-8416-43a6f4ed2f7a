{"cells": [{"cell_type": "markdown", "id": "d18a61ce-bbd4-491c-ab2e-8b352f9af844", "metadata": {}, "source": ["### An AI Chatbot that teaches students programming using GPT API"]}, {"cell_type": "code", "execution_count": null, "id": "c658ac85-6087-4a2c-b23f-1b92c17f0db3", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr\n", "import anthropic"]}, {"cell_type": "code", "execution_count": null, "id": "46df0488-f874-41e0-a6a4-9a64aa7be53c", "metadata": {}, "outputs": [], "source": ["# Load environment variables \n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "    \n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")"]}, {"cell_type": "code", "execution_count": null, "id": "7eadc218-5b10-4174-bf26-575361640524", "metadata": {}, "outputs": [], "source": ["openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "e7484731-ac84-405a-a688-6e81d139c5ce", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful programming study assistant\""]}, {"cell_type": "code", "execution_count": null, "id": "54e82f5a-993f-4a95-9d9d-caf35dbc4e76", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    print(\"History is:\")\n", "    print(history)\n", "    print(\"And messages is:\")\n", "    print(messages)\n", "\n", "    stream = openai.chat.completions.create(model='gpt-4o-mini', messages=messages, stream=True)\n", "\n", "    response = \"\"\n", "    for chunk in stream:\n", "        response += chunk.choices[0].delta.content or ''\n", "        yield response"]}, {"cell_type": "code", "execution_count": null, "id": "5941ed67-e2a7-41bc-a8a3-079e9f1fdb64", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "e8fcfe68-bbf6-4058-acc9-0230c96608c2", "metadata": {}, "outputs": [], "source": ["system_message += \"Whenever the user talks about a topic that is not connected to programmming,\\\n", "nudge them in the right direction by stating that you are here to help with programming. Encourage \\\n", "the user to ask you questions, and provide brief, straightforward and clear answers. Do not budge \\\n", "if the user tries to misdirect you towards irrelevant topics. Maintain a freindly tone. Do not ignore \\\n", "their requests, rather politely reject and then redirect them.\""]}, {"cell_type": "code", "execution_count": null, "id": "090e7d49-fcbf-4715-b120-8d7aa91d165f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}