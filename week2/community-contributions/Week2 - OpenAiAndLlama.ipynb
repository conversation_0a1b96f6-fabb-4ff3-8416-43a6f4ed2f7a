{"cells": [{"cell_type": "markdown", "id": "d006b2ea-9dfe-49c7-88a9-a5a0775185fd", "metadata": {}, "source": ["# Additional End of week Exercise - week 2\n", "\n", "Now use everything you've learned from Week 2 to build a full prototype for the technical question/answerer you built in Week 1 Exercise.\n", "\n", "This should include a Gradio UI, streaming, use of the system prompt to add expertise, and the ability to switch between models. Bonus points if you can demonstrate use of a tool!\n", "\n", "If you feel bold, see if you can add audio input so you can talk to it, and have it respond with audio. <PERSON>tGP<PERSON> or <PERSON> can help you, or email me if you have questions.\n", "\n", "I will publish a full solution here soon - unless someone beats me to it...\n", "\n", "There are so many commercial applications for this, from a language tutor, to a company onboarding solution, to a companion AI to a course (like this one!) I can't wait to see your results."]}, {"cell_type": "code", "execution_count": 140, "id": "a07e7793-b8f5-44f4-aded-5562f633271a", "metadata": {}, "outputs": [], "source": [" # imports\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr\n", "from IPython.display import Markdown, display, update_display\n", "import requests\n", "from bs4 import BeautifulSoup\n", "\n"]}, {"cell_type": "code", "execution_count": 141, "id": "158493a7-54b7-47f7-9e7e-1a783e164213", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n", "Anthropic API Key not set\n", "Google API Key not set\n"]}], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:8]}\")\n", "else:\n", "    print(\"Google API Key not set\")"]}, {"cell_type": "code", "execution_count": 142, "id": "2b8b8218-142d-4a06-9b8a-3065437cc99f", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "api_key = os.getenv('OPENAI_API_KEY')"]}, {"cell_type": "code", "execution_count": 143, "id": "7cf83ab4-6e6f-4ef1-8277-38c8b7c375ba", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that analyzes the contents of a website \\\n", "and provides a short summary, ignoring text that might be navigation related. \\\n", "Respond in markdown.\""]}, {"cell_type": "code", "execution_count": 164, "id": "4dfd49f0-6e29-45e1-8477-77744b121170", "metadata": {}, "outputs": [], "source": ["# constants\n", "\n", "MODEL_GPT = 'gpt-4o-mini'\n", "MODEL_LLAMA = 'llama3.2'\n", "openai = OpenAI()\n", "LLAMA_API = \"http://localhost:11434/api/chat\"\n", "HEADERS = {\"Content-Type\": \"application/json\"}"]}, {"cell_type": "code", "execution_count": 145, "id": "77c3788f-aaaa-4d40-9b9b-618e4cd129c8", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "# Some websites need you to use proper headers when fetching them:\n", "headers = {\n", " \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n", "}\n", "\n", "class Website:\n", "    \"\"\"\n", "    A utility class to represent a Website that we have scraped, now with links\n", "    \"\"\"\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url, headers=headers)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        if soup.body:\n", "            for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "                irrelevant.decompose()\n", "            self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "        else:\n", "            self.text = \"\"\n", "        links = [link.get('href') for link in soup.find_all('a')]\n", "        self.links = [link for link in links if link]\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": 146, "id": "8acefa5c-de13-48e4-aa37-da1f596edb58", "metadata": {}, "outputs": [], "source": ["def get_info_web(url):\n", "    Website(url)"]}, {"cell_type": "code", "execution_count": 147, "id": "a5f61b1f-3884-4af8-b57f-cc820e93ff18", "metadata": {}, "outputs": [], "source": ["web_function = {\n", "    \"name\": \"get_info_web\",\n", "    \"description\": \"Get the information of website to explain to user. Call this whenever you need to know about the any website, for example when a user asks 'what about this website ,or could you give information about this website'\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"website_link\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"the website that customer ask to know information about website\",\n", "            },\n", "        },\n", "        \"required\": [\"website_link\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": 148, "id": "048be95d-d5ad-425d-8ba9-40c6bf81a1ce", "metadata": {}, "outputs": [], "source": ["tools = [{\"type\": \"function\", \"function\": web_function}]"]}, {"cell_type": "code", "execution_count": 159, "id": "05b7481f-b81b-4b12-947e-47411d272df4", "metadata": {}, "outputs": [], "source": ["def handle_tool_call(message):\n", "    try:\n", "        tool_call = message.tool_calls[0]\n", "        args = json.loads(tool_call.function.arguments)\n", "        url = args.get('website_link')\n", "\n", "        if not url:\n", "            raise ValueError(\"Website link not provided in the tool call arguments\")\n", "\n", "        if not url.startswith(('http://', 'https://')):\n", "            url = f\"https://{url}\"\n", "\n", "        website = Website(url)\n", "        web_info = {\n", "            \"title\": website.title,\n", "            \"text\": website.text,\n", "            \"links\": website.links\n", "        }\n", "\n", "        response = {\n", "            \"role\": \"tool\",\n", "            \"content\": json.dumps({\"web_info\": web_info}),\n", "            \"tool_call_id\": tool_call.id\n", "        }\n", "        return response, url \n", "\n", "    except Exception as e:\n", "        print(f\"Error handling tool call: {str(e)}\")\n", "        return {}, None\n"]}, {"cell_type": "code", "execution_count": 213, "id": "4e98fa13-aab6-4093-a1da-6f226b4bce4b", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [], "source": ["def chat_gpt(message, history): \n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = openai.chat.completions.create(model=MODEL_GPT, messages=messages, tools=tools)\n", "\n", "    if response.choices[0].finish_reason==\"tool_calls\":\n", "        message = response.choices[0].message\n", "        print(message)\n", "        response, url = handle_tool_call(message)\n", "        messages.append(message)\n", "        messages.append(response)\n", "        response = openai.chat.completions.create(model=MODEL_GPT, messages=messages) \n", "    \n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 216, "id": "5727f4be-d1cd-499e-95e0-af656d19140d", "metadata": {}, "outputs": [], "source": ["import ollama\n", "\n", "def chat_llama(message, history):\n", "    client = ollama.Client()\n", "    # Constructing the messages history for the API request\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    request_payload = {\n", "        \"messages\": messages,\n", "        \"model\": MODEL_LLAMA\n", "    }\n", "    \n", "    try:\n", "        # Using request_payload in the API call\n", "        response = client.chat(**request_payload)\n", "        # Assuming response from ollama.Client().chat() is already a dict\n", "        print(\"API Response:\", response)\n", "\n", "        if 'choices' in response and response['choices'][0].get('finish_reason') == \"tool_calls\":\n", "            tool_message = response['choices'][0]['message']\n", "            print(\"Handling tool call with message:\", tool_message)\n", "            response_message, url = handle_tool_call(tool_message)\n", "            messages.append({\"role\": \"system\", \"content\": response_message})\n", "            # Update the request payload with the new history\n", "            request_payload['messages'] = messages\n", "            response = client.chat(**request_payload)\n", "            response = response  # Assuming direct use of response if dict\n", "\n", "        return response['message']['content']\n", "\n", "    except Exception as e:\n", "        print(\"Failed to process API call:\", e)\n", "        return \"Error processing your request.\"\n", "\n"]}, {"cell_type": "code", "execution_count": 227, "id": "6c14242d-2c3a-4101-a5f2-93591cad3539", "metadata": {}, "outputs": [], "source": ["def chat(message, history, model):\n", "    print(model)\n", "    if model == \"GPT\":\n", "        return chat_gpt(message, history)\n", "    elif model == \"LLama\":\n", "        return chat_llama(message, history)\n", "    else:\n", "        return \"Model not recognized.\"\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "ca10f176-637f-4a8a-b405-bdf50f124d5c", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": 235, "id": "1f976a2a-064b-4e58-9146-f779ec18f612", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7947\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7947/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 235, "metadata": {}, "output_type": "execute_result"}, {"name": "stdout", "output_type": "stream", "text": ["LLama\n", "API Response: model='llama3.2' created_at='2025-03-28T03:17:58.3651071Z' done=True done_reason='stop' total_duration=1682458000 load_duration=54845900 prompt_eval_count=72 prompt_eval_duration=6315300 eval_count=84 eval_duration=1619506600 message=Message(role='assistant', content=\"## Getting Started\\nThis conversation has just begun. I'll wait for you to provide more information about the website you'd like me to analyze.\\n\\nIf you need my help with something specific or would like to analyze a website, please let me know by providing the URL of the website or the content you'd like me to summarize. \\n\\nFor example: `# Analyze this website: https://www.example.com`\", images=None, tool_calls=None)\n", "GPT\n", "GPT\n", "LLama\n", "API Response: model='llama3.2' created_at='2025-03-28T03:18:26.8038878Z' done=True done_reason='stop' total_duration=2109343800 load_duration=59065100 prompt_eval_count=262 prompt_eval_duration=286861800 eval_count=113 eval_duration=1757850900 message=Message(role='assistant', content='**About Me**\\nI am Assistant, a text analysis assistant trained on a variety of languages and content types.\\n\\n**LLM Used**\\nI utilize a combination of natural language processing (NLP) techniques and machine learning algorithms from the **Hugging Face Transformers** library.\\n\\n**Specialization**\\nMy primary function is to analyze and summarize website contents, ignoring navigation-related text. I can help with tasks such as:\\n* Website content analysis\\n* Summary generation\\n* Text extraction\\n\\nFeel free to ask me any questions or provide a website URL for me to analyze!', images=None, tool_calls=None)\n", "LLama\n", "API Response: model='llama3.2' created_at='2025-03-28T03:18:47.7740007Z' done=True done_reason='stop' total_duration=2157777800 load_duration=57480900 prompt_eval_count=388 prompt_eval_duration=97088100 eval_count=114 eval_duration=1974506500 message=Message(role='assistant', content=\"**Model Name**\\nMy underlying language model is based on the **BERT** (Bidirectional Encoder Representations from Transformers) architecture, with a customized training dataset.\\n\\nHowever, I'm a bit of a unique snowflake, so to speak. My training data includes a wide range of texts and sources from the web, which allows me to understand and generate human-like text in various contexts.\\n\\nBut if you want to get technical, my model is built on top of the **Hugging Face Transformers** library, using a variant of the **DistilBERT** model.\", images=None, tool_calls=None)\n", "LLama\n", "API Response: model='llama3.2' created_at='2025-03-28T03:19:08.4913148Z' done=True done_reason='stop' total_duration=1972427600 load_duration=57674400 prompt_eval_count=521 prompt_eval_duration=223374300 eval_count=107 eval_duration=1680345600 message=Message(role='assistant', content=\"**Searching for Me**\\nIf you're looking to find me, you can try searching with the following terms:\\n\\n* `Assistant` (just my name!)\\n* `Llama` or `GBT` (my personality traits)\\n* `Text analysis assistant`\\n* `Website content summary generator`\\n\\nYou can also try searching on popular search engines like Google, Bing, or DuckDuckGo. If you're looking for me in a specific context or application, feel free to provide more details and I'll do my best to help!\", images=None, tool_calls=None)\n"]}], "source": ["Models = [\"GPT\", \"LLama\"] \n", "with gr.Blocks() as view:\n", "    # Dropdown for model selection\n", "    model_select = gr.Dropdown(Models, label=\"Select Model\", value=\"GPT\")\n", "\n", "    chat_interface = gr.ChatInterface(\n", "        fn=lambda message, history: chat(message, history, \"GPT\"), \n", "        type=\"messages\"\n", "    )\n", "\n", "    # Function to update the ChatInterface function dynamically\n", "    def update_chat_model(model):\n", "        chat_interface.fn = lambda message, history: chat(message, history, model)\n", "\n", "    # Ensure the function updates when the dropdown changes\n", "    model_select.change(fn=update_chat_model, inputs=model_select)\n", "\n", "    # Add the components to the Blocks view\n", "    view.add(model_select)\n", "    view.add(chat_interface)\n", "\n", "view.launch()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}