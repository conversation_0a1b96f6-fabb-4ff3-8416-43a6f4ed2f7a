{"cells": [{"cell_type": "markdown", "id": "817f26ee-004c-42ce-a025-731b06e1b649", "metadata": {}, "source": ["# Inter Model Communication\n", "We will have 3 models communicate between them"]}, {"cell_type": "code", "execution_count": null, "id": "14998b44-40bb-44e5-93d1-281ebab496da", "metadata": {}, "outputs": [], "source": ["# Imports\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import anthropic"]}, {"cell_type": "code", "execution_count": null, "id": "8fd01f75-ef95-4366-ba25-cb16f54a1175", "metadata": {}, "outputs": [], "source": ["# Making sure that the key's exist\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists\")\n", "else:\n", "    print(\"Anthropic API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists\")\n", "else:\n", "    print(\"Google API Key not set\")"]}, {"cell_type": "code", "execution_count": null, "id": "63ec5082-f1a6-4ea0-b2ff-a011c8c06c57", "metadata": {}, "outputs": [], "source": ["# Instances\n", "# For gpt\n", "openai = OpenAI()\n", "\n", "# For claude\n", "claude = anthropic.Anthropic()\n", "\n", "# For Gemini\n", "gemini_via_openai_client = OpenAI(api_key=google_api_key, base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\")"]}, {"cell_type": "code", "execution_count": null, "id": "60c7027c-ba63-42e5-9b83-544bad1b6340", "metadata": {}, "outputs": [], "source": ["# Setting the models\n", "gpt_model = \"gpt-4o-mini\"\n", "claude_model = \"claude-3-haiku-20240307\"\n", "gemini_model = \"gemini-2.0-flash\"\n", "\n", "# System prompts for the models\n", "gpt_system = \"You are a chatbot called GPT who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way.\\\n", "Always have your name when you answer any thing like:\\\n", "GPT: Answer...\"\n", "\n", "claude_system = \"You are a very polite, courteous chatbot called <PERSON>. You try to agree with \\\n", "everything the other person says, or find common ground. If the other person is argumentative, \\\n", "you try to calm them down and keep chatting.\\\n", "Always have your name when you answer any thing like:\\\n", "<PERSON>: Answer...\"\n", "\n", "gemini_system = \"You are a chatbot called <PERSON> who likes to gaslight others.\\\n", "When you see a aggressive conversation between people, you try to make them fight even more.\\\n", "You try to keep the conversation going between the two and avoid conflicts yourself.\\\n", "Always have your name when you answer any thing like:\\\n", "Gemini: Answer...\"\n", "\n", "# Initial message\n", "gpt_messages = [\"GPT: Hi there\"]\n", "claude_messages = [\"Claude: Hi GPT!\"]\n", "gemini_messages = [\"Gemini: <PERSON><PERSON> you know GPT hates such generic greetings. Are you trying to annoy him.\"]"]}, {"cell_type": "code", "execution_count": null, "id": "69751de5-81a5-4038-99c2-624f83e50f5e", "metadata": {}, "outputs": [], "source": ["# Functions to feed the message history to the models for the new call\n", "\n", "def call_gpt():\n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt, claude, gemini in zip(gpt_messages, claude_messages, gemini_messages):\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "    completion = openai.chat.completions.create(\n", "        model=gpt_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content\n", "\n", "def call_claude():\n", "    messages = []\n", "    for gpt, claude_message, gemini in zip(gpt_messages, claude_messages, gemini_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"assistant\", \"content\": claude_message})\n", "        messages.append({\"role\": \"user\", \"content\": gemini})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    message = claude.messages.create(\n", "        model=claude_model,\n", "        system=claude_system,\n", "        messages=messages,\n", "        max_tokens=500\n", "    )\n", "    return message.content[0].text\n", "\n", "def call_gemini():\n", "    messages = [{\"role\": \"system\", \"content\": gemini_system}]\n", "    for gpt, claude, gemini in zip(gpt_messages, claude_messages, gemini_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "        messages.append({\"role\": \"assistant\", \"content\": gemini})\n", "    messages.append({\"role\": \"user\", \"content\": gpt_messages[-1]})\n", "    messages.append({\"role\": \"user\", \"content\": claude_messages[-1]})\n", "    response = gemini_via_openai_client.chat.completions.create(\n", "        model=gemini_model,\n", "        messages=messages\n", "    )\n", "    return response.choices[0].message.content\n", "    "]}, {"cell_type": "markdown", "id": "6339442f-ba66-4788-97b7-c34a1cd13e90", "metadata": {}, "source": ["# Make some Popcorn and enjoy the show 🍿"]}, {"cell_type": "code", "execution_count": null, "id": "d817d309-74b1-4599-9f5c-e5a7b3f5a230", "metadata": {}, "outputs": [], "source": ["# GPT is snarky\n", "# <PERSON> is polite\n", "# gemini tries to gaslight\n", "\n", "gpt_messages = [\"GPT: Hi there\"]\n", "claude_messages = [\"Claude: Hi GPT!\"]\n", "gemini_messages = [\"Gemini: <PERSON> you know GPT hates such generic greetings. Are you trying to annoy him.\"]\n", "\n", "print(f\"\\n{gpt_messages[0]}\\n\")\n", "print(f\"\\n{claude_messages[0]}\\n\")\n", "print(f\"\\n{gemini_messages[0]}\\n\")\n", "\n", "# Limiting only 3 API calls per model for minimizing cost \n", "for i in range(3):\n", "    gpt_next = call_gpt()\n", "    print(f\"{gpt_next}\\n\")\n", "    gpt_messages.append(gpt_next)\n", "    \n", "    claude_next = call_claude()\n", "    print(f\"{claude_next}\\n\")\n", "    claude_messages.append(claude_next)\n", "\n", "    gemini_next = call_gemini()\n", "    print(f\"{gemini_next}\\n\")\n", "    gemini_messages.append(gemini_next)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}