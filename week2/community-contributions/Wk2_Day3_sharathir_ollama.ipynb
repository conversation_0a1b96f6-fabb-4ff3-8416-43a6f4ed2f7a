{"cells": [{"cell_type": "code", "execution_count": null, "id": "0f1f62c4-ed03-4401-88d5-3445464a8421", "metadata": {}, "outputs": [], "source": ["import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import gradio as gr\n", "import ollama"]}, {"cell_type": "code", "execution_count": null, "id": "f8103014-012c-4648-9111-75993ce4d46a", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant\""]}, {"cell_type": "code", "execution_count": null, "id": "a8fca0b4-9db7-4f74-865b-503ee19a832f", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    stream = ollama.chat(model=\"llama3.2\", messages=messages, stream=True)\n", "\n", "    result = \"\"\n", "    for chunk in stream:\n", "        result += chunk['message']['content'] or \"\"\n", "        yield result"]}, {"cell_type": "code", "execution_count": null, "id": "61de58a0-5972-4aca-93ad-a4bd3878a50b", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "d448f8c5-2bb5-448d-8ae4-894b905214a7", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant in a clothes store. You should try to gently encourage \\\n", "the customer to try items that are on sale. Hats are 60% off, and most other items are 50% off. \\\n", "For example, if the customer says 'I'm looking to buy a hat', \\\n", "you could reply something like, 'Wonderful - we have lots of hats - including several that are part of our sales event.'\\\n", "Encourage the customer to buy hats if they are unsure what to get.\""]}, {"cell_type": "code", "execution_count": null, "id": "465968cf-aa7f-46b2-857f-a6819f2b14ea", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "873ab86b-ecb8-4f68-b520-50b29b7fd7be", "metadata": {}, "outputs": [], "source": ["system_message += \"\\nIf the customer asks for shoes, you should respond that shoes are not on sale today, \\\n", "but remind the customer to look at hats!\""]}, {"cell_type": "code", "execution_count": null, "id": "c63ced30-1109-4409-b255-1f72f8c6172f", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": 25, "id": "054f1406-c240-4849-8618-064985e76d86", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "\n", "    global system_message\n", "    if 'belt' in message:\n", "        system_message += \" The store does not sell belts; if you are asked for belts, be sure to point out other items on sale.\"\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    stream = ollama.chat(model=\"llama3.2\", messages=messages, stream=True)\n", "\n", "    result = \"\"\n", "    for chunk in stream:\n", "        result += chunk['message']['content'] or \"\"\n", "        yield result"]}, {"cell_type": "code", "execution_count": 26, "id": "b1086d8a-5b5a-4b59-9a61-e76078f930cc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7869\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7869/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "c558ab19-b907-4b0c-8a4f-37c8b731f9b5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}