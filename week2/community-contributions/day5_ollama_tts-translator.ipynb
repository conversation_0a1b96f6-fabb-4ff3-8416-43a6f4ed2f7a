{"cells": [{"cell_type": "code", "execution_count": null, "id": "a8941402-99ee-4c3e-b852-056df3a77a5d", "metadata": {}, "outputs": [], "source": ["import pyttsx3\n", "import ollama\n", "import gradio as gr\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "2cbdc0ca-648a-40cc-ad30-ad8bf6126aed", "metadata": {}, "outputs": [], "source": ["def talker(response):\n", "    # Initialize text-to-speech engine\n", "    engine = pyttsx3.init()\n", "    engine.say(response)\n", "    engine.runAndWait()"]}, {"cell_type": "code", "execution_count": null, "id": "6a5b4f3c-2c6f-46db-bc66-386b30e2e707", "metadata": {}, "outputs": [], "source": ["system_message = \"you are a helpful assistance\"\n", "MODEL_LLAMA = \"llama3.2\"\n", "\n", "\n", "def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_message}] + history + [{\"role\": \"user\", \"content\": message}]\n", "\n", "    response= ollama.chat(model=MODEL_LLAMA, messages=messages)\n", "\n", "    response = response['message']['content']\n", "\n", "    # Once the full response is generated, speak it out loud\n", "\n", "    talker(response)\n", "\n", "    return response\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "cfdb3be4-a9cb-4564-87d8-4645ce0177b5", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch(share=True)"]}, {"cell_type": "markdown", "id": "38155307-6975-49ef-b65f-7d7b1dd82d32", "metadata": {}, "source": ["# Real life use as a Translator"]}, {"cell_type": "code", "execution_count": null, "id": "fa6e4b93-27e3-4455-80ca-eb7e39d13afc", "metadata": {}, "outputs": [], "source": ["system_message = \"you are helpful translator from english to korean, on the first prompt introduce your self \\\n", "that you are dealing with korean translation and you would like to translate some english words or sentences to korean\" \n", "system_message += \"dont do other tasks apart from translation\""]}, {"cell_type": "code", "execution_count": null, "id": "c0ed5e28-b294-40fc-a97c-11fe264a4d1d", "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(fn=chat, type=\"messages\").launch(share=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c63a02ae-cdc1-45a8-8f51-784d8d5417e2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}