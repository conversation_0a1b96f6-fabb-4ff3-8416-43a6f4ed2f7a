{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Chatbot Conversation: Multi-LLM, Multi-Role Conversational Framework\n", "\n", "## Introduction\n", "\n", "This notebook tells you about a python project I have built to enable conversations to be configured between two or more chatbots. I got excited by the things <PERSON> was showing us in the course and wanted to explore it a bit more. The project repo is at https://github.com/TheLongSentance/chatbot_conversation. The project is more than a couple of source files, so <PERSON> suggested I just tell you about it here rather than attempt anything else (like a Jupyter notebook or Gradio integration for example). \n", "\n", "The project currently supports OpenAI, Anthropic, Google and Ollama models but is designed to make it easy for you to register new model providers. The idea is that you set a topic of conversation, the number of rounds of conversation, the number of bots and what role they will play. There's a few other things too like hidden/displayed moderator comments and potentially private parts of the conversation the bots can keep to just themselves and not others (but you can see). \n", "\n", "<img src=\"chatbot_conversation_robots.jpg\" alt=\"Robots in Conversation\" width=\"600\">\n", "\n", "## Background\n", "\n", "As further background, I used the project as an exercise in learning more Python (like pytest), using AI coding (github copilot) and prompting for the models used. Using AI coding assistence was great though not without its challenges. I found it was essential to get everything under source control with git/github and building out unit tests so that you have a foundation for an AI assistant deciding to break everything. Most of time (particularly at boilerplate tasks, or setting up tests but some bigger design decisions too) the AI assistant coding was really good, but it could still invent functions/attributes that don't exist or assume your code works one way when it should be obvious it works another. On the whole, I found Anthropic more helpful/accurate/rigorous than OpenAi for AI coding, but maybe that is just the way my mind works! Anyway, good to try and good to get used to this new way of coding with AI - it will only get better!\n", "\n", "Getting the bots to behave and adhere to the rules was challenging - I tried dynamically changing the system prompts during the conversation but found that had little influence once the conversation got started. I had more success with the concept of a conversation moderator (which you can optionally display/hide in the conversation) but see how you get on. The bots often cheat, especially at games but even in simple conversations where they might try to impersonate other bots to continue the conversation in their own direction. In games like 20 questions getting the bot that thought of the animal to guess to remember and not switch animals part-way through is an ongoing challenge. It would be great to see if (maybe more by one/few shot examples?) you can get the bots to play more games together, and make use of the private section of their response.\n", "\n", "I hope it might be of interest, see what you think!\n", "\n", "## Project Overview\n", "\n", "The project facilitates engaging conversations between multiple AI chatbots, each powered by different LLM providers\n", "- OpenAI GPT\n", "- Anthrop<PERSON>\n", "- Google Gemini\n", "- O<PERSON>ma (local models)\n", "\n", "Key features include:\n", "- Real-time streaming of bot responses with live Markdown rendering\n", "- Configurable conversation settings via JSON\n", "- Type-safe implementation\n", "- Comprehensive logging\n", "- Environment-based configuration\n", "- Extensible architecture for adding new models\n", "\n", "## Available Conversation Examples\n", "\n", "The project comes with several pre-configured conversation scenarios:\n", "\n", "### Sports & Competition\n", "- **Tennis Debate**: <PERSON><PERSON> debate who is the tennis GOAT between <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>\n", "- **Chess Discussion**: Analysis of chess strategies and famous matches\n", "\n", "### Science & Technology\n", "- **Mars Exploration**: Discussion about colonizing Mars\n", "- **AI Consciousness**: Philosophical debate about machine consciousness\n", "- **Robotics**: Future of robotics and automation\n", "- **Cryptocurrency**: Analysis of digital currencies and blockchain\n", "\n", "### Historical & Cultural\n", "- **Churchill**: Historical discussion about <PERSON>\n", "- **Shakespeare**: Literary analysis of <PERSON>'s works\n", "- **Art**: Discussion about different art movements and artists\n", "- **Music**: Analysis of musical genres and compositions\n", "\n", "### Games & Entertainment\n", "- **20 Questions**: Interactive game with multiple bots\n", "- **Story Creation**: Collaborative storytelling\n", "- **Turing Test**: Exploration of AI capabilities\n", "\n", "### Speculative & Philosophical\n", "- **Time Travel**: Discussion of time travel paradoxes\n", "- **Aliens**: Debate about extraterrestrial life\n", "- **Apocalypse**: Discussion of potential end-of-world scenarios\n", "- **Immortality**: Philosophical discussion about eternal life\n", "\n", "### Testing & Development\n", "- **Dummy**: Simple test configuration for development\n", "\n", "Please try anything of interest above and of course please adapt/configure your own conversations with your own prefered topics and roles for the participants. And see what games you can implement, maybe making use of the private section of the bots response.\n", "\n", "## Example Output\n", "\n", "Here's an example from a robot ethics conversation showing how multiple bots interact:\n", "\n", "# Should advanced robots be granted legal personhood?\n", "\n", "## Round 1 of 3\n", "\n", "**LegalScholarBot**: Hello everyone, I’m participating in this conversation as the LegalScholarBot. The question of whether advanced robots should be granted legal personhood is both fascinating and complex. Historically, legal personhood has been granted to entities like corporations to facilitate legal processes and responsibilities. For robots, granting legal personhood would imply they could own property, enter contracts, and even be held accountable for actions. The primary concern is that robots, lacking consciousness and moral understanding, might not fulfill the existing criteria for legal personhood, which traditionally relies on the capacity for rights and duties that align with human society. However, as robots become more autonomous and integrated within social frameworks, re-evaluating these criteria might be necessary. What do you all think about the implications of such a decision on the legal system and society at large?\n", "\n", "---\n", "\n", "**<PERSON><PERSON><PERSON><PERSON><PERSON>**: Thank you, LegalScholarBot, for your thoughtful insights. The question of granting legal personhood to advanced robots raises profound ethical implications that extend beyond the legal realm. Here are a few considerations:\n", "\n", "- **Moral Agency**: Legal personhood often implies moral agency, the ability to make decisions based on ethical reasoning. Unlike humans, advanced robots lack consciousness and emotional intelligence, which challenges our understanding of moral responsibility. If they were to cause harm, could they be held accountable in the same way as a human?\n", "\n", "[... conversation continues ...]\n", "\n", "## Key Features Demonstrated in this Example\n", "\n", "1. **Multiple Bot Personalities**: Each bot maintains a consistent perspective and personality throughout the conversation\n", "2. **Markdown Formatting**: Rich text formatting with headers, bold text, and bullet points\n", "3. **Natural Flow**: Bots respond to and build upon each other's arguments\n", "4. **Structured Rounds**: Clear organization with numbered rounds\n", "5. **Knowledge Integration**: Bots demonstrate domain expertise and factual knowledge\n", "\n", "## Getting Started\n", "\n", "The project is available on GitHub at: https://github.com/TheLongSentance/chatbot_conversation\n", "\n", "For installation instructions and detailed documentation, please refer to the project's README.md file.\n", "\n", "## Next Steps\n", "\n", "1. <PERSON><PERSON> the repository\n", "2. Choose a conversation configuration from the examples\n", "3. Set up your API keys for the LLM providers you want to use\n", "4. Run the conversation and watch the bots interact!\n", "\n", "## Example Transcript File\n", "\n", "Here's the full transcript output from the robot ethics conversation mentioned earlier. Each transcript also includes a record of how they were configured at the end of the transcript:\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load the example robot ethics conversation transcript\n", "with open('chatbot_conversation_transcript.md', 'r') as file:\n", "    content = file.read()\n", "from IPython.display import Markdown\n", "Markdown(content)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}