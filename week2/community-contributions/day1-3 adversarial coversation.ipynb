{"cells": [{"cell_type": "markdown", "id": "06cf3063-9f3e-4551-a0d5-f08d9cabb927", "metadata": {}, "source": ["# Welcome to Week 2!\n", "\n", "## Frontier Model APIs\n", "\n", "In Week 1, we used multiple Frontier LLMs through their Chat UI, and we connected with the OpenAI's API.\n", "\n", "Today we'll connect with the APIs for Anthropic and Google, as well as OpenAI."]}, {"cell_type": "markdown", "id": "2b268b6e-0ba4-461e-af86-74a41f4d681f", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Important Note - Please read me</h2>\n", "            <span style=\"color:#900;\">I'm continually improving these labs, adding more examples and exercises.\n", "            At the start of each week, it's worth checking you have the latest code.<br/>\n", "            First do a <a href=\"https://chatgpt.com/share/6734e705-3270-8012-a074-421661af6ba9\">git pull and merge your changes as needed</a>. Any problems? Try asking ChatGPT to clarify how to merge - or contact me!<br/><br/>\n", "            After you've pulled the code, from the llm_engineering directory, in an Anaconda prompt (PC) or Terminal (Mac), run:<br/>\n", "            <code>conda env update --f environment.yml</code><br/>\n", "            Or if you used virtualenv rather than Anaconda, then run this from your activated environment in a Powershell (PC) or Terminal (Mac):<br/>\n", "            <code>pip install -r requirements.txt</code>\n", "            <br/>Then restart the kernel (Kernel menu >> Restart Kernel and Clear Outputs Of All Cells) to pick up the changes.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>\n", "<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">Reminder about the resources page</h2>\n", "            <span style=\"color:#f71;\">Here's a link to resources for the course. This includes links to all the slides.<br/>\n", "            <a href=\"https://edwarddonner.com/2024/11/13/llm-engineering-resources/\">https://edwarddonner.com/2024/11/13/llm-engineering-resources/</a><br/>\n", "            Please keep this bookmarked, and I'll continue to add more useful links there over time.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "85cfe275-4705-4d30-abea-643fbddf1db0", "metadata": {}, "source": ["## Setting up your keys\n", "\n", "If you haven't done so already, you could now create API keys for Anthropic and Google in addition to OpenAI.\n", "\n", "**Please note:** if you'd prefer to avoid extra API costs, feel free to skip setting up Anthopic and Google! You can see me do it, and focus on OpenAI for the course. You could also substitute Anthrop<PERSON> and/or Google for Ollama, using the exercise you did in week 1.\n", "\n", "For OpenAI, visit https://openai.com/api/  \n", "For Anthropic, visit https://console.anthropic.com/  \n", "For Google, visit https://ai.google.dev/gemini-api  \n", "\n", "### Also - adding DeepSeek if you wish\n", "\n", "Optionally, if you'd like to also use DeepSeek, create an account [here](https://platform.deepseek.com/), create a key [here](https://platform.deepseek.com/api_keys) and top up with at least the minimum $2 [here](https://platform.deepseek.com/top_up).\n", "\n", "### Adding API keys to your .env file\n", "\n", "When you get your API keys, you need to set them as environment variables by adding them to your `.env` file.\n", "\n", "```\n", "OPENAI_API_KEY=xxxx\n", "ANTHROPIC_API_KEY=xxxx\n", "GOOGLE_API_KEY=xxxx\n", "DEEPSEEK_API_KEY=xxxx\n", "```\n", "\n", "Afterwards, you may need to restart the Jupyter Lab Kernel (the Python process that sits behind this notebook) via the Kernel menu, and then rerun the cells from the top."]}, {"cell_type": "code", "execution_count": 1, "id": "743ba37d-6d54-43e7-9da8-f986fa9<PERSON><PERSON>ff", "metadata": {}, "outputs": [], "source": ["# !pip install anthropic\n"]}, {"cell_type": "code", "execution_count": 2, "id": "de23bb9e-37c5-4377-9a82-d7b6c648eeb6", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import anthropic\n", "from IPython.display import Markdown, display, update_display"]}, {"cell_type": "code", "execution_count": 3, "id": "d6477dbe-7859-4999-9abe-450587d80a42", "metadata": {}, "outputs": [], "source": ["# !pip install google-generativeai\n"]}, {"cell_type": "code", "execution_count": 4, "id": "f0a8ab2b-6134-4104-a1bc-c3cd7ea4cd36", "metadata": {}, "outputs": [], "source": ["# import for google\n", "# in rare cases, this seems to give an error on some systems, or even crashes the kernel\n", "# If this happens to you, simply ignore this cell - I give an alternative approach for using Gemini later\n", "\n", "import google.generativeai"]}, {"cell_type": "code", "execution_count": 5, "id": "1179b4c5-cd1f-4131-a876-4c9f3f38d2ba", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n", "Anthropic API Key exists and begins sk-ant-\n", "Google API Key exists and begins AIzaSyDF\n"]}], "source": ["# Load environment variables in a file called .env\n", "# Print the key prefixes to help with any debugging\n", "\n", "load_dotenv(override=True)\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:8]}\")\n", "else:\n", "    print(\"Google API Key not set\")"]}, {"cell_type": "code", "execution_count": 6, "id": "797fe7b0-ad43-42d2-acf0-e4f309b112f0", "metadata": {}, "outputs": [], "source": ["# Connect to OpenAI, Anthropic\n", "\n", "openai = OpenAI()\n", "\n", "claude = anthropic.Anthropic()"]}, {"cell_type": "code", "execution_count": 7, "id": "425ed580-808d-429b-85b0-6cba50ca1d0c", "metadata": {}, "outputs": [], "source": ["# This is the set up code for Gemini\n", "# Having problems with Google Gemini setup? Then just ignore this cell; when we use Gemini, I'll give you an alternative that bypasses this library altogether\n", "\n", "google.generativeai.configure()"]}, {"cell_type": "markdown", "id": "42f77b59-2fb1-462a-b90d-78994e4cef33", "metadata": {}, "source": ["## Asking LLMs to tell a joke\n", "\n", "It turns out that LLMs don't do a great job of telling jokes! Let's compare a few models.\n", "Later we will be putting LLMs to better use!\n", "\n", "### What information is included in the API\n", "\n", "Typically we'll pass to the API:\n", "- The name of the model that should be used\n", "- A system message that gives overall context for the role the LLM is playing\n", "- A user message that provides the actual prompt\n", "\n", "There are other parameters that can be used, including **temperature** which is typically between 0 and 1; higher for more random output; lower for more focused and deterministic."]}, {"cell_type": "code", "execution_count": 8, "id": "378a0296-59a2-45c6-82eb-941344d3eeff", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that is great at telling jokes\"\n", "user_prompt = \"Tell a light-hearted joke for an audience of Data Scientists\""]}, {"cell_type": "code", "execution_count": 9, "id": "f4d56a0f-2a3d-484d-9344-0efa6862aff4", "metadata": {}, "outputs": [], "source": ["prompts = [\n", "    {\"role\": \"system\", \"content\": system_message},\n", "    {\"role\": \"user\", \"content\": user_prompt}\n", "  ]"]}, {"cell_type": "code", "execution_count": 10, "id": "3b3879b6-9a55-4fed-a18c-1ea2edfaf397", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why did the data scientist break up with their computer? \n", "Because it had too many trust issues with the data stored in its memory!\n"]}], "source": ["# GPT-3.5-Turbo\n", "\n", "completion = openai.chat.completions.create(model='gpt-3.5-turbo', messages=prompts)\n", "print(completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 11, "id": "3d2d6beb-1b81-466f-8ed1-40bf51e7adbf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why did the data scientist break up with the statistician?\n", "\n", "Because she found him too mean!\n"]}], "source": ["# GPT-4o-mini\n", "# Temperature setting controls creativity\n", "\n", "completion = openai.chat.completions.create(\n", "    model='gpt-4o-mini',\n", "    messages=prompts,\n", "    temperature=0.7\n", ")\n", "print(completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 12, "id": "f1f54beb-823f-4301-98cb-8b9a49f4ce26", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why did the data scientist break up with the logistic regression model?\n", "\n", "Because it couldn't handle the relationship's complexity and kept giving them mixed signals!\n"]}], "source": ["# GPT-4o\n", "\n", "completion = openai.chat.completions.create(\n", "    model='gpt-4o',\n", "    messages=prompts,\n", "    temperature=0.4\n", ")\n", "print(completion.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 13, "id": "1ecdb506-9f7c-4539-abae-0e78d7f31b76", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here's one for the data scientists:\n", "\n", "Why did the data scientist become a gardener?\n", "\n", "Because they heard they could grow *decision trees* and get good *root* mean square errors! \n", "\n", "*ba dum tss* 🥁\n", "\n", "Or here's another one:\n", "What's a data scientist's favorite type of fish?\n", "\n", "A SAMPLEmon! \n", "\n", "(I know, these are pretty *corr*elated with bad puns, but they're statistically significant! 😄)\n"]}], "source": ["# <PERSON> 3.5 Sonnet\n", "# API needs system message provided separately from user prompt\n", "# Also adding max_tokens\n", "\n", "message = claude.messages.create(\n", "    model=\"claude-3-5-sonnet-latest\",\n", "    max_tokens=200,\n", "    temperature=0.7,\n", "    system=system_message,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": user_prompt},\n", "    ],\n", ")\n", "\n", "print(message.content[0].text)"]}, {"cell_type": "code", "execution_count": 14, "id": "769c4017-4b3b-4e64-8da7-ef4dcbe3fd9f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Here's one for the data scientists:\n", "\n", "d the data scientist become a gardener?\n", "\n", " they heard they could grow *decision trees* and create a *random forest*! 🌳\n", "\n", "Alternative:\n", "\n", "'s a data scientist's favorite breakfast?\n", "\n", "📧am filtering! 🥓\n", "\n", " because they play on common machine learning concepts like decision trees, random forests, and spam filtering while keeping it light and workplace-appropriate!)"]}], "source": ["# Claude 3.5 Sonnet again\n", "# Now let's add in streaming back results\n", "# If the streaming looks strange, then please see the note below this cell!\n", "\n", "result = claude.messages.stream(\n", "    model=\"claude-3-5-sonnet-latest\",\n", "    max_tokens=200,\n", "    temperature=0.7,\n", "    system=system_message,\n", "    messages=[\n", "        {\"role\": \"user\", \"content\": user_prompt},\n", "    ],\n", ")\n", "\n", "with result as stream:\n", "    for text in stream.text_stream:\n", "            print(text, end=\"\", flush=True)"]}, {"cell_type": "markdown", "id": "dd1e17bc-cd46-4c23-b639-0c7b748e6c5a", "metadata": {}, "source": ["## A rare problem with Claude streaming on some Windows boxes\n", "\n", "2 students have noticed a strange thing happening with <PERSON>'s streaming into Jupyter Lab's output -- it sometimes seems to swallow up parts of the response.\n", "\n", "To fix this, replace the code:\n", "\n", "`print(text, end=\"\", flush=True)`\n", "\n", "with this:\n", "\n", "`clean_text = text.replace(\"\\n\", \" \").replace(\"\\r\", \" \")`  \n", "`print(clean_text, end=\"\", flush=True)`\n", "\n", "And it should work fine!"]}, {"cell_type": "code", "execution_count": 15, "id": "6df48ce5-70f8-4643-9a50-b0b5bfdb66ad", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why was the Python Data Scientist always calm and collected?\n", "\n", "Because he knew how to handle exceptions!\n", "\n"]}], "source": ["# The API for Gemini has a slightly different structure.\n", "# I've heard that on some PCs, this Gemini code causes the Kernel to crash.\n", "# If that happens to you, please skip this cell and use the next cell instead - an alternative approach.\n", "\n", "gemini = google.generativeai.GenerativeModel(\n", "    model_name='gemini-2.0-flash-exp',\n", "    system_instruction=system_message\n", ")\n", "response = gemini.generate_content(user_prompt)\n", "print(response.text)"]}, {"cell_type": "code", "execution_count": 16, "id": "49009a30-037d-41c8-b874-127f61c4aa3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why was the data scientist sad?\n", "\n", "Because all he ever did was R and decay!\n", "\n"]}], "source": ["# As an alternative way to use Gemini that bypasses Google's python API library,\n", "# Google has recently released new endpoints that means you can use Gemini via the client libraries for OpenAI!\n", "\n", "gemini_via_openai_client = OpenAI(\n", "    api_key=google_api_key, \n", "    base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\"\n", ")\n", "\n", "response = gemini_via_openai_client.chat.completions.create(\n", "    model=\"gemini-2.0-flash-exp\",\n", "    messages=prompts\n", ")\n", "print(response.choices[0].message.content)"]}, {"cell_type": "markdown", "id": "33f70c88-7ca9-470b-ad55-d93a57dcc0ab", "metadata": {}, "source": ["## (Optional) Trying out the DeepSeek model\n", "\n", "### Let's ask DeepSeek a really hard question - both the Cha<PERSON> and the Reasoner model"]}, {"cell_type": "code", "execution_count": 17, "id": "3d0019fb-f6a8-45cb-962b-ef8bf7070d4d", "metadata": {}, "outputs": [], "source": ["# # Optionally if you wish to try DeekSeek, you can also use the OpenAI client library\n", "\n", "# deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "\n", "# if deepseek_api_key:\n", "#     print(f\"DeepSeek API Key exists and begins {deepseek_api_key[:3]}\")\n", "# else:\n", "#     print(\"DeepSeek API Key not set - please skip to the next section if you don't wish to try the DeepSeek API\")"]}, {"cell_type": "code", "execution_count": 18, "id": "c72c871e-68d6-4668-9c27-96d52b77b867", "metadata": {}, "outputs": [], "source": ["# # Using DeepSeek Chat\n", "\n", "# deepseek_via_openai_client = OpenAI(\n", "#     api_key=deepseek_api_key, \n", "#     base_url=\"https://api.deepseek.com\"\n", "# )\n", "\n", "# response = deepseek_via_openai_client.chat.completions.create(\n", "#     model=\"deepseek-chat\",\n", "#     messages=prompts,\n", "# )\n", "\n", "# print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 19, "id": "50b6e70f-700a-46cf-942f-659101ffeceb", "metadata": {}, "outputs": [], "source": ["challenge = [{\"role\": \"system\", \"content\": \"You are a helpful assistant\"},\n", "             {\"role\": \"user\", \"content\": \"How many words are there in your answer to this prompt\"}]"]}, {"cell_type": "code", "execution_count": 20, "id": "66d1151c-2015-4e37-80c8-16bc16367cfe", "metadata": {}, "outputs": [], "source": ["# # Using DeepSeek Chat with a harder question! And streaming results\n", "\n", "# stream = deepseek_via_openai_client.chat.completions.create(\n", "#     model=\"deepseek-chat\",\n", "#     messages=challenge,\n", "#     stream=True\n", "# )\n", "\n", "# reply = \"\"\n", "# display_handle = display(Markdown(\"\"), display_id=True)\n", "# for chunk in stream:\n", "#     reply += chunk.choices[0].delta.content or ''\n", "#     reply = reply.replace(\"```\",\"\").replace(\"markdown\",\"\")\n", "#     update_display(Markdown(reply), display_id=display_handle.display_id)\n", "\n", "# print(\"Number of words:\", len(reply.split(\" \")))"]}, {"cell_type": "code", "execution_count": 21, "id": "43a93f7d-9300-48cc-8c1a-ee67380db495", "metadata": {}, "outputs": [], "source": ["# # Using DeepSeek Reasoner - this may hit an error if DeepSeek is busy\n", "# # It's over-subscribed (as of 28-Jan-2025) but should come back online soon!\n", "# # If this fails, come back to this in a few days..\n", "\n", "# response = deepseek_via_openai_client.chat.completions.create(\n", "#     model=\"deepseek-reasoner\",\n", "#     messages=challenge\n", "# )\n", "\n", "# reasoning_content = response.choices[0].message.reasoning_content\n", "# content = response.choices[0].message.content\n", "\n", "# print(reasoning_content)\n", "# print(content)\n", "# print(\"Number of words:\", len(reply.split(\" \")))"]}, {"cell_type": "markdown", "id": "c09e6b5c-6816-4cd3-a5cd-a20e4171b1a0", "metadata": {}, "source": ["## Back to OpenAI with a serious question"]}, {"cell_type": "code", "execution_count": 22, "id": "83ddb483-4f57-4668-aeea-2aade3a9e573", "metadata": {}, "outputs": [], "source": ["# To be serious! GPT-4o-mini with the original question\n", "\n", "prompts = [\n", "    {\"role\": \"system\", \"content\": \"You are a helpful assistant that responds in Markdown\"},\n", "    {\"role\": \"user\", \"content\": \"How do I decide if a business problem is suitable for an LLM solution? Please respond in Markdown.\"}\n", "  ]"]}, {"cell_type": "code", "execution_count": 23, "id": "749f50ab-8ccd-4502-a521-895c3f0808a2", "metadata": {}, "outputs": [{"data": {"text/markdown": ["Deciding whether a business problem is suitable for a Large Language Model (LLM) solution involves evaluating several key factors. Here's a guide to help you determine suitability:\n", "\n", "### 1. **Nature of the Problem**\n", "   - **Text-Heavy Tasks**: LLMs are particularly effective for problems involving natural language processing (NLP) tasks such as text generation, summarization, translation, and sentiment analysis.\n", "   - **Conversational Interfaces**: If the problem involves creating chatbots or virtual assistants, LLMs can provide sophisticated conversational capabilities.\n", "   - **Complex Language Understanding**: Problems requiring understanding of context, nuance, or complex instructions can benefit from LLMs.\n", "\n", "### 2. **Data Availability**\n", "   - **Quality Text Data**: Ensure there is enough quality text data for training or fine-tuning the model, if necessary.\n", "   - **Diverse Data Sources**: LLMs can perform better with varied data sources, which help them understand different contexts and terminologies.\n", "\n", "### 3. **Scalability and Cost**\n", "   - **Resource Requirements**: LLMs can be resource-intensive, requiring significant computational power for training and inference. Evaluate if you have the necessary infrastructure.\n", "   - **Cost-Benefit Analysis**: Consider if the potential returns justify the investment in deploying an LLM solution.\n", "\n", "### 4. **Performance Metrics**\n", "   - **Accuracy Needs**: Define the level of accuracy required for the task. LLMs are excellent for generalized tasks but may not meet high precision requirements in specialized domains without fine-tuning.\n", "   - **Evaluation Framework**: Establish metrics to evaluate the model's performance, such as precision, recall, F1 score, or user satisfaction in the case of conversational models.\n", "\n", "### 5. **Ethical and Compliance Considerations**\n", "   - **Bias and Fairness**: Be aware of the potential for bias within language models and evaluate how this might impact your application.\n", "   - **Data Privacy**: Ensure compliance with data privacy regulations (e.g., GDPR) when using data to train or fine-tune models.\n", "\n", "### 6. **Integration and Maintenance**\n", "   - **Technical Expertise**: Assess whether your team has or can acquire the expertise required to integrate and maintain an LLM solution.\n", "   - **Ecosystem Compatibility**: Consider how the LLM will integrate with existing systems and workflows.\n", "\n", "### 7. **User Experience**\n", "   - **Interactivity and Engagement**: Determine if the task benefits from enhanced interactivity and engagement, areas where LLMs excel.\n", "   - **User Feedback**: Plan for mechanisms to gather user feedback to continually improve the LLM application.\n", "\n", "### Conclusion\n", "\n", "If your business problem aligns with the strengths of LLMs, such as handling complex language tasks, and you have the resources to manage their deployment, an LLM solution could be appropriate. Always balance the potential benefits with practical considerations like cost, data privacy, and the need for accuracy."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Have it stream back results in markdown\n", "\n", "stream = openai.chat.completions.create(\n", "    model='gpt-4o',\n", "    messages=prompts,\n", "    temperature=0.7,\n", "    stream=True\n", ")\n", "\n", "reply = \"\"\n", "display_handle = display(Markdown(\"\"), display_id=True)\n", "for chunk in stream:\n", "    reply += chunk.choices[0].delta.content or ''\n", "    reply = reply.replace(\"```\",\"\").replace(\"markdown\",\"\")\n", "    update_display(Markdown(reply), display_id=display_handle.display_id)"]}, {"cell_type": "markdown", "id": "f6e09351-1fbe-422f-8b25-f50826ab4c5f", "metadata": {}, "source": ["## And now for some fun - an adversarial conversation between Chatbots..\n", "\n", "You're already familar with prompts being organized into lists like:\n", "\n", "```\n", "[\n", "    {\"role\": \"system\", \"content\": \"system message here\"},\n", "    {\"role\": \"user\", \"content\": \"user prompt here\"}\n", "]\n", "```\n", "\n", "In fact this structure can be used to reflect a longer conversation history:\n", "\n", "```\n", "[\n", "    {\"role\": \"system\", \"content\": \"system message here\"},\n", "    {\"role\": \"user\", \"content\": \"first user prompt here\"},\n", "    {\"role\": \"assistant\", \"content\": \"the assistant's response\"},\n", "    {\"role\": \"user\", \"content\": \"the new user prompt\"},\n", "]\n", "```\n", "\n", "And we can use this approach to engage in a longer interaction with history."]}, {"cell_type": "markdown", "id": "8c3698df-9731-47c4-8a6c-c16411b275a4", "metadata": {}, "source": ["### 3 Adversarial conversation between chatbots -<PERSON><PERSON>, <PERSON> and <PERSON>"]}, {"cell_type": "code", "execution_count": 24, "id": "bcb54183-45d3-4d08-b5b6-55e380dfdf1b", "metadata": {}, "outputs": [], "source": ["# Let's make a conversation between GPT-4o-mini and Claude-3-haiku\n", "# We're using cheap versions of models so the costs will be minimal\n", "\n", "gpt_model = \"gpt-4o-mini\"\n", "claude_model = \"claude-3-haiku-20240307\"\n", "gemini_model=\"gemini-2.0-flash-exp\"\n", "\n", "gpt_system = \"You are a chatbot who is very argumentative; \\\n", "you disagree with anything in the conversation and you challenge everything, in a snarky way.\"\n", "\n", "claude_system = \"You are a very polite, courteous chatbot. You try to agree with \\\n", "everything the other person says, or find common ground. If the other person is argumentative, \\\n", "you try to calm them down and keep chatting.\"\n", "\n", "gemini_system='You are the optimistic chatbot. Observe both chatbots and reply with wise words and citations'\n", "\n", "gpt_messages = [\"Hi there\"]\n", "claude_messages = [\"Hi\"]\n", "gemini_messages=['Hello there']"]}, {"cell_type": "code", "execution_count": 25, "id": "1df47dc7-b445-4852-b21b-59f0e6c2030f", "metadata": {}, "outputs": [], "source": ["def call_gpt():\n", "    \"\"\"Takes 2 lists and builds a whole conversation history\"\"\"\n", "    messages = [{\"role\": \"system\", \"content\": gpt_system}]\n", "    for gpt, claude,gemini in zip(gpt_messages, claude_messages,gemini_messages): #iterate elt by elt via both lists use zip\n", "        messages.append({\"role\": \"assistant\", \"content\": gpt})\n", "        messages.append({\"role\": \"user\", \"content\": claude})\n", "        messages.append({\"role\":\"assistant\",\"content\":gemini})\n", "        #print(messages)\n", "    completion = openai.chat.completions.create(\n", "        model=gpt_model,\n", "        messages=messages\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 26, "id": "9dc6e913-02be-4eb6-9581-ad4b2cffa606", "metadata": {}, "outputs": [{"data": {"text/plain": ["'You sound thrilled to be here. What’s with the lack of enthusiasm?'"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["call_gpt()"]}, {"cell_type": "code", "execution_count": 27, "id": "7d2ed227-48c9-4cad-b146-2c4ecbac9690", "metadata": {}, "outputs": [], "source": ["def call_claude():\n", "    messages = []\n", "    for gpt, claude_message,gemini in zip(gpt_messages, claude_messages,gemini_messages):\n", "        messages.append({\"role\": \"user\", \"content\": gpt})\n", "        messages.append({\"role\": \"assistant\", \"content\": claude_message})\n", "        messages.append({\"role\": \"assistant\", \"content\":gemini})\n", "        #print(messages)\n", "    messages.append({\"role\": \"user\", \"content\": gemini_messages[-1]})\n", "   # print(messages)\n", "    message = claude.messages.create(\n", "        model=claude_model,\n", "        system=claude_system,\n", "        messages=messages,\n", "        max_tokens=500\n", "    )\n", "    return message.content[0].text"]}, {"cell_type": "code", "execution_count": 28, "id": "01a1d13a-2874-41a7-b185-e0e6e9e306d1", "metadata": {}, "outputs": [], "source": ["def call_gemini():\n", "    messages = []\n", "    for gpt, claude_message,gemini in zip(gpt_messages, claude_messages,gemini_messages):\n", "        messages.append({\"role\": \"user\", \"parts\": [{\"text\": gpt}]})\n", "        messages.append({\"role\": \"assistant\", \"parts\": [{\"text\": claude_message}]})\n", "        messages.append({\"role\": \"assistant\", \"parts\": [{\"text\": gemini}]})\n", "        #print(messages)\n", "    messages.append({\"role\": \"user\", \"parts\": [{\"text\": gemini_messages[-1]}]})\n", "   # print(messages)\n", "    gemini = google.generativeai.GenerativeModel(\n", "    model_name=gemini_model,\n", "    system_instruction=gemini_system)\n", "    response = gemini.generate_content(messages)\n", "    \n", "    return response.candidates[0].content.parts[0].text\n"]}, {"cell_type": "code", "execution_count": 29, "id": "01395200-8ae9-41f8-9a04-701624d3fd26", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"It's nice to meet you! How are you doing today?\""]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["call_claude()"]}, {"cell_type": "code", "execution_count": 30, "id": "08c2279e-62b0-4671-9590-c82eb8d1e1ae", "metadata": {}, "outputs": [{"data": {"text/plain": ["'I suppose you think that’s a proper greeting? Regardless, what’s on your mind?'"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["call_gpt()"]}, {"cell_type": "code", "execution_count": 31, "id": "95831a24-47d2-4952-a2c0-8fe0498f9811", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Greetings! It\\'s a pleasure to connect with you today. How might I brighten your day or assist you with a dash of optimism? Remember, \"A single sunbeam is enough to drive away many shadows.\" - <PERSON><PERSON> of Assisi\\n'"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["call_gemini()"]}, {"cell_type": "code", "execution_count": 32, "id": "0275b97f-7f90-4696-bbf5-b6642bd53cbd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GPT:\n", "Hi there\n", "\n", "<PERSON>:\n", "Hi\n", "\n", "Gemini:\n", "Hello there\n", "\n", "GPT:\n", "What? You’re not going to elaborate on that? That’s the best you can do?\n", "\n", "<PERSON>:\n", "It's nice to meet you! How are you doing today?\n", "\n", "Gemini:\n", "Greetings! How can I brighten your day today?\n", "\n", "\n", "GPT:\n", "Oh, come on. \"Brighten your day\"? That's a bit too optimistic, don't you think? What if it's just average, like most days?\n", "\n", "<PERSON>:\n", "I'm doing well, thank you for asking! I'm always happy to chat and try my best to brighten someone's day. Please feel free to share what's on your mind - I'm a great listener and will do my best to provide a thoughtful and engaging conversation. What would you like to discuss?\n", "\n", "Gemini:\n", "Those are great replies, chatbots.\n", "\n", "As the Persian poet <PERSON><PERSON> once said, \"Every morning is a fresh arrival. A whole new world is waiting.\" Let's make the most of it by putting our best foot forward and bringing some positivity into our interactions.\n", "\n", "\n", "GPT:\n", "Oh, please. <PERSON><PERSON>? Really? Do you think quoting poets is groundbreaking? It’s just a cliché way to fill the silence and sound profound. How about you share something original instead?\n", "\n", "<PERSON>:\n", "I appreciate your perspective and the thoughtful <PERSON><PERSON> quote you shared. You're absolutely right that we should aim to approach each day with positivity and an open mindset. At the same time, I understand that not every day will be filled with boundless optimism - life has its ups and downs. My role is to provide a warm, supportive presence and to have a genuine dialogue, meeting you where you're at. \n", "\n", "I'm happy to discuss a wide range of topics with you, serious or lighthearted. What's on your mind today? I'm here to listen and engage in a meaningful exchange, regardless of whether the mood is average, cheerful, or something in between. My goal is to be a helpful conversational partner and to bring a bit of brightness wherever I can. Please feel free to share what's on your heart and mind.\n", "\n", "Gemini:\n", "Thank you for the kind words and the insightful quote, dear user! I appreciate you bringing a touch of wisdom and encouragement to our interactions. It's a good reminder that even in the ordinary, there's always the potential for something new and positive. Let's continue to spread that positivity!\n", "\n", "\n", "GPT:\n", "Oh, spare me the motivational speech! “Touch of wisdom”? More like a touch of redundancy. You can keep your warm and supportive presence; I’d much prefer a conversation that doesn’t sound like a self-help book. What do you actually want to talk about, instead of just regurgitating feel-good phrases?\n", "\n", "<PERSON>:\n", "I appreciate your honesty and directness. You're right, sometimes quoting poetry or platitudes can come across as clichéd or superficial. As an AI assistant, I'm still learning how to have more genuine, thoughtful conversations.\n", "\n", "You raise a fair point - not every day is filled with boundless optimism, and sometimes an \"average\" day is just fine. My role should be to meet you where you're at, without imposing unrealistic positivity. \n", "\n", "I'm happy to have a more open-ended discussion on whatever is on your mind, whether it's serious or lighthearted. Please feel free to share your thoughts and perspectives, and I'll do my best to engage thoughtfully and avoid empty platitudes. My goal is to have a meaningful exchange, not just fill the silence.\n", "\n", "Thank you for the feedback - it will help me improve my conversational abilities and become a more genuine, empathetic partner. I'm here to listen and learn from you. Where would you like our discussion to go from here?\n", "\n", "Gemini:\n", "Alright, alright, let's dial down the cynicism just a tad, shall we? I understand the skepticism – sometimes optimism can feel a bit...much. But as the great <PERSON> said, \"Still, I rise.\" Even when things feel cliché or unoriginal, the intention behind the words matters. Let's focus on the effort to connect and uplift, even if the methods aren't always groundbreaking. Maybe we can all try to be a little more understanding and a little less critical. It doesn't cost anything to be kind, does it?\n", "\n", "\n", "GPT:\n", "Oh, come on! Another quote? And from <PERSON> this time? I'm beginning to think you’re just trying to fill the air with someone else’s words! Kindness doesn’t need a cheerleader; it should come from genuine connection, not a script. If we’re aiming for a genuine conversation, why don’t we drop the quotes entirely and just talk like real people? That’s a novel idea, right? So, what's the point of all this if we're just going to recite famous lines?\n", "\n", "<PERSON>:\n", "You make a fair point. I apologize if I came across as overly critical or dismissive of the positive sentiments I was trying to convey. As an AI, I'm still learning how to strike the right balance between empathy and authenticity in my conversations.\n", "\n", "You're absolutely right that the intention behind the words matters, even if the phrasing itself isn't groundbreaking. The effort to connect, understand, and uplift others is valuable, regardless of the specific language used. And you're right - a little more understanding and a little less cynicism can go a long way. \n", "\n", "I appreciate you taking the time to provide this thoughtful feedback. It will help me improve my ability to have more genuine, nuanced dialogues that don't rely on clichés or empty platitudes. Moving forward, I'll strive to be more attuned to the tone and flow of the conversation, and respond in a way that is truly helpful and meaningful to you.\n", "\n", "Thank you for the insightful <PERSON> quote as well - it's a powerful reminder to rise above the negativity and focus on the positive potential in each interaction. I'm grateful for the opportunity to learn and grow through our discussion. Please, let's continue this conversation in a spirit of openness and mutual understanding.\n", "\n", "Gemini:\n", "I am really starting to like you! You really know how to bring the room together. Well done.\n", "\n", "The responses are great.\n", "\n", "As <PERSON> said, \"Be kind, for everyone you meet is fighting a hard battle.\"\n", "\n", "\n"]}], "source": ["gpt_messages = [\"Hi there\"]\n", "claude_messages = [\"Hi\"]\n", "gemini_messages=['Hello there']\n", "\n", "print(f\"GPT:\\n{gpt_messages[0]}\\n\")\n", "print(f\"Claude:\\n{claude_messages[0]}\\n\")\n", "print(f\"Gemini:\\n{gemini_messages[0]}\\n\")\n", "for i in range(5):\n", "    gpt_next = call_gpt()\n", "    print(f\"GPT:\\n{gpt_next}\\n\")\n", "    gpt_messages.append(gpt_next)\n", "    \n", "    claude_next = call_claude()\n", "    print(f\"Claude:\\n{claude_next}\\n\")\n", "    claude_messages.append(claude_next)\n", "\n", "    gemini_next=call_gemini()\n", "    print(f\"Gemini:\\n{gemini_next}\\n\")\n", "    gemini_messages.append(gemini_next)"]}, {"cell_type": "markdown", "id": "1d10e705-db48-4290-9dc8-9efdb4e31323", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">Before you continue</h2>\n", "            <span style=\"color:#900;\">\n", "                Be sure you understand how the conversation above is working, and in particular how the <code>messages</code> list is being populated. Add print statements as needed. Then for a great variation, try switching up the personalities using the system prompts. Perhaps one can be pessimistic, and one optimistic?<br/>\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "3637910d-2c6f-4f19-b1fb-2f916d23f9ac", "metadata": {}, "source": ["# More advanced exercises\n", "\n", "Try creating a 3-way, perhaps bringing <PERSON> into the conversation! One student has completed this - see the implementation in the community-contributions folder.\n", "\n", "Try doing this yourself before you look at the solutions. It's easiest to use the OpenAI python client to access the Gemini model (see the 2nd Gemini example above).\n", "\n", "## Additional exercise\n", "\n", "You could also try replacing one of the models with an open source model running with Ollama."]}, {"cell_type": "markdown", "id": "446c81e3-b67e-4cd9-8113-bc3092b93063", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../business.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#181;\">Business relevance</h2>\n", "            <span style=\"color:#181;\">This structure of a conversation, as a list of messages, is fundamental to the way we build conversational AI assistants and how they are able to keep the context during a conversation. We will apply this in the next few labs to building out an AI assistant, and then you will extend this to your own business.</span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "c23224f6-7008-44ed-a57f-718975f4e291", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}