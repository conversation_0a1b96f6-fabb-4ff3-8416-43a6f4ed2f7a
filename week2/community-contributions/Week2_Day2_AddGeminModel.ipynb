{"cells": [{"cell_type": "markdown", "id": "8b0e11f2-9ea4-48c2-b8d2-d0a4ba967827", "metadata": {}, "source": ["# Gradio Day!\n", "\n", "Today we will build User Interfaces using the outrageously simple Gradio framework.\n", "\n", "Prepare for joy!"]}, {"cell_type": "code", "execution_count": null, "id": "c44c5494-950d-4d2f-8d4f-b87b57c5b330", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import requests\n", "from bs4 import BeautifulSoup\n", "from typing import List\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import google.generativeai\n", "import anthropic"]}, {"cell_type": "code", "execution_count": null, "id": "d1715421-cead-400b-99af-986388a97aff", "metadata": {}, "outputs": [], "source": ["import gradio as gr # oh yeah!"]}, {"cell_type": "code", "execution_count": null, "id": "337d5dfc-0181-4e3b-8ab9-e78e0c3f657b", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv()\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['ANTHROPIC_API_KEY'] = os.getenv('ANTHROPIC_API_KEY', 'your-key-if-not-using-env')\n", "os.environ['GOOGLE_API_KEY'] = os.getenv('GOOGLE_API_KEY', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": null, "id": "22586021-1795-4929-8079-63f5bb4edd4c", "metadata": {}, "outputs": [], "source": ["# Connect to OpenAI, Anthropic and Google\n", "\n", "openai = OpenAI()\n", "\n", "claude = anthropic.Anthropic()\n", "\n", "google.generativeai.configure()"]}, {"cell_type": "code", "execution_count": null, "id": "b16e6021-6dc4-4397-985a-6679d6c8ffd5", "metadata": {}, "outputs": [], "source": ["# A generic system message - no more snarky adversarial AIs!\n", "\n", "system_message = \"You are a helpful assistant\""]}, {"cell_type": "code", "execution_count": null, "id": "02ef9b69-ef31-427d-86d0-b8c799e1c1b1", "metadata": {}, "outputs": [], "source": ["# Let's wrap a call to GPT-4o-mini in a simple function\n", "\n", "def message_gpt(prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    completion = openai.chat.completions.create(\n", "        model='gpt-4o-mini',\n", "        messages=messages,\n", "    )\n", "    return completion.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "id": "aef7d314-2b13-436b-b02d-8de3b72b193f", "metadata": {}, "outputs": [], "source": ["message_gpt(\"What is today's date?\")"]}, {"cell_type": "markdown", "id": "f94013d1-4f27-4329-97e8-8c58db93636a", "metadata": {}, "source": ["## User Interface time!"]}, {"cell_type": "code", "execution_count": null, "id": "bc664b7a-c01d-4fea-a1de-ae22cdd5141a", "metadata": {}, "outputs": [], "source": ["# here's a simple function\n", "\n", "def shout(text):\n", "    print(f\"Shout has been called with input {text}\")\n", "    return text.upper()"]}, {"cell_type": "code", "execution_count": null, "id": "083ea451-d3a0-4d13-b599-93ed49b975e4", "metadata": {}, "outputs": [], "source": ["shout(\"hello\")"]}, {"cell_type": "code", "execution_count": null, "id": "08f1f15a-122e-4502-b112-6ee2817dda32", "metadata": {}, "outputs": [], "source": ["gr.Interface(fn=shout, inputs=\"textbox\", outputs=\"textbox\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "c9a359a4-685c-4c99-891c-bb4d1cb7f426", "metadata": {}, "outputs": [], "source": ["gr.Interface(fn=shout, inputs=\"textbox\", outputs=\"textbox\", flagging_mode=\"never\").launch(share=True)"]}, {"cell_type": "code", "execution_count": null, "id": "3cc67b26-dd5f-406d-88f6-2306ee2950c0", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=shout,\n", "    inputs=[gr.Textbox(label=\"Your message:\", lines=6)],\n", "    outputs=[gr.Textbox(label=\"Response:\", lines=8)],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "f235288e-63a2-4341-935b-1441f9be969b", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=message_gpt,\n", "    inputs=[gr.Textbox(label=\"Your message:\", lines=6)],\n", "    outputs=[gr.Textbox(label=\"Response:\", lines=8)],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "af9a3262-e626-4e4b-80b0-aca152405e63", "metadata": {}, "outputs": [], "source": ["system_message = \"You are a helpful assistant that responds in markdown\"\n", "\n", "view = gr.Interface(\n", "    fn=message_gpt,\n", "    inputs=[gr.Textbox(label=\"Your message:\")],\n", "    outputs=[gr.Mark<PERSON>(label=\"Response:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "88c04ebf-0671-4fea-95c9-bc1565d4bb4f", "metadata": {}, "outputs": [], "source": ["# Let's create a call that streams back results\n", "\n", "def stream_gpt(prompt):\n", "    messages = [\n", "        {\"role\": \"system\", \"content\": system_message},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    stream = openai.chat.completions.create(\n", "        model='gpt-4o-mini',\n", "        messages=messages,\n", "        stream=True\n", "    )\n", "    result = \"\"\n", "    for chunk in stream:\n", "        result += chunk.choices[0].delta.content or \"\"\n", "        yield result"]}, {"cell_type": "code", "execution_count": null, "id": "0bb1f789-ff11-4cba-ac67-11b815e29d09", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=stream_gpt,\n", "    inputs=[gr.Textbox(label=\"Your message:\")],\n", "    outputs=[gr.Mark<PERSON>(label=\"Response:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "bbc8e930-ba2a-4194-8f7c-044659150626", "metadata": {}, "outputs": [], "source": ["def stream_claude(prompt):\n", "    result = claude.messages.stream(\n", "        model=\"claude-3-haiku-20240307\",\n", "        max_tokens=1000,\n", "        temperature=0.7,\n", "        system=system_message,\n", "        messages=[\n", "            {\"role\": \"user\", \"content\": prompt},\n", "        ],\n", "    )\n", "    response = \"\"\n", "    with result as stream:\n", "        for text in stream.text_stream:\n", "            response += text or \"\"\n", "            yield response"]}, {"cell_type": "code", "execution_count": null, "id": "a0066ffd-196e-4eaf-ad1e-d492958b62af", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=stream_claude,\n", "    inputs=[gr.Textbox(label=\"Your message:\")],\n", "    outputs=[gr.Mark<PERSON>(label=\"Response:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "markdown", "id": "72d7de50-22ba-4758-92ea-9a4820947488", "metadata": {}, "source": ["# Add Gemini Model"]}, {"cell_type": "code", "execution_count": null, "id": "026abd83-fb9a-4c8f-9f4d-cc73f9d20779", "metadata": {}, "outputs": [], "source": ["import google.generativeai as genai\n", "\n", "def stream_gemini(prompt):\n", "    gemini = genai.GenerativeModel(\n", "    model_name='gemini-1.5-flash',\n", "    safety_settings=None,\n", "    system_instruction=system_message\n", "    )\n", "\n", "    response = gemini.generate_content(prompt, safety_settings=[\n", "        {\"category\": \"HARM_CATEGORY_DANGEROUS_CONTENT\", \"threshold\": \"BLOCK_NONE\"},\n", "        {\"category\": \"HARM_CATEGORY_SEXUALLY_EXPLICIT\", \"threshold\": \"BLOCK_NONE\"},\n", "        {\"category\": \"HARM_CATEGORY_HATE_SPEECH\", \"threshold\": \"BLOCK_NONE\"},\n", "        {\"category\": \"HARM_CATEGORY_HARASSMENT\", \"threshold\": \"BLOCK_NONE\"}], stream=True)\n", "    \n", "    result = \"\"\n", "    for chunk in response:\n", "        result += chunk.text\n", "        yield result\n"]}, {"cell_type": "code", "execution_count": null, "id": "cf6fc87f-dd11-4668-9faa-19cb4f4865f1", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=stream_gemini,\n", "    inputs=[gr.Textbox(label=\"Your message:\")],\n", "    outputs=[gr.Mark<PERSON>(label=\"Response:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "markdown", "id": "bc5a70b9-2afe-4a7c-9bed-2429229e021b", "metadata": {}, "source": ["## Minor improvement\n", "\n", "I've made a small improvement to this code.\n", "\n", "Previously, it had these lines:\n", "\n", "```\n", "for chunk in result:\n", "  yield chunk\n", "```\n", "\n", "There's actually a more elegant way to achieve this (which Python people might call more 'Pythonic'):\n", "\n", "`yield from result`"]}, {"cell_type": "code", "execution_count": null, "id": "0087623a-4e31-470b-b2e6-d8d16fc7bcf5", "metadata": {}, "outputs": [], "source": ["def stream_model(prompt, model):\n", "    print(model) #Shows what model is being used\n", "    if model==\"GPT\":\n", "        result = stream_gpt(prompt)\n", "    elif model==\"Claude\":\n", "        result = stream_claude(prompt)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    yield from result"]}, {"cell_type": "code", "execution_count": null, "id": "8d8ce810-997c-4b6a-bc4f-1fc847ac8855", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=stream_model,\n", "    inputs=[gr.Textbox(label=\"Your message:\"), gr.Dropdown([\"GPT\", \"<PERSON>\", \"Gemini\"], label=\"Select model\")],\n", "    outputs=[gr.Mark<PERSON>(label=\"Response:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "markdown", "id": "76211a29-e1d5-49a9-b176-bb2d50e85155", "metadata": {}, "source": ["# Added Gemini Model to the Model Selection"]}, {"cell_type": "code", "execution_count": null, "id": "4b1eb9ab-927b-44a7-9565-180bde4453b7", "metadata": {}, "outputs": [], "source": ["def stream_model(prompt, model):\n", "    print(model) #Shows what model is being used\n", "    if model==\"GPT\":\n", "        result = stream_gpt(prompt)\n", "    elif model==\"Claude\":\n", "        result = stream_claude(prompt)\n", "    elif model==\"Gemini\":\n", "        result = stream_gemini(prompt)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    yield from result"]}, {"cell_type": "code", "execution_count": null, "id": "89adb706-8f6d-43c2-b99c-e4786278e7b0", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=stream_model,\n", "    inputs=[gr.Textbox(label=\"Your message:\"), gr.Dropdown([\"GPT\", \"<PERSON>\", \"Gemini\"], label=\"Select model\")],\n", "    outputs=[gr.Mark<PERSON>(label=\"Response:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "markdown", "id": "d933865b-654c-4b92-aa45-cf389f1eda3d", "metadata": {}, "source": ["# Building a company brochure generator\n", "\n", "Now you know how - it's simple!"]}, {"cell_type": "code", "execution_count": null, "id": "1626eb2e-eee8-4183-bda5-1591b58ae3cf", "metadata": {}, "outputs": [], "source": ["# A class to represent a Webpage\n", "\n", "class Website:\n", "    url: str\n", "    title: str\n", "    text: str\n", "\n", "    def __init__(self, url):\n", "        self.url = url\n", "        response = requests.get(url)\n", "        self.body = response.content\n", "        soup = BeautifulSoup(self.body, 'html.parser')\n", "        self.title = soup.title.string if soup.title else \"No title found\"\n", "        for irrelevant in soup.body([\"script\", \"style\", \"img\", \"input\"]):\n", "            irrelevant.decompose()\n", "        self.text = soup.body.get_text(separator=\"\\n\", strip=True)\n", "\n", "    def get_contents(self):\n", "        return f\"Webpage Title:\\n{self.title}\\nWebpage Contents:\\n{self.text}\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "id": "c701ec17-ecd5-4000-9f68-34634c8ed49d", "metadata": {}, "outputs": [], "source": ["system_message = \"You are an assistant that analyzes the contents of a company website landing page \\\n", "and creates a short brochure about the company for prospective customers, investors and recruits. Do not use any logos. Respond in markdown.\""]}, {"cell_type": "code", "execution_count": null, "id": "5def90e0-4343-4f58-9d4a-0e36e445efa4", "metadata": {}, "outputs": [], "source": ["def stream_brochure(company_name, url, model, response_tone):\n", "    prompt = f\"Please generate a {response_tone} company brochure for {company_name}. Here is their landing page:\\n\"\n", "    prompt += Website(url).get_contents()\n", "    if model==\"GPT\":\n", "        result = stream_gpt(prompt)\n", "    elif model==\"Claude\":\n", "        result = stream_claude(prompt)\n", "    elif model==\"Gemini\":\n", "        result = stream_gemini(prompt)\n", "    else:\n", "        raise ValueError(\"Unknown model\")\n", "    yield from result"]}, {"cell_type": "code", "execution_count": null, "id": "66399365-5d67-4984-9d47-93ed26c0bd3d", "metadata": {}, "outputs": [], "source": ["view = gr.Interface(\n", "    fn=stream_brochure,\n", "    inputs=[\n", "        gr.Textbox(label=\"Company name:\"),\n", "        gr.Textbox(label=\"Landing page URL including http:// or https://\"),\n", "        gr.Dropdown([\"GP<PERSON>\", \"<PERSON>\", \"<PERSON>\"], label=\"Select model\"),\n", "        gr.Dropdown([\"Informational\", \"Promotional\", \"Humorous\"], label=\"Select tone\")],\n", "    outputs=[gr.<PERSON><PERSON>(label=\"Brochure:\")],\n", "    flagging_mode=\"never\"\n", ")\n", "view.launch()"]}, {"cell_type": "code", "execution_count": null, "id": "d0fc580a-dc98-48c3-9dd4-b19cd3be5a18", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d3d3bf11-e02c-492b-96f1-f4dd7df6f4d7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}