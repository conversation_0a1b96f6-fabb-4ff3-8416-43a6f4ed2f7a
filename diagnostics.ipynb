{"cells": [{"cell_type": "markdown", "id": "73287ed4-81e3-496a-9e47-f0e8c3770ce9", "metadata": {}, "source": ["# Gathering Essential Diagnostic information\n", "\n", "## Please run this next cell to gather some important data\n", "\n", "Please run the next cell; it should take a minute or so to run (mostly the network test).\n", "<PERSON><PERSON> email me the output of the last <NAME_EMAIL>.  \n", "Alternatively: this will create a file called report.txt - just attach the file to your email."]}, {"cell_type": "code", "execution_count": null, "id": "ed8056e8-efa2-4b6f-a4bb-e7ceb733c517", "metadata": {}, "outputs": [], "source": ["# Run my diagnostics report to collect key information for debugging\n", "# Please email me the results. Either copy & paste the output, or attach the file report.txt\n", "\n", "!pip install -q requests speedtest-cli psutil setuptools\n", "from diagnostics import Diagnostics\n", "Diagnostics().run()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}