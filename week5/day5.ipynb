{"cells": [{"cell_type": "markdown", "id": "dfe37963-1af6-44fc-a841-8e462443f5e6", "metadata": {}, "source": ["## Expert Knowledge Worker\n", "\n", "### A question answering agent that is an expert knowledge worker\n", "### To be used by employees of Insurellm, an Insurance Tech company\n", "### The agent needs to be accurate and the solution should be low cost.\n", "\n", "This project will use RAG (Retrieval Augmented Generation) to ensure our question/answering assistant has high accuracy.\n", "\n", "This first implementation will use a simple, brute-force type of RAG.."]}, {"cell_type": "code", "execution_count": null, "id": "ba2779af-84ef-4227-9e9e-6eaf0df87e77", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import glob\n", "from dotenv import load_dotenv\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "id": "802137aa-8a74-45e0-a487-d1974927d7ca", "metadata": {}, "outputs": [], "source": ["# imports for langchain, plotly and Chroma\n", "\n", "from langchain.document_loaders import DirectoryLoader, TextLoader\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.schema import Document\n", "from langchain_openai import OpenAIEmbeddings, ChatOpenAI\n", "from langchain_chroma import Chroma\n", "import matplotlib.pyplot as plt\n", "from sklearn.manifold import TSNE\n", "import numpy as np\n", "import plotly.graph_objects as go\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.chains import ConversationalRetrievalChain\n", "from langchain.embeddings import HuggingFaceEmbeddings"]}, {"cell_type": "code", "execution_count": null, "id": "58c85082-e417-4708-9efe-81a5d55d1424", "metadata": {}, "outputs": [], "source": ["# price is a factor for our company, so we're going to use a low cost model\n", "\n", "MODEL = \"gpt-4o-mini\"\n", "db_name = \"vector_db\""]}, {"cell_type": "code", "execution_count": null, "id": "ee78efcb-60fe-449e-a944-40bab26261af", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": null, "id": "730711a9-6ffe-4eee-8f48-d6cfb7314905", "metadata": {}, "outputs": [], "source": ["# Read in documents using <PERSON><PERSON><PERSON><PERSON>'s loaders\n", "# Take everything in all the sub-folders of our knowledgebase\n", "\n", "folders = glob.glob(\"knowledge-base/*\")\n", "\n", "def add_metadata(doc, doc_type):\n", "    doc.metadata[\"doc_type\"] = doc_type\n", "    return doc\n", "\n", "# With thanks to <PERSON><PERSON> and <PERSON>, students on the course, for this fix needed for some users \n", "text_loader_kwargs = {'encoding': 'utf-8'}\n", "# If that doesn't work, some Windows users might need to uncomment the next line instead\n", "# text_loader_kwargs={'autodetect_encoding': True}\n", "\n", "documents = []\n", "for folder in folders:\n", "    doc_type = os.path.basename(folder)\n", "    loader = DirectoryLoader(folder, glob=\"**/*.md\", loader_cls=TextLoader, loader_kwargs=text_loader_kwargs)\n", "    folder_docs = loader.load()\n", "    documents.extend([add_metadata(doc, doc_type) for doc in folder_docs])\n", "\n", "text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "chunks = text_splitter.split_documents(documents)\n", "\n", "print(f\"Total number of chunks: {len(chunks)}\")\n", "print(f\"Document types found: {set(doc.metadata['doc_type'] for doc in documents)}\")"]}, {"cell_type": "markdown", "id": "77f7d2a6-ccfa-425b-a1c3-5e55b23bd013", "metadata": {}, "source": ["## A sidenote on Embeddings, and \"Auto-Encoding LLMs\"\n", "\n", "We will be mapping each chunk of text into a Vector that represents the meaning of the text, known as an embedding.\n", "\n", "OpenAI offers a model to do this, which we will use by calling their API with some LangChain code.\n", "\n", "This model is an example of an \"Auto-Encoding LLM\" which generates an output given a complete input.\n", "It's different to all the other LLMs we've discussed today, which are known as \"Auto-Regressive LLMs\", and generate future tokens based only on past context.\n", "\n", "Another example of an Auto-Encoding LLMs is BERT from Google. In addition to embedding, Auto-encoding LLMs are often used for classification.\n", "\n", "### Sidenote\n", "\n", "In week 8 we will return to RAG and vector embeddings, and we will use an open-source vector encoder so that the data never leaves our computer - that's an important consideration when building enterprise systems and the data needs to remain internal."]}, {"cell_type": "code", "execution_count": null, "id": "78998399-ac17-4e28-b15f-0b5f51e6ee23", "metadata": {}, "outputs": [], "source": ["# Put the chunks of data into a Vector Store that associates a Vector Embedding with each chunk\n", "# Chroma is a popular open source Vector Database based on SQLLite\n", "\n", "embeddings = OpenAIEmbeddings()\n", "\n", "# If you would rather use the free Vector Embeddings from HuggingFace sentence-transformers\n", "# Then replace embeddings = OpenAIEmbeddings()\n", "# with:\n", "# from langchain.embeddings import HuggingFaceEmbeddings\n", "# embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-MiniLM-L6-v2\")\n", "\n", "# Delete if already exists\n", "\n", "if os.path.exists(db_name):\n", "    Chroma(persist_directory=db_name, embedding_function=embeddings).delete_collection()\n", "\n", "# Create vectorstore\n", "\n", "vectorstore = Chroma.from_documents(documents=chunks, embedding=embeddings, persist_directory=db_name)\n", "print(f\"Vectorstore created with {vectorstore._collection.count()} documents\")"]}, {"cell_type": "code", "execution_count": null, "id": "ff2e7687-60d4-4920-a1d7-a34b9f70a250", "metadata": {}, "outputs": [], "source": ["# Let's investigate the vectors\n", "\n", "collection = vectorstore._collection\n", "count = collection.count()\n", "\n", "sample_embedding = collection.get(limit=1, include=[\"embeddings\"])[\"embeddings\"][0]\n", "dimensions = len(sample_embedding)\n", "print(f\"There are {count:,} vectors with {dimensions:,} dimensions in the vector store\")"]}, {"cell_type": "markdown", "id": "b0d45462-a818-441c-b010-b85b32bcf618", "metadata": {}, "source": ["## Visualizing the Vector Store\n", "\n", "Let's take a minute to look at the documents and their embedding vectors to see what's going on."]}, {"cell_type": "code", "execution_count": null, "id": "b98adf5e-d464-4bd2-9bdf-bc5b6770263b", "metadata": {}, "outputs": [], "source": ["# Prework (with thanks to <PERSON> for identifying and fixing a bug in this!)\n", "\n", "result = collection.get(include=['embeddings', 'documents', 'metadatas'])\n", "vectors = np.array(result['embeddings'])\n", "documents = result['documents']\n", "metadatas = result['metadatas']\n", "doc_types = [metadata['doc_type'] for metadata in metadatas]\n", "colors = [['blue', 'green', 'red', 'orange'][['products', 'employees', 'contracts', 'company'].index(t)] for t in doc_types]"]}, {"cell_type": "code", "execution_count": null, "id": "427149d5-e5d8-4abd-bb6f-7ef0333cca21", "metadata": {}, "outputs": [], "source": ["# We humans find it easier to visalize things in 2D!\n", "# Reduce the dimensionality of the vectors to 2D using t-SNE\n", "# (t-distributed stochastic neighbor embedding)\n", "\n", "tsne = TSNE(n_components=2, random_state=42)\n", "reduced_vectors = tsne.fit_transform(vectors)\n", "\n", "# Create the 2D scatter plot\n", "fig = go.Figure(data=[go.<PERSON><PERSON>(\n", "    x=reduced_vectors[:, 0],\n", "    y=reduced_vectors[:, 1],\n", "    mode='markers',\n", "    marker=dict(size=5, color=colors, opacity=0.8),\n", "    text=[f\"Type: {t}<br>Text: {d[:100]}...\" for t, d in zip(doc_types, documents)],\n", "    hoverinfo='text'\n", ")])\n", "\n", "fig.update_layout(\n", "    title='2D Chroma Vector Store Visualization',\n", "    scene=dict(xaxis_title='x',yaxis_title='y'),\n", "    width=800,\n", "    height=600,\n", "    margin=dict(r=20, b=10, l=10, t=40)\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e1418e88-acd5-460a-bf2b-4e6efc88e3dd", "metadata": {}, "outputs": [], "source": ["# Let's try 3D!\n", "\n", "tsne = TSNE(n_components=3, random_state=42)\n", "reduced_vectors = tsne.fit_transform(vectors)\n", "\n", "# Create the 3D scatter plot\n", "fig = go.Figure(data=[go.Scatter3d(\n", "    x=reduced_vectors[:, 0],\n", "    y=reduced_vectors[:, 1],\n", "    z=reduced_vectors[:, 2],\n", "    mode='markers',\n", "    marker=dict(size=5, color=colors, opacity=0.8),\n", "    text=[f\"Type: {t}<br>Text: {d[:100]}...\" for t, d in zip(doc_types, documents)],\n", "    hoverinfo='text'\n", ")])\n", "\n", "fig.update_layout(\n", "    title='3D Chroma Vector Store Visualization',\n", "    scene=dict(xaxis_title='x', yaxis_title='y', zaxis_title='z'),\n", "    width=900,\n", "    height=700,\n", "    margin=dict(r=20, b=10, l=10, t=40)\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "id": "9468860b-86a2-41df-af01-b2400cc985be", "metadata": {}, "source": ["## Time to use <PERSON><PERSON><PERSON><PERSON> to bring it all together"]}, {"cell_type": "code", "execution_count": null, "id": "129c7d1e-0094-4479-9459-f9360b95f244", "metadata": {}, "outputs": [], "source": ["# create a new Chat with OpenAI\n", "llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "\n", "# Alternative - if you'd like to use Ollama locally, uncomment this line instead\n", "# llm = ChatOpenAI(temperature=0.7, model_name='llama3.2', base_url='http://localhost:11434/v1', api_key='ollama')\n", "\n", "# set up the conversation memory for the chat\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n", "\n", "# the retriever is an abstraction over the VectorStore that will be used during RAG\n", "retriever = vectorstore.as_retriever()\n", "\n", "# putting it together: set up the conversation chain with the GPT 3.5 LLM, the vector store and memory\n", "conversation_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory)"]}, {"cell_type": "code", "execution_count": null, "id": "968e7bf2-e862-4679-a11f-6c1efb6ec8ca", "metadata": {}, "outputs": [], "source": ["# Let's try a simple question\n", "\n", "query = \"Please explain what Insurellm is in a couple of sentences\"\n", "result = conversation_chain.invoke({\"question\": query})\n", "print(result[\"answer\"])"]}, {"cell_type": "code", "execution_count": null, "id": "5b5a9013-d5d4-4e25-9e7c-cdbb4f33e319", "metadata": {}, "outputs": [], "source": ["# set up a new conversation memory for the chat\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n", "\n", "# putting it together: set up the conversation chain with the GPT 4o-mini LLM, the vector store and memory\n", "conversation_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory)"]}, {"cell_type": "markdown", "id": "bbbcb659-13ce-47ab-8a5e-01b930494964", "metadata": {}, "source": ["## Now we will bring this up in Gradio using the Chat interface -\n", "\n", "A quick and easy way to prototype a chat with an LLM"]}, {"cell_type": "code", "execution_count": null, "id": "c3536590-85c7-4155-bd87-ae78a1467670", "metadata": {}, "outputs": [], "source": ["# Wrapping that in a function\n", "\n", "def chat(question, history):\n", "    result = conversation_chain.invoke({\"question\": question})\n", "    return result[\"answer\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b252d8c1-61a8-406d-b57a-8f708a62b014", "metadata": {}, "outputs": [], "source": ["# And in Gradio:\n", "\n", "view = gr.ChatInterface(chat, type=\"messages\").launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "b55e9abb-e1da-46c5-acba-911868aee329", "metadata": {}, "outputs": [], "source": ["# Let's investigate what gets sent behind the scenes\n", "\n", "from langchain_core.callbacks import StdOutCallbackHandler\n", "\n", "llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n", "\n", "retriever = vectorstore.as_retriever()\n", "\n", "conversation_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory, callbacks=[StdOutCallbackHandler()])\n", "\n", "query = \"Who received the prestigious IIOTY award in 2023?\"\n", "result = conversation_chain.invoke({\"question\": query})\n", "answer = result[\"answer\"]\n", "print(\"\\nAnswer:\", answer)"]}, {"cell_type": "code", "execution_count": null, "id": "2136153b-d2f6-4c58-a0e3-78c3a932cf55", "metadata": {}, "outputs": [], "source": ["# create a new Chat with OpenAI\n", "llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "\n", "# set up the conversation memory for the chat\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n", "\n", "# the retriever is an abstraction over the VectorStore that will be used during RAG; k is how many chunks to use\n", "retriever = vectorstore.as_retriever(search_kwargs={\"k\": 25})\n", "\n", "# putting it together: set up the conversation chain with the GPT 3.5 LLM, the vector store and memory\n", "conversation_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory)"]}, {"cell_type": "code", "execution_count": null, "id": "5c2bfa3c-810b-441b-90d1-31533f14b1e3", "metadata": {}, "outputs": [], "source": ["def chat(question, history):\n", "    result = conversation_chain.invoke({\"question\": question})\n", "    return result[\"answer\"]"]}, {"cell_type": "code", "execution_count": null, "id": "c736f33b-941e-4853-8eaf-2003bd988b18", "metadata": {}, "outputs": [], "source": ["view = gr.ChatInterface(chat, type=\"messages\").launch(inbrowser=True)"]}, {"cell_type": "markdown", "id": "644753e7-17f3-4999-a37a-b6aebf1e4579", "metadata": {}, "source": ["# Exercises\n", "\n", "Try applying this to your own folder of data, so that you create a personal knowledge worker, an expert on your own information!"]}, {"cell_type": "code", "execution_count": null, "id": "30b4745a-0a6c-4544-b78b-c827cfec1fb9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}