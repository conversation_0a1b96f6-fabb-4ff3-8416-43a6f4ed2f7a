{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import glob\n", "from dotenv import load_dotenv\n", "import gradio as gr\n", "# import gemini\n", "import google.generativeai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# imports for langchain and Chroma and plotly\n", "\n", "from langchain.document_loaders import DirectoryLoader, TextLoader\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.schema import Document\n", "from langchain_openai import OpenAIEmbeddings, ChatOpenAI\n", "from langchain_chroma import Chroma\n", "from langchain_google_genai import GoogleGenerativeAIEmbeddings\n", "\n", "import numpy as np\n", "from sklearn.manifold import TSNE\n", "import plotly.graph_objects as go"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# price is a factor for our company, so we're going to use a low cost model\n", "\n", "MODEL = \"gemini-1.5-flash\"\n", "db_name = \"vector_db\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv()\n", "os.environ['GOOGLE_API_KEY'] = os.getenv('GOOGLE_API_KEY', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["google.generativeai.configure()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# Read in documents using <PERSON><PERSON><PERSON><PERSON>'s loaders\n", "# Take everything in all the sub-folders of our knowledgebase\n", "\n", "folders = glob.glob(\"knowledge-base/*\")\n", "\n", "# With thanks to <PERSON><PERSON> and <PERSON>, students on the course, for this fix needed for some users \n", "text_loader_kwargs = {'encoding': 'utf-8'}\n", "# If that doesn't work, some Windows users might need to uncomment the next line instead\n", "# text_loader_kwargs={'autodetect_encoding': True}\n", "\n", "documents = []\n", "for folder in folders:\n", "    doc_type = os.path.basename(folder)\n", "    loader = DirectoryLoader(folder, glob=\"**/*.md\", loader_cls=TextLoader, loader_kwargs=text_loader_kwargs)\n", "    folder_docs = loader.load()\n", "    for doc in folder_docs:\n", "        doc.metadata[\"doc_type\"] = doc_type\n", "        documents.append(doc)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Created a chunk of size 1088, which is longer than the specified 1000\n"]}], "source": ["text_splitter = CharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "chunks = text_splitter.split_documents(documents)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["123"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["len(chunks)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document types found: employees, contracts, products, company\n"]}], "source": ["doc_types = set(chunk.metadata['doc_type'] for chunk in chunks)\n", "print(f\"Document types found: {', '.join(doc_types)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Embegging using langchain_google_genai"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["embeddings = GoogleGenerativeAIEmbeddings(model=\"models/embedding-001\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Check if a Chroma Datastore already exists - if so, delete the collection to start from scratch\n", "\n", "if os.path.exists(db_name):\n", "    Chroma(persist_directory=db_name, embedding_function=embeddings).delete_collection()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vectorstore created with 123 documents\n"]}], "source": ["# Create our Chroma vectorstore!\n", "\n", "vectorstore = Chroma.from_documents(documents=chunks, embedding=embeddings, persist_directory=db_name)\n", "print(f\"Vectorstore created with {vectorstore._collection.count()} documents\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The vectors have 768 dimensions\n"]}], "source": ["# Get one vector and find how many dimensions it has\n", "\n", "collection = vectorstore._collection\n", "sample_embedding = collection.get(limit=1, include=[\"embeddings\"])[\"embeddings\"][0]\n", "dimensions = len(sample_embedding)\n", "print(f\"The vectors have {dimensions:,} dimensions\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([-1.85247306e-02,  1.97027717e-03, -1.15211494e-02,  2.23240890e-02,\n", "        8.41063485e-02,  3.64531651e-02,  2.63696015e-02,  1.50563465e-02,\n", "        4.84857559e-02,  3.80692482e-02,  1.83093594e-04,  2.24398952e-02,\n", "        4.60567214e-02,  4.58190292e-02,  3.74429822e-02, -5.23896851e-02,\n", "        1.15476940e-02,  3.38097848e-02, -3.03355325e-02, -8.63027293e-03,\n", "        5.64942770e-02,  2.51798406e-02,  1.38015151e-02, -2.07526479e-02,\n", "       -1.87167544e-02, -5.78521052e-03,  3.82627323e-02, -5.68991937e-02,\n", "       -4.89688739e-02,  4.87425253e-02, -5.03955260e-02,  4.04499583e-02,\n", "       -1.47977415e-02, -2.80260411e-03, -2.85318792e-02, -1.24896644e-02,\n", "       -1.88693665e-02,  3.28911357e-02,  1.54064260e-02, -1.13518359e-02,\n", "        1.19983163e-02, -4.97919060e-02, -7.15689212e-02,  3.09262015e-02,\n", "        3.62883396e-02, -2.03951504e-02, -7.55731598e-04,  2.51011271e-02,\n", "        3.39337029e-02, -5.55131771e-02, -2.86268047e-03, -7.47634424e-03,\n", "        3.86099182e-02, -3.56446877e-02,  1.85160991e-02, -1.19267786e-02,\n", "        1.68699641e-02,  1.58497505e-02, -1.08698392e-02,  2.08130740e-02,\n", "        6.39916444e-03,  3.05734184e-02,  5.82463294e-02, -1.44922675e-03,\n", "       -1.79196689e-02, -2.34130044e-02, -3.13566029e-02,  1.37667591e-02,\n", "        4.96128462e-02,  5.82867675e-03, -2.33113561e-02,  2.03036945e-02,\n", "        7.26327226e-02, -7.70192454e-03,  2.78026573e-02, -1.37509912e-01,\n", "       -1.44480485e-02,  4.16051000e-02,  1.67854633e-02,  2.36726133e-03,\n", "       -2.00128066e-03, -3.60025503e-02, -6.90808743e-02, -3.29498723e-02,\n", "       -5.02625778e-02,  3.79297920e-02, -3.34151275e-02,  1.56359505e-02,\n", "       -3.85190472e-02,  1.16659962e-02, -4.66518424e-04, -2.63051875e-02,\n", "        5.54691255e-02, -6.97175264e-02, -1.66818849e-03,  2.73272246e-02,\n", "       -1.61965825e-02, -7.92282149e-02,  4.47267629e-02,  6.27311831e-03,\n", "       -1.52192293e-02, -5.41190691e-02, -5.28662018e-02,  1.95346586e-02,\n", "        4.98477593e-02,  1.75764207e-02,  2.77924556e-02,  4.11877260e-02,\n", "       -8.70027393e-03,  1.09095387e-02, -7.46374056e-02, -1.40648121e-02,\n", "        8.47891625e-03,  1.82989165e-02,  5.40199410e-03, -4.91827056e-02,\n", "        3.01663689e-02,  1.20082296e-01,  4.19785194e-02,  5.37006371e-02,\n", "        1.95586067e-02,  3.67937014e-02,  5.55788800e-02,  3.01843323e-02,\n", "        1.23615358e-02, -2.52238587e-02, -1.90039817e-03,  1.25963325e-02,\n", "        1.96099468e-02, -2.76104994e-02,  8.50712322e-03, -3.35235824e-03,\n", "       -1.83853842e-02, -8.47999286e-03,  4.49112691e-02,  7.80286118e-02,\n", "        3.13673019e-02, -5.87284006e-02,  6.18342683e-03, -3.69714014e-02,\n", "       -6.11646585e-02,  8.15040059e-03, -2.09620073e-02,  3.29048000e-02,\n", "       -2.39007361e-02,  3.13391797e-02, -6.29583746e-02,  9.62914992e-03,\n", "        4.69451919e-02, -1.55548938e-02, -1.08551867e-02, -1.75406560e-02,\n", "       -2.78927013e-02, -3.97054665e-02,  1.15165431e-02,  3.07822004e-02,\n", "       -9.11642238e-03,  4.40496877e-02, -8.59784335e-03,  2.35226303e-02,\n", "        4.97264899e-02, -1.00569446e-02,  3.46257500e-02,  3.96797732e-02,\n", "       -3.16511723e-03, -4.84315120e-02, -2.08059177e-02, -5.34345349e-03,\n", "       -7.20019713e-02,  1.50311925e-02,  1.43422689e-02,  2.80486885e-02,\n", "       -2.79754773e-02, -3.76880877e-02, -1.73238665e-02, -6.98957294e-02,\n", "        3.06093972e-03,  4.12527993e-02, -5.45395259e-03, -3.08096465e-02,\n", "       -1.91735979e-02, -2.10986007e-02,  7.85525597e-04,  3.09847631e-02,\n", "        1.55055597e-02, -6.56506643e-02,  6.37451485e-02, -3.55708376e-02,\n", "       -3.29639725e-02,  1.39867906e-02,  1.76938977e-02, -2.20224354e-02,\n", "       -6.27441108e-02, -3.61145250e-02, -2.66809091e-02,  4.22038734e-02,\n", "        8.49101413e-03,  3.20192124e-03,  1.21845759e-03,  1.31745469e-02,\n", "        4.93204966e-02,  6.24106042e-02,  7.91884307e-03,  1.63087379e-02,\n", "        3.43066305e-02, -8.45552480e-04,  6.95117190e-02, -1.53776845e-02,\n", "       -4.45214882e-02, -3.96845117e-03, -5.38600758e-02,  4.33417298e-02,\n", "       -4.64314111e-02, -2.47553438e-02,  2.38111801e-02, -1.99962985e-02,\n", "        2.90647522e-02,  3.60554457e-02, -2.77763233e-04, -2.24469882e-02,\n", "        1.94191746e-02,  2.43108328e-02, -1.08723459e-03,  8.53982661e-03,\n", "       -6.51547760e-02,  3.65577033e-03, -3.34729366e-02, -7.59119075e-03,\n", "        3.89748104e-02, -1.48010068e-02,  6.33744663e-03,  6.05361424e-02,\n", "        1.90376677e-02,  1.85515098e-02,  4.76264358e-02,  2.00010519e-02,\n", "       -4.09411034e-03,  3.57255787e-02,  3.37230526e-02,  3.47398221e-02,\n", "       -6.82447255e-02,  2.74445787e-02,  4.82460391e-03,  7.15916380e-02,\n", "       -6.75637498e-02, -1.93010531e-02, -6.33795038e-02,  2.39340160e-02,\n", "        2.15932559e-02,  4.74238284e-02,  1.11402851e-02,  2.44186521e-02,\n", "       -6.22628024e-03, -5.45446090e-02, -7.23260865e-02,  3.84008549e-02,\n", "       -5.59312366e-02,  3.70877385e-02, -4.52155173e-02,  4.30228785e-02,\n", "        6.93516359e-02, -4.22157235e-02,  1.48834940e-03, -3.84283415e-03,\n", "        1.17617855e-02, -9.66931786e-03, -5.06984442e-02, -2.44104918e-02,\n", "       -3.45009454e-02,  4.94865663e-02,  1.08481916e-02, -2.43156664e-02,\n", "        1.05220899e-02, -1.72448978e-02,  1.81394501e-03,  3.08941212e-02,\n", "        2.51201186e-02,  4.36747409e-02,  4.71153371e-02, -4.59319763e-02,\n", "        7.45190587e-03,  3.21745686e-02,  4.70025688e-02, -5.51542779e-03,\n", "       -4.25801054e-03, -6.29816437e-03, -4.47728485e-02, -1.48455966e-02,\n", "        2.29813550e-02, -1.95379239e-02, -2.13512853e-02, -5.86819425e-02,\n", "       -1.85773782e-02, -2.24611926e-04, -2.30959151e-02,  1.88287124e-02,\n", "       -9.51578654e-03,  3.44732031e-02,  2.91043818e-02, -8.33908617e-02,\n", "        2.76501887e-02, -7.12599382e-02,  2.41419370e-03, -6.75831065e-02,\n", "        2.15027742e-02, -1.03543000e-02, -2.02222615e-02, -1.35693680e-02,\n", "        6.46096654e-03, -9.09610838e-03,  3.30464281e-02, -2.29563769e-02,\n", "        2.99834702e-02,  1.66380852e-02,  3.34749632e-02,  2.78630331e-02,\n", "        1.45139797e-02, -1.32757183e-02, -1.14772804e-02,  3.63563970e-02,\n", "        9.40349512e-03,  6.22012764e-02,  1.20176319e-02, -3.24308984e-02,\n", "        5.28422650e-04,  2.68275104e-02, -1.50545193e-02, -3.12765595e-03,\n", "        1.37070632e-02,  5.76969311e-02, -6.79700868e-03, -7.21968431e-03,\n", "       -3.15651856e-02, -2.84020957e-02, -5.55845089e-02,  3.14262249e-02,\n", "       -7.47790784e-02,  1.28980130e-02, -2.81751752e-02, -2.86569409e-02,\n", "       -1.47787528e-02,  1.91606581e-02, -2.45286450e-02, -6.41258880e-02,\n", "        2.65480876e-02, -2.25590970e-02, -2.64642686e-02,  4.59829271e-02,\n", "        6.15315847e-02,  4.93693724e-02,  1.72816720e-02,  5.70014864e-02,\n", "       -5.09416722e-02,  1.95028335e-02, -3.13961804e-02, -5.73463403e-02,\n", "        3.55050527e-02,  2.45417990e-02,  2.33551096e-02, -4.55264412e-02,\n", "       -1.20000392e-02,  4.08036597e-02,  7.19558867e-03, -4.95873280e-02,\n", "       -7.97256920e-03,  4.70858114e-03,  4.23983438e-03, -5.18187229e-03,\n", "       -6.00059377e-03,  3.15771773e-02,  1.29322298e-02, -7.47607742e-03,\n", "        4.01974749e-03,  2.60308161e-02,  4.14611734e-02, -2.92321835e-02,\n", "       -3.74425612e-02, -4.02047671e-02,  6.41225129e-02,  8.02149065e-03,\n", "       -1.94793742e-03,  7.89933465e-03,  1.84414722e-02,  1.19220549e-02,\n", "        6.97300653e-04,  1.27605693e-02,  2.13440992e-02,  3.44099663e-02,\n", "       -3.82834598e-02,  2.09364947e-02, -1.36689912e-03,  2.60304064e-02,\n", "        1.03309892e-01, -3.83628765e-03, -1.42918769e-02, -3.21982279e-02,\n", "       -8.87776911e-03, -5.79702482e-02,  1.24155525e-02,  1.60176177e-02,\n", "        4.33206372e-03, -7.67913694e-03, -3.71407345e-02, -2.65847482e-02,\n", "       -4.84832413e-02, -1.18830036e-02,  2.10484881e-02, -2.14275811e-02,\n", "       -2.90587395e-02, -7.65146539e-02,  2.17941366e-02,  3.07247695e-02,\n", "        2.21321993e-02, -5.37583865e-02, -5.45986630e-02, -1.95994209e-02,\n", "        6.53655156e-02, -2.08480917e-02,  7.71053275e-03,  2.30464060e-02,\n", "       -2.38716491e-02, -3.17029133e-02, -1.65972225e-02, -3.12259868e-02,\n", "       -1.02742575e-01,  2.13919654e-02,  3.29860821e-02,  2.92449985e-02,\n", "       -1.30653549e-02, -6.27970276e-03,  4.92750034e-02,  1.64137091e-02,\n", "        3.23879197e-02, -1.53172854e-02, -3.81413139e-02, -8.04919656e-03,\n", "       -1.08133154e-02,  7.60126188e-02, -2.81727463e-02, -9.25896503e-03,\n", "        5.59587255e-02, -2.48033758e-02,  1.91262476e-02, -2.15144064e-02,\n", "       -2.70498525e-02, -3.91287804e-02, -4.47372459e-02, -3.99288572e-02,\n", "       -2.82600634e-02, -1.05496094e-01,  2.90084053e-02, -8.19884017e-02,\n", "       -1.79860294e-02, -4.93140221e-02, -2.89700292e-02, -3.26706134e-02,\n", "       -1.13929007e-02,  6.25480041e-02,  2.09988412e-02,  3.40786166e-02,\n", "        4.22775038e-02, -9.97621939e-03, -1.95572786e-02, -4.95181680e-02,\n", "        2.30757538e-02, -2.02779286e-02,  3.71993929e-02, -3.11168879e-02,\n", "        2.57904008e-02,  4.26239781e-02,  2.33973619e-02,  4.00689989e-03,\n", "       -2.46374980e-02, -5.06165298e-03, -1.54379653e-02,  4.66948171e-04,\n", "       -4.85785725e-03,  5.66424802e-02, -2.09541935e-02, -3.06122117e-02,\n", "        2.08306196e-03,  3.58040929e-02, -1.36380978e-02,  4.87826997e-03,\n", "       -1.25667257e-02,  2.91131213e-02,  4.39725257e-03,  3.34668048e-02,\n", "       -3.95729318e-02,  6.97005540e-02,  1.17042959e-02,  1.88927595e-02,\n", "       -4.99272123e-02, -3.45216766e-02,  1.57779772e-02,  4.84501049e-02,\n", "        9.73086059e-03,  8.45093578e-02,  6.21386804e-02, -8.33165832e-04,\n", "       -3.10367141e-02, -4.03451733e-03,  1.24619470e-03, -5.44636734e-02,\n", "        7.75545537e-02, -4.69428711e-02,  2.10666824e-02,  3.30061316e-02,\n", "       -2.82400660e-02, -2.27502231e-02,  2.11734921e-02,  3.06038912e-02,\n", "       -4.69192192e-02, -2.65527479e-02,  2.12218873e-02, -1.94136128e-02,\n", "       -3.65071930e-02,  4.94123343e-03,  2.02455316e-02, -3.83306704e-02,\n", "        2.75366195e-02, -2.11303458e-02, -9.70205888e-02, -3.63156945e-02,\n", "       -2.60391142e-02, -5.47648259e-02,  2.71793101e-02,  3.20913754e-02,\n", "       -4.93624136e-02, -3.55423577e-02, -1.88178215e-02,  6.94152117e-02,\n", "       -7.48152062e-02, -8.00276175e-03,  3.83800156e-02, -1.82128046e-02,\n", "        1.16246035e-02, -3.29671726e-02,  3.58484033e-03,  2.86987368e-02,\n", "        2.99137942e-02, -2.61925906e-02,  1.54190417e-02,  3.33075263e-02,\n", "       -3.46757914e-03,  1.81147065e-02,  2.02620104e-02, -7.87869543e-02,\n", "       -7.31143402e-03,  2.13454408e-03, -5.03857173e-02, -3.85818235e-03,\n", "        3.64176147e-02, -2.58632395e-02, -2.47921981e-02, -4.48929071e-02,\n", "       -1.56746642e-03,  2.25882754e-02, -2.29092613e-02, -2.98154745e-02,\n", "       -3.63126658e-02, -2.87724007e-03,  1.69772059e-02,  1.35097727e-02,\n", "        5.65643348e-02,  3.67655046e-02, -1.18822688e-02, -3.93256024e-02,\n", "        5.84133416e-02, -1.66928973e-02, -2.85255332e-02,  2.45231064e-03,\n", "        6.42824322e-02,  1.12834880e-02,  7.07072765e-02, -6.12733029e-02,\n", "       -3.22022736e-02,  1.49255954e-02, -3.45885344e-02,  5.64290285e-02,\n", "        1.45710120e-02,  2.65258271e-02, -2.20487174e-02,  4.53800596e-02,\n", "       -2.44657323e-02, -2.35221051e-02,  5.31864055e-02,  3.79638225e-02,\n", "        3.60472314e-02, -7.53597310e-03, -2.83951834e-02,  3.89870517e-02,\n", "       -2.53880899e-02,  7.42309308e-03, -7.19177909e-03, -2.33137272e-02,\n", "        7.28014112e-02, -7.79018700e-02,  9.64842457e-03, -2.72194725e-02,\n", "        2.04009134e-02, -4.13496494e-02,  8.00416097e-02, -3.60673741e-02,\n", "        4.44941409e-03,  3.92931253e-02,  1.36698354e-02,  1.24587072e-02,\n", "        1.00127915e-02,  7.43277296e-02,  4.00649104e-03, -4.89665568e-02,\n", "       -1.82240052e-04, -1.41077256e-02, -2.97611952e-02, -1.74682311e-04,\n", "        2.24157814e-02,  4.44416255e-02, -4.01153713e-02, -6.28807694e-02,\n", "        1.47870714e-02, -2.36048526e-03,  1.80037152e-02,  1.93315167e-02,\n", "        7.11953864e-02,  2.82566436e-02, -2.44845683e-03, -1.15027081e-03,\n", "        6.96809217e-02, -7.51282647e-03,  7.46430457e-02,  4.62826341e-02,\n", "       -1.57173667e-02, -1.77645404e-02, -6.00871742e-02, -4.73721325e-03,\n", "       -2.26073875e-03,  7.37745641e-03, -9.78859235e-03, -1.78285630e-03,\n", "       -1.11999512e-01,  3.77576649e-02,  2.25516558e-02,  1.88177861e-02,\n", "       -2.03207228e-02,  6.17188103e-02,  3.49288732e-02, -8.87825638e-02,\n", "       -4.09724452e-02,  4.36148830e-02, -5.32415183e-03, -2.60976851e-02,\n", "        7.11308792e-02,  6.35896670e-03,  3.25526879e-03,  1.12947663e-02,\n", "        1.56234000e-02, -2.11693402e-02,  3.77066508e-02, -3.17939967e-02,\n", "       -1.39819952e-02,  1.79927405e-02,  2.04036627e-02,  2.92575965e-03,\n", "       -1.45869134e-02, -2.90152151e-02, -5.97235262e-02, -1.11356348e-01,\n", "       -3.18385735e-02, -2.38965661e-03, -6.12345934e-02,  4.60752286e-03,\n", "        2.72978023e-02,  6.74417708e-03,  6.17338419e-02,  4.96751778e-02,\n", "       -6.44939207e-03,  3.66540253e-02,  6.50297524e-03,  4.99960519e-02,\n", "        4.00801897e-02, -3.11222542e-02, -6.01028092e-02,  3.36206071e-02,\n", "        1.11553874e-02, -1.01943649e-02, -1.93773943e-03,  8.48573353e-03,\n", "       -2.81138644e-02, -4.14620228e-02, -5.91190718e-03, -4.40563932e-02,\n", "       -3.85563564e-03,  3.15620564e-03,  3.58664691e-02, -2.53184307e-02,\n", "       -2.90389216e-05,  5.32585476e-03,  1.12847844e-02,  1.09254308e-02,\n", "       -2.80107949e-02, -2.64293756e-02,  1.36288069e-02,  2.05743704e-02,\n", "        5.06558456e-02,  2.03972589e-03,  6.15928322e-03,  1.65107157e-02,\n", "        7.66068920e-02,  1.06601194e-02,  2.15027258e-02, -1.87675226e-02,\n", "       -8.91032163e-03,  5.78406416e-02, -3.35133038e-02,  1.11876021e-03,\n", "       -3.03310864e-02,  8.82029254e-03, -1.71672814e-02, -1.08657381e-03,\n", "        3.43640856e-02,  6.27818331e-03, -2.87505034e-02, -5.35019450e-02,\n", "       -6.20333590e-02,  7.05959573e-02, -2.40503754e-02, -3.69300060e-02,\n", "       -1.34815788e-02, -3.37581560e-02,  2.64684986e-02, -1.33448904e-02,\n", "       -1.59186460e-02,  3.17284912e-02,  1.24617647e-02,  1.01900354e-01,\n", "        5.25732934e-02, -1.05239293e-02, -9.43460036e-04, -4.58779857e-02,\n", "       -4.57871556e-02, -1.21272868e-02, -3.97307090e-02,  2.81554665e-02,\n", "        4.01902646e-02, -5.47600538e-03, -1.49628508e-03,  1.42910369e-02,\n", "        5.93335070e-02, -4.52512540e-02, -4.55521718e-02,  2.89121401e-02,\n", "       -1.18271308e-02,  6.30670190e-02,  4.18886282e-02, -5.92090562e-03,\n", "        9.88560263e-03, -4.83246380e-03,  2.92682964e-02,  4.01030742e-02,\n", "       -4.30496857e-02, -7.91318994e-03, -5.26147615e-03, -8.48481245e-03,\n", "        3.12878750e-02,  2.27111876e-02, -3.72377895e-02, -1.53291542e-02])"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["sample_embedding"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Visualizing vector"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Prework\n", "\n", "result = collection.get(include=['embeddings', 'documents', 'metadatas'])\n", "vectors = np.array(result['embeddings'])\n", "documents = result['documents']\n", "doc_types = [metadata['doc_type'] for metadata in result['metadatas']]\n", "colors = [['blue', 'green', 'red', 'orange'][['products', 'employees', 'contracts', 'company'].index(t)] for t in doc_types]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hoverinfo": "text", "marker": {"color": ["orange", "orange", "orange", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue"], "opacity": 0.8, "size": 5}, "mode": "markers", "text": ["Type: company<br>Text: # About Insurellm\n\nInsurellm was founded by <PERSON> in 2015 as an insurance tech startup des...", "Type: company<br>Text: # Careers at Insurellm\n\nInsurellm is hiring! We are looking for talented software engineers, data sc...", "Type: company<br>Text: # Overview of Insurellm\n\nInsurellm is an innovative insurance tech firm with 200 employees across th...", "Type: contracts<br>Text: # Contract with Apex Reinsurance for Rellm: AI-Powered Enterprise Reinsurance Solution\n\n## Terms\n\n1....", "Type: contracts<br>Text: ## Renewal\n\n1. **Automatic Renewal**: This Agreement will automatically renew for successive one-yea...", "Type: contracts<br>Text: 2. **Seamless Integrations**: The architecture of Rellm allows for easy integration with existing sy...", "Type: contracts<br>Text: 1. **Technical Support**: Provider shall offer dedicated technical support to the Client via phone, ...", "Type: contracts<br>Text: **Insurellm, Inc.**  \n_____________________________  \nAuthorized Signature   \nDate: ________________...", "Type: contracts<br>Text: # Contract with Belvedere Insurance for Markellm\n\n## Terms\nThis Contract (\"Agreement\") is made and e...", "Type: contracts<br>Text: ## Renewal\n1. **Renewal Terms**: This Agreement may be renewed for additional one-year terms upon mu...", "Type: contracts<br>Text: ## Features\n1. **AI-Powered Matching**: Belvedere Insurance will benefit from Markellm's AI-powered ...", "Type: contracts<br>Text: ## Support\n1. **Technical Support**: Technical support will be available from 9 AM to 7 PM EST, Mond...", "Type: contracts<br>Text: **Belvedere Insurance**  \nSignature: ______________________  \nName: [Authorized Signatory]  \nTitle: ...", "Type: contracts<br>Text: # Contract with BrightWay Solutions for Markellm\n\n**Contract Date:** October 5, 2023  \n**Contract ID...", "Type: contracts<br>Text: 3. **Service Level Agreement (SLA):**  \n   Insurellm commits to a 99.9% uptime for the platform with...", "Type: contracts<br>Text: 2. **Real-Time Quote Availability:**  \n   Consumers sourced via BrightWay Solutions will receive rea...", "Type: contracts<br>Text: 3. **Training and Onboarding:**  \n   Insurellm agrees to provide one free training session on how to...", "Type: contracts<br>Text: # Contract with EverGuard Insurance for Rellm: AI-Powered Enterprise Reinsurance Solution\n\n**Contrac...", "Type: contracts<br>Text: 4. **Usage Rights**: EverGuard Insurance is granted a non-exclusive, non-transferable license to acc...", "Type: contracts<br>Text: 1. **Core Functionality**: Rellm provides EverGuard Insurance with advanced AI-driven analytics, sea...", "Type: contracts<br>Text: 1. **Customer Support**: Insurellm will provide EverGuard Insurance with 24/7 customer support, incl...", "Type: contracts<br>Text: ---\n\n**Signatures**  \n**For Insurellm**: __________________________  \n**Name**: <PERSON>  \n**Title...", "Type: contracts<br>Text: # Contract with GreenField Holdings for Markellm\n\n**Effective Date:** November 15, 2023  \n**Contract...", "Type: contracts<br>Text: ## Renewal\n1. **Automatic Renewal**: This contract will automatically renew for sequential one-year ...", "Type: contracts<br>Text: ## Features\n1. **AI-Powered Matching**: Access to advanced algorithms that connect GreenField Holdin...", "Type: contracts<br>Text: ## Support\n1. **Customer Support Access**: The Client will have access to dedicated support through ...", "Type: contracts<br>Text: **Signatures:**  \n_________________________                           _________________________  \n**...", "Type: contracts<br>Text: # Contract with Greenstone Insurance for Homellm\n\n---\n\n## Terms\n\n1. **Parties**: This Contract (\"Agr...", "Type: contracts<br>Text: 4. **Payment Terms**: \n   - The Customer shall pay an amount of $10,000 per month for the Standard T...", "Type: contracts<br>Text: ---\n\n## Features\n\n- **AI-Powered Risk Assessment**: Customer will have access to enhanced risk evalu...", "Type: contracts<br>Text: - **Customer Portal**: A dedicated portal will be provided, allowing the Customer's clients to manag...", "Type: contracts<br>Text: ______________________________  \n[Name], [Title]  \nDate: ______________________\n\n**For Greenstone In...", "Type: contracts<br>Text: # Contract with GreenValley Insurance for Homellm\n\n**Contract Date:** October 6, 2023  \n**Contract N...", "Type: contracts<br>Text: 4. **Confidentiality:** Both parties agree to maintain the confidentiality of proprietary informatio...", "Type: contracts<br>Text: 1. **AI-Powered Risk Assessment:** Access to advanced AI algorithms for real-time risk evaluations.\n...", "Type: contracts<br>Text: 3. **Regular Updates:** Insurellm will offer ongoing updates and enhancements to the Homellm platfor...", "Type: contracts<br>Text: # Contract with Pinnacle Insurance Co. for Homellm\n\n## Terms\nThis contract (\"Contract\") is entered i...", "Type: contracts<br>Text: ## Renewal\n1. **Renewal Terms**: At the end of the initial term, this Contract shall automatically r...", "Type: contracts<br>Text: ## Features\n1. **AI-Powered Risk Assessment**: Utilized for tailored underwriting decisions specific...", "Type: contracts<br>Text: ## Support\n1. **Technical Support**: Insurellm shall provide 24/7 technical support via an email and...", "Type: contracts<br>Text: # Contract with Roadway Insurance Inc. for Carllm\n\n---\n\n## Terms\n\n1. **Agreement Effective Date**: T...", "Type: contracts<br>Text: ---\n\n## Renewal\n\n1. **Automatic Renewal**: This agreement will automatically renew for an additional...", "Type: contracts<br>Text: ---\n\n## Features\n\n1. **Access to Core Features**: Roadway Insurance Inc. will have access to all Pro...", "Type: contracts<br>Text: ---\n\n## Support\n\n1. **Technical Support**: Roadway Insurance Inc. will receive priority technical su...", "Type: contracts<br>Text: # Contract with Stellar Insurance Co. for Rellm\n\n## Terms\nThis contract is made between **Insurellm*...", "Type: contracts<br>Text: ### Termination\nEither party may terminate this agreement with a **30-day written notice**. In the e...", "Type: contracts<br>Text: ## Features\nStellar Insurance Co. will receive access to the following features of the Rellm product...", "Type: contracts<br>Text: ## Support\nInsurellm provides Stellar Insurance Co. with the following support services:\n\n- **24/7 T...", "Type: contracts<br>Text: # Contract with TechDrive Insurance for Carllm\n\n**Contract Date:** October 1, 2024  \n**Contract Dura...", "Type: contracts<br>Text: ## Renewal\n\n1. **Automatic Renewal**: This contract shall automatically renew for additional one-yea...", "Type: contracts<br>Text: ## Support\n\n1. **Customer Support**: Insurellm will provide 24/7 customer support to TechDrive Insur...", "Type: contracts<br>Text: **TechDrive Insurance Representative:**  \nName: <PERSON>  \nTitle: Operations Director  \nDate: _...", "Type: contracts<br>Text: # Contract with Velocity Auto Solutions for Carllm\n\n**Contract Date:** October 1, 2023  \n**Contract ...", "Type: contracts<br>Text: ## Renewal\n\n1. **Automatic Renewal**: This contract will automatically renew for successive 12-month...", "Type: contracts<br>Text: ## Support\n\n1. **Customer Support**: Velocity Auto Solutions will have access to Insurellm’s custome...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1990  \n- **Job Title:** Backend ...", "Type: employees<br>Text: ## Annual Performance History\n- **2020:**  \n  - Completed onboarding successfully.  \n  - Met expecta...", "Type: employees<br>Text: ## Compensation History\n- **2020:** Base Salary: $80,000  \n- **2021:** Base Salary Increase to $90,0...", "Type: employees<br>Text: <PERSON> continues to be a vital asset at Insurellm, contributing significantly to innovative backe...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth**: March 15, 1993  \n- **Job Title**: Sales ...", "Type: employees<br>Text: ## Annual Performance History  \n- **2021**:  \n  - **Performance Rating**: 4.5/5  \n  - **Key Achievem...", "Type: employees<br>Text: - **2022**:  \n  - **Base Salary**: $65,000 (Promotion to Senior SDR)  \n  - **Bonus**: $13,000 (20% o...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1995  \n- **Job Title:** Sales...", "Type: employees<br>Text: ## Annual Performance History  \n- **2022** - Rated as \"Exceeds Expectations.\" <PERSON> achieved ...", "Type: employees<br>Text: ## Other HR Notes\n- <PERSON> is an active member of the Diversity and Inclusion committee at Ins...", "Type: employees<br>Text: # <PERSON>\n\n## Summary\n- **Date of Birth**: March 15, 1985  \n- **Job Title**: Co-Founder & Ch...", "Type: employees<br>Text: - **2010 - 2013**: Business Analyst at Edge Analytics  \n  Prior to joining Innovate, <PERSON> worked as...", "Type: employees<br>Text: - **2018**: **Exceeds Expectations**  \n  Under <PERSON>’s pivoted vision, Insurellm launched two new su...", "Type: employees<br>Text: - **2022**: **Satisfactory**  \n  <PERSON> focused on rebuilding team dynamics and addressing employee c...", "Type: employees<br>Text: ## Compensation History\n- **2015**: $150,000 base salary + Significant equity stake  \n- **2016**: $1...", "Type: employees<br>Text: ## Other HR Notes\n- **Professional Development**: <PERSON> has actively participated in leadership trai...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** August 12, 1990  \n- **Job Title:** Acco...", "Type: employees<br>Text: - **2017-2019:** Marketing Intern  \n  - Assisted with market research and campaign development for s...", "Type: employees<br>Text: ## Compensation History\n| Year | Base Salary | Bonus         | Total Compensation |\n|------|--------...", "Type: employees<br>Text: <PERSON> exemplifies the kind of talent that drives Insurellm's success and is an invaluable ass...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 18, 1991  \n- **Job Title:** Digital...", "Type: employees<br>Text: - **January 2017 - May 2018**: Marketing Intern  \n  - Supported the Marketing team by collaborating ...", "Type: employees<br>Text: - **2021**:  \n  - Performance Rating: Meets Expectations  \n  - Key Achievements: Contributed to the ...", "Type: employees<br>Text: - **Professional Development Goals**:  \n  - <PERSON> aims to become a Marketing Manager within the...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1993  \n- **Job Title:** Sales...", "Type: employees<br>Text: ## Annual Performance History\n- **2021:** First year at Insurellm; achieved 90% of monthly targets. ...", "Type: employees<br>Text: ## Other HR Notes\n- Jordan has shown an interest in continuing education, actively participating in ...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1990\n- **Job Title:** Fro...", "Type: employees<br>Text: ## Annual Performance History\n- **2019:** Exceeds Expectations - Continuously delivered high-quality...", "Type: employees<br>Text: ## Compensation History\n- **June 2018:** Starting Salary - $85,000\n- **June 2019:** Salary Increase ...", "Type: employees<br>Text: ## Other HR Notes\n- <PERSON> has been an integral part of club initiatives, including the In...", "Type: employees<br>Text: # HR Record\n\n# <PERSON><PERSON>\n\n## Summary\n- **Date of Birth:** January 15, 1991  \n- **Job Title:** ...", "Type: employees<br>Text: ## Insurellm Career Progression\n- **January 2017 - October 2018**: **Junior Data Engineer**  \n  * Ma...", "Type: employees<br>Text: ## Annual Performance History\n- **2017**: *Meets Expectations*  \n  <PERSON><PERSON> showed potential in her ro...", "Type: employees<br>Text: - **2021**: *Exceeds Expectations*  \n  <PERSON><PERSON> spearheaded the transition to a new data warehousing s...", "Type: employees<br>Text: ## Compensation History\n- **2017**: $70,000 (Junior Data Engineer)  \n- **2018**: $75,000 (Junior Dat...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth**: May 14, 1990  \n- **Job Title**: <PERSON><PERSON>...", "Type: employees<br>Text: ## Annual Performance History\n- **2018**: **3/5** - Adaptable team player but still learning to take...", "Type: employees<br>Text: ## Compensation History\n- **March 2018**: Initial salary of $80,000.\n- **July 2019**: Salary increas...", "Type: employees<br>Text: # <PERSON>\n\n## Summary\n- **Date of Birth:** October 14, 1990\n- **Job Title:** HR Generalist\n-...", "Type: employees<br>Text: ## Annual Performance History\n- **2020:** Exceeds Expectations  \n  <PERSON> demonstrated exce...", "Type: employees<br>Text: ## Compensation History\n- **2020:** Base Salary - $55,000  \n  The entry-level salary matched industr...", "Type: employees<br>Text: - **2023:** Base Salary - $70,000  \n  Recognized for substantial improvement in employee relations m...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** April 12, 1989  \n- **Job Title:** Sen...", "Type: employees<br>Text: ## Annual Performance History\n- **2023:** Rating: 4.5/5  \n  *<PERSON> exceeded expectations, successfu...", "Type: employees<br>Text: ## Compensation History\n- **2023:** Base Salary: $115,000 + Bonus: $15,000  \n  *Annual bonus based o...", "Type: employees<br>Text: - **Engagement in Company Culture:** Regularly participates in team-building events and contributes ...", "Type: products<br>Text: # Product Summary\n\n# Carllm\n\n## Summary\n\nCarllm is an innovative auto insurance product developed by...", "Type: products<br>Text: - **Instant Quoting**: With Carllm, insurance companies can offer near-instant quotes to customers, ...", "Type: products<br>Text: - **Mobile Integration**: Carllm is designed to work seamlessly with mobile applications, providing ...", "Type: products<br>Text: - **Professional Tier**: $2,500/month\n  - For medium-sized companies.\n  - All Basic Tier features pl...", "Type: products<br>Text: ### Q2 2025: Customer Experience Improvements\n- Launch of a new **mobile app** for end-users.\n- Intr...", "Type: products<br>Text: # Product Summary\n\n# Homellm\n\n## Summary\nHomellm is an innovative home insurance product developed b...", "Type: products<br>Text: ### 2. Dynamic Pricing Model\nWith Homellm's innovative dynamic pricing model, insurance providers ca...", "Type: products<br>Text: ### 5. Multi-Channel Integration\nHomellm seamlessly integrates into existing insurance platforms, pr...", "Type: products<br>Text: - **Basic Tier:** Starting at $5,000/month for small insurers with basic integration features.\n- **S...", "Type: products<br>Text: All tiers include a comprehensive training program and ongoing updates to ensure optimal performance...", "Type: products<br>Text: With Homellm, Insurellm is committed to transforming the landscape of home insurance, ensuring both ...", "Type: products<br>Text: # Product Summary\n\n# Markellm\n\n## Summary\n\nMarkellm is an innovative two-sided marketplace designed ...", "Type: products<br>Text: - **User-Friendly Interface**: Designed with user experience in mind, Markellm features an intuitive...", "Type: products<br>Text: - **Customer Support**: Our dedicated support team is always available to assist both consumers and ...", "Type: products<br>Text: ### For Insurance Companies:\n- **Basic Listing Fee**: $199/month for a featured listing on the platf...", "Type: products<br>Text: ### Q3 2025\n- Initiate a comprehensive marketing campaign targeting both consumers and insurers to i...", "Type: products<br>Text: # Product Summary\n\n# Rellm: AI-Powered Enterprise Reinsurance Solution\n\n## Summary\n\nRellm is an inno...", "Type: products<br>Text: ### Seamless Integrations\nRellm's architecture is designed for effortless integration with existing ...", "Type: products<br>Text: ### Regulatory Compliance Tools\nRellm includes built-in compliance tracking features to help organiz...", "Type: products<br>Text: Join the growing number of organizations leveraging Rellm to enhance their reinsurance processes whi...", "Type: products<br>Text: Experience the future of reinsurance with Rellm, where innovation meets reliability. Let Insurellm h..."], "type": "scatter", "x": [2.1049793, 1.1863052, 1.4862374, -5.244703, -4.6875825, -3.938663, -7.065274, -13.5899725, -9.856695, -13.868874, -5.6077223, -7.7878904, -10.650882, -8.596619, -7.607886, -7.044941, -8.118247, -4.3257694, -3.7956166, -3.1995866, -6.4049, -6.2257085, -9.424744, -13.633935, -4.918413, -8.846364, -13.630306, -9.190956, -10.57125, -4.0693502, -8.158554, -13.862557, -8.649788, -7.214466, -5.36645, -7.494893, -9.623619, -13.9268875, -4.0489416, -8.71199, -11.229432, -13.288615, -12.044058, -10.3613825, -6.9435472, -6.0978713, -5.2625675, -6.3455467, -10.479305, -10.707319, -8.29903, -8.511846, -9.630703, -9.749146, -9.0578, 5.959655, 11.447374, 10.058615, 5.1624084, 8.816244, 10.980077, 9.303604, 7.8448887, 10.387505, 7.9188, 2.9157553, 5.2268667, 5.738741, 6.06246, 11.117995, 3.259488, 5.9528317, 12.1910305, 7.9038677, 4.751993, 5.9953322, 6.4600663, 7.3864727, 5.8371596, 9.382967, 11.086662, 11.166579, 10.636894, 12.461003, 10.982859, 11.124385, 11.667279, 12.73921, 12.9148855, 12.973071, 12.19851, 7.131914, 12.053937, 9.205491, 15.479876, 14.208124, 14.651664, 15.361577, 6.732294, 9.61941, 9.963041, 9.356099, -0.892312, -1.6616712, -2.0991518, -1.8988599, -1.0763571, -3.4787161, -3.2296891, -2.6272976, -1.5834669, -1.5236322, 0.20386986, -4.8010993, -5.9114547, -5.690189, -4.8725724, -3.9543898, -1.7254385, -2.615607, -2.4413817, -1.358858, -0.21650138], "y": [-1.160884, 0.29916492, -0.18965857, -3.9546766, -2.6938837, -1.5114936, -1.7658606, 1.6283244, 3.3563676, -1.0218278, 4.734356, -1.5868372, 2.7326808, 3.8717313, 3.0319402, 4.8651543, 3.84119, -4.5347166, -3.602797, -3.3519456, -5.259293, -5.811519, 4.7673006, -1.0121828, 3.0695078, 5.869272, 1.72016, 0.70035094, 0.31958526, 1.6364757, -0.49663937, 0.7449636, 0.77033013, 0.90882516, 1.2580742, 0.38005096, -0.45788804, -1.3838352, 2.8216114, -1.3808312, -2.8460462, -2.3889477, -4.978076, -5.0466166, -3.2549055, -2.8125684, -1.6414757, -2.1152701, -2.9129503, -3.7577167, -5.231769, -6.0865116, -3.3624432, -3.9013338, -4.3533516, 4.2022624, -1.1752989, -1.4045172, 4.0687327, 2.8832786, -0.17034641, 2.065217, 2.5553873, 0.5539435, 2.2194517, -2.4600935, -4.2555146, -4.4346094, -4.551813, -2.6811168, -2.7749152, 0.9942546, -0.88645107, -0.5169783, 0.9356758, -0.5277238, -0.9503327, -1.6551013, -0.8439842, 3.890908, 2.1762133, 2.625817, 4.373835, 0.739714, -2.2775772, 4.309124, -5.931021, -4.830216, -3.0594008, -4.583869, -4.6539454, 4.349339, -1.5038458, -0.50115377, 0.57530403, -0.9931708, -0.62294304, 0.3860171, 2.6113834, -3.046981, -2.302129, -4.026367, 3.9122264, 3.7329102, 4.04289, 4.7394605, 5.348665, 0.87496454, 1.837953, 1.1089472, 1.8076365, 1.6846453, 0.07279262, 5.578082, 6.1154733, 6.3361335, 6.382683, 6.6129003, -2.4845295, -0.93237317, -1.7474884, -1.460983, -0.6520413]}], "layout": {"height": 600, "margin": {"b": 10, "l": 10, "r": 20, "t": 40}, "scene": {"xaxis": {"title": {"text": "x"}}, "yaxis": {"title": {"text": "y"}}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "2D Chroma Vector Store Visualization"}, "width": 800}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# We humans find it easier to visalize things in 2D!\n", "# Reduce the dimensionality of the vectors to 2D using t-SNE\n", "# (t-distributed stochastic neighbor embedding)\n", "\n", "tsne = TSNE(n_components=2, random_state=42)\n", "reduced_vectors = tsne.fit_transform(vectors)\n", "\n", "# Create the 2D scatter plot\n", "fig = go.Figure(data=[go.<PERSON><PERSON>(\n", "    x=reduced_vectors[:, 0],\n", "    y=reduced_vectors[:, 1],\n", "    mode='markers',\n", "    marker=dict(size=5, color=colors, opacity=0.8),\n", "    text=[f\"Type: {t}<br>Text: {d[:100]}...\" for t, d in zip(doc_types, documents)],\n", "    hoverinfo='text'\n", ")])\n", "\n", "fig.update_layout(\n", "    title='2D Chroma Vector Store Visualization',\n", "    scene=dict(xaxis_title='x',yaxis_title='y'),\n", "    width=800,\n", "    height=600,\n", "    margin=dict(r=20, b=10, l=10, t=40)\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"hoverinfo": "text", "marker": {"color": ["orange", "orange", "orange", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "red", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "green", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue", "blue"], "opacity": 0.8, "size": 5}, "mode": "markers", "text": ["Type: company<br>Text: # About Insurellm\n\nInsurellm was founded by <PERSON> in 2015 as an insurance tech startup des...", "Type: company<br>Text: # Careers at Insurellm\n\nInsurellm is hiring! We are looking for talented software engineers, data sc...", "Type: company<br>Text: # Overview of Insurellm\n\nInsurellm is an innovative insurance tech firm with 200 employees across th...", "Type: contracts<br>Text: # Contract with Apex Reinsurance for Rellm: AI-Powered Enterprise Reinsurance Solution\n\n## Terms\n\n1....", "Type: contracts<br>Text: ## Renewal\n\n1. **Automatic Renewal**: This Agreement will automatically renew for successive one-yea...", "Type: contracts<br>Text: 2. **Seamless Integrations**: The architecture of Rellm allows for easy integration with existing sy...", "Type: contracts<br>Text: 1. **Technical Support**: Provider shall offer dedicated technical support to the Client via phone, ...", "Type: contracts<br>Text: **Insurellm, Inc.**  \n_____________________________  \nAuthorized Signature   \nDate: ________________...", "Type: contracts<br>Text: # Contract with Belvedere Insurance for Markellm\n\n## Terms\nThis Contract (\"Agreement\") is made and e...", "Type: contracts<br>Text: ## Renewal\n1. **Renewal Terms**: This Agreement may be renewed for additional one-year terms upon mu...", "Type: contracts<br>Text: ## Features\n1. **AI-Powered Matching**: Belvedere Insurance will benefit from Markellm's AI-powered ...", "Type: contracts<br>Text: ## Support\n1. **Technical Support**: Technical support will be available from 9 AM to 7 PM EST, Mond...", "Type: contracts<br>Text: **Belvedere Insurance**  \nSignature: ______________________  \nName: [Authorized Signatory]  \nTitle: ...", "Type: contracts<br>Text: # Contract with BrightWay Solutions for Markellm\n\n**Contract Date:** October 5, 2023  \n**Contract ID...", "Type: contracts<br>Text: 3. **Service Level Agreement (SLA):**  \n   Insurellm commits to a 99.9% uptime for the platform with...", "Type: contracts<br>Text: 2. **Real-Time Quote Availability:**  \n   Consumers sourced via BrightWay Solutions will receive rea...", "Type: contracts<br>Text: 3. **Training and Onboarding:**  \n   Insurellm agrees to provide one free training session on how to...", "Type: contracts<br>Text: # Contract with EverGuard Insurance for Rellm: AI-Powered Enterprise Reinsurance Solution\n\n**Contrac...", "Type: contracts<br>Text: 4. **Usage Rights**: EverGuard Insurance is granted a non-exclusive, non-transferable license to acc...", "Type: contracts<br>Text: 1. **Core Functionality**: Rellm provides EverGuard Insurance with advanced AI-driven analytics, sea...", "Type: contracts<br>Text: 1. **Customer Support**: Insurellm will provide EverGuard Insurance with 24/7 customer support, incl...", "Type: contracts<br>Text: ---\n\n**Signatures**  \n**For Insurellm**: __________________________  \n**Name**: <PERSON>  \n**Title...", "Type: contracts<br>Text: # Contract with GreenField Holdings for Markellm\n\n**Effective Date:** November 15, 2023  \n**Contract...", "Type: contracts<br>Text: ## Renewal\n1. **Automatic Renewal**: This contract will automatically renew for sequential one-year ...", "Type: contracts<br>Text: ## Features\n1. **AI-Powered Matching**: Access to advanced algorithms that connect GreenField Holdin...", "Type: contracts<br>Text: ## Support\n1. **Customer Support Access**: The Client will have access to dedicated support through ...", "Type: contracts<br>Text: **Signatures:**  \n_________________________                           _________________________  \n**...", "Type: contracts<br>Text: # Contract with Greenstone Insurance for Homellm\n\n---\n\n## Terms\n\n1. **Parties**: This Contract (\"Agr...", "Type: contracts<br>Text: 4. **Payment Terms**: \n   - The Customer shall pay an amount of $10,000 per month for the Standard T...", "Type: contracts<br>Text: ---\n\n## Features\n\n- **AI-Powered Risk Assessment**: Customer will have access to enhanced risk evalu...", "Type: contracts<br>Text: - **Customer Portal**: A dedicated portal will be provided, allowing the Customer's clients to manag...", "Type: contracts<br>Text: ______________________________  \n[Name], [Title]  \nDate: ______________________\n\n**For Greenstone In...", "Type: contracts<br>Text: # Contract with GreenValley Insurance for Homellm\n\n**Contract Date:** October 6, 2023  \n**Contract N...", "Type: contracts<br>Text: 4. **Confidentiality:** Both parties agree to maintain the confidentiality of proprietary informatio...", "Type: contracts<br>Text: 1. **AI-Powered Risk Assessment:** Access to advanced AI algorithms for real-time risk evaluations.\n...", "Type: contracts<br>Text: 3. **Regular Updates:** Insurellm will offer ongoing updates and enhancements to the Homellm platfor...", "Type: contracts<br>Text: # Contract with Pinnacle Insurance Co. for Homellm\n\n## Terms\nThis contract (\"Contract\") is entered i...", "Type: contracts<br>Text: ## Renewal\n1. **Renewal Terms**: At the end of the initial term, this Contract shall automatically r...", "Type: contracts<br>Text: ## Features\n1. **AI-Powered Risk Assessment**: Utilized for tailored underwriting decisions specific...", "Type: contracts<br>Text: ## Support\n1. **Technical Support**: Insurellm shall provide 24/7 technical support via an email and...", "Type: contracts<br>Text: # Contract with Roadway Insurance Inc. for Carllm\n\n---\n\n## Terms\n\n1. **Agreement Effective Date**: T...", "Type: contracts<br>Text: ---\n\n## Renewal\n\n1. **Automatic Renewal**: This agreement will automatically renew for an additional...", "Type: contracts<br>Text: ---\n\n## Features\n\n1. **Access to Core Features**: Roadway Insurance Inc. will have access to all Pro...", "Type: contracts<br>Text: ---\n\n## Support\n\n1. **Technical Support**: Roadway Insurance Inc. will receive priority technical su...", "Type: contracts<br>Text: # Contract with Stellar Insurance Co. for Rellm\n\n## Terms\nThis contract is made between **Insurellm*...", "Type: contracts<br>Text: ### Termination\nEither party may terminate this agreement with a **30-day written notice**. In the e...", "Type: contracts<br>Text: ## Features\nStellar Insurance Co. will receive access to the following features of the Rellm product...", "Type: contracts<br>Text: ## Support\nInsurellm provides Stellar Insurance Co. with the following support services:\n\n- **24/7 T...", "Type: contracts<br>Text: # Contract with TechDrive Insurance for Carllm\n\n**Contract Date:** October 1, 2024  \n**Contract Dura...", "Type: contracts<br>Text: ## Renewal\n\n1. **Automatic Renewal**: This contract shall automatically renew for additional one-yea...", "Type: contracts<br>Text: ## Support\n\n1. **Customer Support**: Insurellm will provide 24/7 customer support to TechDrive Insur...", "Type: contracts<br>Text: **TechDrive Insurance Representative:**  \nName: <PERSON>  \nTitle: Operations Director  \nDate: _...", "Type: contracts<br>Text: # Contract with Velocity Auto Solutions for Carllm\n\n**Contract Date:** October 1, 2023  \n**Contract ...", "Type: contracts<br>Text: ## Renewal\n\n1. **Automatic Renewal**: This contract will automatically renew for successive 12-month...", "Type: contracts<br>Text: ## Support\n\n1. **Customer Support**: Velocity Auto Solutions will have access to Insurellm’s custome...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1990  \n- **Job Title:** Backend ...", "Type: employees<br>Text: ## Annual Performance History\n- **2020:**  \n  - Completed onboarding successfully.  \n  - Met expecta...", "Type: employees<br>Text: ## Compensation History\n- **2020:** Base Salary: $80,000  \n- **2021:** Base Salary Increase to $90,0...", "Type: employees<br>Text: <PERSON> continues to be a vital asset at Insurellm, contributing significantly to innovative backe...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth**: March 15, 1993  \n- **Job Title**: Sales ...", "Type: employees<br>Text: ## Annual Performance History  \n- **2021**:  \n  - **Performance Rating**: 4.5/5  \n  - **Key Achievem...", "Type: employees<br>Text: - **2022**:  \n  - **Base Salary**: $65,000 (Promotion to Senior SDR)  \n  - **Bonus**: $13,000 (20% o...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1995  \n- **Job Title:** Sales...", "Type: employees<br>Text: ## Annual Performance History  \n- **2022** - Rated as \"Exceeds Expectations.\" <PERSON> achieved ...", "Type: employees<br>Text: ## Other HR Notes\n- <PERSON> is an active member of the Diversity and Inclusion committee at Ins...", "Type: employees<br>Text: # <PERSON>\n\n## Summary\n- **Date of Birth**: March 15, 1985  \n- **Job Title**: Co-Founder & Ch...", "Type: employees<br>Text: - **2010 - 2013**: Business Analyst at Edge Analytics  \n  Prior to joining Innovate, <PERSON> worked as...", "Type: employees<br>Text: - **2018**: **Exceeds Expectations**  \n  Under <PERSON>’s pivoted vision, Insurellm launched two new su...", "Type: employees<br>Text: - **2022**: **Satisfactory**  \n  <PERSON> focused on rebuilding team dynamics and addressing employee c...", "Type: employees<br>Text: ## Compensation History\n- **2015**: $150,000 base salary + Significant equity stake  \n- **2016**: $1...", "Type: employees<br>Text: ## Other HR Notes\n- **Professional Development**: <PERSON> has actively participated in leadership trai...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** August 12, 1990  \n- **Job Title:** Acco...", "Type: employees<br>Text: - **2017-2019:** Marketing Intern  \n  - Assisted with market research and campaign development for s...", "Type: employees<br>Text: ## Compensation History\n| Year | Base Salary | Bonus         | Total Compensation |\n|------|--------...", "Type: employees<br>Text: <PERSON> exemplifies the kind of talent that drives Insurellm's success and is an invaluable ass...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 18, 1991  \n- **Job Title:** Digital...", "Type: employees<br>Text: - **January 2017 - May 2018**: Marketing Intern  \n  - Supported the Marketing team by collaborating ...", "Type: employees<br>Text: - **2021**:  \n  - Performance Rating: Meets Expectations  \n  - Key Achievements: Contributed to the ...", "Type: employees<br>Text: - **Professional Development Goals**:  \n  - <PERSON> aims to become a Marketing Manager within the...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1993  \n- **Job Title:** Sales...", "Type: employees<br>Text: ## Annual Performance History\n- **2021:** First year at Insurellm; achieved 90% of monthly targets. ...", "Type: employees<br>Text: ## Other HR Notes\n- Jordan has shown an interest in continuing education, actively participating in ...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** March 15, 1990\n- **Job Title:** Fro...", "Type: employees<br>Text: ## Annual Performance History\n- **2019:** Exceeds Expectations - Continuously delivered high-quality...", "Type: employees<br>Text: ## Compensation History\n- **June 2018:** Starting Salary - $85,000\n- **June 2019:** Salary Increase ...", "Type: employees<br>Text: ## Other HR Notes\n- <PERSON> has been an integral part of club initiatives, including the In...", "Type: employees<br>Text: # HR Record\n\n# <PERSON><PERSON>\n\n## Summary\n- **Date of Birth:** January 15, 1991  \n- **Job Title:** ...", "Type: employees<br>Text: ## Insurellm Career Progression\n- **January 2017 - October 2018**: **Junior Data Engineer**  \n  * Ma...", "Type: employees<br>Text: ## Annual Performance History\n- **2017**: *Meets Expectations*  \n  <PERSON><PERSON> showed potential in her ro...", "Type: employees<br>Text: - **2021**: *Exceeds Expectations*  \n  <PERSON><PERSON> spearheaded the transition to a new data warehousing s...", "Type: employees<br>Text: ## Compensation History\n- **2017**: $70,000 (Junior Data Engineer)  \n- **2018**: $75,000 (Junior Dat...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth**: May 14, 1990  \n- **Job Title**: <PERSON><PERSON>...", "Type: employees<br>Text: ## Annual Performance History\n- **2018**: **3/5** - Adaptable team player but still learning to take...", "Type: employees<br>Text: ## Compensation History\n- **March 2018**: Initial salary of $80,000.\n- **July 2019**: Salary increas...", "Type: employees<br>Text: # <PERSON>\n\n## Summary\n- **Date of Birth:** October 14, 1990\n- **Job Title:** HR Generalist\n-...", "Type: employees<br>Text: ## Annual Performance History\n- **2020:** Exceeds Expectations  \n  <PERSON> demonstrated exce...", "Type: employees<br>Text: ## Compensation History\n- **2020:** Base Salary - $55,000  \n  The entry-level salary matched industr...", "Type: employees<br>Text: - **2023:** Base Salary - $70,000  \n  Recognized for substantial improvement in employee relations m...", "Type: employees<br>Text: # HR Record\n\n# <PERSON>\n\n## Summary\n- **Date of Birth:** April 12, 1989  \n- **Job Title:** Sen...", "Type: employees<br>Text: ## Annual Performance History\n- **2023:** Rating: 4.5/5  \n  *<PERSON> exceeded expectations, successfu...", "Type: employees<br>Text: ## Compensation History\n- **2023:** Base Salary: $115,000 + Bonus: $15,000  \n  *Annual bonus based o...", "Type: employees<br>Text: - **Engagement in Company Culture:** Regularly participates in team-building events and contributes ...", "Type: products<br>Text: # Product Summary\n\n# Carllm\n\n## Summary\n\nCarllm is an innovative auto insurance product developed by...", "Type: products<br>Text: - **Instant Quoting**: With Carllm, insurance companies can offer near-instant quotes to customers, ...", "Type: products<br>Text: - **Mobile Integration**: Carllm is designed to work seamlessly with mobile applications, providing ...", "Type: products<br>Text: - **Professional Tier**: $2,500/month\n  - For medium-sized companies.\n  - All Basic Tier features pl...", "Type: products<br>Text: ### Q2 2025: Customer Experience Improvements\n- Launch of a new **mobile app** for end-users.\n- Intr...", "Type: products<br>Text: # Product Summary\n\n# Homellm\n\n## Summary\nHomellm is an innovative home insurance product developed b...", "Type: products<br>Text: ### 2. Dynamic Pricing Model\nWith Homellm's innovative dynamic pricing model, insurance providers ca...", "Type: products<br>Text: ### 5. Multi-Channel Integration\nHomellm seamlessly integrates into existing insurance platforms, pr...", "Type: products<br>Text: - **Basic Tier:** Starting at $5,000/month for small insurers with basic integration features.\n- **S...", "Type: products<br>Text: All tiers include a comprehensive training program and ongoing updates to ensure optimal performance...", "Type: products<br>Text: With Homellm, Insurellm is committed to transforming the landscape of home insurance, ensuring both ...", "Type: products<br>Text: # Product Summary\n\n# Markellm\n\n## Summary\n\nMarkellm is an innovative two-sided marketplace designed ...", "Type: products<br>Text: - **User-Friendly Interface**: Designed with user experience in mind, Markellm features an intuitive...", "Type: products<br>Text: - **Customer Support**: Our dedicated support team is always available to assist both consumers and ...", "Type: products<br>Text: ### For Insurance Companies:\n- **Basic Listing Fee**: $199/month for a featured listing on the platf...", "Type: products<br>Text: ### Q3 2025\n- Initiate a comprehensive marketing campaign targeting both consumers and insurers to i...", "Type: products<br>Text: # Product Summary\n\n# Rellm: AI-Powered Enterprise Reinsurance Solution\n\n## Summary\n\nRellm is an inno...", "Type: products<br>Text: ### Seamless Integrations\nRellm's architecture is designed for effortless integration with existing ...", "Type: products<br>Text: ### Regulatory Compliance Tools\nRellm includes built-in compliance tracking features to help organiz...", "Type: products<br>Text: Join the growing number of organizations leveraging Rellm to enhance their reinsurance processes whi...", "Type: products<br>Text: Experience the future of reinsurance with Rellm, where innovation meets reliability. Let Insurellm h..."], "type": "scatter3d", "x": [81.53388, 53.523838, 65.4336, -11.2568, -35.23297, -58.1388, -70.451775, 80.44417, 25.835087, -99.28855, 25.3601, 71.5455, 39.470325, 11.782631, -3.366674, 23.596594, 20.719059, 9.189867, -6.907728, 41.62049, 3.820471, 22.429987, -1.9527842, 2.6615057, 9.8561535, -25.084528, 88.3859, -43.759174, -70.171425, 64.19189, -73.61963, 58.55072, -8.85301, -21.603752, 2.7881224, -45.822075, -42.858322, 0.59138376, -17.384357, -64.93836, -5.5359893, -11.441331, -11.330225, -20.265352, -39.243156, -63.98278, -81.72354, -71.28366, -9.971935, -31.514902, -18.16162, -4.766756, -22.621572, -37.923866, -47.165283, -48.194252, 20.253887, 223.44736, -51.686974, -42.731888, -3.2548769, -18.483477, -44.07783, 7.867005, 26.948917, 106.128426, 53.68431, 28.933582, 34.222527, -16.782572, -37.06238, -52.3044, 34.171013, -16.1603, -48.797993, -75.184235, -81.12384, -65.20964, -78.65246, -58.300514, -27.88297, -41.794777, -83.91477, -41.56064, 7.7734685, -74.547615, -19.879875, -8.129884, -1.8509603, 14.149119, -4.45687, -53.21423, 6.1975307, 35.461143, -14.680159, -20.67162, -23.223652, -5.4168777, 17.79015, 25.157133, 11.091784, 45.41651, 63.17549, 50.12626, 30.874004, 35.734764, 80.13974, 57.350708, 36.339565, 25.682257, 78.46156, 61.396954, -83.5418, 68.61663, 47.78963, 47.6066, 49.488094, 80.07241, 57.53512, 79.77016, 33.869728, 63.889473, -32.792236], "y": [19.283733, 11.140304, 34.85373, 66.58248, 20.496525, 18.66278, 63.37658, -38.804882, 36.968765, 11.0408945, -2.8027616, -0.6743983, 61.195312, 26.506996, 19.132858, 17.96988, 41.849804, 83.186935, -16.386538, 84.47603, 21.801687, 7.00924, 51.315266, -36.286488, -3.6705906, 61.74415, -32.81874, 91.45265, 64.3316, -72.978806, 43.9395, -35.78357, 8.203194, 68.245834, 55.084503, 68.109634, 82.05149, -18.306131, 1.9083865, 63.671555, 26.325958, -9.275049, -32.211662, 29.510502, 52.090054, 24.063622, 26.914606, 46.11639, 50.52323, 41.756107, 30.933, 101.60333, 75.41632, 16.445831, 91.67727, -41.476246, -49.900795, 15.246118, -25.914267, -37.00789, -70.7613, -40.41268, -24.553493, -97.089226, -107.92218, 8.401706, -64.885956, -64.041595, -59.68835, -63.750614, -17.248238, -9.267501, -71.522736, -10.604969, 13.532798, -19.520105, -42.81251, -65.766785, -22.81011, -46.88173, -63.41763, -65.60392, -49.578648, -92.86681, -72.58249, -63.928215, -13.93383, -46.0743, -75.10812, -51.170418, -37.75353, -67.76687, 103.12513, -74.91113, -83.86663, -107.106705, -93.31034, -99.96602, -5.3622127, -24.922474, -45.96636, -35.28478, 59.117798, 48.3417, 51.33213, 76.26867, 52.505924, 27.40437, 14.75013, 28.69867, 4.7155714, -7.247487, -34.02125, -1.7822995, 0.14419319, 19.239779, -8.316879, 23.734856, 85.2096, -47.07777, 61.757893, 59.476, 38.759068], "z": [-14.29453, -34.71637, -28.59993, -22.65612, -8.054486, 58.79845, -3.3757033, 14.808398, -66.40088, -2.968062, 21.89932, -62.77509, -59.61707, -42.63285, -20.359333, -6.960816, -24.642426, -7.943236, 98.445076, -8.870388, 101.567474, 109.36959, -54.67124, 18.714115, 60.153572, -67.19362, -5.8205786, 63.059887, 65.17404, 4.4357395, -27.966211, -5.472546, 264.10822, 52.605568, 50.606712, 39.145245, -33.420185, 0.98187125, 70.157196, -46.92285, 17.546955, 28.075523, 57.448467, 47.964592, -15.538981, 3.036544, 33.89405, 18.556377, 8.348332, 20.07917, -83.82076, -44.158554, 11.562198, 26.33919, 7.3630586, 28.997889, -19.409937, 213.27306, 47.549023, -27.961267, -33.38058, -29.46969, -67.14885, -33.69737, -4.7212925, -12.064441, 51.81019, 51.387836, 29.224623, -1.6933604, 2.5197253, -38.66655, -32.058723, -46.31407, -51.627632, -18.293224, -9.943942, -7.30543, 6.786018, -47.776237, -52.915794, -71.69413, -59.169884, -39.777233, -0.20590062, -75.22719, -96.832146, -103.80248, -78.61911, -96.8633, -80.49291, 34.09247, 34.139854, -69.749855, 50.413597, -3.7609684, 16.896708, 37.713326, -83.134605, -47.321796, -50.20056, -63.635235, 74.50581, 53.450645, 71.70312, 64.599655, 45.940273, 84.374146, 64.53904, 43.600063, 62.300507, 74.37096, 55.983784, 20.760025, -0.25919074, 15.355031, 36.588936, 24.362068, 21.700638, -40.98002, 17.98443, 6.0238895, 88.67324]}], "layout": {"height": 700, "margin": {"b": 10, "l": 10, "r": 20, "t": 40}, "scene": {"xaxis": {"title": {"text": "x"}}, "yaxis": {"title": {"text": "y"}}, "zaxis": {"title": {"text": "z"}}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "heatmapgl": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmapgl"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "3D Chroma Vector Store Visualization"}, "width": 900}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Let's try 3D!\n", "\n", "tsne = TSNE(n_components=3, random_state=42)\n", "reduced_vectors = tsne.fit_transform(vectors)\n", "\n", "# Create the 3D scatter plot\n", "fig = go.Figure(data=[go.Scatter3d(\n", "    x=reduced_vectors[:, 0],\n", "    y=reduced_vectors[:, 1],\n", "    z=reduced_vectors[:, 2],\n", "    mode='markers',\n", "    marker=dict(size=5, color=colors, opacity=0.8),\n", "    text=[f\"Type: {t}<br>Text: {d[:100]}...\" for t, d in zip(doc_types, documents)],\n", "    hoverinfo='text'\n", ")])\n", "\n", "fig.update_layout(\n", "    title='3D Chroma Vector Store Visualization',\n", "    scene=dict(xaxis_title='x', yaxis_title='y', zaxis_title='z'),\n", "    width=900,\n", "    height=700,\n", "    margin=dict(r=20, b=10, l=10, t=40)\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}