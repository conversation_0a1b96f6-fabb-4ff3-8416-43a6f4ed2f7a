{"cells": [{"cell_type": "markdown", "id": "d032fee4-7e37-43d5-974e-b6d29f0813e9", "metadata": {}, "source": ["# Langchain + FAISS + Ollama\n", "\n", "This workbook demonstrate RAG with FAISS & ollama (local)\n", "\n", "**Notes:**\n", "1. <PERSON><PERSON><PERSON><PERSON><PERSON>: https://ollama.com/download\n", "2. Download ollama embed: `ollama pull nomic-embed-text`\n", "3. Download the model (eg. `ollama pull mistral-openorca`)\n", "4. `ollama serve`"]}, {"cell_type": "code", "execution_count": 18, "id": "63c10e01-860b-43ce-b6aa-d8cbfa91f630", "metadata": {}, "outputs": [], "source": ["import os\n", "import glob\n", "\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": 2, "id": "afeec974-6332-46b3-adf9-a19110b92a50", "metadata": {}, "outputs": [], "source": ["# imports for langchain\n", "\n", "from langchain.document_loaders import DirectoryLoader, TextLoader\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.schema import Document\n", "\n", "from langchain.vectorstores import FAISS\n", "\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.chains import ConversationalRetrievalChain"]}, {"cell_type": "markdown", "id": "05d1dec0-2695-4057-817f-d3403ab88669", "metadata": {}, "source": ["## Directory setup"]}, {"cell_type": "code", "execution_count": 3, "id": "5a759958-8a8d-48f0-b34c-a435d97faad9", "metadata": {}, "outputs": [], "source": ["# Read in documents using <PERSON><PERSON><PERSON><PERSON>'s loaders\n", "# Take everything in all the sub-folders of our knowledgebase\n", "\n", "folders = glob.glob(\"knowledge-base/*\")\n", "\n", "# With thanks to <PERSON><PERSON> and <PERSON>, students on the course, for this fix needed for some users \n", "text_loader_kwargs = {'encoding': 'utf-8'}\n", "# If that doesn't work, some Windows users might need to uncomment the next line instead\n", "# text_loader_kwargs={'autodetect_encoding': True}\n", "\n", "documents = []\n", "for folder in folders:\n", "    doc_type = os.path.basename(folder)\n", "    loader = DirectoryLoader(folder, glob=\"**/*.md\", loader_cls=TextLoader, loader_kwargs=text_loader_kwargs)\n", "    folder_docs = loader.load()\n", "    for doc in folder_docs:\n", "        doc.metadata[\"doc_type\"] = doc_type\n", "        documents.append(doc)"]}, {"cell_type": "code", "execution_count": 4, "id": "cc7ec6de-22bc-4f07-b41e-cce55bfceb29", "metadata": {}, "outputs": [{"data": {"text/plain": ["31"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# This step is to make sure setup above works\n", "len(documents)"]}, {"cell_type": "code", "execution_count": 5, "id": "afe5b057-a99e-4566-8984-bb5ae5831de5", "metadata": {}, "outputs": [], "source": ["text_splitter = CharacterTextSplitter(chunk_size=1300, chunk_overlap=200)\n", "chunks = text_splitter.split_documents(documents)"]}, {"cell_type": "code", "execution_count": 6, "id": "e14c7c99-628b-4a3a-ad0b-f2df20762db8", "metadata": {}, "outputs": [{"data": {"text/plain": ["91"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["len(chunks)"]}, {"cell_type": "code", "execution_count": 7, "id": "ddd22f41-6f0e-49ea-b5a5-5f8cde314aa9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Document types found: products, employees, company, contracts\n"]}], "source": ["doc_types = set(chunk.metadata['doc_type'] for chunk in chunks)\n", "print(f\"Document types found: {', '.join(doc_types)}\")"]}, {"cell_type": "markdown", "id": "33f02751-9d75-459a-8039-d7e5bb962d9b", "metadata": {}, "source": ["## Convert to vector database\n", "\n", "In this version, I use `nomic-embed-text' from ollama library: https://ollama.com/library/nomic-embed-text.\n", "Not sure if there are another embed model."]}, {"cell_type": "code", "execution_count": 8, "id": "1e2b354d-ff60-4de3-8ace-192da1501563", "metadata": {}, "outputs": [], "source": ["from langchain_ollama import OllamaEmbeddings, ChatOllama\n", "\n", "db_name = \"knowledge_base_1\"\n", "embeddings = OllamaEmbeddings(model=\"nomic-embed-text\")"]}, {"cell_type": "code", "execution_count": 10, "id": "845e9f81-6a50-4088-a921-b68faff18976", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 91 vectors with 768 dimensions in the vector store\n"]}], "source": ["# Create our FAISS vectorstore!\n", "\n", "vectorstore = FAISS.from_documents(chunks, embedding=embeddings)\n", "\n", "total_vectors = vectorstore.index.ntotal\n", "dimensions = vectorstore.index.d\n", "\n", "print(f\"There are {total_vectors} vectors with {dimensions:,} dimensions in the vector store\")"]}, {"cell_type": "markdown", "id": "50b5ae64-ca8c-470c-b546-591feebb0217", "metadata": {}, "source": ["## Chat setup\n", "\n", "I add `callbacks=[StdOutCallbackHandler()]` parameter to see what exactly retriever passed to the model as <PERSON> teach us."]}, {"cell_type": "code", "execution_count": 11, "id": "e8cfeedd-496f-4e66-8682-aff4bf8d360e", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_3188\\683569231.py:8: Lang<PERSON>hainDeprecationWarning: Please see the migration guide at: https://python.langchain.com/docs/versions/migrating_memory/\n", "  memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n"]}], "source": ["# create a new <PERSON><PERSON> with <PERSON><PERSON><PERSON>\n", "from langchain_core.callbacks import StdOutCallbackHandler\n", "\n", "MODEL = \"mistral-openorca\"\n", "llm = ChatOllama(temperature=0.7, model=MODEL)\n", "\n", "# set up the conversation memory for the chat\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n", "\n", "# the retriever is an abstraction over the VectorStore that will be used during RAG\n", "retriever = vectorstore.as_retriever(search_kwargs={\"k\": 13})\n", "\n", "# putting it together: set up the conversation chain with the GPT 4o-mini LLM, the vector store and memory\n", "conversation_chain = ConversationalRetrievalChain.from_llm(llm=llm, \n", "                                                           retriever=retriever, \n", "                                                           memory=memory, \n", "                                                           callbacks=[StdOutCallbackHandler()])"]}, {"cell_type": "code", "execution_count": 12, "id": "4dc8f215-e076-4ae2-a7b7-6d663a358ae3", "metadata": {}, "outputs": [{"data": {"text/plain": ["4"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# run a quick test - should return a list of documents = 4\n", "question = \"Insurellm\"\n", "docs = vectorstore.similarity_search(question)\n", "len(docs)"]}, {"cell_type": "code", "execution_count": 13, "id": "6328550f-5422-4e4f-a9d5-b9c4bca229d5", "metadata": {}, "outputs": [{"data": {"text/plain": ["Document(metadata={'source': 'knowledge-base\\\\company\\\\about.md', 'doc_type': 'company'}, page_content=\"# About Insurellm\\n\\nInsurellm was founded by <PERSON> in 2015 as an insurance tech startup designed to disrupt an industry in need of innovative products. It's first product was Markellm, the marketplace connecting consumers with insurance providers.\\nIt rapidly expanded, adding new products and clients, reaching 200 emmployees by 2024 with 12 offices across the US.\")"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["docs[0]"]}, {"cell_type": "markdown", "id": "772cca6a-fc02-47e3-bceb-9076a3eeac56", "metadata": {}, "source": ["## Let's Chat!!"]}, {"cell_type": "code", "execution_count": 19, "id": "d7db3fae-6f17-46da-a098-65bb9467918e", "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\u001b[1m> Entering new ConversationalRetrievalChain chain...\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mGiven the following conversation and a follow up question, rephrase the follow up question to be a standalone question, in its original language.\n", "\n", "Chat History:\n", "\n", "Human: tell me about Insurellm\n", "Assistant:  Insurellm is an innovative insurance company that specializes in providing personalized, tech-driven insurance solutions to individuals and businesses. They offer a wide range of insurance products and services, including life, health, property, and auto insurance. The company leverages the latest technology and data analytics to provide customers with tailored policies that meet their specific needs.\n", "\n", "Some key features and benefits of Insurellm include:\n", "\n", "1. Personalized Insurance Solutions: Insurellm uses advanced data analysis techniques to understand your unique risk profile and tailor insurance policies accordingly. This ensures that you receive the best possible coverage at competitive rates.\n", "\n", "2. Seamless Digital Experience: Insurellm offers a user-friendly online platform that allows customers to access their policy information, manage claims, and renew policies with ease. The platform is designed for a seamless digital experience, making it simple to navigate and use.\n", "\n", "3. Real-Time Quotes: With Insurellm's proprietary algorithms, users can receive real-time quotes for various insurance products based on their individual risk profile and preferences. This enables customers to make informed decisions when selecting policies that suit their needs and budget.\n", "\n", "4. Robust Risk Management Tools: The company provides a range of tools and resources to help individuals and businesses manage risks proactively, including loss prevention tips, risk assessments, and training programs.\n", "\n", "5. Dedicated Customer Support: Insurellm offers dedicated customer support teams available 24/7 to assist with any inquiries or concerns. Their professionals are well-trained and knowledgeable, ensuring that customers receive prompt and effective assistance.\n", "\n", "6. Claims Management: In case of an insured event, Insurellm's claims management team works efficiently to process the claim quickly, providing support and guidance throughout the entire process.\n", "\n", "7. Transparent Pricing: Insurellm is committed to transparent pricing, ensuring that customers are well-informed about their insurance coverage and costs. They offer various options for payment, including flexible monthly installments, making it easy for clients to manage their policies.\n", "\n", "In conclusion, Insurellm is a dynamic insurance company that offers personalized, tech-driven solutions to cater to the unique needs of individuals and businesses. Their commitment to innovation, customer satisfaction, and transparency makes them a valuable partner in managing risks and ensuring financial security.\n", "Follow Up Input: tell me about Insurellm\n", "Standalone question:\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new StuffDocumentsChain chain...\u001b[0m\n", "\n", "\n", "\u001b[1m> Entering new LLMChain chain...\u001b[0m\n", "Prompt after formatting:\n", "\u001b[32;1m\u001b[1;3mSystem: Use the following pieces of context to answer the user's question. \n", "If you don't know the answer, just say that you don't know, don't try to make up an answer.\n", "----------------\n", "# Overview of Insurellm\n", "\n", "Insurellm is an innovative insurance tech firm with 200 employees across the US.\n", "Insurellm offers 4 insurance software products:\n", "- Carllm, a portal for auto insurance companies\n", "- Homellm, a portal for home insurance companies\n", "- Rellm, an enterprise platform for the reinsurance sector\n", "- Marketllm, a marketplace for connecting consumers with insurance providers\n", "  \n", "Insurellm has more than 300 clients worldwide.\n", "\n", "# About Insurellm\n", "\n", "Insurellm was founded by <PERSON> in 2015 as an insurance tech startup designed to disrupt an industry in need of innovative products. It's first product was Markellm, the marketplace connecting consumers with insurance providers.\n", "It rapidly expanded, adding new products and clients, reaching 200 emmployees by 2024 with 12 offices across the US.\n", "\n", "---\n", "\n", "## Features\n", "\n", "1. **Access to Core Features**: Roadway Insurance Inc. will have access to all Professional Tier features, including:\n", "   - AI-Powered Risk Assessment\n", "   - Advanced Analytics & Fraud Detection\n", "   - Instant Quoting System\n", "   - Customizable Coverage Plans\n", "   - Customer Insights Dashboard\n", "   \n", "2. **Mobile Integration**: All features will be accessible through a mobile application that Insurellm will provide.\n", "3. **Customer Support**: Includes 24/7 automated customer support via AI chatbots and access to dedicated account management support during business hours.\n", "\n", "---\n", "\n", "## Support\n", "\n", "1. **Technical Support**: Roadway Insurance Inc. will receive priority technical support from Insurellm for any issues arising from the Carllm product.\n", "2. **Training**: Insurellm will provide up to 5 training sessions for Roadway Insurance Inc. staff on the effective use of the Carllm platform, scheduled at mutual convenience.\n", "3. **Updates and Maintenance**: Regular updates to the Carllm platform will be conducted quarterly, and any maintenance outages will be communicated at least 48 hours in advance.\n", "\n", "---\n", "\n", "## Support\n", "Insurellm provides Stellar Insurance Co. with the following support services:\n", "\n", "- **24/7 Technical Support**: Access to dedicated support representatives via phone and online chat.\n", "- **Quarterly Account Review**: Meetings to discuss performance metrics and uncover additional needs.\n", "- **Training Sessions**: Initial orientation and ongoing training opportunities to maximize the effectiveness of Rellm usage.\n", "- **Updates and Upgrades**: Regular software updates and enhancements are included as part of the subscription.\n", "\n", "Stellar Insurance Co. acknowledges receipt of the Rellm product summary and agrees to the terms set forth above. By signing below, both parties confirm their acceptance of this contract.\n", "\n", "**For Insurellm**  \n", "______________________________  \n", "[Signature]  \n", "[Name, Title]  \n", "[Date]  \n", "\n", "**For Stellar Insurance Co.**  \n", "______________________________  \n", "[Signature]  \n", "[Name, Title]  \n", "[Date]\n", "\n", "## Features\n", "1. **AI-Powered Matching**: Belvedere Insurance will benefit from Markellm's AI-powered matching, ensuring the best-fit customers are identified and connected.\n", "2. **Real-Time Quotes**: Access to real-time quotes will enhance the customer acquisition process, facilitating timely and informed decision-making.\n", "3. **Data Insights**: Client shall have access to Markellm's analytics dashboard, allowing insights into consumer behavior and market trends.\n", "4. **Customization Options**: Belvedere Insurance can leverage optional premium features and analytics upon payment of an additional $9.99/month.\n", "5. **Customer Support**: Insurellm will provide dedicated support to Belvedere Insurance, ensuring any issues or queries are promptly addressed.\n", "\n", "## Support\n", "1. **Technical Support**: Technical support will be available from 9 AM to 7 PM EST, Monday through Friday via email and phone.\n", "2. **Response Times**: Insurellm agrees to respond to all support queries within 24 business hours. Emergency support will be prioritized throughout the contract period.\n", "3. **Training**: Insurellm will offer a comprehensive training session for the Client’s staff upon beginning the service to ensure effective utilization of the features.\n", "\n", "## Pricing\n", "\n", "Insurellm offers flexible pricing plans for Rellm to cater to various business needs:\n", "\n", "- **Basic Plan**: $5,000/month\n", "  - Includes access to core features and standard integrations.\n", "  \n", "- **Professional Plan**: $10,000/month\n", "  - Includes all features, advanced integrations, and priority customer support.\n", "  \n", "- **Enterprise Plan**: Custom pricing\n", "  - Tailored solutions with personalized features, extensive integrations, and dedicated account management.\n", "\n", "Join the growing number of organizations leveraging Rellm to enhance their reinsurance processes while driving profitability and compliance. \n", "\n", "## 2025-2026 Roadmap\n", "\n", "At Insurellm, we are committed to the continuous improvement of Rellm. Our roadmap for 2025-2026 includes:\n", "\n", "- **Q3 2025**: \n", "  - Launch of the Rellm Mobile App for on-the-go insights and management.\n", "  - Introduction of augmented reality (AR) features for interactive risk assessments.\n", "\n", "- **Q1 2026**: \n", "  - Deployment of advanced machine learning models for even more accurate risk predictions.\n", "  - Expansion of integration capabilities to support emerging technologies in the insurance sector.\n", "\n", "# Product Summary\n", "\n", "# Homellm\n", "\n", "## Summary\n", "Homellm is an innovative home insurance product developed by Insurellm that leverages advanced AI technology to revolutionize the way insurance providers offer coverage to homeowners. Designed for both B2B and B2C segments, Homellm empowers insurers to provide personalized, data-driven policies, enhancing customer experience while minimizing risk and operational costs. By integrating seamlessly with existing systems, Homellm helps insurance companies streamline their processes and stay competitive in the ever-evolving insurance industry.\n", "\n", "## Features\n", "### 1. AI-Powered Risk Assessment\n", "Homellm utilizes sophisticated AI algorithms to analyze vast datasets, allowing insurance companies to assess risks accurately. This feature provides real-time insights for underwriting decisions, enabling insurers to tailor policies to individual customer needs.\n", "\n", "### 2. Dynamic Pricing Model\n", "With Homellm's innovative dynamic pricing model, insurance providers can offer flexible premiums based on real-time risk evaluations and historical data. This adaptability ensures that customers pay a fair price that accurately reflects their unique risk profile.\n", "\n", "## Features\n", "\n", "1. **Included Features**: Under the Professional Tier, TechDrive Insurance will have access to the following features of Carllm:\n", "   - AI-Powered Risk Assessment\n", "   - Instant Quoting\n", "   - Customizable Coverage Plans\n", "   - <PERSON>aud Detection\n", "   - Customer Insights Dashboard\n", "   - Mobile Integration\n", "   - Automated Customer Support\n", "\n", "2. **System Requirements**: TechDrive Insurance must ensure that their existing systems meet the technical requirements to integrate with Carllm, as outlined in the onboarding documentation provided by Insurellm.\n", "\n", "## Support\n", "\n", "1. **Customer Support**: Insurellm will provide 24/7 customer support to TechDrive Insurance via AI-driven chatbots, ensuring timely resolution of inquiries and issues.\n", "\n", "2. **Training**: TechDrive Insurance staff will receive onboarding training sessions to ensure effective utilization of the Carllm platform, scheduled within the first two weeks of contract commencement.\n", "\n", "3. **System Updates**: The Provider will push regular updates to improve system performance and add new features. TechDrive Insurance will receive prior notification of any significant upgrades that may affect current operations.\n", "\n", "---\n", "\n", "**Signatures:**\n", "\n", "**Insurellm Representative:**  \n", "Name: <PERSON>  \n", "Title: Account Manager  \n", "Date: ____________\n", "\n", "## Support\n", "1. **Technical Support**: Insurellm shall provide 24/7 technical support via an email and phone assistance for the duration of this Contract.\n", "2. **Training**: Insurellm will conduct an onsite training session for Client employees upon implementation, and quarterly training webinars will be made available thereafter.\n", "3. **Updates and Maintenance**: Insurellm will provide regular system updates and maintenance, ensuring that the software is operating at peak efficiency.\n", "\n", "By signing below, both parties agree to the terms set forth in this Contract for the use of the Homellm product.\n", "\n", "____  \n", "**Insurellm Authorized Signature**  \n", "Name: <PERSON>  \n", "Title: VP of Sales  \n", "Date: ____________\n", "\n", "____  \n", "**Pinnacle Insurance Co. Authorized Signature**  \n", "Name: <PERSON>  \n", "Title: Chief Operating Officer  \n", "Date: ____________\n", "\n", "- **Predictive Maintenance Alerts**: The Customer will receive alerts regarding potential maintenance needs for insured properties, enhancing client satisfaction and reducing claims.\n", "\n", "- **Multi-Channel Integration**: Homellm will integrate with the Customer's existing platforms to create seamless service delivery.\n", "\n", "- **Customer Portal**: A dedicated portal will be provided, allowing the Customer's clients to manage their accounts 24/7.\n", "\n", "---\n", "\n", "## Support\n", "\n", "1. **Training**: Provider will offer a comprehensive training program at the start of the term to ensure the Customer's staff can effectively use the Homellm product.\n", "\n", "2. **Ongoing Support**: The Provider will supply ongoing technical support via email and phone during business hours (9 am - 5 pm EST) throughout the contract duration.\n", "\n", "3. **Updates and Improvements**: Customer will receive all software updates and feature enhancements as they become available, without additional charge.\n", "\n", "--- \n", "\n", "**AGREEMENT SIGNATURES**\n", "\n", "By signing below, the parties acknowledge their acceptance of the terms of this Agreement.\n", "\n", "**For Insurellm:**\n", "\n", "______________________________  \n", "[Name], [Title]  \n", "Date: ______________________\n", "\n", "**For Greenstone Insurance:**\n", "\n", "______________________________  \n", "[Name], [Title]  \n", "Date: ______________________\n", "\n", "---\n", "\n", "### Support\n", "1. **Dedicated Customer Support:**  \n", "   BrightWay Solutions will have access to a dedicated support team from Insurellm during standard business hours (9 AM - 7 PM EST).\n", "\n", "2. **Additional Support Services:**  \n", "   Technical support for integration and maintenance will be available. An optional premium support package can be purchased for $49.99/month, which includes 24/7 support and advanced troubleshooting.\n", "\n", "3. **Training and Onboarding:**  \n", "   Insurellm agrees to provide one free training session on how to utilize the Markellm platform effectively for BrightWay Solutions’ team upon contract signing.\n", "\n", "### Signatures\n", "By signing below, both parties agree to the terms and conditions outlined in this Contract.\n", "\n", "__________________________  \n", "**[Name], [Title]**  \n", "**Insurellm**  \n", "Date: ______________________\n", "\n", "__________________________  \n", "**[Name], [Title]**  \n", "**BrightWay Solutions**  \n", "Date: ______________________\n", "\n", "---\n", "\n", "This document serves as a formal agreement between Insurellm and BrightWay Solutions, ensuring a successful partnership focused on enhancing the insurance shopping experience for consumers.\n", "\n", "4. **Client Portal Access**: EverGuard Insurance will have access to both client and broker portals, enhancing communication and collaboration with its partners.\n", "\n", "## Support\n", "\n", "1. **Customer Support**: Insurellm will provide EverGuard Insurance with 24/7 customer support, including live chat, email, and phone assistance for any technical issues or inquiries regarding Rellm.\n", "\n", "2. **Training Services**: Insurellm will provide initial training for EverGuard Insurance staff to ensure proper utilization of Rellm features. Additional training sessions can be scheduled upon request at an agreed fee.\n", "\n", "3. **Updates and Upgrades**: EverGuard Insurance will receive all platform updates and upgrades at no additional cost during the contract term, including enhancements outlined in Insurellm’s 2025-2026 roadmap.\n", "\n", "4. **Feedback Mechanisms**: EverGuard Insurance is encouraged to provide feedback regarding Rellm’s functionalities and any desired features, which will be considered for future updates.\n", "\n", "---\n", "\n", "**Signatures**  \n", "**For Insurellm**: __________________________  \n", "**Name**: <PERSON>  \n", "**Title**: Chief Operating Officer  \n", "**Date**: _________________\n", "\n", "---\n", "\n", "## Support\n", "\n", "Insurellm commits to providing comprehensive support to GreenValley Insurance, which includes:\n", "\n", "1. **Onboarding:** An extensive training program for the GreenValley staff to ensure effective use of Homellm.\n", "   \n", "2. **Dedicated Support Team:** A dedicated support team available 24/7 to address any technical issues or inquiries.\n", "\n", "3. **Regular Updates:** Insurellm will offer ongoing updates and enhancements to the Homellm platform, including new features and security improvements.\n", "\n", "4. **Feedback Implementation:** Insurellm will actively solicit feedback from GreenValley Insurance to ensure Homellm continues to meet their evolving needs.\n", "\n", "---\n", "\n", "**Signatures:**\n", "\n", "_________________________________  \n", "**[Name]**  \n", "**Title**: CEO  \n", "**Insurellm, Inc.**\n", "\n", "_________________________________  \n", "**[Name]**  \n", "**Title**: COO  \n", "**GreenValley Insurance, LLC**  \n", "\n", "---\n", "\n", "This agreement represents the complete understanding of both parties regarding the use of the Homellm product and supersedes any prior agreements or communications.\n", "Human:  What services and features does Insurellm provide for personalized insurance solutions?\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "\u001b[1m> Finished chain.\u001b[0m\n", "\n", "Answer:  Insurellm provides various services and features for personalized insurance solutions, which include:\n", "\n", "1. Customized Coverage: Insurellm offers tailored insurance policies based on individual needs and preferences. This means that customers can choose the coverage that best suits their requirements, ensuring they are not overpaying or underinsured.\n", "\n", "2. Digital Insurance Platform: The platform provides a user-friendly digital interface for customers to manage their policies, make claims, and access various insurance services. This simplifies the process and makes it more convenient for policyholders.\n", "\n", "3. Data-driven Analysis: Insurellm utilizes advanced data analytics and machine learning algorithms to analyze customer behavior, preferences, and risk factors to provide personalized insurance recommendations. This helps to improve the accuracy of coverage and pricing.\n", "\n", "4. Personalized Pricing: Insurellm uses various factors such as age, lifestyle, health, location, and more to determine the most suitable insurance plan at the best possible price for each customer.\n", "\n", "5. Policy Comparison: The platform allows customers to compare different insurance policies from multiple providers based on their specific needs. This helps them make an informed decision and choose the best plan that meets their requirements.\n", "\n", "6. Claims Management: Insurellm offers a streamlined claims management process, allowing policyholders to file claims, track progress, and receive updates through the digital platform.\n", "\n", "7. Risk Assessment and Prevention: The platform utilizes data analysis and predictive modeling to identify potential risks for customers. This helps in preventing losses by offering advice on risk-mitigation strategies and adjusting insurance coverage accordingly.\n", "\n", "8. Dynamic Coverage: Insurellm enables policyholders to adjust their coverage according to life events or changes in circumstances, ensuring that they are always protected as their needs evolve.\n", "\n", "9. Multi-policy Discounts: By offering multiple types of insurance policies under one platform, customers can benefit from discounts for bundling their coverages, making it more cost-effective.\n", "\n", "10. 24/7 Customer Support: Insurellm provides round-the-clock customer support to assist policyholders with any queries or concerns they may have related to their insurance policies.\n"]}], "source": ["# Chat test\n", "query = \"tell me about Insurellm\"\n", "result = conversation_chain.invoke({\"question\": query})\n", "answer = result[\"answer\"]\n", "print(\"\\nAnswer:\", answer)"]}, {"cell_type": "code", "execution_count": 15, "id": "102a8222-f4a6-4c87-8cce-86a89adb19e9", "metadata": {}, "outputs": [], "source": ["# Clearing memory before new chat\n", "memory.clear()"]}, {"cell_type": "code", "execution_count": 16, "id": "a2377cef-1ea6-4395-8147-db9c684fdb65", "metadata": {}, "outputs": [], "source": ["def chat_gradio(question, history):\n", "    result = conversation_chain.invoke({\"question\": question})\n", "    return result[\"answer\"]"]}, {"cell_type": "code", "execution_count": 17, "id": "4c357dd8-252c-4dd5-85be-277d3ed53d79", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7860\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7860/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Ui launch\n", "view = gr.ChatInterface(chat_gradio, type=\"messages\").launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "ebeff830-49f0-4bf6-a3b2-10ba038db1c0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.5"}}, "nbformat": 4, "nbformat_minor": 5}