{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DocuSeek AI: Secure and Intelligent Document Retrieval\n", "\n", "**Created by <PERSON><PERSON><PERSON>**\n", "\n", "**DocuSeek AI** is a tool designed to enable secure and intelligent interaction with private documents. It utilizes a Retrieval-Augmented Generation (RAG) system, combining Large Language Models (LLMs) and vector search to provide accurate, context-aware responses based on uploaded content. Developed as part of [LLM Engineering course](https://www.udemy.com/course/llm-engineering-master-ai-and-large-language-models/learn/lecture/46867711#content) by <PERSON><PERSON>, it demonstrates practical applications of AI in document processing and data privacy, offering a hands-on example of secure, AI-driven data retrieval.\n", "\n", "**Key Features of DocuSeek AI:**\n", "- **Privacy-Focused**: All data is processed locally, ensuring your documents remain private.\n", "- **Intelligent**: Leverages LLMs and vector search for precise, contextually relevant answers.\n", "- **Versatile**: Supports multiple file formats and provides interactive visualizations of data relationships.\n", "\n", "Explore the source code and setup instructions at [DocuSeek AI GitHub](https://github.com/alakob/ai_private_document_retriever) to experiment with RAG systems and LLM applications in document retrieval.\n", "\n", "---\n", "\n", "### Table of Contents\n", "1. [Overview of DocuSeek AI](#overview-of-docuseek-ai)\n", "2. [Document Upload and Processing](#document-upload-and-processing)\n", "3. [Querying Your Documents](#querying-your-documents)\n", "4. [Visualizing Data Relationships](#visualizing-data-relationships)\n", "5. [Setup and Exploration](#setup-and-exploration)\n", "\n", "---\n", "\n", "## Overview of DocuSeek AI\n", "\n", "### An AI-Powered Document Retrieval Tool\n", "**DocuSeek AI** provides an intuitive interface for users to interact with their private documents using AI technology. It is designed to be accessible to users of all levels while showcasing advanced concepts such as Retrieval-Augmented Generation (RAG) and vector-based search.\n", "\n", "![Interface Screenshot](docuseek1.png)  \n", "*Figure 1: The main interface of DocuSeek AI, where users begin their document exploration journey.*\n", "\n", "The tool integrates seamlessly with local files, ensuring data privacy and security throughout the process.\n", "\n", "---\n", "\n", "## Document Upload and Processing\n", "\n", "### Preparing Your Files for Intelligent Querying\n", "To begin, users upload their documents in supported formats (e.g., PDFs, XLSX, PPTX, HTML, PNG, JPEG, TIFF,  BMP, JSON, USPTO XML, JATS XML, Markdown, SdciiDoc,  text files). DocuSeek AI processes these files locally, transforming them into a searchable knowledge base optimized for AI-driven querying.\n", "\n", "![Upload Screenshot](doc_upload.png)  \n", "*Figure 2: The document upload section, displaying supported formats and processing status.*\n", "\n", "This step ensures that your content is ready for accurate and efficient retrieval without compromising privacy.\n", "\n", "---\n", "\n", "## Querying Your Documents\n", "\n", "### Retrieving Contextually Relevant Answers\n", "Once your documents are processed, you can query the system by asking questions related to your content. DocuSeek AI’s RAG system retrieves precise, context-rich answers directly from your uploaded files.\n", "\n", "![Query Screenshot](docuseek3.png)  \n", "*Figure 3: The query interface, where users input questions and receive detailed, relevant responses.*\n", "\n", "This feature demonstrates the power of combining LLMs with vector search to deliver tailored responses, making it ideal for educational and professional use cases.\n", "\n", "---\n", "\n", "## Visualizing Data Relationships\n", "\n", "### Understanding Connections Within Your Documents\n", "**DocuSeek AI** enhances document exploration by generating interactive visualizations that reveal relationships and patterns within your data. This feature is particularly useful for identifying key concepts and their connections.\n", "\n", "![Visualization Screenshot](docuseek4.png)  \n", "*Figure 4: A visualization highlighting relationships and concepts extracted from the documents.*\n", "\n", "These visualizations provide a deeper understanding of your content, making complex information more accessible and actionable.\n", "\n", "---\n", "\n", "## Setup and Exploration\n", "\n", "### Experiment with DocuSeek AI\n", "To explore **DocuSeek AI** and its underlying technology, clone the repository from [DocuSeek AI GitHub](https://github.com/alakob/ai_private_document_retriever) and follow the setup instructions. This provides an opportunity to:\n", "- Experiment with RAG systems and vector search.\n", "- Understand LLM applications in secure document retrieval.\n", "- Customize the tool for specific use cases or educational projects.\n", "\n", "---\n", "\n", "### Acknowledgments\n", "Special thanks to <PERSON><PERSON> for his transformative [LLM Engineering course](https://www.udemy.com/course/llm-engineering-master-ai-and-large-language-models/learn/lecture/46867711#content) that inspired this project.\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}