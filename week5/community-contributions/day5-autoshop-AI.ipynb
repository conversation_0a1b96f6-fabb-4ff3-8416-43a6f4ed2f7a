{"cells": [{"cell_type": "code", "execution_count": null, "id": "ba2779af-84ef-4227-9e9e-6eaf0df87e77", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import glob\n", "from dotenv import load_dotenv\n", "import gradio as gr\n", "import json"]}, {"cell_type": "code", "execution_count": null, "id": "802137aa-8a74-45e0-a487-d1974927d7ca", "metadata": {}, "outputs": [], "source": ["# imports for langchain, plotly and Chroma\n", "\n", "from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain.schema import Document\n", "from langchain_openai import OpenAIEmbeddings, ChatOpenAI\n", "from langchain_chroma import Chroma\n", "import matplotlib.pyplot as plt\n", "from sklearn.manifold import TSNE\n", "import numpy as np \n", "import plotly.graph_objects as go\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.chains import ConversationalRetrievalChain"]}, {"cell_type": "code", "execution_count": null, "id": "58c85082-e417-4708-9efe-81a5d55d1424", "metadata": {}, "outputs": [], "source": ["# price is a factor for our company, so we're going to use a low cost model\n", "\n", "MODEL = \"gpt-4o-mini\"\n", "db_name = \"vector_db\""]}, {"cell_type": "code", "execution_count": null, "id": "ee78efcb-60fe-449e-a944-40bab26261af", "metadata": {}, "outputs": [], "source": ["# Load environment variables in a file called .env\n", "\n", "load_dotenv(override=True)\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')"]}, {"cell_type": "code", "execution_count": null, "id": "b14e6c30-37c6-4eac-845b-5471aa75f587", "metadata": {}, "outputs": [], "source": ["##<PERSON><PERSON> json\n", "with open(\"knowledge-base/auto_shop.json\", 'r') as f: #place auto_shop.json file inside your knowledge-base folder\n", "    data = json.load(f)"]}, {"cell_type": "code", "execution_count": null, "id": "408bc620-477f-47fd-b9e8-ab9d21843ecd", "metadata": {}, "outputs": [], "source": ["#Convert to Langchain\n", "documents = []\n", "for item in data:\n", "    content = item[\"content\"]\n", "    metadata = item.get(\"metadata\", {})\n", "    documents.append(Document(page_content=content, metadata=metadata))"]}, {"cell_type": "code", "execution_count": null, "id": "0371d472-cd14-4967-bc09-9b78e233809f", "metadata": {}, "outputs": [], "source": ["#Chunk documents\n", "splitter = RecursiveCharacterTextSplitter(chunk_size=300, chunk_overlap=50, separators=[\"\\n\\n\", \"\\n\", \",\", \" \", \"\"])\n", "chunks = splitter.split_documents(documents)"]}, {"cell_type": "code", "execution_count": null, "id": "91c2404b-b3c9-4c7f-b199-9895e429a3da", "metadata": {}, "outputs": [], "source": ["doc_types = set(chunk.metadata['source'] for chunk in chunks)\n", "#print(f\"Document types found: {', '.join(doc_types)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "78998399-ac17-4e28-b15f-0b5f51e6ee23", "metadata": {}, "outputs": [], "source": ["embeddings = OpenAIEmbeddings()\n", "\n", "# Delete if already exists\n", "\n", "if os.path.exists(db_name):\n", "    Chroma(persist_directory=db_name, embedding_function=embeddings).delete_collection()\n", "\n", "# Create vectorstore\n", "\n", "vectorstore = Chroma.from_documents(documents=chunks, embedding=embeddings, persist_directory=db_name)\n", "#print(f\"Vectorstore created with {vectorstore._collection.count()} documents\")"]}, {"cell_type": "code", "execution_count": null, "id": "ff2e7687-60d4-4920-a1d7-a34b9f70a250", "metadata": {}, "outputs": [], "source": ["# # Let's investigate the vectors. Use for debugging if needed\n", "\n", "# collection = vectorstore._collection\n", "# count = collection.count()\n", "\n", "# sample_embedding = collection.get(limit=1, include=[\"embeddings\"])[\"embeddings\"][0]\n", "# dimensions = len(sample_embedding)\n", "# print(f\"There are {count:,} vectors with {dimensions:,} dimensions in the vector store\")"]}, {"cell_type": "code", "execution_count": null, "id": "129c7d1e-0094-4479-9459-f9360b95f244", "metadata": {}, "outputs": [], "source": ["# create a new Chat with OpenAI\n", "llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "\n", "\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)\n", "\n", "# the retriever is an abstraction over the VectorStore that will be used during RAG\n", "retriever = vectorstore.as_retriever()\n", "\n", "# putting it together: set up the conversation chain with the GPT 3.5 LLM, the vector store and memory\n", "conversation_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory)"]}, {"cell_type": "markdown", "id": "bbbcb659-13ce-47ab-8a5e-01b930494964", "metadata": {}, "source": ["## Now we will bring this up in Gradio using the Chat interface -"]}, {"cell_type": "code", "execution_count": null, "id": "c3536590-85c7-4155-bd87-ae78a1467670", "metadata": {}, "outputs": [], "source": ["# Wrapping that in a function\n", "\n", "def chat(question, history):\n", "    result = conversation_chain.invoke({\"question\": question})\n", "    return result[\"answer\"]"]}, {"cell_type": "code", "execution_count": null, "id": "b252d8c1-61a8-406d-b57a-8f708a62b014", "metadata": {}, "outputs": [], "source": ["# And in Gradio:\n", "\n", "view = gr.ChatInterface(chat, type=\"messages\").launch(inbrowser=True)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}