# Sameer <PERSON>

**Quant AI/ML @ Wells Fargo | M.Tech. (CDS) @ IISc, Bangalore | B.Tech. (Mechanical) @ GCOE, Amravati**  
📍 Hyderabad, Telangana, India  
📧 sameer123<PERSON><PERSON><PERSON><PERSON>@gmail.com    
🔗 [LinkedIn](https://www.linkedin.com/in/sameer-khadatkar/)

---

## Summary

I currently serve as a Quantitative Analytics Specialist within Wells Fargo's Model Risk Management (MRM) team at India and Philippines. My primary responsibility involves validating AI/ML models, with a focus on fraud detection, as well as models used in marketing, credit scoring, and natural language processing (NLP). In this role, I ensure the conceptual soundness of models, conduct performance testing, conduct explainability analysis and rigorously challenge models by developing challenger models to detect weaknesses.

Additionally, I ensure compliance with regulatory standards set by Wells Fargo, in alignment with guidelines from the Federal Reserve and the OCC. I work closely with model development and risk management teams, providing validation feedback and recommending improvements. I also contribute to documentation and reporting, preparing validation reports, and ensuring the ongoing monitoring of model performance.

With a strong foundation in Machine Learning, Deep Learning, and High-Performance Computing gained during my graduate studies at the Indian Institute of Science, Bangalore, and a Bachelor's degree in Mechanical Engineering, I bring a unique blend of skills at the intersection of advanced technology and engineering. My expertise allows me to tackle complex challenges, drive innovation, and contribute to cutting-edge solutions in diverse industries.

---

## Professional Experience

### Wells Fargo International Solutions Private Ltd  
**Quantitative Analytics Specialist – AVP**  
📍 Hyderabad, Telangana, India  
📅 August 2022 – September 2023

- Collaborating with a team overseeing an inventory of ∼300 models focused on Fraud Detection, primarily utilizing Logistic Regression, Extreme Gradient Boosting (XGBoost), and Neural Network models.
- Conduct validation of AI/ML models by ensuring conceptual soundness, performing performance testing, carrying out explainability analysis, and developing surrogate, challenger, and offset models to uncover potential weaknesses.
- Joined the team during its expansion in India, playing a key role in building trust with US stakeholders. Recognized with the **Manager’s Spotlight Award** for outstanding dedication and contributions.
- Developing a module to assist Validators in benchmarking anomaly detection models (Isolation Forest, Extended Isolation Forest, Autoencoders, Histogram-Based Outlier Score (HBOS), etc.) and assessing them using clustering performance metrics.
- Created a validation playbook for fraud detection vendor models and developed an Excel-based policy library to facilitate quick reference for team members.

---

## Highlighted Projects at Wells Fargo

### ✅ Check Authorization Model | Validation

- Validated a high-impact machine learning model for check authorization, ensuring compliance with regulatory and bank's MRM standards.
- Reviewed model objectives, assumptions, architecture, and data pipeline.
- Assessed performance using AUC, recall, KS statistic, and PSI across time.
- Performed explainability analysis using multicollinearity checks, surrogate models (overall and segment level), SHAP, PDP, H-Statistic, 2D-PDPs, and sensitivity analysis.
- Identified local weaknesses through segmentation and built offset models to detect missed signals.
- Developed challenger models using YOLOv5, SigNet, TrOCR (Transformer-based OCR), XGBoost model, and pixel-based feature engineering.

### 🧠 Word Embedding Explainability Research

- Collaborated with the Bank’s Chief Model Risk Officer on a research project focused on the explainability of word embeddings using clustering techniques such as Spectral Clustering, HDBSCAN, and analysis of ReLU neural network activation patterns.
- Utilized Sentence Transformer embeddings (SBERT) and applied dimensionality reduction methods including PCA, UMAP, and t-SNE for cluster interpretation and visualization.
- Extended the research by developing a Mixture of Experts model leveraging XGBoost.

---

## Education

**Indian Institute of Science (IISc), Bangalore**  
📅 2020 – 2022  
🎓 Master of Technology (M.Tech.), Computational and Data Sciences  
📍 Bengaluru, Karnataka  
**CGPA:** 9.1 / 10.0

**Government College of Engineering, Amravati (GCoEA)**  
📅 2015 – 2019  
🎓 Bachelor of Technology (B.Tech.), Mechanical Engineering  
📍 Amravati, Maharashtra  
**CGPA:** 8.29 / 10.0

---

## Certifications

- Advanced Data Science with IBM (Coursera)
- HYPERMESH (SHELL MESH AND SOLID MESH)
- Introduction to Big Data (Coursera)
- MASTERCAM (Design, Turning and Milling)
- CREO PARAMETRIC

---

## Research Publication

**Subspace Recursive Fermi-Operator Expansion Strategies for Large-Scale DFT Eigenvalue Problems on HPC Architectures**  
📝 Sameer Khadatkar, Phani Motamarri (MATRIX Lab)
📅 July 20, 2023  
📚 *Journal of Chemical Physics, 159, 031102 (2023)*  
🔗 [Publication Link](https://pubs.aip.org/aip/jcp/article/159/3/031102/2903241/Subspace-recursive-Fermi-operator-expansion)

- Implemented recursive Fermi-operator expansion methods on multi-node CPU (PARAM Pravega) and GPU (ORNL Summit) systems for large-scale DFT problems.
- Applied mixed-precision strategies achieving 2× to 4× speedup over diagonalization.
- Benchmarked using MPI and SLATE for distributed dense linear algebra.

---

## Academic, Independent and Other Projects

- **LLM-Powered Multimodal Airline Chatbot**: Built a chatbot with GPT-4o-mini, supporting both text and voice, generating pop-art city images. Stack: Python, Gradio, custom tools.
- **Future Stock Price Prediction for MAANG**: Used yfinance, Stateful LSTM vs XGBoost. LSTM outperformed with ~0.02 MAE.
- **Duplicate Question Detection**: LSTM Siamese Network with Word2Vec and GloVe. GloVe performed better.
- **Music Genre Classification**: Used MFCCs and spectral features. Best result: 76% ± 3% accuracy with SVM.
- **Algorithm Implementation from Scratch**: PCA, LDA, GMM, TF-IDF, and backpropagation for DNNs.

---

## Skills

**Knowledge Areas:**  
Model Risk Management, Machine Learning, Deep Learning, High-Performance Computing

**Programming Languages:**  
Python, C, C++ (OpenMP, MPI, CUDA), SQL

**Python Libraries & Tools:**  
Numpy, Pandas, Scikit-Learn, PyTorch, TensorFlow (Keras), PySpark, Matplotlib

---

## Relevant Courses

- Machine Learning for Signal Processing (IISc)
- Advanced Data Science with IBM (Coursera)
- Deep Learning (NPTEL)
- Pattern Recognition and Neural Networks (NPTEL)
- Numerical Linear Algebra (IISc)
- Data Analysis and Visualization (IISc)
- Numerical Solution of Differential Equations (IISc)
- Parallel Programming (IISc)
- Introduction to Big Data (Coursera)
-  LLM Engineering: Master AI, Large Language Models & Agents (Udemy)

---

## Extracurricular Activities

- **Project Associate** at MATRIX Lab, CDS Department, IISc.
- **Teaching Assistant** for “DS284: Numerical Linear Algebra” at IISc.
- Led suspension operations for SAE BAJA Team at GCoE Amravati.
- Organized Annual Social Gathering as Joint Secretary at GCoE Amravati.

---

## Top Skills

- Data Reporting
- SQL
- Microsoft Excel
