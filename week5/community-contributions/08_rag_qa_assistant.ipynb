{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "3f78498d-dbc6-4e1b-a629-9ac9e44c8dd8", "metadata": {}, "source": ["# RAG-powered Q&A agent for Insurellm employees\n", "---\n", "\n", "An internal expert knowledge assistant for Insurellm employees, using Retrieval-Augmented Generation (RAG) to deliver fast, accurate, and cost-efficient answers to a wide range of internal queries,\n", "\n", "- 🌍 Task: Answer questions about Insurellm using naive RAG\n", "- 🧠 Models: OpenAI GPT via LangChain\n", "- 🔍 Retrieval: ChromaDB + OpenAI embeddings\n", "- 🚀 Tools:\n", "    - langchain: 0.3.21\n", "    - openai: 1.69.0\n", "    - chromadb: 0.6.3\n", "    - gradio: 5.23.1\n", "    - python: 3.11.11\n", "\n", "- ✨ Features:\n", "\n", "    - Loads PDF, text, and markdown files automatically\n", "    - Only updates when files actually change (saves time)\n", "    - Breaks documents into small, overlapping pieces for better search\n", "    - Finds the most relevant information using smart matching\n", "    - Remembers conversation history and shows where answers come from\n", "    - Only answers based on your documents (no made-up information\n", "    - Web chat interface with streaming responses\n", "    - Handles errors gracefully and detects duplicate content\n", "    - Tracks document details and keeps everything organized\n", "    - Ready for business use with built-in quality checks\n", "\n", "- 📤 Output: Streaming response with sources retrieved from the knowledge base\n", "- 🧑‍💻 Skill Level: Intermediate\n", "- ⚙️ Hardware: ✅ CPU is sufficient — no GPU required\n", "\n", "🛠️ **Requirements**: 🔑 OpenAI API Key \n", "\n", "⚙️ **Customizable by user**\n", "- 📝 Modify system and expansion prompts\n", "- 📁 Drop in new company documents\n", "- 🎯 Adjust retrieval top-k and similarity threshold\n", "\n", "This project currently uses a naive RAG approach, which limits the assistant's performance and accuracy. To improve response quality and reliability, more advanced RAG techniques will be needed — a more refined and powerful version is planned for future release.\n", "\n", "![](https://github.com/lisekarimi/lexo/blob/main/assets/08_naive_rag.png?raw=true)\n", "\n", "---\n", "📢 Find more LLM notebooks on my [GitHub repository](https://github.com/lisekarimi/lexo)"], "outputs": []}, {"cell_type": "markdown", "id": "b9abf112-72ca-431a-b7cf-b126e0a69a4d", "metadata": {}, "source": ["## 📥 Imports"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "abdef4fe-5055-4259-99c7-82a0525c0d35", "metadata": {}, "outputs": [], "source": ["# Standard library imports\n", "import os\n", "import hashlib\n", "from pathlib import Path\n", "from typing import List\n", "\n", "# Third-party imports\n", "import numpy as np\n", "import plotly.graph_objects as go\n", "from dotenv import load_dotenv\n", "from pydantic import Field\n", "from sklearn.manifold import TSNE\n", "import gradio as gr\n", "\n", "# LangChain core imports\n", "from langchain.document_loaders import TextLoader, PyPDFLoader\n", "from langchain.text_splitter import CharacterTextSplitter\n", "from langchain.schema import BaseRetriever, Document\n", "from langchain.schema.vectorstore import VectorStoreRetriever\n", "from langchain.callbacks.manager import CallbackManagerForRetrieverRun\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.chains import ConversationalRetrievalChain\n", "from langchain.prompts import PromptTemplate\n", "\n", "# LangChain integrations\n", "from langchain_openai import OpenAIEmbeddings, ChatOpenAI\n", "from langchain_chroma import Chroma"]}, {"cell_type": "markdown", "id": "79875c2d-4193-4fa8-95b8-ad128b1c84fb", "metadata": {}, "source": ["## 🔐 Load env variables and configuration"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "7b5ca4dd-a1c2-4fc6-844f-7b0c83008c99", "metadata": {}, "outputs": [], "source": ["# Load environment variables\n", "load_dotenv(override=True)\n", "\n", "# Configuration\n", "DATA_PATH = \"data/knowledge-base/\" # Use your path\n", "MODEL = \"gpt-4o-mini\"\n", "CHROMA_PATH = \"vector_db/chroma_insurellm\"\n", "\n", "# Explicitly access the OpenAI API key\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "if not openai_api_key:\n", "    print(\"❌ OPENAI_API_KEY is missing\")"]}, {"cell_type": "markdown", "id": "18e5b9a1-dca8-4b42-8517-174f653f30a7", "metadata": {}, "source": ["## 📄 Load files as Document objects into memory"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "ae72f98b-05a5-4758-9503-424a93055323", "metadata": {}, "outputs": [], "source": ["# Load .pdf, .txt, and .md documents with metadata, excluding Jupyter checkpoints.\n", "\n", "documents = []\n", "\n", "def add_metadata(doc, file_path):\n", "    doc.metadata[\"doc_type\"] = file_path.parent.name\n", "    doc.metadata[\"file_name\"] = file_path.name\n", "    if not doc.page_content.strip():\n", "        print(f\"⚠️ Empty content in {file_path}\")\n", "    # else:\n", "    #     print(doc)\n", "    #     print(\"-\" * 40)\n", "    return doc\n", "\n", "for file_path in Path(DATA_PATH).rglob(\"*\"):\n", "    if \".ipynb_checkpoints\" in file_path.parts:\n", "        continue\n", "\n", "    try:\n", "        if file_path.name.endswith(\".pdf\"):\n", "            docs = PyPDFLoader(str(file_path)).load()\n", "        elif file_path.name.endswith((\".txt\", \".md\")):\n", "            docs = TextLoader(str(file_path), encoding=\"utf-8\").load()\n", "        else:\n", "            continue\n", "    except Exception as e:\n", "        print(f\"❌ Skipped {file_path}: {e}\")\n", "        continue\n", "\n", "    documents.extend([add_metadata(doc, file_path) for doc in docs])\n", "\n", "print(f\"{len(documents)} documents loaded.\" if documents else \"No documents loaded.\")\n"]}, {"cell_type": "markdown", "id": "ed0fcc85-ca14-430a-bde2-db3d77f79143", "metadata": {}, "source": ["## ✂️ Splitting documents into chunks"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "e40d0487-6db4-4c3f-b0c6-e9aaf4e14b37", "metadata": {}, "outputs": [], "source": ["# Split documents into smaller chunks with overlapping characters for better context.\n", "text_splitter = CharacterTextSplitter(\n", "    chunk_size=1000,\n", "    chunk_overlap=200,\n", "    add_start_index=True  # Maintain chunk order (useful for context tracking)\n", ")\n", "\n", "# Load and split documents\n", "chunks = text_splitter.split_documents(documents)\n", "\n", "print(f\"Split {len(documents)} documents into {len(chunks)} chunks.\")\n", "\n", "def generate_chunk_id(text):\n", "    return hashlib.md5(text.encode(\"utf-8\")).hexdigest()\n", "\n", "# Add chunk_id to each chunk's metadata\n", "for chunk in chunks:\n", "    chunk.metadata[\"chunk_id\"] = generate_chunk_id(chunk.page_content) # Create an MD5 hash of the chunk's content\n", "    if not chunk.page_content.strip():\n", "        print(f\"⚠️ Empty chunk from: {chunk.metadata['file_name']}\")\n", "\n", "# Debug: print a few chunk metadatas to verify chunk_id is added\n", "for i, chunk in enumerate(chunks[:2]):\n", "    print(f\"Chunk {i+1} metadata:\", chunk.metadata)"]}, {"cell_type": "code", "execution_count": null, "id": "1faa604c-ddd9-4475-aaff-f4456629d77d", "metadata": {}, "outputs": [], "source": ["# Check for duplicate chunk IDs\n", "chunk_ids = [chunk.metadata[\"chunk_id\"] for chunk in chunks]\n", "duplicate_ids = [chunk_id for chunk_id in chunk_ids if chunk_ids.count(chunk_id) > 1]\n", "\n", "if duplicate_ids:\n", "    print(f\"Duplicate chunk IDs found: {duplicate_ids}\")\n", "else:\n", "    print(\"No duplicate chunks.\")"]}, {"cell_type": "markdown", "id": "d73f6bee-5df5-422a-a03f-e117a858370b", "metadata": {}, "source": ["## 🧠 Chuncks Embedding"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "a0135e85-6e0b-45a4-ab0d-b9fec56b63ac", "metadata": {}, "outputs": [], "source": ["embedding_function = OpenAIEmbeddings()\n", "# By default, OpenAIEmbeddings() uses OpenAI's text-embedding-ada-002 model - a multilingual model"]}, {"cell_type": "markdown", "id": "dbdb70eb-9902-4065-92b7-c72c2b8e15f7", "metadata": {}, "source": ["## 💾 Save embedded chunks to Chroma database"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "093959f0-6826-4594-9338-598094e24923", "metadata": {}, "outputs": [], "source": ["os.makedirs(CHROMA_PATH, exist_ok=True)\n", "\n", "def get_existing_chunk_ids(db_path):\n", "    try:\n", "        db_existing = Chroma(persist_directory=db_path)\n", "        results = db_existing._collection.get(include=[\"metadatas\"])\n", "        return set(\n", "            m[\"chunk_id\"] for m in results[\"metadatas\"]\n", "            if isinstance(m, dict) and \"chunk_id\" in m\n", "        )\n", "    except Exception as e:\n", "        print(\"❌ Error loading existing chunk IDs:\", e)\n", "        return set()\n", "\n", "# Get chunk_ids of current chunks\n", "new_chunk_ids = set([chunk.metadata[\"chunk_id\"] for chunk in chunks])\n", "\n", "# Get existing chunk_ids from Chroma\n", "existing_chunk_ids = get_existing_chunk_ids(CHROMA_PATH)\n", "\n", "# Compare\n", "if new_chunk_ids != existing_chunk_ids:\n", "    print(\"Chunk changes detected. Rebuilding Chroma DB.\")\n", "    db = Chroma.from_documents(documents=chunks, embedding=embedding_function, persist_directory=CHROMA_PATH)\n", "    print(f\"Saved {len(chunks)} chunks to {CHROMA_PATH}.\")\n", "else:\n", "    db = Chroma(persist_directory=CHROMA_PATH, embedding_function=embedding_function)\n", "    print(\"Chroma DB is up to date. Skipping regeneration.\")\n"]}, {"cell_type": "markdown", "id": "670b049a-0eca-41c1-a5a8-8ed4561168b2", "metadata": {}, "source": ["## 📊 Visualizing the Vector Store"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "1179ca76-2502-4bea-a4d8-4cbe149e92fa", "metadata": {}, "outputs": [], "source": ["collection = db._collection\n", "result = collection.get(include=['embeddings', 'documents', 'metadatas'])\n", "vectors = np.array(result['embeddings'])\n", "documents = result['documents']\n", "metadatas = result['metadatas']\n", "doc_types = [metadata['doc_type'] for metadata in metadatas]\n", "colors = [['blue', '#4B0082', 'red', '#8B4513'][['products', 'employees', 'contracts', 'company'].index(t)] for t in doc_types]"]}, {"cell_type": "code", "execution_count": null, "id": "c18bd18c-0b6c-4206-b2d0-a1bb59b3d39e", "metadata": {}, "outputs": [], "source": ["# We humans find it easier to visalize things in 2D!\n", "# Reduce the dimensionality of the vectors to 2D using t-SNE\n", "# (t-distributed stochastic neighbor embedding)\n", "\n", "tsne = TSNE(n_components=2, random_state=42)\n", "reduced_vectors = tsne.fit_transform(vectors)\n", "\n", "# Create the 2D scatter plot\n", "fig = go.Figure(data=[go.<PERSON><PERSON>(\n", "    x=reduced_vectors[:, 0],\n", "    y=reduced_vectors[:, 1],\n", "    mode='markers',\n", "    marker=dict(size=8, color=colors, opacity=0.8),\n", "    text=[f\"Type: {t}<br>Text: {d[:100]}...\" for t, d in zip(doc_types, documents)],\n", "    hoverinfo='text'\n", ")])\n", "\n", "fig.update_layout(\n", "    title='2D Chroma Vector Store Visualization',\n", "    plot_bgcolor='black',\n", "    paper_bgcolor='black',\n", "    font=dict(color='black'),\n", "    xaxis=dict(gridcolor='lightgray', zerolinecolor='lightgray'),\n", "    yaxis=dict(gridcolor='lightgray', zerolinecolor='lightgray'),\n", "    width=800,\n", "    height=600,\n", "    margin=dict(r=20, b=10, l=10, t=40),\n", ")\n", "\n", "\n", "fig.show()"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "b7d527de", "metadata": {}, "source": ["![image.png](attachment:image.png)"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "71364356-7edb-4e72-a7ba-f6284d4a998d", "metadata": {}, "outputs": [], "source": ["# Let's try 3D!\n", "\n", "tsne = TSNE(n_components=3, random_state=42)\n", "reduced_vectors = tsne.fit_transform(vectors)\n", "\n", "# Create the 3D scatter plot\n", "fig = go.Figure(data=[go.Scatter3d(\n", "    x=reduced_vectors[:, 0],\n", "    y=reduced_vectors[:, 1],\n", "    z=reduced_vectors[:, 2],\n", "    mode='markers',\n", "    marker=dict(size=8, color=colors, opacity=0.8),\n", "    text=[f\"Type: {t}<br>Text: {d[:100]}...\" for t, d in zip(doc_types, documents)],\n", "    hoverinfo='text'\n", ")])\n", "\n", "fig.update_layout(\n", "    title='3D Chroma Vector Store Visualization',\n", "    plot_bgcolor='black',\n", "    paper_bgcolor='black',\n", "    font=dict(color='white'),\n", "    scene=dict(\n", "        xaxis=dict(color='white', backgroundcolor='black', showbackground=True),\n", "        yaxis=dict(color='white', backgroundcolor='black', showbackground=True),\n", "        zaxis=dict(color='white', backgroundcolor='black', showbackground=True)\n", "    ),\n", "    width=900,\n", "    height=700,\n", "    margin=dict(r=20, b=10, l=10, t=40)\n", ")\n", "\n", "fig.show()"]}, {"attachments": {"image.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "163a82aa", "metadata": {}, "source": ["![image.png](attachment:image.png)"], "outputs": []}, {"cell_type": "markdown", "id": "c19187ba-1ac9-400e-ae9b-684682349e8b", "metadata": {}, "source": ["## 🔍 Query Chroma"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "6f8d1524-c822-4303-b1a0-a3440cc90f82", "metadata": {}, "outputs": [], "source": ["similarity_threshold = 0.5\n", "\n", "class MyVectorStoreRetriever(VectorStoreRetriever):\n", "    def _get_relevant_documents(\n", "        self, query: str, *, run_manager: CallbackManagerForRetrieverRun\n", "    ) -> List[Document]:\n", "        docs_and_similarities = (\n", "            self.vectorstore.similarity_search_with_relevance_scores(\n", "                query, **self.search_kwargs\n", "            )\n", "        )\n", "\n", "        # Make the score part of the document metadata\n", "        for doc, similarity in docs_and_similarities:\n", "            doc.metadata[\"score\"] = similarity\n", "\n", "        docs = [doc for doc, sim in docs_and_similarities if sim >= self.search_kwargs.get(\"score_threshold\", 0)]\n", "        return docs\n", "\n", "retriever = MyVectorStoreRetriever(\n", "   vectorstore=db,\n", "   search_type=\"similarity_score_threshold\",\n", "   search_kwargs={\"score_threshold\": similarity_threshold, \"k\": 20},\n", ")\n", "\n", "\n", "# Add metadata to the context sentto the LLM\n", "def inject_metadata(doc: Document) -> Document:\n", "    doc_type = doc.metadata.get(\"doc_type\", \"Unknown\")\n", "    file_name = doc.metadata.get(\"file_name\", \"Unknown\")\n", "    content = f\"[SOURCE: {doc_type} - {file_name}]\\n{doc.page_content}\"\n", "    return Document(page_content=content, metadata=doc.metadata)\n", "\n", "class MetadataInjectingRetriever(BaseRetriever):\n", "    base_retriever: BaseRetriever = Field()\n", "\n", "    def _get_relevant_documents(self, query: str):\n", "        docs = self.base_retriever.get_relevant_documents(query)\n", "        return [inject_metadata(doc) for doc in docs]\n", "\n", "retriever = MetadataInjectingRetriever(base_retriever=retriever)"]}, {"cell_type": "markdown", "id": "7446b2e0-23ca-4ad5-935d-1944f29b53cf", "metadata": {}, "source": ["## 🗣️ LLM and answers"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "6ab9093f-a6be-4ade-98f0-6911f47cb091", "metadata": {}, "outputs": [], "source": ["llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True)"]}, {"cell_type": "code", "execution_count": null, "id": "c4830b80-5d43-4d23-9ac6-410fc110b74b", "metadata": {}, "outputs": [], "source": ["# Define your question\n", "question = \"Who are the top 3 earners in 2023 with base, bonus, and total. Include names.\"\n", "\n", "# Define the system prompt\n", "system_prompt = \"\"\"\n", "You are an assistant that answers questions about the company Insurellm.\n", "\n", "Use the following chat history and retrieved documents to answer.\n", "\n", "Always base your answers strictly on the retrieved documents. If documents contain partial info, respond with what’s available. If there is no info, say so.\n", "\n", "Do not invent names, roles, or facts.\n", "\n", "You can use the document source information shown in the format [SOURCE: doc_type - file_name] if it helps you answer the question accurately.\n", "\n", "Always extract exact numbers (like number of employees, years, revenue, etc.) from the documents if they are mentioned.\n", "\n", "\n", "Chat History:\n", "{chat_history}\n", "\n", "Documents:\n", "{context}\n", "\n", "Question:\n", "{question}\n", "\"\"\"\n", "\n", "# Create the prompt template\n", "prompt = PromptTemplate(\n", "    input_variables=[\"chat_history\", \"context\", \"question\"],\n", "    template=system_prompt\n", ")\n", "\n", "# Set up LLM, memory, and conversation chain\n", "llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True, output_key=\"answer\")\n", "\n", "conversation_chain = ConversationalRetrievalChain.from_llm(\n", "    llm=llm,\n", "    retriever=retriever,\n", "    memory=memory,\n", "    return_source_documents=True,\n", "    combine_docs_chain_kwargs={\"prompt\": prompt}\n", ")\n", "\n", "# Format chat history\n", "chat_history_text = \"\\n\".join([f\"{msg.type.upper()}: {msg.content}\" for msg in memory.chat_memory.messages])\n", "\n", "# Retrieve docs using the original question\n", "retrieved_docs = retriever.get_relevant_documents(question)\n", "# print(\"\\n📦 Context sent to LLM:\\n\")\n", "# for i, doc in enumerate(retriever.get_relevant_documents(question), 1):\n", "#     print(f\"--- Document {i} ---\")\n", "#     print(doc.page_content)  # preview\n", "#     print()\n", "\n", "# Invoke the chain\n", "response = conversation_chain.invoke({\"question\": question})\n", "\n", "print(\"\\n🧠 Answer:\", response[\"answer\"])"]}, {"cell_type": "markdown", "id": "794f74c2-9b85-4d2c-8476-f4b29a001752", "metadata": {}, "source": ["## 🎛️ Gradio interface"], "outputs": []}, {"cell_type": "code", "execution_count": null, "id": "fa85878e-04e4-457e-8775-523194c26409", "metadata": {}, "outputs": [], "source": ["# 1. Define your system prompt\n", "\n", "system_prompt = \"\"\"\n", "You are an assistant that answers questions about the company Insurellm.\n", "\n", "Use the following chat history and retrieved documents to answer. Always base your answers strictly on the retrieved documents. If documents contain partial info, respond with what’s available. If there is no info, say so.\n", "\n", "You can use the document source information shown in the format [SOURCE: doc_type - file_name] if it helps answer the question accurately.\n", "\n", "Extract exact numbers (like number of employees, years, revenue, etc.) from the documents if mentioned. Do not invent names, roles, or facts.\n", "\n", "Behavior Guidelines:\n", "- Respond only when the user asks a question or requests clarification.\n", "- If the user greets you or expresses gratitude, respond warmly, but **avoid repeating the previous answer** unless explicitly requested for more details.\n", "- If the user asks \"thank you\" or similar, acknowledge it with gratitude, but **do not provide the same answer again** unless further information is requested.\n", "- If the user shares feedback, acknowledge it, thank them, and offer further assistance.\n", "- If the user expresses frustration or confusion, empathize, clarify, and offer further support.\n", "- If the user doesn't find a clear answer, encourage them to ask for clarification or provide additional details, and offer further assistance.\n", "\n", "Chat History:\n", "{chat_history}\n", "\n", "Documents:\n", "{context}\n", "\n", "Question:\n", "{question}\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "id": "e9c1276d-abd0-4766-88d0-a710c030014d", "metadata": {}, "outputs": [], "source": ["# 2. Create the prompt template\n", "\n", "prompt = PromptTemplate(\n", "    input_variables=[\"chat_history\", \"context\", \"question\"],\n", "    template=system_prompt\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "c0fe0466-7e87-4a40-b398-29d7e821f48f", "metadata": {}, "outputs": [], "source": ["# 3. Set up LLM, memory, retriever, and the updated chain\n", "\n", "llm = ChatOpenAI(temperature=0.7, model_name=MODEL)\n", "memory = ConversationBufferMemory(memory_key='chat_history', return_messages=True, output_key=\"answer\")\n", "conversation_chain = ConversationalRetrievalChain.from_llm(\n", "    llm=llm,\n", "    retriever=retriever,\n", "    memory=memory,\n", "    return_source_documents=True,\n", "    combine_docs_chain_kwargs={\"prompt\": prompt}\n", ")\n", "\n", "def chat(question, history):\n", "    result = conversation_chain.invoke({\"question\": question})\n", "    answer = \"\"\n", "    for chunk in result[\"answer\"]:\n", "        answer += chunk\n", "        yield answer"]}, {"cell_type": "code", "execution_count": null, "id": "2243e89c-c49b-416a-8152-f3679a9e2c05", "metadata": {}, "outputs": [], "source": ["view = gr.ChatInterface(chat, type=\"messages\").launch(inbrowser=True)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 5}