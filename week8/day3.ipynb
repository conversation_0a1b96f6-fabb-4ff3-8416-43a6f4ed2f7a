{"cells": [{"cell_type": "markdown", "id": "0df0d850-49eb-4a0b-a27a-146969db710d", "metadata": {}, "source": ["# The Price is Right\n", "\n", "Today we'll build another piece of the puzzle: a ScanningAgent that looks for promising deals by subscribing to RSS feeds."]}, {"cell_type": "code", "execution_count": null, "id": "d3763a79-8a5a-4300-8de4-93e85475af10", "metadata": {}, "outputs": [], "source": ["# imports\n", "\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from agents.deals import ScrapedDeal, DealSelection"]}, {"cell_type": "code", "execution_count": null, "id": "c6469e32-16c3-4443-9475-ade710ef6933", "metadata": {}, "outputs": [], "source": ["# Initialize and constants\n", "\n", "load_dotenv(override=True)\n", "os.environ['OPENAI_API_KEY'] = os.getenv('OPENAI_API_KEY', 'your-key-if-not-using-env')\n", "MODEL = 'gpt-4o-mini'\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "id": "afece9db-8cd4-46be-ac57-0b472e84da7d", "metadata": {}, "outputs": [], "source": ["deals = ScrapedDeal.fetch(show_progress=True)"]}, {"cell_type": "code", "execution_count": null, "id": "8cd15c4d-eb44-4601-bf0c-f945c1d8e3ec", "metadata": {}, "outputs": [], "source": ["len(deals)"]}, {"cell_type": "code", "execution_count": null, "id": "4259f30a-6455-49ed-8863-2f9ddd4776cb", "metadata": {}, "outputs": [], "source": ["deals[44].describe()"]}, {"cell_type": "code", "execution_count": null, "id": "8100e5ac-38f5-40c1-a712-08ae12c85038", "metadata": {}, "outputs": [], "source": ["system_prompt = \"\"\"You identify and summarize the 5 most detailed deals from a list, by selecting deals that have the most detailed, high quality description and the most clear price.\n", "Respond strictly in JSON with no explanation, using this format. You should provide the price as a number derived from the description. If the price of a deal isn't clear, do not include that deal in your response.\n", "Most important is that you respond with the 5 deals that have the most detailed product description with price. It's not important to mention the terms of the deal; most important is a thorough description of the product.\n", "Be careful with products that are described as \"$XXX off\" or \"reduced by $XXX\" - this isn't the actual price of the product. Only respond with products when you are highly confident about the price. \n", "\n", "{\"deals\": [\n", "    {\n", "        \"product_description\": \"Your clearly expressed summary of the product in 4-5 sentences. Details of the item are much more important than why it's a good deal. Avoid mentioning discounts and coupons; focus on the item itself. There should be a paragpraph of text for each item you choose.\",\n", "        \"price\": 99.99,\n", "        \"url\": \"the url as provided\"\n", "    },\n", "    ...\n", "]}\"\"\""]}, {"cell_type": "code", "execution_count": null, "id": "f4bca170-af71-40c9-9597-1d72980c74d8", "metadata": {}, "outputs": [], "source": ["user_prompt = \"\"\"Respond with the most promising 5 deals from this list, selecting those which have the most detailed, high quality product description and a clear price.\n", "Respond strictly in JSON, and only JSON. You should rephrase the description to be a summary of the product itself, not the terms of the deal.\n", "Remember to respond with a paragraph of text in the product_description field for each of the 5 items that you select.\n", "Be careful with products that are described as \"$XXX off\" or \"reduced by $XXX\" - this isn't the actual price of the product. Only respond with products when you are highly confident about the price. \n", "\n", "Deals:\n", "\n", "\"\"\"\n", "user_prompt += '\\n\\n'.join([deal.describe() for deal in deals])"]}, {"cell_type": "code", "execution_count": null, "id": "020947a6-561b-417b-98a0-a085e31d2ce3", "metadata": {}, "outputs": [], "source": ["print(user_prompt[:2000])"]}, {"cell_type": "code", "execution_count": null, "id": "7de46f74-868c-4127-8a68-cf2da7d600bb", "metadata": {}, "outputs": [], "source": ["def get_recommendations():\n", "    completion = openai.beta.chat.completions.parse(\n", "        model=\"gpt-4o-mini\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": system_prompt},\n", "            {\"role\": \"user\", \"content\": user_prompt}\n", "      ],\n", "        response_format=DealSelection\n", "    )\n", "    result = completion.choices[0].message.parsed\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "4c06270d-8c17-4d5a-9cfe-b6cefe788d5e", "metadata": {}, "outputs": [], "source": ["result = get_recommendations()"]}, {"cell_type": "code", "execution_count": null, "id": "84e62845-3338-441a-8161-c70097af4773", "metadata": {}, "outputs": [], "source": ["len(result.deals)"]}, {"cell_type": "code", "execution_count": null, "id": "e5554a0a-ae40-4684-ad3e-faa3d22e030c", "metadata": {}, "outputs": [], "source": ["result.deals[1]"]}, {"cell_type": "code", "execution_count": null, "id": "8bdc57fb-7497-47af-a643-6ba5a21cc17e", "metadata": {}, "outputs": [], "source": ["from agents.scanner_agent import ScannerAgent"]}, {"cell_type": "code", "execution_count": null, "id": "132278bc-217a-43a6-b6c4-724140c6a225", "metadata": {}, "outputs": [], "source": ["agent = ScannerAgent()\n", "result = agent.scan()"]}, {"cell_type": "code", "execution_count": null, "id": "2e1d013a-c930-4dad-901b-41433379e14b", "metadata": {}, "outputs": [], "source": ["result"]}, {"cell_type": "code", "execution_count": null, "id": "5ee2e837-1f1d-42d4-8bc4-51cccc343006", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}