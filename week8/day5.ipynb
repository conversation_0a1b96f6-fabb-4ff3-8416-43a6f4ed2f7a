{"cells": [{"cell_type": "markdown", "id": "a71ed017-e1b0-4299-88b3-f0eb05adc4df", "metadata": {}, "source": ["# The Price is Right\n", "\n", "The final step is to build a User Interface\n", "\n", "We will use more advanced aspects of Gradio - building piece by piece."]}, {"cell_type": "code", "execution_count": null, "id": "614c6202-4575-448d-98ee-78b735775d2b", "metadata": {}, "outputs": [], "source": ["import gradio as gr\n", "from deal_agent_framework import DealAgentFramework\n", "from agents.deals import Opportunity, Deal"]}, {"cell_type": "code", "execution_count": null, "id": "0534e714-5a9c-45c6-998c-3472ac0bb8b5", "metadata": {}, "outputs": [], "source": ["with gr.Blocks(title=\"The Price is Right\", fill_width=True) as ui:\n", "\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:24px\">The Price is Right - Deal Hunting Agentic AI</div>')\n", "    with gr.<PERSON>():\n", "        gr.Markdown('<div style=\"text-align: center;font-size:14px\">Autonomous agent framework that finds online deals, collaborating with a proprietary fine-tuned LLM deployed on Modal, and a RAG pipeline with a frontier model and Chroma.</div>')\n", "        \n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "18c12c10-750c-4da3-8df5-f2bc3393f9e0", "metadata": {}, "outputs": [], "source": ["# Updated to change from height to max_height due to change in Gradio v5\n", "# With much thanks to student <PERSON> for raising this\n", "\n", "with gr.Blocks(title=\"The Price is Right\", fill_width=True) as ui:\n", "\n", "    initial_deal = Deal(product_description=\"Example description\", price=100.0, url=\"https://cnn.com\")\n", "    initial_opportunity = Opportunity(deal=initial_deal, estimate=200.0, discount=100.0)\n", "    opportunities = gr.State([initial_opportunity])\n", "\n", "    def get_table(opps):\n", "        return [[opp.deal.product_description, opp.deal.price, opp.estimate, opp.discount, opp.deal.url] for opp in opps]\n", "\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:24px\">\"The Price is Right\" - Deal Hunting Agentic AI</div>')\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:14px\">Deals surfaced so far:</div>')\n", "    with gr.<PERSON>():\n", "        opportunities_dataframe = gr.Dataframe(\n", "            headers=[\"Description\", \"Price\", \"Estimate\", \"Discount\", \"URL\"],\n", "            wrap=True,\n", "            column_widths=[4, 1, 1, 1, 2],\n", "            row_count=10,\n", "            col_count=5,\n", "            max_height=400,\n", "        )\n", "\n", "    ui.load(get_table, inputs=[opportunities], outputs=[opportunities_dataframe])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "87106328-a17a-447e-90b9-c547613468da", "metadata": {}, "outputs": [], "source": ["agent_framework = DealAgentFramework()\n", "agent_framework.init_agents_as_needed()\n", "\n", "with gr.Blocks(title=\"The Price is Right\", fill_width=True) as ui:\n", "\n", "    initial_deal = Deal(product_description=\"Example description\", price=100.0, url=\"https://cnn.com\")\n", "    initial_opportunity = Opportunity(deal=initial_deal, estimate=200.0, discount=100.0)\n", "    opportunities = gr.State([initial_opportunity])\n", "\n", "    def get_table(opps):\n", "        return [[opp.deal.product_description, opp.deal.price, opp.estimate, opp.discount, opp.deal.url] for opp in opps]\n", "\n", "    def do_select(opportunities, selected_index: gr.SelectData):\n", "        row = selected_index.index[0]\n", "        opportunity = opportunities[row]\n", "        agent_framework.planner.messenger.alert(opportunity)\n", "\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:24px\">\"The Price is Right\" - Deal Hunting Agentic AI</div>')\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:14px\">Deals surfaced so far:</div>')\n", "    with gr.<PERSON>():\n", "        opportunities_dataframe = gr.Dataframe(\n", "            headers=[\"Description\", \"Price\", \"Estimate\", \"Discount\", \"URL\"],\n", "            wrap=True,\n", "            column_widths=[4, 1, 1, 1, 2],\n", "            row_count=10,\n", "            col_count=5,\n", "            max_height=400,\n", "        )\n", "\n", "    ui.load(get_table, inputs=[opportunities], outputs=[opportunities_dataframe])\n", "    opportunities_dataframe.select(do_select, inputs=[opportunities], outputs=[])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "markdown", "id": "ecfed67b-ebcb-4e17-ad15-a7151f940119", "metadata": {}, "source": ["# Time for the code\n", "\n", "And now we'll move to the price_is_right.py code, followed by price_is_right_final.py"]}, {"cell_type": "markdown", "id": "d783af8a-08a8-4e59-886a-7ca32f16bcf5", "metadata": {}, "source": ["# Running the final product\n", "\n", "## Just hit shift + enter in the next cell, and let the deals flow in!!\n", "\n", "Note that the Frontier Agent will use DeepSeek if there's a DEEPSEEK_API_KEY in your .env file, otherwise gpt-4o-mini."]}, {"cell_type": "code", "execution_count": null, "id": "48506465-1c7a-433f-a665-b277a8b4665c", "metadata": {}, "outputs": [], "source": ["!python price_is_right_final.py"]}, {"cell_type": "markdown", "id": "242d1243-fbec-4807-988b-8f70c8c9b806", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../important.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#900;\">But wait!! There's more..</h2>\n", "            <span style=\"color:#900;\">If you're not fed up of product prices yet 😂 I've built this out some more!<br/>\n", "            If you look in my repo <a href=\"https://github.com/ed-donner/tech2ai\">tech2ai</a>, in segment3/lab1 is a neural network implementation of the pricer in pure PyTorch. It does pretty well..<br/>\n", "            And if you look in my repo <a href=\"https://github.com/ed-donner/agentic\">Agentic</a> in the workshop folder is the same Agent project taken further. There's a new version of the PlanningAgent called AutonomousPlanningAgent that uses multiple Tools, and a MessagingAgent that uses claude-3.7 to write texts. The AutonomousPlanningAgent uses the fantastic OpenAI Agents SDK and the mighty MCP protocol from Anthropic.<br/>\n", "            If you're intrigued by Agents and MCP, and would like to learn more, then I also have a <a href=\"https://www.udemy.com/course/the-complete-agentic-ai-engineering-course/?referralCode=1B6986CDBD91FFC3651A\">companion course called the Complete Agentic AI Engineering Course</a> that might interest you (if you haven't had enough of me by now!!)\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "id": "331a2044-566f-4866-be4d-7542b7dfdf3f", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../thankyou.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#090;\">CONGRATULATIONS AND THANK YOU!!!</h2>\n", "            <span style=\"color:#090;\">\n", "                It's so fabulous that you've made it to the very end! My heartiest congratulations. Please stay in touch! I'm <a href=\"https://www.linkedin.com/in/eddonner/\">here on LinkedIn</a> if we're not already connected and I'm on X at <a href=\"https://x.com/edwarddonner\">@edwarddonner</a>. And my editor would be cross with me if I didn't mention one more time: it makes a HUGE difference when students rate this course on Udemy - it's one of the main ways that Udemy decides whether to show it to others. <br/><br/><PERSON><PERSON> thanks again for putting up with me for 8 weeks and getting all the way to the final cell! I'm excited to hear all about your career as an LLM Engineer. If you post on LinkedIn about completing the course and tag me, then I'll weigh in to amplify your achievement. <br/><b>You could not have picked a better time to be in this field.</b>\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "f9dd0a27-7d46-4c9e-bbe4-a61c9c899c99", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}