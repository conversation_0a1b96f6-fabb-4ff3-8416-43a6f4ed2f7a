{"cells": [{"cell_type": "markdown", "id": "23f53670-1a73-46ba-a754-4a497e8e0e64", "metadata": {}, "source": ["# The Price is Right\n", "\n", "First we'll polish off 2 more simple agents:\n", "\n", "The **Messaging Agent** to send push notifications\n", "\n", "The **Planning Agent** to coordinate activities\n", "\n", "Then we'll put it all together into an Agent Framework.\n", "\n", "For the Push Notification, we will be using a nifty platform called Pushover.  \n", "You'll need to set up a free account and add 2 tokens to your `.env` file:\n", "\n", "```\n", "PUSHOVER_USER=xxx\n", "PUSHOVER_TOKEN=xxx\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "80d683d9-9e92-44ae-af87-a413ca84db21", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from agents.messaging_agent import MessagingAgent"]}, {"cell_type": "code", "execution_count": null, "id": "5ba769cc-5301-4810-b01f-cab584cfb3b3", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "DB = \"products_vectorstore\""]}, {"cell_type": "code", "execution_count": null, "id": "e05cc427-3d2c-4792-ade1-d356f95a82a9", "metadata": {}, "outputs": [], "source": ["agent = MessagingAgent()"]}, {"cell_type": "code", "execution_count": null, "id": "5ec518f5-dae4-44b1-a185-d7eaf853ec00", "metadata": {}, "outputs": [], "source": ["agent.push(\"MASSIVE NEWS!!!\")"]}, {"cell_type": "markdown", "id": "7f2781ad-e122-4570-8fad-a2fe6452414e", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left;\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../resources.jpg\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#f71;\">Additional resource: more sophisticated planning agent</h2>\n", "            <span style=\"color:#f71;\">The Planning Agent that we use in the next cell is simply a python script that calls the other Agents; frankly that's all we require for this project. But if you're intrigued to see a more Autonomous version in which we give the Planning Agent tools and allow it to decide which Agents to call, see my implementation of <a href=\"https://github.com/ed-donner/agentic/blob/main/workshop/price_agents/autonomous_planning_agent.py\">AutonomousPlanningAgent</a> in my related repo, <a href=\"https://github.com/ed-donner/agentic\">Agentic</a>. This is an example with multiple tools that dynamically decides which function to call.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "id": "57b3a014-0b15-425a-a29b-6fefc5006dee", "metadata": {}, "outputs": [], "source": ["import chromadb\n", "DB = \"products_vectorstore\"\n", "client = chromadb.PersistentClient(path=DB)\n", "collection = client.get_or_create_collection('products')\n", "from agents.planning_agent import PlanningAgent"]}, {"cell_type": "code", "execution_count": null, "id": "a5c31c39-e357-446e-9cec-b4775c298941", "metadata": {}, "outputs": [], "source": ["planner = PlanningAgent(collection)"]}, {"cell_type": "code", "execution_count": null, "id": "d9ac771b-ea12-41c0-a7ce-05f12e27ad9e", "metadata": {}, "outputs": [], "source": ["planner.plan()"]}, {"cell_type": "code", "execution_count": null, "id": "8dd94a70-3202-452b-9ef0-551d6feb159b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}