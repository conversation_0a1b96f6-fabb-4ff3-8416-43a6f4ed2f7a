{"cells": [{"cell_type": "code", "execution_count": null, "id": "bc0e1c1c-be6a-4395-bbbd-eeafc9330d7e", "metadata": {}, "outputs": [], "source": ["# import modal\n", "import modal"]}, {"cell_type": "code", "execution_count": null, "id": "0d240622-8422-4c99-8464-c04d063e4cb6", "metadata": {}, "outputs": [], "source": ["# !modal setup"]}, {"cell_type": "code", "execution_count": null, "id": "0050c070-146f-4c26-8045-5ff284761199", "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "code", "execution_count": null, "id": "ebf35de4-ef8f-4e5b-8d4e-9a1771bfbe25", "metadata": {}, "outputs": [], "source": ["os.environ['PYTHONIOENCODING'] = 'utf-8'"]}, {"cell_type": "code", "execution_count": null, "id": "7f90d857-2f12-4521-bb90-28efd917f7d1", "metadata": {}, "outputs": [], "source": ["!modal deploy pricer_service"]}, {"cell_type": "code", "execution_count": null, "id": "1dec70ff-1986-4405-8624-9bbbe0ce1f4a", "metadata": {}, "outputs": [], "source": ["pricer = modal.Cls.from_name(\"pricer-service\", \"Pricer\")"]}, {"cell_type": "code", "execution_count": null, "id": "17776139-0d9e-4ad0-bcd0-82d3a92ca61f", "metadata": {}, "outputs": [], "source": ["pricer().price.remote(\"Quadcast HyperX condenser mic, connects via usb-c to your computer for crystal clear audio\")"]}, {"cell_type": "code", "execution_count": null, "id": "deb6cdf6-bcb0-49fb-8671-bb5eb22f02e3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}