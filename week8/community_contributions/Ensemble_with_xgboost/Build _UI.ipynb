{"cells": [{"cell_type": "markdown", "id": "a71ed017-e1b0-4299-88b3-f0eb05adc4df", "metadata": {}, "source": ["# Build UI\n", "\n", "We will use more advanced aspects of Gradio - building piece by piece."]}, {"cell_type": "code", "execution_count": null, "id": "614c6202-4575-448d-98ee-78b735775d2b", "metadata": {}, "outputs": [], "source": ["import gradio as gr\n", "from deal_agent_framework import DealAgentFramework\n", "from agents.deals import Opportunity, Deal"]}, {"cell_type": "code", "execution_count": null, "id": "0534e714-5a9c-45c6-998c-3472ac0bb8b5", "metadata": {}, "outputs": [], "source": ["with gr.Blocks(title=\"Deal Intel\", fill_width=True) as ui:\n", "\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:24px\">Deal Intel - Deal Hunting Agentic AI</div>')\n", "    with gr.<PERSON>():\n", "        gr.Markdown('<div style=\"text-align: center;font-size:14px\">Autonomous agent framework that finds online deals, collaborating with a proprietary fine-tuned LLM deployed on Modal, and a RAG pipeline with a frontier model and Chroma.</div>')\n", "        \n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "18c12c10-750c-4da3-8df5-f2bc3393f9e0", "metadata": {}, "outputs": [], "source": ["# Updated to change from height to max_height due to change in Gradio v5\n", "# With much thanks to student <PERSON> for raising this\n", "\n", "with gr.Blocks(title=\"Deal Intel\", fill_width=True) as ui:\n", "\n", "    initial_deal = Deal(product_description=\"Example description\", price=100.0, url=\"https://cnn.com\")\n", "    initial_opportunity = Opportunity(deal=initial_deal, estimate=200.0, discount=100.0)\n", "    opportunities = gr.State([initial_opportunity])\n", "\n", "    def get_table(opps):\n", "        return [[opp.deal.product_description, opp.deal.price, opp.estimate, opp.discount, opp.deal.url] for opp in opps]\n", "\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:24px\">\"Deal Intel\" - Deal Hunting Agentic AI</div>')\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:14px\">Deals surfaced so far:</div>')\n", "    with gr.<PERSON>():\n", "        opportunities_dataframe = gr.Dataframe(\n", "            headers=[\"Description\", \"Price\", \"Estimate\", \"Discount\", \"URL\"],\n", "            wrap=True,\n", "            column_widths=[4, 1, 1, 1, 2],\n", "            row_count=10,\n", "            col_count=5,\n", "            max_height=400,\n", "        )\n", "\n", "    ui.load(get_table, inputs=[opportunities], outputs=[opportunities_dataframe])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "87106328-a17a-447e-90b9-c547613468da", "metadata": {}, "outputs": [], "source": ["agent_framework = DealAgentFramework()\n", "agent_framework.init_agents_as_needed()\n", "\n", "with gr.Blocks(title=\"Deal Intel\", fill_width=True) as ui:\n", "\n", "    initial_deal = Deal(product_description=\"Example description\", price=100.0, url=\"https://cnn.com\")\n", "    initial_opportunity = Opportunity(deal=initial_deal, estimate=200.0, discount=100.0)\n", "    opportunities = gr.State([initial_opportunity])\n", "\n", "    def get_table(opps):\n", "        return [[opp.deal.product_description, opp.deal.price, opp.estimate, opp.discount, opp.deal.url] for opp in opps]\n", "\n", "    def do_select(opportunities, selected_index: gr.SelectData):\n", "        row = selected_index.index[0]\n", "        opportunity = opportunities[row]\n", "        agent_framework.planner.messenger.alert(opportunity)\n", "\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:24px\">\"Deal Intel\" - Deal Hunting Agentic AI</div>')\n", "    with gr.<PERSON>():\n", "        gr.<PERSON>('<div style=\"text-align: center;font-size:14px\">Deals surfaced so far:</div>')\n", "    with gr.<PERSON>():\n", "        opportunities_dataframe = gr.Dataframe(\n", "            headers=[\"Description\", \"Price\", \"Estimate\", \"Discount\", \"URL\"],\n", "            wrap=True,\n", "            column_widths=[4, 1, 1, 1, 2],\n", "            row_count=10,\n", "            col_count=5,\n", "            max_height=400,\n", "        )\n", "\n", "    ui.load(get_table, inputs=[opportunities], outputs=[opportunities_dataframe])\n", "    opportunities_dataframe.select(do_select, inputs=[opportunities], outputs=[])\n", "\n", "ui.launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "id": "48506465-1c7a-433f-a665-b277a8b4665c", "metadata": {}, "outputs": [], "source": ["!python price_is_right_final.py"]}, {"cell_type": "code", "execution_count": null, "id": "f9dd0a27-7d46-4c9e-bbe4-a61c9c899c99", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d1504cb8-7bf7-4dc4-9b1a-eaba79404aac", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ed84afd-4a04-43d6-8a3b-5143deaf96b2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}