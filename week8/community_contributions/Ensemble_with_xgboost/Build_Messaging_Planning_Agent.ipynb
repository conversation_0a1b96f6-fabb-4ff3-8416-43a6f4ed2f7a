{"cells": [{"cell_type": "markdown", "id": "23f53670-1a73-46ba-a754-4a497e8e0e64", "metadata": {}, "source": ["# Messaging Agent and Planning Agent\n", "\n", "Then we'll put it all together into an Agent Framework."]}, {"cell_type": "code", "execution_count": null, "id": "80d683d9-9e92-44ae-af87-a413ca84db21", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from agents.messaging_agent import MessagingAgent"]}, {"cell_type": "code", "execution_count": null, "id": "5ba769cc-5301-4810-b01f-cab584cfb3b3", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "DB = \"products_vectorstore\""]}, {"cell_type": "code", "execution_count": null, "id": "e05cc427-3d2c-4792-ade1-d356f95a82a9", "metadata": {}, "outputs": [], "source": ["agent = MessagingAgent()"]}, {"cell_type": "code", "execution_count": null, "id": "5ec518f5-dae4-44b1-a185-d7eaf853ec00", "metadata": {}, "outputs": [], "source": ["agent.push(\"MASSIVE NEWS!!!\")"]}, {"cell_type": "code", "execution_count": null, "id": "57b3a014-0b15-425a-a29b-6fefc5006dee", "metadata": {}, "outputs": [], "source": ["import chromadb\n", "DB = \"products_vectorstore\"\n", "client = chromadb.PersistentClient(path=DB)\n", "collection = client.get_or_create_collection('products')\n", "from agents.planning_agent import PlanningAgent"]}, {"cell_type": "code", "execution_count": null, "id": "a5c31c39-e357-446e-9cec-b4775c298941", "metadata": {}, "outputs": [], "source": ["planner = PlanningAgent(collection)"]}, {"cell_type": "code", "execution_count": null, "id": "d9ac771b-ea12-41c0-a7ce-05f12e27ad9e", "metadata": {}, "outputs": [], "source": ["planner.plan()"]}, {"cell_type": "code", "execution_count": null, "id": "d91ac0bb-738e-4be5-9074-d583190b1e2a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}